<?php

namespace Modules\Production\App\Console\Commands;

use Illuminate\Console\Command;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\Models\User;

class AssignSuperAdminProductionPermissions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'production:assign-super-admin 
                            {--user-id= : Specific user ID to assign super admin role}
                            {--email= : Specific user email to assign super admin role}
                            {--role-name=super-admin : Name of the super admin role}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Assign all production permissions to super admin role and optionally assign role to a user';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔐 Assigning Production Module permissions to Super Admin...');
        $this->newLine();

        // Get or create super admin role
        $roleName = $this->option('role-name');
        $superAdminRole = $this->getOrCreateSuperAdminRole($roleName);

        // Get all production permissions
        $productionPermissions = Permission::where('name', 'like', 'production.%')->get();

        if ($productionPermissions->isEmpty()) {
            $this->error('❌ No production permissions found!');
            $this->warn('Please run the following command first:');
            $this->line('php artisan module:seed Production');
            return 1;
        }

        // Assign all production permissions to super admin
        $superAdminRole->givePermissionTo($productionPermissions);

        $this->info("✅ Assigned {$productionPermissions->count()} production permissions to '{$superAdminRole->name}' role");
        $this->newLine();

        // Display assigned permissions
        $this->info('📋 Production permissions assigned:');
        foreach ($productionPermissions->chunk(5) as $chunk) {
            $permissionNames = $chunk->pluck('name')->map(fn($name) => "  • $name")->implode("\n");
            $this->line($permissionNames);
        }
        $this->newLine();

        // Optionally assign role to a specific user
        $user = $this->getUserToAssignRole();
        if ($user) {
            $this->assignRoleToUser($user, $superAdminRole);
        }

        $this->info('🎉 Super admin production permissions setup completed!');
        return 0;
    }

    /**
     * Get or create super admin role.
     */
    private function getOrCreateSuperAdminRole(string $roleName): Role
    {
        // Check for existing super admin roles
        $possibleRoles = [
            $roleName,
            'super-admin',
            'superadmin', 
            'super_admin',
            'admin',
            'administrator'
        ];

        foreach ($possibleRoles as $name) {
            $role = Role::where('name', $name)->first();
            if ($role) {
                $this->info("📍 Found existing role: '{$role->name}'");
                return $role;
            }
        }

        // Create new super admin role
        $role = Role::create([
            'name' => $roleName,
            'guard_name' => 'web'
        ]);

        $this->info("🆕 Created new super admin role: '{$role->name}'");
        return $role;
    }

    /**
     * Get user to assign role to.
     */
    private function getUserToAssignRole(): ?User
    {
        $userId = $this->option('user-id');
        $email = $this->option('email');

        if ($userId) {
            $user = User::find($userId);
            if (!$user) {
                $this->error("❌ User with ID {$userId} not found");
                return null;
            }
            return $user;
        }

        if ($email) {
            $user = User::where('email', $email)->first();
            if (!$user) {
                $this->error("❌ User with email {$email} not found");
                return null;
            }
            return $user;
        }

        // Ask if user wants to assign role to someone
        if ($this->confirm('Do you want to assign the super admin role to a specific user?')) {
            $email = $this->ask('Enter user email address:');
            $user = User::where('email', $email)->first();
            
            if (!$user) {
                $this->error("❌ User with email {$email} not found");
                return null;
            }
            
            return $user;
        }

        return null;
    }

    /**
     * Assign role to user.
     */
    private function assignRoleToUser(User $user, Role $role): void
    {
        if ($user->hasRole($role->name)) {
            $this->warn("⚠️  User '{$user->email}' already has the '{$role->name}' role");
            return;
        }

        $user->assignRole($role);
        $this->info("👤 Assigned '{$role->name}' role to user: {$user->email}");
        
        // Show user's current roles
        $userRoles = $user->roles->pluck('name')->implode(', ');
        $this->line("   User's current roles: {$userRoles}");
    }
}
