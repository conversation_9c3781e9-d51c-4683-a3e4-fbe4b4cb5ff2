<?php

namespace Modules\Production\App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Http\Response;
use Modules\Production\App\Models\CuttingPlan;
use Modules\Production\App\Models\Marker;
use Modules\Production\App\Models\CuttingEntry;
use Modules\Production\App\Models\FabricIssue;
use Modules\Production\App\Models\ProductionPlan;
use Modules\Production\App\Http\Requests\CuttingPlanRequest;
use Modules\Production\App\Http\Requests\MarkerRequest;
use Modules\Production\App\Http\Requests\CuttingEntryRequest;
use Modules\Production\App\Http\Requests\FabricIssueRequest;
use PDF;

class CuttingController extends Controller
{
    /**
     * Display a listing of cutting plans.
     */
    public function index(Request $request): View
    {
        $query = CuttingPlan::with(['productionPlan', 'markers', 'cuttingEntries']);
        
        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }
        
        if ($request->filled('style_number')) {
            $query->where('style_number', 'like', '%' . $request->style_number . '%');
        }
        
        if ($request->filled('po_number')) {
            $query->where('po_number', 'like', '%' . $request->po_number . '%');
        }
        
        if ($request->filled('cutting_date')) {
            $query->whereDate('cutting_date', $request->cutting_date);
        }
        
        // Apply sorting
        $sortBy = $request->get('sort_by', 'cutting_date');
        $sortDirection = $request->get('sort_direction', 'desc');
        $query->orderBy($sortBy, $sortDirection);
        
        $cuttingPlans = $query->paginate(15)->appends($request->query());
        
        return view('production::cutting.index', compact('cuttingPlans'));
    }

    /**
     * Show the form for creating a new cutting plan.
     */
    public function create(): View
    {
        $productionPlans = ProductionPlan::active()->get();
        return view('production::cutting.create', compact('productionPlans'));
    }

    /**
     * Store a newly created cutting plan.
     */
    public function store(CuttingPlanRequest $request): RedirectResponse
    {
        $data = $request->validated();
        $data['cutting_number'] = CuttingPlan::generateCuttingNumber();

        // Process size breakdown
        if ($request->has('size_breakdown_sizes') && $request->has('size_breakdown_quantities')) {
            $sizes = $request->size_breakdown_sizes;
            $quantities = $request->size_breakdown_quantities;
            $sizeBreakdown = [];

            for ($i = 0; $i < count($sizes); $i++) {
                if (!empty($sizes[$i]) && !empty($quantities[$i])) {
                    $sizeBreakdown[$sizes[$i]] = (int) $quantities[$i];
                }
            }

            $data['size_breakdown'] = $sizeBreakdown;

            // Calculate total pieces
            $data['total_pieces'] = array_sum($sizeBreakdown);
        }

        // Set default status if not provided
        if (empty($data['status'])) {
            $data['status'] = 'planned';
        }

        $cuttingPlan = CuttingPlan::create($data);

        return redirect()->route('production.cutting.show', $cuttingPlan)
            ->with('success', 'Cutting plan created successfully.');
    }

    /**
     * Display the specified cutting plan.
     */
    public function show(CuttingPlan $cuttingPlan): View
    {
        $cuttingPlan->load([
            'productionPlan',
            'markers',
            'cuttingEntries',
            'fabricIssues'
        ]);
        
        // Calculate cutting metrics
        $metrics = $this->calculateCuttingMetrics($cuttingPlan);
        
        return view('production::cutting.show', compact('cuttingPlan', 'metrics'));
    }

    /**
     * Show the form for editing the specified cutting plan.
     */
    public function edit(CuttingPlan $cuttingPlan): View
    {
        $productionPlans = ProductionPlan::active()->get();
        return view('production::cutting.edit', compact('cuttingPlan', 'productionPlans'));
    }

    /**
     * Update the specified cutting plan.
     */
    public function update(CuttingPlanRequest $request, CuttingPlan $cuttingPlan): RedirectResponse
    {
        $data = $request->validated();

        // Process size breakdown
        if ($request->has('size_breakdown_sizes') && $request->has('size_breakdown_quantities')) {
            $sizes = $request->size_breakdown_sizes;
            $quantities = $request->size_breakdown_quantities;
            $sizeBreakdown = [];

            for ($i = 0; $i < count($sizes); $i++) {
                if (!empty($sizes[$i]) && !empty($quantities[$i])) {
                    $sizeBreakdown[$sizes[$i]] = (int) $quantities[$i];
                }
            }

            $data['size_breakdown'] = $sizeBreakdown;

            // Calculate total pieces
            $data['total_pieces'] = array_sum($sizeBreakdown);
        }

        $cuttingPlan->update($data);

        return redirect()->route('production.cutting.show', $cuttingPlan)
            ->with('success', 'Cutting plan updated successfully.');
    }

    /**
     * Remove the specified cutting plan.
     */
    public function destroy(CuttingPlan $cuttingPlan): RedirectResponse
    {
        if ($cuttingPlan->status === 'in_progress') {
            return back()->with('error', 'Cannot delete a cutting plan that is in progress.');
        }
        
        $cuttingPlan->delete();
        
        return redirect()->route('production.cutting.index')
            ->with('success', 'Cutting plan deleted successfully.');
    }

    /**
     * Show marker planning interface.
     */
    public function markerPlanning(Request $request): View
    {
        $cuttingPlans = CuttingPlan::with(['markers'])
            ->where('status', '!=', 'completed')
            ->get();
        
        $selectedPlan = null;
        if ($request->filled('cutting_plan_id')) {
            $selectedPlan = CuttingPlan::with(['markers'])->find($request->cutting_plan_id);
        }
        
        return view('production::cutting.marker-planning', compact('cuttingPlans', 'selectedPlan'));
    }

    /**
     * Store a new marker.
     */
    public function storeMarker(MarkerRequest $request): RedirectResponse
    {
        $data = $request->validated();
        $data['marker_number'] = Marker::generateMarkerNumber();

        // Get the cutting plan to populate additional fields
        $cuttingPlan = CuttingPlan::find($data['cutting_plan_id']);
        if ($cuttingPlan) {
            $data['style_number'] = $cuttingPlan->style_number;
            $data['marker_width'] = $cuttingPlan->fabric_width;

            // Create default size ratio from cutting plan size breakdown
            if ($cuttingPlan->size_breakdown && is_array($cuttingPlan->size_breakdown)) {
                $data['size_ratio'] = $cuttingPlan->size_breakdown;
            } else {
                $data['size_ratio'] = ['M' => 1]; // Default ratio
            }
        }

        // Set default values
        $data['status'] = 'draft';
        $data['efficiency_percentage'] = $data['marker_efficiency'] ?? 85;
        $data['total_pieces_per_marker'] = $cuttingPlan->total_pieces ?? 0;
        $data['fabric_utilization'] = $data['fabric_utilization'] ?? 90;

        $marker = Marker::create($data);

        return back()->with('success', 'Marker created successfully.');
    }

    /**
     * Show cutting entries interface.
     */
    public function entries(Request $request): View
    {
        $query = CuttingEntry::with(['cuttingPlan', 'marker']);
        
        // Apply filters
        if ($request->filled('bundle_number')) {
            $query->where('bundle_number', 'like', '%' . $request->bundle_number . '%');
        }
        
        if ($request->filled('style_number')) {
            $query->where('style_number', 'like', '%' . $request->style_number . '%');
        }
        
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }
        
        if ($request->filled('cutting_date')) {
            $query->whereDate('cutting_datetime', $request->cutting_date);
        }
        
        $cuttingEntries = $query->orderBy('cutting_datetime', 'desc')
            ->paginate(20)
            ->appends($request->query());
        
        $cuttingPlans = CuttingPlan::where('status', '!=', 'completed')->get();
        
        return view('production::cutting.entries', compact('cuttingEntries', 'cuttingPlans'));
    }

    /**
     * Store a new cutting entry.
     */
    public function storeEntry(CuttingEntryRequest $request): RedirectResponse
    {
        $data = $request->validated();
        $data['bundle_number'] = CuttingEntry::generateBundleNumber();
        $data['cutting_datetime'] = now();
        
        $cuttingEntry = CuttingEntry::create($data);
        
        // Generate QR code
        $cuttingEntry->update(['qr_code' => $cuttingEntry->generateQRCode()]);
        
        return back()->with('success', 'Cutting entry created successfully.');
    }

    /**
     * Show fabric issues interface.
     */
    public function fabricIssues(Request $request): View
    {
        $query = FabricIssue::with(['cuttingPlan']);
        
        // Apply filters
        if ($request->filled('issue_number')) {
            $query->where('issue_number', 'like', '%' . $request->issue_number . '%');
        }
        
        if ($request->filled('fabric_type')) {
            $query->where('fabric_type', 'like', '%' . $request->fabric_type . '%');
        }
        
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }
        
        if ($request->filled('issue_date')) {
            $query->whereDate('issue_datetime', $request->issue_date);
        }
        
        $fabricIssues = $query->orderBy('issue_datetime', 'desc')
            ->paginate(20)
            ->appends($request->query());
        
        $cuttingPlans = CuttingPlan::where('status', '!=', 'completed')->get();
        
        return view('production::cutting.fabric-issues', compact('fabricIssues', 'cuttingPlans'));
    }

    /**
     * Store a new fabric issue.
     */
    public function storeFabricIssue(FabricIssueRequest $request): RedirectResponse
    {
        $data = $request->validated();
        $data['issue_number'] = FabricIssue::generateIssueNumber();
        $data['issue_datetime'] = now();

        $fabricIssue = FabricIssue::create($data);

        return back()->with('success', 'Fabric issue recorded successfully.');
    }

    /**
     * Show fabric issue details.
     */
    public function showFabricIssue(FabricIssue $fabricIssue): View
    {
        $fabricIssue->load(['cuttingPlan']);

        return view('production::cutting.fabric-issue-show', compact('fabricIssue'));
    }

    /**
     * Record fabric return.
     */
    public function recordFabricReturn(Request $request, FabricIssue $fabricIssue): RedirectResponse
    {
        $request->validate([
            'returned_quantity' => 'required|numeric|min:0|max:' . ($fabricIssue->issued_quantity - $fabricIssue->returned_quantity),
            'wastage_quantity' => 'nullable|numeric|min:0',
            'return_notes' => 'nullable|string|max:500'
        ]);

        $newReturnedQuantity = $fabricIssue->returned_quantity + $request->returned_quantity;
        $newWastageQuantity = $fabricIssue->wastage_quantity + ($request->wastage_quantity ?? 0);

        // Determine new status
        $newStatus = 'partially_returned';
        if ($newReturnedQuantity >= $fabricIssue->issued_quantity) {
            $newStatus = 'fully_returned';
        }

        $fabricIssue->update([
            'returned_quantity' => $newReturnedQuantity,
            'wastage_quantity' => $newWastageQuantity,
            'status' => $newStatus,
            'return_notes' => $request->return_notes
        ]);

        return back()->with('success', 'Fabric return recorded successfully.');
    }

    /**
     * Export cutting report as PDF.
     */
    public function exportPdf(Request $request): Response
    {
        $query = CuttingPlan::with(['productionPlan', 'markers', 'cuttingEntries', 'fabricIssues']);
        
        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }
        
        if ($request->filled('cutting_date_from')) {
            $query->whereDate('cutting_date', '>=', $request->cutting_date_from);
        }
        
        if ($request->filled('cutting_date_to')) {
            $query->whereDate('cutting_date', '<=', $request->cutting_date_to);
        }
        
        $cuttingPlans = $query->get();
        
        $pdf = PDF::loadView('production::exports.cutting-pdf', compact('cuttingPlans'));
        
        return $pdf->download('cutting-report-' . now()->format('Y-m-d') . '.pdf');
    }

    /**
     * Calculate cutting metrics.
     */
    private function calculateCuttingMetrics(CuttingPlan $cuttingPlan): array
    {
        $totalCut = $cuttingPlan->cuttingEntries->sum('quantity') ?? 0;
        $totalFabricConsumed = $cuttingPlan->cuttingEntries->sum('fabric_consumed') ?? 0;
        $totalFabricIssued = $cuttingPlan->fabricIssues->sum('issued_quantity') ?? 0;
        $totalFabricWastage = $cuttingPlan->fabricIssues->sum('wastage_quantity') ?? 0;

        // Ensure we have numeric values for calculations
        $totalCut = is_numeric($totalCut) ? (float) $totalCut : 0;
        $totalFabricConsumed = is_numeric($totalFabricConsumed) ? (float) $totalFabricConsumed : 0;
        $totalFabricIssued = is_numeric($totalFabricIssued) ? (float) $totalFabricIssued : 0;
        $totalFabricWastage = is_numeric($totalFabricWastage) ? (float) $totalFabricWastage : 0;

        $totalPieces = $cuttingPlan->total_pieces ?? 0;
        $totalPieces = is_numeric($totalPieces) ? (float) $totalPieces : 0;

        $cuttingProgress = $totalPieces > 0
            ? ($totalCut / $totalPieces) * 100
            : 0;

        $fabricUtilization = $totalFabricIssued > 0
            ? (($totalFabricConsumed / $totalFabricIssued) * 100)
            : 0;

        $wastagePercentage = $totalFabricIssued > 0
            ? (($totalFabricWastage / $totalFabricIssued) * 100)
            : 0;

        $cuttingEfficiency = $cuttingPlan->cutting_efficiency ?? 0;
        $cuttingEfficiency = is_numeric($cuttingEfficiency) ? (float) $cuttingEfficiency : 0;

        return [
            'total_cut' => $totalCut,
            'cutting_progress' => round($cuttingProgress, 2),
            'total_fabric_consumed' => $totalFabricConsumed,
            'total_fabric_issued' => $totalFabricIssued,
            'fabric_utilization' => round($fabricUtilization, 2),
            'wastage_percentage' => round($wastagePercentage, 2),
            'efficiency' => round($cuttingEfficiency, 2)
        ];
    }
}
