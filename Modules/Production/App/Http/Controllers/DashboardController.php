<?php

namespace Modules\Production\App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\Response;
use Modules\Production\App\Models\ProductionPlan;
use Modules\Production\App\Models\CuttingEntry;
use Modules\Production\App\Models\SewingEntry;
use Modules\Production\App\Models\FinishingEntry;
use Modules\Production\App\Models\WashingEntry;
use Modules\Production\App\Models\QualityInspection;
use Modules\Production\App\Models\SewingLine;
use Carbon\Carbon;
use PDF;
use Excel;

class DashboardController extends Controller
{
    /**
     * Display the production dashboard.
     */
    public function index(Request $request): View
    {
        $dateRange = $this->getDateRange($request);
        
        // Daily production summary
        $dailyProduction = $this->getDailyProductionSummary($dateRange);
        
        // Line efficiency data
        $lineEfficiency = $this->getLineEfficiencyData($dateRange);
        
        // Quality metrics
        $qualityMetrics = $this->getQualityMetrics($dateRange);
        
        // WIP levels
        $wipLevels = $this->getWipLevels();
        
        // Production bottlenecks
        $bottlenecks = $this->getProductionBottlenecks($dateRange);
        
        // Chart data for frontend
        $chartData = $this->getChartData($dateRange);
        
        return view('production::dashboard.index', compact(
            'dailyProduction',
            'lineEfficiency',
            'qualityMetrics',
            'wipLevels',
            'bottlenecks',
            'chartData',
            'dateRange'
        ));
    }

    /**
     * Export production summary as PDF.
     */
    public function exportProductionSummaryPdf(Request $request): Response
    {
        $dateRange = $this->getDateRange($request);
        $data = $this->getExportData($dateRange);
        
        $pdf = PDF::loadView('production::exports.production-summary-pdf', compact('data', 'dateRange'));
        
        return $pdf->download('production-summary-' . now()->format('Y-m-d') . '.pdf');
    }

    /**
     * Export production summary as Excel.
     */
    public function exportProductionSummaryExcel(Request $request)
    {
        $dateRange = $this->getDateRange($request);
        $data = $this->getExportData($dateRange);
        
        return Excel::download(
            new ProductionSummaryExport($data),
            'production-summary-' . now()->format('Y-m-d') . '.xlsx'
        );
    }

    /**
     * Get date range from request or default to current month.
     */
    private function getDateRange(Request $request): array
    {
        $startDate = $request->get('start_date', now()->startOfMonth()->format('Y-m-d'));
        $endDate = $request->get('end_date', now()->endOfMonth()->format('Y-m-d'));
        
        return [
            'start_date' => Carbon::parse($startDate),
            'end_date' => Carbon::parse($endDate)
        ];
    }

    /**
     * Get daily production summary.
     */
    private function getDailyProductionSummary(array $dateRange): array
    {
        $startDate = $dateRange['start_date'];
        $endDate = $dateRange['end_date'];
        
        return [
            'cutting' => CuttingEntry::whereBetween('cutting_datetime', [$startDate, $endDate])
                ->sum('quantity'),
            'sewing' => SewingEntry::whereBetween('input_datetime', [$startDate, $endDate])
                ->sum('output_quantity'),
            'finishing' => FinishingEntry::whereBetween('start_datetime', [$startDate, $endDate])
                ->sum('output_quantity'),
            'washing' => WashingEntry::whereBetween('wash_start_datetime', [$startDate, $endDate])
                ->sum('output_quantity'),
            'target' => ProductionPlan::active()
                ->whereBetween('start_date', [$startDate, $endDate])
                ->sum('target_quantity')
        ];
    }

    /**
     * Get line efficiency data.
     */
    private function getLineEfficiencyData(array $dateRange): array
    {
        $startDate = $dateRange['start_date'];
        $endDate = $dateRange['end_date'];
        
        return SewingLine::active()
            ->with(['sewingEntries' => function ($query) use ($startDate, $endDate) {
                $query->whereBetween('input_datetime', [$startDate, $endDate])
                      ->whereNotNull('efficiency_achieved');
            }])
            ->get()
            ->map(function ($line) {
                $avgEfficiency = $line->sewingEntries->avg('efficiency_achieved') ?? 0;
                return [
                    'line_number' => $line->line_number,
                    'line_name' => $line->line_name,
                    'target_efficiency' => $line->target_efficiency,
                    'actual_efficiency' => round($avgEfficiency, 2),
                    'variance' => round($avgEfficiency - $line->target_efficiency, 2)
                ];
            })
            ->toArray();
    }

    /**
     * Get quality metrics.
     */
    private function getQualityMetrics(array $dateRange): array
    {
        $startDate = $dateRange['start_date'];
        $endDate = $dateRange['end_date'];
        
        $inspections = QualityInspection::whereBetween('inspection_datetime', [$startDate, $endDate])->get();
        
        $totalInspections = $inspections->count();
        $passedInspections = $inspections->where('inspection_result', 'pass')->count();
        $failedInspections = $inspections->where('inspection_result', 'fail')->count();
        
        return [
            'total_inspections' => $totalInspections,
            'pass_rate' => $totalInspections > 0 ? round(($passedInspections / $totalInspections) * 100, 2) : 0,
            'fail_rate' => $totalInspections > 0 ? round(($failedInspections / $totalInspections) * 100, 2) : 0,
            'avg_defect_rate' => $inspections->avg('defect_percentage') ?? 0
        ];
    }

    /**
     * Get WIP levels.
     */
    private function getWipLevels(): array
    {
        return [
            'cutting_wip' => CuttingEntry::where('status', 'cut')->sum('quantity'),
            'sewing_wip' => SewingEntry::whereIn('status', ['input', 'in_progress'])->sum('input_quantity') -
                           SewingEntry::whereIn('status', ['output', 'completed'])->sum('output_quantity'),
            'finishing_wip' => FinishingEntry::whereIn('status', ['input', 'in_progress'])->sum('input_quantity'),
            'washing_wip' => WashingEntry::where('status', 'in_wash')->sum('input_quantity')
        ];
    }

    /**
     * Get production bottlenecks.
     */
    private function getProductionBottlenecks(array $dateRange): array
    {
        // Identify bottlenecks based on WIP levels and efficiency
        $lines = SewingLine::active()
            ->withCount(['sewingEntries as wip_count' => function ($query) {
                $query->whereIn('status', ['input', 'in_progress']);
            }])
            ->get();
        
        return $lines->where('wip_count', '>', 100)
                    ->sortByDesc('wip_count')
                    ->take(5)
                    ->values()
                    ->toArray();
    }

    /**
     * Get chart data for frontend visualization.
     */
    private function getChartData(array $dateRange): array
    {
        $startDate = $dateRange['start_date'];
        $endDate = $dateRange['end_date'];
        
        // Daily production trend
        $dailyTrend = [];
        $currentDate = $startDate->copy();
        
        while ($currentDate <= $endDate) {
            $dailyTrend[] = [
                'date' => $currentDate->format('Y-m-d'),
                'cutting' => CuttingEntry::whereDate('cutting_datetime', $currentDate)->sum('quantity'),
                'sewing' => SewingEntry::whereDate('input_datetime', $currentDate)->sum('output_quantity'),
                'finishing' => FinishingEntry::whereDate('start_datetime', $currentDate)->sum('output_quantity')
            ];
            $currentDate->addDay();
        }
        
        return [
            'daily_trend' => $dailyTrend,
            'efficiency_by_line' => $this->getLineEfficiencyData($dateRange),
            'quality_trend' => $this->getQualityTrendData($dateRange)
        ];
    }

    /**
     * Get quality trend data.
     */
    private function getQualityTrendData(array $dateRange): array
    {
        $startDate = $dateRange['start_date'];
        $endDate = $dateRange['end_date'];
        
        return QualityInspection::selectRaw('DATE(inspection_datetime) as date, AVG(defect_percentage) as avg_defect_rate')
            ->whereBetween('inspection_datetime', [$startDate, $endDate])
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->toArray();
    }

    /**
     * Get export data.
     */
    private function getExportData(array $dateRange): array
    {
        return [
            'daily_production' => $this->getDailyProductionSummary($dateRange),
            'line_efficiency' => $this->getLineEfficiencyData($dateRange),
            'quality_metrics' => $this->getQualityMetrics($dateRange),
            'wip_levels' => $this->getWipLevels()
        ];
    }
}
