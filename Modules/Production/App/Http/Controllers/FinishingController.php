<?php

namespace Modules\Production\App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Modules\Production\App\Models\FinishingEntry;
use Modules\Production\App\Models\FinishingProcess;
use Modules\Production\App\Models\SewingEntry;
use Modules\Production\App\Http\Requests\FinishingEntryRequest;
use Modules\Production\App\Http\Requests\FinishingProcessRequest;

class FinishingController extends Controller
{
    /**
     * Display a listing of finishing entries.
     */
    public function index(Request $request): View
    {
        $query = FinishingEntry::with(['sewingEntry', 'finishingProcess']);
        
        // Apply filters
        if ($request->filled('bundle_number')) {
            $query->where('bundle_number', 'like', '%' . $request->bundle_number . '%');
        }
        
        if ($request->filled('style_number')) {
            $query->where('style_number', 'like', '%' . $request->style_number . '%');
        }
        
        if ($request->filled('finishing_process_id')) {
            $query->where('finishing_process_id', $request->finishing_process_id);
        }
        
        if ($request->filled('qc_status')) {
            $query->where('qc_status', $request->qc_status);
        }
        
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }
        
        if ($request->filled('start_date')) {
            $query->whereDate('start_datetime', $request->start_date);
        }
        
        // Apply sorting
        $sortBy = $request->get('sort_by', 'start_datetime');
        $sortDirection = $request->get('sort_direction', 'desc');
        $query->orderBy($sortBy, $sortDirection);
        
        $finishingEntries = $query->paginate(20)->appends($request->query());
        
        $finishingProcesses = FinishingProcess::active()->ordered()->get();
        
        return view('production::finishing.index', compact('finishingEntries', 'finishingProcesses'));
    }

    /**
     * Show the form for creating a new finishing entry.
     */
    public function create(): View
    {
        $sewingEntries = SewingEntry::where('status', 'completed')
            ->with(['bundleTicket'])
            ->get();
        $finishingProcesses = FinishingProcess::active()->ordered()->get();
        
        return view('production::finishing.create', compact('sewingEntries', 'finishingProcesses'));
    }

    /**
     * Store a newly created finishing entry.
     */
    public function store(FinishingEntryRequest $request): RedirectResponse
    {
        $data = $request->validated();
        $data['start_datetime'] = now();
        
        // Get bundle details from sewing entry
        $sewingEntry = SewingEntry::with('bundleTicket')->find($data['sewing_entry_id']);
        $data['bundle_number'] = $sewingEntry->bundleTicket->bundle_number;
        $data['style_number'] = $sewingEntry->bundleTicket->style_number;
        $data['po_number'] = $sewingEntry->bundleTicket->po_number;
        $data['color'] = $sewingEntry->bundleTicket->color;
        $data['size'] = $sewingEntry->bundleTicket->size;
        
        $finishingEntry = FinishingEntry::create($data);
        
        return redirect()->route('production.finishing.show', $finishingEntry)
            ->with('success', 'Finishing entry created successfully.');
    }

    /**
     * Display the specified finishing entry.
     */
    public function show(FinishingEntry $finishingEntry): View
    {
        $finishingEntry->load(['sewingEntry.bundleTicket', 'finishingProcess', 'washingEntries']);
        
        return view('production::finishing.show', compact('finishingEntry'));
    }

    /**
     * Show the form for editing the specified finishing entry.
     */
    public function edit(FinishingEntry $finishingEntry): View
    {
        $finishingProcesses = FinishingProcess::active()->ordered()->get();
        
        return view('production::finishing.edit', compact('finishingEntry', 'finishingProcesses'));
    }

    /**
     * Update the specified finishing entry.
     */
    public function update(FinishingEntryRequest $request, FinishingEntry $finishingEntry): RedirectResponse
    {
        $data = $request->validated();
        
        // Calculate efficiency if output is provided
        if (isset($data['output_quantity']) && $data['output_quantity'] > 0) {
            $data['end_datetime'] = now();
            $data['efficiency_achieved'] = $finishingEntry->calculateEfficiency();
        }
        
        $finishingEntry->update($data);
        
        return redirect()->route('production.finishing.show', $finishingEntry)
            ->with('success', 'Finishing entry updated successfully.');
    }

    /**
     * Remove the specified finishing entry.
     */
    public function destroy(FinishingEntry $finishingEntry): RedirectResponse
    {
        if ($finishingEntry->status === 'completed') {
            return back()->with('error', 'Cannot delete a completed finishing entry.');
        }
        
        $finishingEntry->delete();
        
        return redirect()->route('production.finishing.index')
            ->with('success', 'Finishing entry deleted successfully.');
    }

    /**
     * Show finishing processes management.
     */
    public function processes(Request $request): View
    {
        $query = FinishingProcess::query();
        
        // Apply filters
        if ($request->filled('process_type')) {
            $query->where('process_type', $request->process_type);
        }
        
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }
        
        $finishingProcesses = $query->ordered()->paginate(15)->appends($request->query());
        
        return view('production::finishing.processes', compact('finishingProcesses'));
    }

    /**
     * Store a new finishing process.
     */
    public function storeProcess(FinishingProcessRequest $request): RedirectResponse
    {
        $data = $request->validated();
        
        // Generate process code if not provided
        if (empty($data['process_code'])) {
            $data['process_code'] = strtoupper(substr($data['process_name'], 0, 3)) . 
                                   str_pad(FinishingProcess::count() + 1, 3, '0', STR_PAD_LEFT);
        }
        
        FinishingProcess::create($data);
        
        return back()->with('success', 'Finishing process created successfully.');
    }

    /**
     * Get finishing statistics for dashboard.
     */
    public function getFinishingStats(Request $request): array
    {
        $dateRange = [
            'start_date' => $request->get('start_date', now()->startOfMonth()->format('Y-m-d')),
            'end_date' => $request->get('end_date', now()->endOfMonth()->format('Y-m-d'))
        ];
        
        $entries = FinishingEntry::whereBetween('start_datetime', [$dateRange['start_date'], $dateRange['end_date']]);
        
        return [
            'total_processed' => $entries->sum('output_quantity'),
            'total_defects' => $entries->sum('defect_quantity'),
            'total_rework' => $entries->sum('rework_quantity'),
            'defect_rate' => $entries->avg('defect_percentage') ?? 0,
            'qc_pass_rate' => $entries->where('qc_status', 'pass')->count() / max($entries->count(), 1) * 100,
            'process_efficiency' => $entries->avg('efficiency_achieved') ?? 0,
            'by_process' => FinishingProcess::active()
                ->withCount(['finishingEntries as total_processed' => function ($query) use ($dateRange) {
                    $query->whereBetween('start_datetime', [$dateRange['start_date'], $dateRange['end_date']])
                          ->selectRaw('SUM(output_quantity)');
                }])
                ->get()
                ->toArray()
        ];
    }
}
