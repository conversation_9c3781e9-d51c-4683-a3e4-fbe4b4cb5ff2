<?php

namespace Modules\Production\App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Http\Response;
use Modules\Production\App\Models\ProductionPlan;
use Modules\Production\App\Http\Requests\ProductionPlanRequest;
use PDF;
use Excel;

class PlanningController extends Controller
{
    /**
     * Display a listing of production plans.
     */
    public function index(Request $request): View
    {
        $query = ProductionPlan::query();
        
        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }
        
        if ($request->filled('style_number')) {
            $query->where('style_number', 'like', '%' . $request->style_number . '%');
        }
        
        if ($request->filled('po_number')) {
            $query->where('po_number', 'like', '%' . $request->po_number . '%');
        }
        
        if ($request->filled('start_date')) {
            $query->whereDate('start_date', '>=', $request->start_date);
        }
        
        if ($request->filled('end_date')) {
            $query->whereDate('end_date', '<=', $request->end_date);
        }
        
        // Apply sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortDirection = $request->get('sort_direction', 'desc');
        $query->orderBy($sortBy, $sortDirection);
        
        $productionPlans = $query->with(['cuttingPlans'])
            ->paginate(15)
            ->appends($request->query());
        
        return view('production::planning.index', compact('productionPlans'));
    }

    /**
     * Show the form for creating a new production plan.
     */
    public function create(): View
    {
        return view('production::planning.create');
    }

    /**
     * Store a newly created production plan.
     */
    public function store(ProductionPlanRequest $request): RedirectResponse
    {
        $data = $request->validated();
        $data['plan_number'] = ProductionPlan::generatePlanNumber();
        
        $productionPlan = ProductionPlan::create($data);
        
        return redirect()->route('production.planning.show', $productionPlan)
            ->with('success', 'Production plan created successfully.');
    }

    /**
     * Display the specified production plan.
     */
    public function show(ProductionPlan $productionPlan): View
    {
        $productionPlan->load([
            'cuttingPlans.markers',
            'cuttingPlans.cuttingEntries',
            'cuttingPlans.fabricIssues'
        ]);
        
        // Calculate progress metrics
        $metrics = $this->calculatePlanMetrics($productionPlan);
        
        return view('production::planning.show', compact('productionPlan', 'metrics'));
    }

    /**
     * Show the form for editing the specified production plan.
     */
    public function edit(ProductionPlan $productionPlan): View
    {
        return view('production::planning.edit', compact('productionPlan'));
    }

    /**
     * Update the specified production plan.
     */
    public function update(ProductionPlanRequest $request, ProductionPlan $productionPlan): RedirectResponse
    {
        $productionPlan->update($request->validated());
        
        return redirect()->route('production.planning.show', $productionPlan)
            ->with('success', 'Production plan updated successfully.');
    }

    /**
     * Remove the specified production plan.
     */
    public function destroy(ProductionPlan $productionPlan): RedirectResponse
    {
        if ($productionPlan->status === 'in_progress') {
            return back()->with('error', 'Cannot delete a production plan that is in progress.');
        }
        
        $productionPlan->delete();
        
        return redirect()->route('production.planning.index')
            ->with('success', 'Production plan deleted successfully.');
    }

    /**
     * Approve a production plan.
     */
    public function approve(ProductionPlan $productionPlan): RedirectResponse
    {
        if ($productionPlan->status !== 'draft') {
            return back()->with('error', 'Only draft plans can be approved.');
        }
        
        $productionPlan->update(['status' => 'approved']);
        
        return back()->with('success', 'Production plan approved successfully.');
    }

    /**
     * Export production plans as PDF.
     */
    public function exportPdf(Request $request): Response
    {
        $query = ProductionPlan::query();
        
        // Apply same filters as index
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }
        
        if ($request->filled('style_number')) {
            $query->where('style_number', 'like', '%' . $request->style_number . '%');
        }
        
        if ($request->filled('po_number')) {
            $query->where('po_number', 'like', '%' . $request->po_number . '%');
        }
        
        $productionPlans = $query->with(['cuttingPlans'])->get();
        
        $pdf = PDF::loadView('production::exports.planning-pdf', compact('productionPlans'));
        
        return $pdf->download('production-plans-' . now()->format('Y-m-d') . '.pdf');
    }

    /**
     * Export production plans as Excel.
     */
    public function exportExcel(Request $request)
    {
        $query = ProductionPlan::query();
        
        // Apply same filters as index
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }
        
        if ($request->filled('style_number')) {
            $query->where('style_number', 'like', '%' . $request->style_number . '%');
        }
        
        if ($request->filled('po_number')) {
            $query->where('po_number', 'like', '%' . $request->po_number . '%');
        }
        
        $productionPlans = $query->with(['cuttingPlans'])->get();
        
        return Excel::download(
            new ProductionPlansExport($productionPlans),
            'production-plans-' . now()->format('Y-m-d') . '.xlsx'
        );
    }

    /**
     * Calculate production plan metrics.
     */
    private function calculatePlanMetrics(ProductionPlan $productionPlan): array
    {
        $totalCut = $productionPlan->cuttingPlans->sum(function ($plan) {
            return $plan->cuttingEntries->sum('quantity');
        });
        
        $totalFabricIssued = $productionPlan->cuttingPlans->sum(function ($plan) {
            return $plan->fabricIssues->sum('issued_quantity');
        });
        
        $totalFabricConsumed = $productionPlan->cuttingPlans->sum(function ($plan) {
            return $plan->fabricIssues->sum('consumed_quantity');
        });
        
        $cuttingProgress = $productionPlan->target_quantity > 0 
            ? ($totalCut / $productionPlan->target_quantity) * 100 
            : 0;
        
        $fabricUtilization = $totalFabricIssued > 0 
            ? ($totalFabricConsumed / $totalFabricIssued) * 100 
            : 0;
        
        return [
            'total_cut' => $totalCut,
            'cutting_progress' => round($cuttingProgress, 2),
            'total_fabric_issued' => $totalFabricIssued,
            'total_fabric_consumed' => $totalFabricConsumed,
            'fabric_utilization' => round($fabricUtilization, 2),
            'days_remaining' => now()->diffInDays($productionPlan->end_date, false),
            'is_on_schedule' => $cuttingProgress >= ($productionPlan->production_duration > 0 
                ? (now()->diffInDays($productionPlan->start_date) / $productionPlan->production_duration) * 100 
                : 0)
        ];
    }
}
