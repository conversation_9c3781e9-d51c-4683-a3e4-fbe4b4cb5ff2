<?php

namespace Modules\Production\App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Http\Response;
use Modules\Production\App\Models\QualityInspection;
use Modules\Production\App\Models\DefectLog;
use Modules\Production\App\Models\AqlChart;
use Modules\Production\App\Models\SewingEntry;
use Modules\Production\App\Models\FinishingEntry;
use Modules\Production\App\Models\WashingEntry;
use Modules\Production\App\Http\Requests\QualityInspectionRequest;
use Modules\Production\App\Http\Requests\AqlChartRequest;
use PDF;

class QualityController extends Controller
{
    /**
     * Display a listing of quality inspections.
     */
    public function index(Request $request): View
    {
        $query = QualityInspection::with(['inspectable', 'aqlChart', 'defectLogs']);
        
        // Apply filters
        if ($request->filled('inspection_number')) {
            $query->where('inspection_number', 'like', '%' . $request->inspection_number . '%');
        }
        
        if ($request->filled('bundle_number')) {
            $query->where('bundle_number', 'like', '%' . $request->bundle_number . '%');
        }
        
        if ($request->filled('inspection_stage')) {
            $query->where('inspection_stage', $request->inspection_stage);
        }
        
        if ($request->filled('inspection_result')) {
            $query->where('inspection_result', $request->inspection_result);
        }
        
        if ($request->filled('inspector_name')) {
            $query->where('inspector_name', 'like', '%' . $request->inspector_name . '%');
        }
        
        if ($request->filled('inspection_date')) {
            $query->whereDate('inspection_datetime', $request->inspection_date);
        }
        
        // Apply sorting
        $sortBy = $request->get('sort_by', 'inspection_datetime');
        $sortDirection = $request->get('sort_direction', 'desc');
        $query->orderBy($sortBy, $sortDirection);
        
        $qualityInspections = $query->paginate(20)->appends($request->query());
        
        return view('production::quality.index', compact('qualityInspections'));
    }

    /**
     * Show the form for creating a new quality inspection.
     */
    public function create(Request $request): View
    {
        $inspectableType = $request->get('inspectable_type', 'sewing');
        
        $inspectableItems = $this->getInspectableItems($inspectableType);
        $aqlCharts = AqlChart::all();
        
        return view('production::quality.create', compact('inspectableItems', 'aqlCharts', 'inspectableType'));
    }

    /**
     * Store a newly created quality inspection.
     */
    public function store(QualityInspectionRequest $request): RedirectResponse
    {
        $data = $request->validated();
        $data['inspection_number'] = QualityInspection::generateInspectionNumber();
        $data['inspection_datetime'] = now();
        
        // Get inspectable item details
        $inspectable = $this->getInspectableModel($data['inspectable_type'])::find($data['inspectable_id']);
        $data = array_merge($data, $this->extractInspectableDetails($inspectable));
        
        // Calculate defect percentage
        $data['defect_percentage'] = $data['inspected_quantity'] > 0 
            ? ($data['defect_quantity'] / $data['inspected_quantity']) * 100 
            : 0;
        
        // Determine inspection result
        if ($data['aql_chart_id']) {
            $aqlChart = AqlChart::find($data['aql_chart_id']);
            $data['inspection_result'] = $aqlChart->determineResult($data['defect_quantity']);
        } else {
            $data['inspection_result'] = $this->determineResultWithoutAql($data);
        }
        
        $qualityInspection = QualityInspection::create($data);
        
        // Create defect logs if provided
        if ($request->has('defect_logs')) {
            $this->createDefectLogs($qualityInspection, $request->defect_logs);
        }
        
        return redirect()->route('production.quality.show', $qualityInspection)
            ->with('success', 'Quality inspection created successfully.');
    }

    /**
     * Display the specified quality inspection.
     */
    public function show(QualityInspection $qualityInspection): View
    {
        $qualityInspection->load(['inspectable', 'aqlChart', 'defectLogs']);
        
        return view('production::quality.show', compact('qualityInspection'));
    }

    /**
     * Show the form for editing the specified quality inspection.
     */
    public function edit(QualityInspection $qualityInspection): View
    {
        $aqlCharts = AqlChart::all();
        
        return view('production::quality.edit', compact('qualityInspection', 'aqlCharts'));
    }

    /**
     * Update the specified quality inspection.
     */
    public function update(QualityInspectionRequest $request, QualityInspection $qualityInspection): RedirectResponse
    {
        $data = $request->validated();
        
        // Recalculate defect percentage
        $data['defect_percentage'] = $data['inspected_quantity'] > 0 
            ? ($data['defect_quantity'] / $data['inspected_quantity']) * 100 
            : 0;
        
        // Redetermine inspection result
        if ($data['aql_chart_id']) {
            $aqlChart = AqlChart::find($data['aql_chart_id']);
            $data['inspection_result'] = $aqlChart->determineResult($data['defect_quantity']);
        } else {
            $data['inspection_result'] = $this->determineResultWithoutAql($data);
        }
        
        $qualityInspection->update($data);
        
        return redirect()->route('production.quality.show', $qualityInspection)
            ->with('success', 'Quality inspection updated successfully.');
    }

    /**
     * Remove the specified quality inspection.
     */
    public function destroy(QualityInspection $qualityInspection): RedirectResponse
    {
        $qualityInspection->delete();
        
        return redirect()->route('production.quality.index')
            ->with('success', 'Quality inspection deleted successfully.');
    }

    /**
     * Show AQL charts management.
     */
    public function aqlCharts(Request $request): View
    {
        $query = AqlChart::query();
        
        // Apply filters
        if ($request->filled('aql_level')) {
            $query->where('aql_level', $request->aql_level);
        }
        
        if ($request->filled('inspection_level')) {
            $query->where('inspection_level', $request->inspection_level);
        }
        
        if ($request->filled('inspection_type')) {
            $query->where('inspection_type', $request->inspection_type);
        }
        
        $aqlCharts = $query->orderBy('aql_level')->orderBy('lot_size_min')->paginate(15)->appends($request->query());
        
        return view('production::quality.aql-charts', compact('aqlCharts'));
    }

    /**
     * Store a new AQL chart.
     */
    public function storeAqlChart(AqlChartRequest $request): RedirectResponse
    {
        AqlChart::create($request->validated());
        
        return back()->with('success', 'AQL chart created successfully.');
    }

    /**
     * Show quality reports.
     */
    public function reports(Request $request): View
    {
        $dateRange = [
            'start_date' => $request->get('start_date', now()->startOfMonth()->format('Y-m-d')),
            'end_date' => $request->get('end_date', now()->endOfMonth()->format('Y-m-d'))
        ];
        
        $qualityMetrics = $this->getQualityMetrics($dateRange);
        $defectAnalysis = $this->getDefectAnalysis($dateRange);
        $trendData = $this->getTrendData($dateRange);
        
        return view('production::quality.reports', compact('qualityMetrics', 'defectAnalysis', 'trendData', 'dateRange'));
    }

    /**
     * Show defect analysis.
     */
    public function defectAnalysis(Request $request): View
    {
        $dateRange = [
            'start_date' => $request->get('start_date', now()->startOfMonth()->format('Y-m-d')),
            'end_date' => $request->get('end_date', now()->endOfMonth()->format('Y-m-d'))
        ];
        
        $defectAnalysis = $this->getDetailedDefectAnalysis($dateRange);
        
        return view('production::quality.defect-analysis', compact('defectAnalysis', 'dateRange'));
    }

    /**
     * Show trend analysis.
     */
    public function trendAnalysis(Request $request): View
    {
        $dateRange = [
            'start_date' => $request->get('start_date', now()->startOfMonth()->format('Y-m-d')),
            'end_date' => $request->get('end_date', now()->endOfMonth()->format('Y-m-d'))
        ];
        
        $trendData = $this->getDetailedTrendData($dateRange);
        
        return view('production::quality.trend-analysis', compact('trendData', 'dateRange'));
    }

    /**
     * Export quality report as PDF.
     */
    public function exportPdf(Request $request): Response
    {
        $dateRange = [
            'start_date' => $request->get('start_date', now()->startOfMonth()->format('Y-m-d')),
            'end_date' => $request->get('end_date', now()->endOfMonth()->format('Y-m-d'))
        ];
        
        $qualityInspections = QualityInspection::with(['defectLogs'])
            ->whereBetween('inspection_datetime', [$dateRange['start_date'], $dateRange['end_date']])
            ->get();
        
        $qualityMetrics = $this->getQualityMetrics($dateRange);
        $defectAnalysis = $this->getDefectAnalysis($dateRange);
        
        $pdf = PDF::loadView('production::exports.quality-pdf', compact('qualityInspections', 'qualityMetrics', 'defectAnalysis', 'dateRange'));
        
        return $pdf->download('quality-report-' . now()->format('Y-m-d') . '.pdf');
    }

    /**
     * Get inspectable items based on type.
     */
    private function getInspectableItems(string $type): \Illuminate\Database\Eloquent\Collection
    {
        switch ($type) {
            case 'sewing':
                return SewingEntry::where('status', 'completed')->with('bundleTicket')->get();
            case 'finishing':
                return FinishingEntry::where('status', 'completed')->get();
            case 'washing':
                return WashingEntry::where('status', 'completed')->get();
            default:
                return collect();
        }
    }

    /**
     * Get inspectable model class.
     */
    private function getInspectableModel(string $type): string
    {
        switch ($type) {
            case 'sewing':
                return SewingEntry::class;
            case 'finishing':
                return FinishingEntry::class;
            case 'washing':
                return WashingEntry::class;
            default:
                return SewingEntry::class;
        }
    }

    /**
     * Extract details from inspectable item.
     */
    private function extractInspectableDetails($inspectable): array
    {
        if ($inspectable instanceof SewingEntry) {
            return [
                'bundle_number' => $inspectable->bundleTicket->bundle_number,
                'style_number' => $inspectable->bundleTicket->style_number,
                'po_number' => $inspectable->bundleTicket->po_number,
                'color' => $inspectable->bundleTicket->color,
                'size' => $inspectable->bundleTicket->size
            ];
        } else {
            return [
                'bundle_number' => $inspectable->bundle_number,
                'style_number' => $inspectable->style_number,
                'po_number' => $inspectable->po_number,
                'color' => $inspectable->color,
                'size' => $inspectable->size
            ];
        }
    }

    /**
     * Determine result without AQL chart.
     */
    private function determineResultWithoutAql(array $data): string
    {
        if ($data['critical_defects'] > 0) {
            return 'fail';
        } elseif ($data['defect_percentage'] > 4.0) {
            return 'fail';
        } elseif ($data['defect_percentage'] > 2.5) {
            return 'conditional_pass';
        } else {
            return 'pass';
        }
    }

    /**
     * Create defect logs.
     */
    private function createDefectLogs(QualityInspection $inspection, array $defectLogs): void
    {
        foreach ($defectLogs as $defectData) {
            $defectData['quality_inspection_id'] = $inspection->id;
            $defectData['detection_datetime'] = now();
            DefectLog::create($defectData);
        }
    }

    /**
     * Get quality metrics.
     */
    private function getQualityMetrics(array $dateRange): array
    {
        $inspections = QualityInspection::whereBetween('inspection_datetime', [$dateRange['start_date'], $dateRange['end_date']])->get();
        
        $totalInspections = $inspections->count();
        $passedInspections = $inspections->where('inspection_result', 'pass')->count();
        $failedInspections = $inspections->where('inspection_result', 'fail')->count();
        
        return [
            'total_inspections' => $totalInspections,
            'pass_rate' => $totalInspections > 0 ? round(($passedInspections / $totalInspections) * 100, 2) : 0,
            'fail_rate' => $totalInspections > 0 ? round(($failedInspections / $totalInspections) * 100, 2) : 0,
            'avg_defect_rate' => $inspections->avg('defect_percentage') ?? 0,
            'critical_defects' => $inspections->sum('critical_defects'),
            'major_defects' => $inspections->sum('major_defects'),
            'minor_defects' => $inspections->sum('minor_defects')
        ];
    }

    /**
     * Get defect analysis.
     */
    private function getDefectAnalysis(array $dateRange): array
    {
        return DefectLog::whereBetween('detection_datetime', [$dateRange['start_date'], $dateRange['end_date']])
            ->selectRaw('defect_category, defect_severity, COUNT(*) as count, SUM(defect_count) as total_defects')
            ->groupBy('defect_category', 'defect_severity')
            ->get()
            ->toArray();
    }

    /**
     * Get trend data.
     */
    private function getTrendData(array $dateRange): array
    {
        return QualityInspection::selectRaw('DATE(inspection_datetime) as date, AVG(defect_percentage) as avg_defect_rate, COUNT(*) as inspection_count')
            ->whereBetween('inspection_datetime', [$dateRange['start_date'], $dateRange['end_date']])
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->toArray();
    }

    /**
     * Get detailed defect analysis.
     */
    private function getDetailedDefectAnalysis(array $dateRange): array
    {
        return [
            'by_category' => DefectLog::whereBetween('detection_datetime', [$dateRange['start_date'], $dateRange['end_date']])
                ->selectRaw('defect_category, COUNT(*) as count, SUM(defect_count) as total_defects, SUM(cost_impact) as total_cost')
                ->groupBy('defect_category')
                ->get()
                ->toArray(),
            'by_severity' => DefectLog::whereBetween('detection_datetime', [$dateRange['start_date'], $dateRange['end_date']])
                ->selectRaw('defect_severity, COUNT(*) as count, SUM(defect_count) as total_defects')
                ->groupBy('defect_severity')
                ->get()
                ->toArray(),
            'top_defects' => DefectLog::whereBetween('detection_datetime', [$dateRange['start_date'], $dateRange['end_date']])
                ->selectRaw('defect_name, COUNT(*) as count, SUM(defect_count) as total_defects')
                ->groupBy('defect_name')
                ->orderByDesc('total_defects')
                ->limit(10)
                ->get()
                ->toArray()
        ];
    }

    /**
     * Get detailed trend data.
     */
    private function getDetailedTrendData(array $dateRange): array
    {
        return [
            'daily_trend' => QualityInspection::selectRaw('DATE(inspection_datetime) as date, AVG(defect_percentage) as avg_defect_rate, COUNT(*) as inspection_count, SUM(CASE WHEN inspection_result = "pass" THEN 1 ELSE 0 END) as pass_count')
                ->whereBetween('inspection_datetime', [$dateRange['start_date'], $dateRange['end_date']])
                ->groupBy('date')
                ->orderBy('date')
                ->get()
                ->toArray(),
            'stage_trend' => QualityInspection::selectRaw('inspection_stage, AVG(defect_percentage) as avg_defect_rate, COUNT(*) as inspection_count')
                ->whereBetween('inspection_datetime', [$dateRange['start_date'], $dateRange['end_date']])
                ->groupBy('inspection_stage')
                ->get()
                ->toArray()
        ];
    }
}
