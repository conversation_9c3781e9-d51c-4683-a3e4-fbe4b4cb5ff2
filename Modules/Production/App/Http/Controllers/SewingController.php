<?php

namespace Modules\Production\App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Http\Response;
use Modules\Production\App\Models\SewingEntry;
use Modules\Production\App\Models\BundleTicket;
use Modules\Production\App\Models\SewingLine;
use Modules\Production\App\Models\Operator;
use Modules\Production\App\Models\CuttingEntry;
use Modules\Production\App\Http\Requests\SewingEntryRequest;
use Modules\Production\App\Http\Requests\BundleTicketRequest;
use PDF;

class SewingController extends Controller
{
    /**
     * Display a listing of sewing entries.
     */
    public function index(Request $request): View
    {
        $query = SewingEntry::with(['bundleTicket', 'sewingLine', 'operator']);
        
        // Apply filters
        if ($request->filled('sewing_line_id')) {
            $query->where('sewing_line_id', $request->sewing_line_id);
        }
        
        if ($request->filled('operator_id')) {
            $query->where('operator_id', $request->operator_id);
        }
        
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }
        
        if ($request->filled('input_date')) {
            $query->whereDate('input_datetime', $request->input_date);
        }
        
        // Apply sorting
        $sortBy = $request->get('sort_by', 'input_datetime');
        $sortDirection = $request->get('sort_direction', 'desc');
        $query->orderBy($sortBy, $sortDirection);
        
        $sewingEntries = $query->paginate(20)->appends($request->query());
        
        $sewingLines = SewingLine::active()->get();
        $operators = Operator::active()->get();
        
        return view('production::sewing.index', compact('sewingEntries', 'sewingLines', 'operators'));
    }

    /**
     * Show the form for creating a new sewing entry.
     */
    public function create(): View
    {
        $bundleTickets = BundleTicket::where('status', 'issued')->get();
        $sewingLines = SewingLine::active()->get();
        $operators = Operator::active()->get();
        
        return view('production::sewing.create', compact('bundleTickets', 'sewingLines', 'operators'));
    }

    /**
     * Store a newly created sewing entry.
     */
    public function store(SewingEntryRequest $request): RedirectResponse
    {
        $data = $request->validated();
        $data['input_datetime'] = now();
        
        $sewingEntry = SewingEntry::create($data);
        
        // Update bundle ticket status
        $sewingEntry->bundleTicket->update(['status' => 'in_sewing']);
        
        return redirect()->route('production.sewing.show', $sewingEntry)
            ->with('success', 'Sewing entry created successfully.');
    }

    /**
     * Display the specified sewing entry.
     */
    public function show(SewingEntry $sewingEntry): View
    {
        $sewingEntry->load(['bundleTicket', 'sewingLine', 'operator', 'finishingEntries']);
        
        return view('production::sewing.show', compact('sewingEntry'));
    }

    /**
     * Show the form for editing the specified sewing entry.
     */
    public function edit(SewingEntry $sewingEntry): View
    {
        $sewingLines = SewingLine::active()->get();
        $operators = Operator::active()->get();
        
        return view('production::sewing.edit', compact('sewingEntry', 'sewingLines', 'operators'));
    }

    /**
     * Update the specified sewing entry.
     */
    public function update(SewingEntryRequest $request, SewingEntry $sewingEntry): RedirectResponse
    {
        $data = $request->validated();
        
        // Calculate efficiency if output is provided
        if (isset($data['output_quantity']) && $data['output_quantity'] > 0) {
            $data['output_datetime'] = now();
            $data['efficiency_achieved'] = $sewingEntry->calculateEfficiency();
            
            // Update bundle ticket status
            $sewingEntry->bundleTicket->update(['status' => 'completed']);
        }
        
        $sewingEntry->update($data);
        
        return redirect()->route('production.sewing.show', $sewingEntry)
            ->with('success', 'Sewing entry updated successfully.');
    }

    /**
     * Remove the specified sewing entry.
     */
    public function destroy(SewingEntry $sewingEntry): RedirectResponse
    {
        if ($sewingEntry->status === 'completed') {
            return back()->with('error', 'Cannot delete a completed sewing entry.');
        }
        
        // Reset bundle ticket status
        $sewingEntry->bundleTicket->update(['status' => 'issued']);
        
        $sewingEntry->delete();
        
        return redirect()->route('production.sewing.index')
            ->with('success', 'Sewing entry deleted successfully.');
    }

    /**
     * Show line tracking interface.
     */
    public function lineTracking(Request $request): View
    {
        $selectedDate = $request->get('date', now()->format('Y-m-d'));
        
        $sewingLines = SewingLine::active()
            ->with(['sewingEntries' => function ($query) use ($selectedDate) {
                $query->whereDate('input_datetime', $selectedDate);
            }])
            ->get();
        
        // Calculate line metrics
        $lineMetrics = $sewingLines->map(function ($line) {
            $entries = $line->sewingEntries;
            
            return [
                'line' => $line,
                'total_input' => $entries->sum('input_quantity'),
                'total_output' => $entries->sum('output_quantity'),
                'total_defects' => $entries->sum('defect_quantity'),
                'avg_efficiency' => $entries->avg('efficiency_achieved') ?? 0,
                'wip_count' => $line->wip_count,
                'target_vs_actual' => [
                    'target' => $line->daily_capacity,
                    'actual' => $entries->sum('output_quantity')
                ]
            ];
        });
        
        return view('production::sewing.line-tracking', compact('lineMetrics', 'selectedDate'));
    }

    /**
     * Show efficiency analysis.
     */
    public function efficiency(Request $request): View
    {
        $dateRange = [
            'start_date' => $request->get('start_date', now()->startOfMonth()->format('Y-m-d')),
            'end_date' => $request->get('end_date', now()->endOfMonth()->format('Y-m-d'))
        ];
        
        // Line efficiency data
        $lineEfficiency = SewingLine::active()
            ->with(['sewingEntries' => function ($query) use ($dateRange) {
                $query->whereBetween('input_datetime', [$dateRange['start_date'], $dateRange['end_date']])
                      ->whereNotNull('efficiency_achieved');
            }])
            ->get()
            ->map(function ($line) {
                $avgEfficiency = $line->sewingEntries->avg('efficiency_achieved') ?? 0;
                return [
                    'line_number' => $line->line_number,
                    'line_name' => $line->line_name,
                    'target_efficiency' => $line->target_efficiency,
                    'actual_efficiency' => round($avgEfficiency, 2),
                    'variance' => round($avgEfficiency - $line->target_efficiency, 2),
                    'total_production' => $line->sewingEntries->sum('output_quantity')
                ];
            });
        
        // Operator efficiency data
        $operatorEfficiency = Operator::active()
            ->with(['sewingEntries' => function ($query) use ($dateRange) {
                $query->whereBetween('input_datetime', [$dateRange['start_date'], $dateRange['end_date']])
                      ->whereNotNull('efficiency_achieved');
            }])
            ->get()
            ->map(function ($operator) {
                $avgEfficiency = $operator->sewingEntries->avg('efficiency_achieved') ?? 0;
                return [
                    'operator_id' => $operator->operator_id,
                    'name' => $operator->name,
                    'skill_level' => $operator->skill_level,
                    'efficiency_rating' => $operator->efficiency_rating,
                    'actual_efficiency' => round($avgEfficiency, 2),
                    'total_production' => $operator->sewingEntries->sum('output_quantity')
                ];
            })
            ->sortByDesc('actual_efficiency');
        
        return view('production::sewing.efficiency', compact('lineEfficiency', 'operatorEfficiency', 'dateRange'));
    }

    /**
     * Show bundle tickets interface.
     */
    public function bundleTickets(Request $request): View
    {
        $query = BundleTicket::with(['cuttingEntry', 'sewingLine']);
        
        // Apply filters
        if ($request->filled('ticket_number')) {
            $query->where('ticket_number', 'like', '%' . $request->ticket_number . '%');
        }
        
        if ($request->filled('bundle_number')) {
            $query->where('bundle_number', 'like', '%' . $request->bundle_number . '%');
        }
        
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }
        
        if ($request->filled('sewing_line_id')) {
            $query->where('sewing_line_id', $request->sewing_line_id);
        }
        
        $bundleTickets = $query->orderBy('issued_datetime', 'desc')
            ->paginate(20)
            ->appends($request->query());
        
        $sewingLines = SewingLine::active()->get();
        $cuttingEntries = CuttingEntry::where('status', 'cut')->get();
        
        return view('production::sewing.bundle-tickets', compact('bundleTickets', 'sewingLines', 'cuttingEntries'));
    }

    /**
     * Store a new bundle ticket.
     */
    public function storeBundleTicket(BundleTicketRequest $request): RedirectResponse
    {
        $data = $request->validated();
        $data['ticket_number'] = BundleTicket::generateTicketNumber();
        $data['issued_datetime'] = now();
        
        $bundleTicket = BundleTicket::create($data);
        
        // Generate barcode and QR code
        $bundleTicket->update([
            'barcode' => $bundleTicket->generateBarcode(),
            'qr_code' => $bundleTicket->generateQRCode()
        ]);
        
        // Update cutting entry status
        $bundleTicket->cuttingEntry->update(['status' => 'sent_to_sewing']);
        
        return back()->with('success', 'Bundle ticket created successfully.');
    }

    /**
     * Export sewing report as PDF.
     */
    public function exportPdf(Request $request): Response
    {
        $dateRange = [
            'start_date' => $request->get('start_date', now()->startOfMonth()->format('Y-m-d')),
            'end_date' => $request->get('end_date', now()->endOfMonth()->format('Y-m-d'))
        ];
        
        $sewingEntries = SewingEntry::with(['bundleTicket', 'sewingLine', 'operator'])
            ->whereBetween('input_datetime', [$dateRange['start_date'], $dateRange['end_date']])
            ->get();
        
        $lineEfficiency = SewingLine::active()
            ->with(['sewingEntries' => function ($query) use ($dateRange) {
                $query->whereBetween('input_datetime', [$dateRange['start_date'], $dateRange['end_date']]);
            }])
            ->get();
        
        $pdf = PDF::loadView('production::exports.sewing-pdf', compact('sewingEntries', 'lineEfficiency', 'dateRange'));
        
        return $pdf->download('sewing-report-' . now()->format('Y-m-d') . '.pdf');
    }
}
