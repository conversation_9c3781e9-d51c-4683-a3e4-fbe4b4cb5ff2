<?php

namespace Modules\Production\App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Modules\Production\App\Models\WashingEntry;
use Modules\Production\App\Models\WashingRecipe;
use Modules\Production\App\Models\FinishingEntry;
use Modules\Production\App\Http\Requests\WashingEntryRequest;
use Modules\Production\App\Http\Requests\WashingRecipeRequest;

class WashingController extends Controller
{
    /**
     * Display a listing of washing entries.
     */
    public function index(Request $request): View
    {
        $query = WashingEntry::with(['finishingEntry', 'washingRecipe']);
        
        // Apply filters
        if ($request->filled('wash_lot_number')) {
            $query->where('wash_lot_number', 'like', '%' . $request->wash_lot_number . '%');
        }
        
        if ($request->filled('bundle_number')) {
            $query->where('bundle_number', 'like', '%' . $request->bundle_number . '%');
        }
        
        if ($request->filled('washing_recipe_id')) {
            $query->where('washing_recipe_id', $request->washing_recipe_id);
        }
        
        if ($request->filled('wash_quality')) {
            $query->where('wash_quality', $request->wash_quality);
        }
        
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }
        
        if ($request->filled('wash_date')) {
            $query->whereDate('wash_start_datetime', $request->wash_date);
        }
        
        // Apply sorting
        $sortBy = $request->get('sort_by', 'wash_start_datetime');
        $sortDirection = $request->get('sort_direction', 'desc');
        $query->orderBy($sortBy, $sortDirection);
        
        $washingEntries = $query->paginate(20)->appends($request->query());
        
        $washingRecipes = WashingRecipe::active()->get();
        
        return view('production::washing.index', compact('washingEntries', 'washingRecipes'));
    }

    /**
     * Show the form for creating a new washing entry.
     */
    public function create(): View
    {
        $finishingEntries = FinishingEntry::where('qc_status', 'pass')
            ->where('status', 'completed')
            ->with(['sewingEntry.bundleTicket'])
            ->get();
        $washingRecipes = WashingRecipe::active()->get();
        
        return view('production::washing.create', compact('finishingEntries', 'washingRecipes'));
    }

    /**
     * Store a newly created washing entry.
     */
    public function store(WashingEntryRequest $request): RedirectResponse
    {
        $data = $request->validated();
        $data['wash_lot_number'] = WashingEntry::generateWashLotNumber();
        $data['wash_start_datetime'] = now();
        
        // Get bundle details from finishing entry
        $finishingEntry = FinishingEntry::find($data['finishing_entry_id']);
        $data['bundle_number'] = $finishingEntry->bundle_number;
        $data['style_number'] = $finishingEntry->style_number;
        $data['po_number'] = $finishingEntry->po_number;
        $data['color'] = $finishingEntry->color;
        $data['size'] = $finishingEntry->size;
        
        $washingEntry = WashingEntry::create($data);
        
        return redirect()->route('production.washing.show', $washingEntry)
            ->with('success', 'Washing entry created successfully.');
    }

    /**
     * Display the specified washing entry.
     */
    public function show(WashingEntry $washingEntry): View
    {
        $washingEntry->load(['finishingEntry.sewingEntry.bundleTicket', 'washingRecipe']);
        
        return view('production::washing.show', compact('washingEntry'));
    }

    /**
     * Show the form for editing the specified washing entry.
     */
    public function edit(WashingEntry $washingEntry): View
    {
        $washingRecipes = WashingRecipe::active()->get();
        
        return view('production::washing.edit', compact('washingEntry', 'washingRecipes'));
    }

    /**
     * Update the specified washing entry.
     */
    public function update(WashingEntryRequest $request, WashingEntry $washingEntry): RedirectResponse
    {
        $data = $request->validated();
        
        // Set end datetime if output is provided
        if (isset($data['output_quantity']) && $data['output_quantity'] > 0) {
            $data['wash_end_datetime'] = now();
        }
        
        $washingEntry->update($data);
        
        return redirect()->route('production.washing.show', $washingEntry)
            ->with('success', 'Washing entry updated successfully.');
    }

    /**
     * Remove the specified washing entry.
     */
    public function destroy(WashingEntry $washingEntry): RedirectResponse
    {
        if ($washingEntry->status === 'completed') {
            return back()->with('error', 'Cannot delete a completed washing entry.');
        }
        
        $washingEntry->delete();
        
        return redirect()->route('production.washing.index')
            ->with('success', 'Washing entry deleted successfully.');
    }

    /**
     * Show washing recipes management.
     */
    public function recipes(Request $request): View
    {
        $query = WashingRecipe::query();
        
        // Apply filters
        if ($request->filled('wash_type')) {
            $query->where('wash_type', $request->wash_type);
        }
        
        if ($request->filled('garment_type')) {
            $query->where('garment_type', $request->garment_type);
        }
        
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }
        
        $washingRecipes = $query->paginate(15)->appends($request->query());
        
        return view('production::washing.recipes', compact('washingRecipes'));
    }

    /**
     * Store a new washing recipe.
     */
    public function storeRecipe(WashingRecipeRequest $request): RedirectResponse
    {
        $data = $request->validated();
        
        // Generate recipe code if not provided
        if (empty($data['recipe_code'])) {
            $data['recipe_code'] = strtoupper(substr($data['wash_type'], 0, 3)) . 
                                  str_pad(WashingRecipe::count() + 1, 3, '0', STR_PAD_LEFT);
        }
        
        WashingRecipe::create($data);
        
        return back()->with('success', 'Washing recipe created successfully.');
    }

    /**
     * Show the form for editing the specified washing recipe.
     */
    public function editRecipe(WashingRecipe $washingRecipe): View
    {
        return view('production::washing.edit-recipe', compact('washingRecipe'));
    }

    /**
     * Update the specified washing recipe.
     */
    public function updateRecipe(WashingRecipeRequest $request, WashingRecipe $washingRecipe): RedirectResponse
    {
        $washingRecipe->update($request->validated());
        
        return redirect()->route('production.washing.recipes')
            ->with('success', 'Washing recipe updated successfully.');
    }

    /**
     * Get washing statistics for dashboard.
     */
    public function getWashingStats(Request $request): array
    {
        $dateRange = [
            'start_date' => $request->get('start_date', now()->startOfMonth()->format('Y-m-d')),
            'end_date' => $request->get('end_date', now()->endOfMonth()->format('Y-m-d'))
        ];
        
        $entries = WashingEntry::whereBetween('wash_start_datetime', [$dateRange['start_date'], $dateRange['end_date']]);
        
        return [
            'total_washed' => $entries->sum('output_quantity'),
            'total_damaged' => $entries->sum('damage_quantity'),
            'damage_rate' => $entries->avg('damage_percentage') ?? 0,
            'avg_shrinkage' => $entries->avg('actual_shrinkage') ?? 0,
            'total_cost' => $entries->sum('wash_cost'),
            'quality_distribution' => [
                'excellent' => $entries->where('wash_quality', 'excellent')->count(),
                'good' => $entries->where('wash_quality', 'good')->count(),
                'acceptable' => $entries->where('wash_quality', 'acceptable')->count(),
                'poor' => $entries->where('wash_quality', 'poor')->count()
            ],
            'by_wash_type' => WashingRecipe::active()
                ->withCount(['washingEntries as total_washed' => function ($query) use ($dateRange) {
                    $query->whereBetween('wash_start_datetime', [$dateRange['start_date'], $dateRange['end_date']])
                          ->selectRaw('SUM(output_quantity)');
                }])
                ->get()
                ->toArray()
        ];
    }
}
