<?php

namespace Modules\Production\App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class AqlChartRequest extends FormRequest
{
    public function authorize(): bool { return true; }
    public function rules(): array
    {
        return [
            'aql_level' => 'required|string|max:10',
            'lot_size_min' => 'required|integer|min:1',
            'lot_size_max' => 'required|integer|gte:lot_size_min',
            'sample_size' => 'required|integer|min:1',
            'acceptance_number' => 'required|integer|min:0',
            'rejection_number' => 'required|integer|gt:acceptance_number',
            'inspection_level' => 'required|in:I,II,III',
            'inspection_type' => 'required|in:normal,tightened,reduced',
            'description' => 'nullable|string'
        ];
    }
}
