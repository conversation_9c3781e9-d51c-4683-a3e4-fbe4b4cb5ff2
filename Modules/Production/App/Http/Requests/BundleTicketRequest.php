<?php

namespace Modules\Production\App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class BundleTicketRequest extends FormRequest
{
    public function authorize(): bool { return true; }
    public function rules(): array
    {
        return [
            'cutting_entry_id' => 'required|exists:cutting_entries,id',
            'bundle_number' => 'required|string',
            'style_number' => 'required|string',
            'po_number' => 'required|string',
            'color' => 'required|string',
            'size' => 'required|string',
            'quantity' => 'required|integer|min:1',
            'smv' => 'required|numeric|min:0',
            'sewing_line_id' => 'nullable|exists:sewing_lines,id',
            'status' => 'required|in:issued,in_sewing,completed,rejected',
            'notes' => 'nullable|string'
        ];
    }
}
