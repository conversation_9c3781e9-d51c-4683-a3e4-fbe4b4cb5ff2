<?php

namespace Modules\Production\App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CuttingEntryRequest extends FormRequest
{
    public function authorize(): bool { return true; }
    public function rules(): array
    {
        return [
            'cutting_plan_id' => 'required|exists:cutting_plans,id',
            'marker_id' => 'nullable|exists:markers,id',
            'style_number' => 'required|string',
            'po_number' => 'required|string',
            'color' => 'required|string',
            'size' => 'required|string',
            'quantity' => 'required|integer|min:1',
            'fabric_consumed' => 'required|numeric|min:0',
            'cutter_name' => 'required|string',
            'status' => 'required|in:cut,sent_to_sewing,completed',
            'defects' => 'nullable|string',
            'notes' => 'nullable|string'
        ];
    }
}
