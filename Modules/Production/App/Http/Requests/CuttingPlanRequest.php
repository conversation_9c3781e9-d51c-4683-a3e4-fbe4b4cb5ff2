<?php

namespace Modules\Production\App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CuttingPlanRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'production_plan_id' => 'nullable|exists:production_plans,id',
            'style_number' => 'required|string|max:255',
            'po_number' => 'required|string|max:255',
            'color' => 'required|string|max:255',
            'fabric_width' => 'required|numeric|min:0',
            'shrinkage_percentage' => 'required|numeric|min:0|max:20',
            'ply_count' => 'nullable|integer|min:1',
            'marker_length' => 'nullable|numeric|min:0',
            'marker_efficiency' => 'nullable|numeric|min:0|max:100',
            'cutting_efficiency' => 'nullable|numeric|min:0|max:100',
            'size_breakdown_sizes' => 'nullable|array',
            'size_breakdown_sizes.*' => 'nullable|string',
            'size_breakdown_quantities' => 'nullable|array',
            'size_breakdown_quantities.*' => 'nullable|integer|min:1',
            'total_fabric_required' => 'required|numeric|min:0',
            'fabric_consumption_per_piece' => 'nullable|numeric|min:0',
            'total_pieces' => 'nullable|integer|min:0',
            'fabric_wastage' => 'nullable|numeric|min:0',
            'status' => 'nullable|in:planned,in_progress,completed,cancelled',
            'cutting_date' => 'required|date',
            'notes' => 'nullable|string'
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'production_plan_id.required' => 'Production plan is required.',
            'production_plan_id.exists' => 'Selected production plan does not exist.',
            'style_number.required' => 'Style number is required.',
            'po_number.required' => 'PO number is required.',
            'color.required' => 'Color is required.',
            'fabric_width.required' => 'Fabric width is required.',
            'shrinkage_percentage.max' => 'Shrinkage percentage cannot exceed 20%.',
            'ply_count.required' => 'Ply count is required.',
            'ply_count.min' => 'Ply count must be at least 1.',
            'marker_length.required' => 'Marker length is required.',
            'marker_efficiency.required' => 'Marker efficiency is required.',
            'marker_efficiency.max' => 'Marker efficiency cannot exceed 100%.',
            'size_breakdown.required' => 'Size breakdown is required.',
            'total_fabric_required.required' => 'Total fabric required is required.',
            'cutting_date.required' => 'Cutting date is required.'
        ];
    }
}
