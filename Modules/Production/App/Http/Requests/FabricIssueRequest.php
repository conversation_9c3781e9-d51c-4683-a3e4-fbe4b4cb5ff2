<?php

namespace Modules\Production\App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class FabricIssueRequest extends FormRequest
{
    public function authorize(): bool { return true; }
    public function rules(): array
    {
        return [
            'cutting_plan_id' => 'required|exists:cutting_plans,id',
            'fabric_type' => 'required|string',
            'color' => 'required|string',
            'issued_quantity' => 'required|numeric|min:0',
            'unit' => 'required|in:yards,meters,pieces',
            'issued_to' => 'required|string',
            'status' => 'nullable|in:issued,partially_returned,fully_returned,consumed',
            'notes' => 'nullable|string'
        ];
    }
}
