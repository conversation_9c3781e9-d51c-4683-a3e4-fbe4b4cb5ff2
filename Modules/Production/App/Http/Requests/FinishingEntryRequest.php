<?php

namespace Modules\Production\App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class FinishingEntryRequest extends FormRequest
{
    public function authorize(): bool { return true; }
    public function rules(): array
    {
        return [
            'sewing_entry_id' => 'required|exists:sewing_entries,id',
            'finishing_process_id' => 'required|exists:finishing_processes,id',
            'input_quantity' => 'required|integer|min:1',
            'output_quantity' => 'nullable|integer|min:0',
            'defect_quantity' => 'nullable|integer|min:0',
            'rework_quantity' => 'nullable|integer|min:0',
            'operator_name' => 'required|string',
            'measurements' => 'nullable|array',
            'defect_details' => 'nullable|array',
            'qc_status' => 'required|in:pass,fail,rework,pending',
            'status' => 'required|in:input,in_progress,completed,rejected',
            'notes' => 'nullable|string'
        ];
    }
}
