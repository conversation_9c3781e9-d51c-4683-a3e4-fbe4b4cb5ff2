<?php

namespace Modules\Production\App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class FinishingProcessRequest extends FormRequest
{
    public function authorize(): bool { return true; }
    public function rules(): array
    {
        return [
            'process_name' => 'required|string|max:255',
            'process_code' => 'nullable|string|max:50|unique:finishing_processes,process_code',
            'description' => 'nullable|string',
            'standard_time_minutes' => 'required|numeric|min:0',
            'sequence_order' => 'required|integer|min:1',
            'process_type' => 'required|in:buttoning,trimming,folding,poly_bagging,labeling,measurement,quality_check',
            'is_mandatory' => 'required|boolean',
            'status' => 'required|in:active,inactive'
        ];
    }
}
