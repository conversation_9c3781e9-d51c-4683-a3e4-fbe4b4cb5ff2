<?php

namespace Modules\Production\App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class MarkerRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'cutting_plan_id' => 'required|exists:cutting_plans,id',
            'marker_length' => 'required|numeric|min:0',
            'marker_efficiency' => 'required|numeric|min:0|max:100',
            'ply_count' => 'required|integer|min:1',
            'fabric_utilization' => 'nullable|numeric|min:0|max:100',
            'size_layout' => 'nullable|string',
            'notes' => 'nullable|string'
        ];
    }
}
