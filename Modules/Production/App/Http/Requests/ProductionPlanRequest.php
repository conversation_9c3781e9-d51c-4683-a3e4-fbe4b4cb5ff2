<?php

namespace Modules\Production\App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ProductionPlanRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'order_id' => 'nullable|integer',
            'style_number' => 'required|string|max:255',
            'po_number' => 'required|string|max:255',
            'target_quantity' => 'required|integer|min:1',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'line_capacity' => 'required|integer|min:0',
            'efficiency_percentage' => 'required|numeric|min:0|max:200',
            'fabric_requirements' => 'nullable|array',
            'fabric_requirements.*.fabric_type' => 'required_with:fabric_requirements|string',
            'fabric_requirements.*.quantity' => 'required_with:fabric_requirements|numeric|min:0',
            'fabric_requirements.*.unit' => 'required_with:fabric_requirements|string',
            'accessories_requirements' => 'nullable|array',
            'accessories_requirements.*.accessory_type' => 'required_with:accessories_requirements|string',
            'accessories_requirements.*.quantity' => 'required_with:accessories_requirements|numeric|min:0',
            'accessories_requirements.*.unit' => 'required_with:accessories_requirements|string',
            'fabric_wastage_percentage' => 'required|numeric|min:0|max:50',
            'status' => 'required|in:draft,approved,in_progress,completed,cancelled',
            'notes' => 'nullable|string'
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'style_number.required' => 'Style number is required.',
            'po_number.required' => 'PO number is required.',
            'target_quantity.required' => 'Target quantity is required.',
            'target_quantity.min' => 'Target quantity must be at least 1.',
            'start_date.required' => 'Start date is required.',
            'end_date.required' => 'End date is required.',
            'end_date.after' => 'End date must be after start date.',
            'line_capacity.required' => 'Line capacity is required.',
            'efficiency_percentage.required' => 'Efficiency percentage is required.',
            'efficiency_percentage.max' => 'Efficiency percentage cannot exceed 200%.',
            'fabric_wastage_percentage.required' => 'Fabric wastage percentage is required.',
            'fabric_wastage_percentage.max' => 'Fabric wastage percentage cannot exceed 50%.'
        ];
    }
}
