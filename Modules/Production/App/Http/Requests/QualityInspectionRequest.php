<?php

namespace Modules\Production\App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class QualityInspectionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'inspectable_type' => 'required|string',
            'inspectable_id' => 'required|integer',
            'aql_chart_id' => 'nullable|exists:aql_charts,id',
            'inspection_stage' => 'required|in:inline,end_line,pre_final,final,audit',
            'lot_quantity' => 'required|integer|min:1',
            'sample_size' => 'required|integer|min:1|lte:lot_quantity',
            'inspected_quantity' => 'required|integer|min:1|lte:sample_size',
            'defect_quantity' => 'required|integer|min:0|lte:inspected_quantity',
            'major_defects' => 'required|integer|min:0|lte:defect_quantity',
            'minor_defects' => 'required|integer|min:0|lte:defect_quantity',
            'critical_defects' => 'required|integer|min:0|lte:defect_quantity',
            'inspector_name' => 'required|string|max:255',
            'defect_details' => 'nullable|array',
            'corrective_actions' => 'nullable|string',
            'notes' => 'nullable|string'
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'inspectable_type.required' => 'Inspectable type is required.',
            'inspectable_id.required' => 'Inspectable item is required.',
            'inspection_stage.required' => 'Inspection stage is required.',
            'lot_quantity.required' => 'Lot quantity is required.',
            'sample_size.required' => 'Sample size is required.',
            'sample_size.lte' => 'Sample size cannot exceed lot quantity.',
            'inspected_quantity.required' => 'Inspected quantity is required.',
            'inspected_quantity.lte' => 'Inspected quantity cannot exceed sample size.',
            'defect_quantity.lte' => 'Defect quantity cannot exceed inspected quantity.',
            'inspector_name.required' => 'Inspector name is required.'
        ];
    }
}
