<?php

namespace Modules\Production\App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class SewingEntryRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'bundle_ticket_id' => 'required|exists:bundle_tickets,id',
            'sewing_line_id' => 'required|exists:sewing_lines,id',
            'operator_id' => 'nullable|exists:operators,id',
            'input_quantity' => 'required|integer|min:1',
            'output_quantity' => 'nullable|integer|min:0|lte:input_quantity',
            'defect_quantity' => 'nullable|integer|min:0|lte:output_quantity',
            'actual_smv' => 'nullable|numeric|min:0',
            'defect_types' => 'nullable|array',
            'defect_types.*' => 'string',
            'status' => 'required|in:input,in_progress,output,completed',
            'notes' => 'nullable|string'
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'bundle_ticket_id.required' => 'Bundle ticket is required.',
            'bundle_ticket_id.exists' => 'Selected bundle ticket does not exist.',
            'sewing_line_id.required' => 'Sewing line is required.',
            'sewing_line_id.exists' => 'Selected sewing line does not exist.',
            'operator_id.exists' => 'Selected operator does not exist.',
            'input_quantity.required' => 'Input quantity is required.',
            'input_quantity.min' => 'Input quantity must be at least 1.',
            'output_quantity.lte' => 'Output quantity cannot exceed input quantity.',
            'defect_quantity.lte' => 'Defect quantity cannot exceed output quantity.'
        ];
    }
}
