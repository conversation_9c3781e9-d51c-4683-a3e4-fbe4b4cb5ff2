<?php

namespace Modules\Production\App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class WashingEntryRequest extends FormRequest
{
    public function authorize(): bool { return true; }
    public function rules(): array
    {
        return [
            'finishing_entry_id' => 'required|exists:finishing_entries,id',
            'washing_recipe_id' => 'required|exists:washing_recipes,id',
            'input_quantity' => 'required|integer|min:1',
            'output_quantity' => 'nullable|integer|min:0',
            'damage_quantity' => 'nullable|integer|min:0',
            'pre_wash_measurements' => 'nullable|array',
            'post_wash_measurements' => 'nullable|array',
            'actual_shrinkage' => 'nullable|numeric|min:0',
            'chemical_consumption' => 'nullable|array',
            'wash_cost' => 'nullable|numeric|min:0',
            'operator_name' => 'required|string',
            'wash_quality' => 'required|in:excellent,good,acceptable,poor',
            'status' => 'required|in:in_wash,completed,rejected,rework',
            'damage_details' => 'nullable|string',
            'notes' => 'nullable|string'
        ];
    }
}
