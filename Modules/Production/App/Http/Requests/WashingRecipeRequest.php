<?php

namespace Modules\Production\App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class WashingRecipeRequest extends FormRequest
{
    public function authorize(): bool { return true; }
    public function rules(): array
    {
        return [
            'recipe_name' => 'required|string|max:255',
            'recipe_code' => 'nullable|string|max:50|unique:washing_recipes,recipe_code',
            'wash_type' => 'required|in:enzyme_wash,acid_wash,stone_wash,softener_treatment,bleach_wash,normal_wash',
            'description' => 'nullable|string',
            'chemicals_required' => 'required|array',
            'water_temperature' => 'required|numeric|min:0|max:100',
            'wash_time_minutes' => 'required|integer|min:1',
            'rinse_cycles' => 'required|integer|min:1',
            'expected_shrinkage' => 'required|numeric|min:0|max:20',
            'process_steps' => 'required|array',
            'garment_type' => 'required|in:denim_pants,denim_jacket,denim_shirt,other',
            'status' => 'required|in:active,inactive,testing',
            'notes' => 'nullable|string'
        ];
    }
}
