<?php

namespace Modules\Production\App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;

class AqlChart extends Model
{
    use HasFactory;

    protected $fillable = [
        'aql_level',
        'lot_size_min',
        'lot_size_max',
        'sample_size',
        'acceptance_number',
        'rejection_number',
        'inspection_level',
        'inspection_type',
        'description'
    ];

    /**
     * Get the quality inspections for this AQL chart.
     */
    public function qualityInspections(): HasMany
    {
        return $this->hasMany(QualityInspection::class);
    }

    /**
     * Find appropriate AQL chart for given lot size and AQL level.
     */
    public static function findForLotSize(int $lotSize, string $aqlLevel, string $inspectionLevel = 'II', string $inspectionType = 'normal'): ?self
    {
        return static::where('aql_level', $aqlLevel)
                    ->where('inspection_level', $inspectionLevel)
                    ->where('inspection_type', $inspectionType)
                    ->where('lot_size_min', '<=', $lotSize)
                    ->where('lot_size_max', '>=', $lotSize)
                    ->first();
    }

    /**
     * Determine inspection result based on defect count.
     */
    public function determineResult(int $defectCount): string
    {
        if ($defectCount <= $this->acceptance_number) {
            return 'pass';
        } elseif ($defectCount >= $this->rejection_number) {
            return 'fail';
        } else {
            return 'conditional_pass';
        }
    }

    /**
     * Scope by inspection level.
     */
    public function scopeByInspectionLevel($query, $level)
    {
        return $query->where('inspection_level', $level);
    }

    /**
     * Scope by inspection type.
     */
    public function scopeByInspectionType($query, $type)
    {
        return $query->where('inspection_type', $type);
    }
}
