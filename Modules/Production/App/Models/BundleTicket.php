<?php

namespace Modules\Production\App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class BundleTicket extends Model
{
    use HasFactory;

    protected $fillable = [
        'cutting_entry_id',
        'ticket_number',
        'bundle_number',
        'style_number',
        'po_number',
        'color',
        'size',
        'quantity',
        'smv',
        'barcode',
        'qr_code',
        'issued_datetime',
        'sewing_line_id',
        'status',
        'notes'
    ];

    protected $casts = [
        'issued_datetime' => 'datetime',
        'smv' => 'decimal:2'
    ];

    /**
     * Get the cutting entry that owns this bundle ticket.
     */
    public function cuttingEntry(): BelongsTo
    {
        return $this->belongsTo(CuttingEntry::class);
    }

    /**
     * Get the sewing line assigned to this bundle ticket.
     */
    public function sewingLine(): BelongsTo
    {
        return $this->belongsTo(SewingLine::class);
    }

    /**
     * Get the sewing entries for this bundle ticket.
     */
    public function sewingEntries(): HasMany
    {
        return $this->hasMany(SewingEntry::class);
    }

    /**
     * Get the total standard minutes for this bundle.
     */
    public function getTotalStandardMinutesAttribute(): float
    {
        return $this->quantity * $this->smv;
    }

    /**
     * Generate unique ticket number.
     */
    public static function generateTicketNumber(): string
    {
        $prefix = 'TK';
        $date = now()->format('Ymd');
        $lastTicket = static::where('ticket_number', 'like', $prefix . $date . '%')
                           ->orderBy('ticket_number', 'desc')
                           ->first();
        
        if ($lastTicket) {
            $lastNumber = intval(substr($lastTicket->ticket_number, -4));
            $newNumber = str_pad($lastNumber + 1, 4, '0', STR_PAD_LEFT);
        } else {
            $newNumber = '0001';
        }
        
        return $prefix . $date . $newNumber;
    }

    /**
     * Generate barcode for bundle tracking.
     */
    public function generateBarcode(): string
    {
        return $this->ticket_number . $this->bundle_number;
    }

    /**
     * Generate QR code for bundle tracking.
     */
    public function generateQRCode(): string
    {
        return base64_encode(json_encode([
            'ticket_number' => $this->ticket_number,
            'bundle_number' => $this->bundle_number,
            'style_number' => $this->style_number,
            'po_number' => $this->po_number,
            'color' => $this->color,
            'size' => $this->size,
            'quantity' => $this->quantity,
            'smv' => $this->smv,
            'issued_date' => $this->issued_datetime->format('Y-m-d')
        ]));
    }

    /**
     * Scope for tickets by status.
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }
}
