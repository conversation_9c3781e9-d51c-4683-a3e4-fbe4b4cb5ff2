<?php

namespace Modules\Production\App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class CuttingEntry extends Model
{
    use HasFactory;

    protected $fillable = [
        'cutting_plan_id',
        'marker_id',
        'bundle_number',
        'style_number',
        'po_number',
        'color',
        'size',
        'quantity',
        'fabric_consumed',
        'cutter_name',
        'cutting_datetime',
        'qr_code',
        'status',
        'defects',
        'notes'
    ];

    protected $casts = [
        'cutting_datetime' => 'datetime',
        'fabric_consumed' => 'decimal:2'
    ];

    /**
     * Get the cutting plan that owns this entry.
     */
    public function cuttingPlan(): BelongsTo
    {
        return $this->belongsTo(CuttingPlan::class);
    }

    /**
     * Get the marker that was used for this entry.
     */
    public function marker(): BelongsTo
    {
        return $this->belongsTo(Marker::class);
    }

    /**
     * Get the bundle tickets for this cutting entry.
     */
    public function bundleTickets(): HasMany
    {
        return $this->hasMany(BundleTicket::class);
    }

    /**
     * Generate unique bundle number.
     */
    public static function generateBundleNumber(): string
    {
        $prefix = 'BN';
        $date = now()->format('Ymd');
        $lastBundle = static::where('bundle_number', 'like', $prefix . $date . '%')
                           ->orderBy('bundle_number', 'desc')
                           ->first();
        
        if ($lastBundle) {
            $lastNumber = intval(substr($lastBundle->bundle_number, -4));
            $newNumber = str_pad($lastNumber + 1, 4, '0', STR_PAD_LEFT);
        } else {
            $newNumber = '0001';
        }
        
        return $prefix . $date . $newNumber;
    }

    /**
     * Generate QR code for bundle tracking.
     */
    public function generateQRCode(): string
    {
        return base64_encode(json_encode([
            'bundle_number' => $this->bundle_number,
            'style_number' => $this->style_number,
            'po_number' => $this->po_number,
            'color' => $this->color,
            'size' => $this->size,
            'quantity' => $this->quantity,
            'cutting_date' => $this->cutting_datetime->format('Y-m-d')
        ]));
    }

    /**
     * Scope for bundles by status.
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }
}
