<?php

namespace Modules\Production\App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class CuttingPlan extends Model
{
    use HasFactory;

    protected $fillable = [
        'production_plan_id',
        'cutting_number',
        'style_number',
        'po_number',
        'color',
        'fabric_width',
        'shrinkage_percentage',
        'ply_count',
        'marker_length',
        'marker_efficiency',
        'cutting_efficiency',
        'size_breakdown',
        'total_fabric_required',
        'fabric_wastage',
        'fabric_consumption_per_piece',
        'total_pieces',
        'status',
        'cutting_date',
        'notes'
    ];

    protected $casts = [
        'cutting_date' => 'date',
        'fabric_width' => 'decimal:2',
        'shrinkage_percentage' => 'decimal:2',
        'marker_length' => 'decimal:2',
        'marker_efficiency' => 'decimal:2',
        'cutting_efficiency' => 'decimal:2',
        'total_fabric_required' => 'decimal:2',
        'fabric_wastage' => 'decimal:2',
        'fabric_consumption_per_piece' => 'decimal:2',
        'total_pieces' => 'integer',
        'size_breakdown' => 'array'
    ];

    /**
     * Get the production plan that owns this cutting plan.
     */
    public function productionPlan(): BelongsTo
    {
        return $this->belongsTo(ProductionPlan::class);
    }

    /**
     * Get the markers for this cutting plan.
     */
    public function markers(): HasMany
    {
        return $this->hasMany(Marker::class);
    }

    /**
     * Get the cutting entries for this cutting plan.
     */
    public function cuttingEntries(): HasMany
    {
        return $this->hasMany(CuttingEntry::class);
    }

    /**
     * Get the fabric issues for this cutting plan.
     */
    public function fabricIssues(): HasMany
    {
        return $this->hasMany(FabricIssue::class);
    }

    /**
     * Get the total pieces to cut.
     */
    public function getTotalPiecesAttribute(): int
    {
        // If total_pieces is set as a database field, use it
        if (isset($this->attributes['total_pieces']) && is_numeric($this->attributes['total_pieces'])) {
            return (int) $this->attributes['total_pieces'];
        }

        // Otherwise calculate from size breakdown
        if (!$this->size_breakdown || !is_array($this->size_breakdown)) {
            return 0;
        }

        $total = 0;
        foreach ($this->size_breakdown as $item) {
            if (is_array($item) && isset($item['quantity']) && is_numeric($item['quantity'])) {
                $total += (int) $item['quantity'];
            } elseif (is_numeric($item)) {
                $total += (int) $item;
            }
        }

        return $total;
    }

    /**
     * Get the fabric utilization percentage.
     */
    public function getFabricUtilizationAttribute(): float
    {
        if ($this->total_fabric_required == 0) return 0;
        $actualUsed = $this->total_fabric_required - $this->fabric_wastage;
        return ($actualUsed / $this->total_fabric_required) * 100;
    }

    /**
     * Get the cutting efficiency.
     */
    public function getCuttingEfficiencyAttribute(): float
    {
        $totalCut = $this->cuttingEntries()->sum('quantity');
        if ($this->total_pieces == 0) return 0;
        return ($totalCut / $this->total_pieces) * 100;
    }

    /**
     * Generate unique cutting number.
     */
    public static function generateCuttingNumber(): string
    {
        $prefix = 'CT';
        $date = now()->format('Ymd');
        $lastCutting = static::where('cutting_number', 'like', $prefix . $date . '%')
                            ->orderBy('cutting_number', 'desc')
                            ->first();
        
        if ($lastCutting) {
            $lastNumber = intval(substr($lastCutting->cutting_number, -4));
            $newNumber = str_pad($lastNumber + 1, 4, '0', STR_PAD_LEFT);
        } else {
            $newNumber = '0001';
        }
        
        return $prefix . $date . $newNumber;
    }

    /**
     * Scope for plans by status.
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }
}
