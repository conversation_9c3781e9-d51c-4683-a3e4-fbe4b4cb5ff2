<?php

namespace Modules\Production\App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class DefectLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'quality_inspection_id',
        'defect_code',
        'defect_name',
        'defect_category',
        'defect_severity',
        'defect_description',
        'defect_location',
        'defect_count',
        'detected_by',
        'detection_datetime',
        'action_taken',
        'corrective_action',
        'responsible_operator',
        'cost_impact',
        'status',
        'notes'
    ];

    protected $casts = [
        'detection_datetime' => 'datetime',
        'cost_impact' => 'decimal:2'
    ];

    /**
     * Get the quality inspection that owns this defect log.
     */
    public function qualityInspection(): BelongsTo
    {
        return $this->belongsTo(QualityInspection::class);
    }

    /**
     * Check if defect is critical.
     */
    public function getIsCriticalAttribute(): bool
    {
        return $this->defect_severity === 'critical';
    }

    /**
     * Check if defect is resolved.
     */
    public function getIsResolvedAttribute(): bool
    {
        return in_array($this->status, ['resolved', 'closed']);
    }

    /**
     * Scope for defects by category.
     */
    public function scopeByCategory($query, $category)
    {
        return $query->where('defect_category', $category);
    }

    /**
     * Scope for defects by severity.
     */
    public function scopeBySeverity($query, $severity)
    {
        return $query->where('defect_severity', $severity);
    }

    /**
     * Scope for critical defects.
     */
    public function scopeCritical($query)
    {
        return $query->where('defect_severity', 'critical');
    }

    /**
     * Scope for open defects.
     */
    public function scopeOpen($query)
    {
        return $query->where('status', 'open');
    }

    /**
     * Scope for resolved defects.
     */
    public function scopeResolved($query)
    {
        return $query->whereIn('status', ['resolved', 'closed']);
    }
}
