<?php

namespace Modules\Production\App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class FabricIssue extends Model
{
    use HasFactory;

    protected $fillable = [
        'cutting_plan_id',
        'issue_number',
        'fabric_type',
        'color',
        'issued_quantity',
        'returned_quantity',
        'consumed_quantity',
        'wastage_quantity',
        'unit',
        'issued_to',
        'issue_datetime',
        'return_datetime',
        'status',
        'notes'
    ];

    protected $casts = [
        'issue_datetime' => 'datetime',
        'return_datetime' => 'datetime',
        'issued_quantity' => 'decimal:2',
        'returned_quantity' => 'decimal:2',
        'consumed_quantity' => 'decimal:2',
        'wastage_quantity' => 'decimal:2'
    ];

    /**
     * Get the cutting plan that owns this fabric issue.
     */
    public function cuttingPlan(): BelongsTo
    {
        return $this->belongsTo(CuttingPlan::class);
    }

    /**
     * Get the balance quantity.
     */
    public function getBalanceQuantityAttribute(): float
    {
        return $this->issued_quantity - $this->returned_quantity - $this->consumed_quantity - $this->wastage_quantity;
    }

    /**
     * Get the utilization percentage.
     */
    public function getUtilizationPercentageAttribute(): float
    {
        if ($this->issued_quantity == 0) return 0;
        return ($this->consumed_quantity / $this->issued_quantity) * 100;
    }

    /**
     * Get the wastage percentage.
     */
    public function getWastagePercentageAttribute(): float
    {
        if ($this->issued_quantity == 0) return 0;
        return ($this->wastage_quantity / $this->issued_quantity) * 100;
    }

    /**
     * Generate unique issue number.
     */
    public static function generateIssueNumber(): string
    {
        $prefix = 'FI';
        $date = now()->format('Ymd');
        $lastIssue = static::where('issue_number', 'like', $prefix . $date . '%')
                          ->orderBy('issue_number', 'desc')
                          ->first();
        
        if ($lastIssue) {
            $lastNumber = intval(substr($lastIssue->issue_number, -4));
            $newNumber = str_pad($lastNumber + 1, 4, '0', STR_PAD_LEFT);
        } else {
            $newNumber = '0001';
        }
        
        return $prefix . $date . $newNumber;
    }
}
