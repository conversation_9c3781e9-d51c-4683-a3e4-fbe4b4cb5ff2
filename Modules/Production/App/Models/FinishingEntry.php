<?php

namespace Modules\Production\App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;

class FinishingEntry extends Model
{
    use HasFactory;

    protected $fillable = [
        'sewing_entry_id',
        'finishing_process_id',
        'bundle_number',
        'style_number',
        'po_number',
        'color',
        'size',
        'input_quantity',
        'output_quantity',
        'defect_quantity',
        'rework_quantity',
        'start_datetime',
        'end_datetime',
        'operator_name',
        'actual_time_minutes',
        'efficiency_achieved',
        'measurements',
        'defect_details',
        'qc_status',
        'status',
        'notes'
    ];

    protected $casts = [
        'start_datetime' => 'datetime',
        'end_datetime' => 'datetime',
        'actual_time_minutes' => 'decimal:2',
        'efficiency_achieved' => 'decimal:2',
        'measurements' => 'array',
        'defect_details' => 'array'
    ];

    /**
     * Get the sewing entry that owns this finishing entry.
     */
    public function sewingEntry(): BelongsTo
    {
        return $this->belongsTo(SewingEntry::class);
    }

    /**
     * Get the finishing process that owns this finishing entry.
     */
    public function finishingProcess(): BelongsTo
    {
        return $this->belongsTo(FinishingProcess::class);
    }

    /**
     * Get the washing entries for this finishing entry.
     */
    public function washingEntries(): HasMany
    {
        return $this->hasMany(WashingEntry::class);
    }

    /**
     * Get the quality inspections for this finishing entry.
     */
    public function qualityInspections(): MorphMany
    {
        return $this->morphMany(QualityInspection::class, 'inspectable');
    }

    /**
     * Get the good quantity (output - defects - rework).
     */
    public function getGoodQuantityAttribute(): int
    {
        return $this->output_quantity - $this->defect_quantity - $this->rework_quantity;
    }

    /**
     * Get the defect percentage.
     */
    public function getDefectPercentageAttribute(): float
    {
        if ($this->output_quantity == 0) return 0;
        return ($this->defect_quantity / $this->output_quantity) * 100;
    }

    /**
     * Get the rework percentage.
     */
    public function getReworkPercentageAttribute(): float
    {
        if ($this->output_quantity == 0) return 0;
        return ($this->rework_quantity / $this->output_quantity) * 100;
    }

    /**
     * Calculate efficiency based on standard time.
     */
    public function calculateEfficiency(): float
    {
        if (!$this->actual_time_minutes || !$this->finishingProcess) {
            return 0;
        }
        
        $standardMinutes = $this->finishingProcess->standard_time_minutes * $this->output_quantity;
        return ($standardMinutes / $this->actual_time_minutes) * 100;
    }

    /**
     * Scope for entries by QC status.
     */
    public function scopeByQcStatus($query, $status)
    {
        return $query->where('qc_status', $status);
    }

    /**
     * Scope for entries by status.
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }
}
