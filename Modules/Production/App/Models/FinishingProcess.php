<?php

namespace Modules\Production\App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;

class FinishingProcess extends Model
{
    use HasFactory;

    protected $fillable = [
        'process_name',
        'process_code',
        'description',
        'standard_time_minutes',
        'sequence_order',
        'process_type',
        'is_mandatory',
        'status'
    ];

    protected $casts = [
        'standard_time_minutes' => 'decimal:2',
        'is_mandatory' => 'boolean'
    ];

    /**
     * Get the finishing entries for this process.
     */
    public function finishingEntries(): HasMany
    {
        return $this->hasMany(FinishingEntry::class);
    }

    /**
     * Scope for active processes.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope for mandatory processes.
     */
    public function scopeMandatory($query)
    {
        return $query->where('is_mandatory', true);
    }

    /**
     * Scope by process type.
     */
    public function scopeByType($query, $type)
    {
        return $query->where('process_type', $type);
    }

    /**
     * Scope ordered by sequence.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sequence_order');
    }
}
