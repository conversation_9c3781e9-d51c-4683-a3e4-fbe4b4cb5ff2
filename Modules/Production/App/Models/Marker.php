<?php

namespace Modules\Production\App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Marker extends Model
{
    use HasFactory;

    protected $fillable = [
        'cutting_plan_id',
        'marker_number',
        'style_number',
        'marker_length',
        'marker_width',
        'marker_efficiency',
        'ply_count',
        'size_ratio',
        'size_layout',
        'total_pieces_per_marker',
        'fabric_utilization',
        'efficiency_percentage',
        'marker_file_path',
        'status',
        'notes'
    ];

    protected $casts = [
        'marker_length' => 'decimal:2',
        'marker_width' => 'decimal:2',
        'marker_efficiency' => 'decimal:2',
        'fabric_utilization' => 'decimal:2',
        'efficiency_percentage' => 'decimal:2',
        'size_ratio' => 'array'
    ];

    /**
     * Get the cutting plan that owns this marker.
     */
    public function cuttingPlan(): BelongsTo
    {
        return $this->belongsTo(CuttingPlan::class);
    }

    /**
     * Get the cutting entries for this marker.
     */
    public function cuttingEntries(): HasMany
    {
        return $this->hasMany(CuttingEntry::class);
    }

    /**
     * Get the marker area.
     */
    public function getMarkerAreaAttribute(): float
    {
        return $this->marker_length * $this->marker_width;
    }

    /**
     * Generate unique marker number.
     */
    public static function generateMarkerNumber(): string
    {
        $prefix = 'MK';
        $date = now()->format('Ymd');
        $lastMarker = static::where('marker_number', 'like', $prefix . $date . '%')
                           ->orderBy('marker_number', 'desc')
                           ->first();
        
        if ($lastMarker) {
            $lastNumber = intval(substr($lastMarker->marker_number, -4));
            $newNumber = str_pad($lastNumber + 1, 4, '0', STR_PAD_LEFT);
        } else {
            $newNumber = '0001';
        }
        
        return $prefix . $date . $newNumber;
    }
}
