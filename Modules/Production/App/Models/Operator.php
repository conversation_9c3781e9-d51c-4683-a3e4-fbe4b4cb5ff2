<?php

namespace Modules\Production\App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Operator extends Model
{
    use HasFactory;

    protected $fillable = [
        'operator_id',
        'name',
        'designation',
        'sewing_line_id',
        'skill_level',
        'hourly_rate',
        'efficiency_rating',
        'machine_skills',
        'joining_date',
        'status',
        'notes'
    ];

    protected $casts = [
        'joining_date' => 'date',
        'hourly_rate' => 'decimal:2',
        'efficiency_rating' => 'decimal:2',
        'machine_skills' => 'array'
    ];

    /**
     * Get the sewing line that owns this operator.
     */
    public function sewingLine(): BelongsTo
    {
        return $this->belongsTo(SewingLine::class);
    }

    /**
     * Get the sewing entries for this operator.
     */
    public function sewingEntries(): HasMany
    {
        return $this->hasMany(SewingEntry::class);
    }

    /**
     * Get the operator's experience in years.
     */
    public function getExperienceYearsAttribute(): float
    {
        return $this->joining_date->diffInYears(now());
    }

    /**
     * Get today's efficiency.
     */
    public function getTodayEfficiencyAttribute(): float
    {
        $today = now()->format('Y-m-d');
        $entries = $this->sewingEntries()
                       ->whereDate('input_datetime', $today)
                       ->whereNotNull('efficiency_achieved')
                       ->get();
        
        if ($entries->isEmpty()) return 0;
        
        return $entries->avg('efficiency_achieved');
    }

    /**
     * Get today's production count.
     */
    public function getTodayProductionAttribute(): int
    {
        $today = now()->format('Y-m-d');
        return $this->sewingEntries()
                   ->whereDate('input_datetime', $today)
                   ->sum('output_quantity');
    }

    /**
     * Check if operator is active.
     */
    public function getIsActiveAttribute(): bool
    {
        return $this->status === 'active';
    }

    /**
     * Scope for active operators.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope by skill level.
     */
    public function scopeBySkillLevel($query, $skillLevel)
    {
        return $query->where('skill_level', $skillLevel);
    }
}
