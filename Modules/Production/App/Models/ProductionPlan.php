<?php

namespace Modules\Production\App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ProductionPlan extends Model
{
    use HasFactory;

    protected $fillable = [
        'plan_number',
        'order_id',
        'style_number',
        'po_number',
        'target_quantity',
        'start_date',
        'end_date',
        'line_capacity',
        'efficiency_percentage',
        'fabric_requirements',
        'accessories_requirements',
        'fabric_wastage_percentage',
        'status',
        'notes'
    ];

    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'efficiency_percentage' => 'decimal:2',
        'fabric_wastage_percentage' => 'decimal:2',
        'fabric_requirements' => 'array',
        'accessories_requirements' => 'array'
    ];

    /**
     * Get the cutting plans for this production plan.
     */
    public function cuttingPlans(): HasMany
    {
        return $this->hasMany(CuttingPlan::class);
    }

    /**
     * Get the total fabric requirement with wastage.
     */
    public function getTotalFabricRequirementAttribute(): float
    {
        $baseRequirement = collect($this->fabric_requirements)->sum('quantity') ?? 0;
        $wastageMultiplier = 1 + ($this->fabric_wastage_percentage / 100);
        return $baseRequirement * $wastageMultiplier;
    }

    /**
     * Get the production duration in days.
     */
    public function getProductionDurationAttribute(): int
    {
        return $this->start_date->diffInDays($this->end_date) + 1;
    }

    /**
     * Get the daily target quantity.
     */
    public function getDailyTargetAttribute(): float
    {
        return $this->target_quantity / $this->production_duration;
    }

    /**
     * Check if the plan is active.
     */
    public function getIsActiveAttribute(): bool
    {
        return in_array($this->status, ['approved', 'in_progress']);
    }

    /**
     * Scope for active plans.
     */
    public function scopeActive($query)
    {
        return $query->whereIn('status', ['approved', 'in_progress']);
    }

    /**
     * Scope for plans by date range.
     */
    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('start_date', [$startDate, $endDate])
                    ->orWhereBetween('end_date', [$startDate, $endDate]);
    }

    /**
     * Generate unique plan number.
     */
    public static function generatePlanNumber(): string
    {
        $prefix = 'PP';
        $date = now()->format('Ymd');
        $lastPlan = static::where('plan_number', 'like', $prefix . $date . '%')
                          ->orderBy('plan_number', 'desc')
                          ->first();
        
        if ($lastPlan) {
            $lastNumber = intval(substr($lastPlan->plan_number, -4));
            $newNumber = str_pad($lastNumber + 1, 4, '0', STR_PAD_LEFT);
        } else {
            $newNumber = '0001';
        }
        
        return $prefix . $date . $newNumber;
    }
}
