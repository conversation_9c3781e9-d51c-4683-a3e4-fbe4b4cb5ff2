<?php

namespace Modules\Production\App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class QualityInspection extends Model
{
    use HasFactory;

    protected $fillable = [
        'inspection_number',
        'inspectable_type',
        'inspectable_id',
        'aql_chart_id',
        'bundle_number',
        'style_number',
        'po_number',
        'color',
        'size',
        'inspection_stage',
        'lot_quantity',
        'sample_size',
        'inspected_quantity',
        'defect_quantity',
        'major_defects',
        'minor_defects',
        'critical_defects',
        'defect_percentage',
        'inspection_result',
        'inspector_name',
        'inspection_datetime',
        'defect_details',
        'corrective_actions',
        'notes'
    ];

    protected $casts = [
        'inspection_datetime' => 'datetime',
        'defect_percentage' => 'decimal:2',
        'defect_details' => 'array'
    ];

    /**
     * Get the inspectable model (sewing entry, finishing entry, or washing entry).
     */
    public function inspectable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Boot the model and set up morph map.
     */
    protected static function boot()
    {
        parent::boot();

        // Set up the morph map for polymorphic relationships
        \Illuminate\Database\Eloquent\Relations\Relation::morphMap([
            'sewing' => \Modules\Production\App\Models\SewingEntry::class,
            'finishing' => \Modules\Production\App\Models\FinishingEntry::class,
            'washing' => \Modules\Production\App\Models\WashingEntry::class,
        ]);
    }

    /**
     * Get the AQL chart that owns this quality inspection.
     */
    public function aqlChart(): BelongsTo
    {
        return $this->belongsTo(AqlChart::class);
    }

    /**
     * Get the defect logs for this quality inspection.
     */
    public function defectLogs(): HasMany
    {
        return $this->hasMany(DefectLog::class);
    }

    /**
     * Get the total defects count.
     */
    public function getTotalDefectsAttribute(): int
    {
        return $this->critical_defects + $this->major_defects + $this->minor_defects;
    }

    /**
     * Calculate defect percentage.
     */
    public function calculateDefectPercentage(): float
    {
        if ($this->inspected_quantity == 0) return 0;
        return ($this->defect_quantity / $this->inspected_quantity) * 100;
    }

    /**
     * Determine inspection result based on AQL chart.
     */
    public function determineResult(): string
    {
        if (!$this->aqlChart) {
            // Fallback logic if no AQL chart is assigned
            if ($this->critical_defects > 0) {
                return 'fail';
            } elseif ($this->defect_percentage > 4.0) {
                return 'fail';
            } elseif ($this->defect_percentage > 2.5) {
                return 'conditional_pass';
            } else {
                return 'pass';
            }
        }

        return $this->aqlChart->determineResult($this->defect_quantity);
    }

    /**
     * Generate unique inspection number.
     */
    public static function generateInspectionNumber(): string
    {
        $prefix = 'QI';
        $date = now()->format('Ymd');
        $lastInspection = static::where('inspection_number', 'like', $prefix . $date . '%')
                               ->orderBy('inspection_number', 'desc')
                               ->first();
        
        if ($lastInspection) {
            $lastNumber = intval(substr($lastInspection->inspection_number, -4));
            $newNumber = str_pad($lastNumber + 1, 4, '0', STR_PAD_LEFT);
        } else {
            $newNumber = '0001';
        }
        
        return $prefix . $date . $newNumber;
    }

    /**
     * Scope for inspections by stage.
     */
    public function scopeByStage($query, $stage)
    {
        return $query->where('inspection_stage', $stage);
    }

    /**
     * Scope for inspections by result.
     */
    public function scopeByResult($query, $result)
    {
        return $query->where('inspection_result', $result);
    }

    /**
     * Scope for failed inspections.
     */
    public function scopeFailed($query)
    {
        return $query->where('inspection_result', 'fail');
    }
}
