<?php

namespace Modules\Production\App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;

class SewingEntry extends Model
{
    use HasFactory;

    protected $fillable = [
        'bundle_ticket_id',
        'sewing_line_id',
        'operator_id',
        'input_datetime',
        'output_datetime',
        'input_quantity',
        'output_quantity',
        'defect_quantity',
        'actual_smv',
        'efficiency_achieved',
        'defect_types',
        'status',
        'notes'
    ];

    protected $casts = [
        'input_datetime' => 'datetime',
        'output_datetime' => 'datetime',
        'actual_smv' => 'decimal:2',
        'efficiency_achieved' => 'decimal:2',
        'defect_types' => 'array'
    ];

    /**
     * Get the bundle ticket that owns this sewing entry.
     */
    public function bundleTicket(): BelongsTo
    {
        return $this->belongsTo(BundleTicket::class);
    }

    /**
     * Get the sewing line that owns this sewing entry.
     */
    public function sewingLine(): BelongsTo
    {
        return $this->belongsTo(SewingLine::class);
    }

    /**
     * Get the operator that owns this sewing entry.
     */
    public function operator(): BelongsTo
    {
        return $this->belongsTo(Operator::class);
    }

    /**
     * Get the finishing entries for this sewing entry.
     */
    public function finishingEntries(): HasMany
    {
        return $this->hasMany(FinishingEntry::class);
    }

    /**
     * Get the quality inspections for this sewing entry.
     */
    public function qualityInspections(): MorphMany
    {
        return $this->morphMany(QualityInspection::class, 'inspectable');
    }

    /**
     * Get the processing time in minutes.
     */
    public function getProcessingTimeMinutesAttribute(): ?float
    {
        if (!$this->output_datetime || !$this->input_datetime) {
            return null;
        }
        
        return $this->input_datetime->diffInMinutes($this->output_datetime);
    }

    /**
     * Get the good quantity (output - defects).
     */
    public function getGoodQuantityAttribute(): int
    {
        return $this->output_quantity - $this->defect_quantity;
    }

    /**
     * Get the defect percentage.
     */
    public function getDefectPercentageAttribute(): float
    {
        if ($this->output_quantity == 0) return 0;
        return ($this->defect_quantity / $this->output_quantity) * 100;
    }

    /**
     * Calculate efficiency based on SMV.
     */
    public function calculateEfficiency(): float
    {
        if (!$this->processing_time_minutes || !$this->bundleTicket) {
            return 0;
        }
        
        $standardMinutes = $this->bundleTicket->smv * $this->output_quantity;
        return ($standardMinutes / $this->processing_time_minutes) * 100;
    }

    /**
     * Scope for entries by date range.
     */
    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('input_datetime', [$startDate, $endDate]);
    }

    /**
     * Scope for entries by status.
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }
}
