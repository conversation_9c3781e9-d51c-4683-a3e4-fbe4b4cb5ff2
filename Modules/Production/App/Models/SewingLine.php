<?php

namespace Modules\Production\App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;

class SewingLine extends Model
{
    use HasFactory;

    protected $fillable = [
        'line_number',
        'line_name',
        'total_operators',
        'helpers',
        'daily_capacity',
        'target_efficiency',
        'supervisor_name',
        'line_chief_name',
        'machine_types',
        'status',
        'notes'
    ];

    protected $casts = [
        'target_efficiency' => 'decimal:2',
        'machine_types' => 'array'
    ];

    /**
     * Get the operators for this sewing line.
     */
    public function operators(): Has<PERSON>any
    {
        return $this->hasMany(Operator::class);
    }

    /**
     * Get the bundle tickets for this sewing line.
     */
    public function bundleTickets(): HasMany
    {
        return $this->hasMany(BundleTicket::class);
    }

    /**
     * Get the sewing entries for this sewing line.
     */
    public function sewingEntries(): HasMany
    {
        return $this->hasMany(SewingEntry::class);
    }

    /**
     * Get the current efficiency for today.
     */
    public function getCurrentEfficiencyAttribute(): float
    {
        $today = now()->format('Y-m-d');
        $entries = $this->sewingEntries()
                       ->whereDate('input_datetime', $today)
                       ->whereNotNull('efficiency_achieved')
                       ->get();
        
        if ($entries->isEmpty()) return 0;
        
        return $entries->avg('efficiency_achieved');
    }

    /**
     * Get today's production count.
     */
    public function getTodayProductionAttribute(): int
    {
        $today = now()->format('Y-m-d');
        return $this->sewingEntries()
                   ->whereDate('input_datetime', $today)
                   ->sum('output_quantity');
    }

    /**
     * Get the WIP (Work in Progress) count.
     */
    public function getWipCountAttribute(): int
    {
        return $this->sewingEntries()
                   ->whereIn('status', ['input', 'in_progress'])
                   ->sum('input_quantity') - 
               $this->sewingEntries()
                   ->whereIn('status', ['output', 'completed'])
                   ->sum('output_quantity');
    }

    /**
     * Check if line is active.
     */
    public function getIsActiveAttribute(): bool
    {
        return $this->status === 'active';
    }

    /**
     * Scope for active lines.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }
}
