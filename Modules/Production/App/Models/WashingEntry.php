<?php

namespace Modules\Production\App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;

class WashingEntry extends Model
{
    use HasFactory;

    protected $fillable = [
        'finishing_entry_id',
        'washing_recipe_id',
        'wash_lot_number',
        'bundle_number',
        'style_number',
        'po_number',
        'color',
        'size',
        'input_quantity',
        'output_quantity',
        'damage_quantity',
        'pre_wash_measurements',
        'post_wash_measurements',
        'actual_shrinkage',
        'chemical_consumption',
        'wash_cost',
        'wash_start_datetime',
        'wash_end_datetime',
        'operator_name',
        'wash_quality',
        'status',
        'damage_details',
        'notes'
    ];

    protected $casts = [
        'wash_start_datetime' => 'datetime',
        'wash_end_datetime' => 'datetime',
        'pre_wash_measurements' => 'array',
        'post_wash_measurements' => 'array',
        'actual_shrinkage' => 'decimal:2',
        'chemical_consumption' => 'array',
        'wash_cost' => 'decimal:2'
    ];

    /**
     * Get the finishing entry that owns this washing entry.
     */
    public function finishingEntry(): BelongsTo
    {
        return $this->belongsTo(FinishingEntry::class);
    }

    /**
     * Get the washing recipe that owns this washing entry.
     */
    public function washingRecipe(): BelongsTo
    {
        return $this->belongsTo(WashingRecipe::class);
    }

    /**
     * Get the quality inspections for this washing entry.
     */
    public function qualityInspections(): MorphMany
    {
        return $this->morphMany(QualityInspection::class, 'inspectable');
    }

    /**
     * Get the good quantity (output - damage).
     */
    public function getGoodQuantityAttribute(): int
    {
        return $this->output_quantity - $this->damage_quantity;
    }

    /**
     * Get the damage percentage.
     */
    public function getDamagePercentageAttribute(): float
    {
        if ($this->input_quantity == 0) return 0;
        return ($this->damage_quantity / $this->input_quantity) * 100;
    }

    /**
     * Get the wash duration in minutes.
     */
    public function getWashDurationMinutesAttribute(): ?int
    {
        if (!$this->wash_end_datetime || !$this->wash_start_datetime) {
            return null;
        }
        
        return $this->wash_start_datetime->diffInMinutes($this->wash_end_datetime);
    }

    /**
     * Generate unique wash lot number.
     */
    public static function generateWashLotNumber(): string
    {
        $prefix = 'WL';
        $date = now()->format('Ymd');
        $lastLot = static::where('wash_lot_number', 'like', $prefix . $date . '%')
                        ->orderBy('wash_lot_number', 'desc')
                        ->first();
        
        if ($lastLot) {
            $lastNumber = intval(substr($lastLot->wash_lot_number, -4));
            $newNumber = str_pad($lastNumber + 1, 4, '0', STR_PAD_LEFT);
        } else {
            $newNumber = '0001';
        }
        
        return $prefix . $date . $newNumber;
    }

    /**
     * Scope for entries by status.
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope for entries by wash quality.
     */
    public function scopeByWashQuality($query, $quality)
    {
        return $query->where('wash_quality', $quality);
    }
}
