<?php

namespace Modules\Production\App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;

class WashingRecipe extends Model
{
    use HasFactory;

    protected $fillable = [
        'recipe_name',
        'recipe_code',
        'wash_type',
        'description',
        'chemicals_required',
        'water_temperature',
        'wash_time_minutes',
        'rinse_cycles',
        'expected_shrinkage',
        'process_steps',
        'garment_type',
        'status',
        'notes'
    ];

    protected $casts = [
        'chemicals_required' => 'array',
        'water_temperature' => 'decimal:2',
        'expected_shrinkage' => 'decimal:2',
        'process_steps' => 'array'
    ];

    /**
     * Get the washing entries for this recipe.
     */
    public function washingEntries(): HasMany
    {
        return $this->hasMany(WashingEntry::class);
    }

    /**
     * Get the total chemical cost for this recipe.
     */
    public function getTotalChemicalCostAttribute(): float
    {
        return collect($this->chemicals_required)->sum('cost') ?? 0;
    }

    /**
     * Scope for active recipes.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope by wash type.
     */
    public function scopeByWashType($query, $washType)
    {
        return $query->where('wash_type', $washType);
    }

    /**
     * Scope by garment type.
     */
    public function scopeByGarmentType($query, $garmentType)
    {
        return $query->where('garment_type', $garmentType);
    }
}
