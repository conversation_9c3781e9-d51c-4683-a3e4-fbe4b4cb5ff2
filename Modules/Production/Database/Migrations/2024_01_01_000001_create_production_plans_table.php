<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('production_plans', function (Blueprint $table) {
            $table->id();
            $table->string('plan_number')->unique();
            $table->unsignedBigInteger('order_id')->nullable();
            $table->string('style_number');
            $table->string('po_number');
            $table->integer('target_quantity');
            $table->date('start_date');
            $table->date('end_date');
            $table->integer('line_capacity')->default(0);
            $table->decimal('efficiency_percentage', 5, 2)->default(85.00);
            $table->json('fabric_requirements')->nullable(); // Store fabric requirements as JSON
            $table->json('accessories_requirements')->nullable(); // Store accessories requirements as JSON
            $table->decimal('fabric_wastage_percentage', 5, 2)->default(5.00);
            $table->enum('status', ['draft', 'approved', 'in_progress', 'completed', 'cancelled'])->default('draft');
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->index(['order_id', 'style_number']);
            $table->index(['po_number']);
            $table->index(['start_date', 'end_date']);
            $table->index(['status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('production_plans');
    }
};
