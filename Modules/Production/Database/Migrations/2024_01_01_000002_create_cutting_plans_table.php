<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cutting_plans', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('production_plan_id');
            $table->string('cutting_number')->unique();
            $table->string('style_number');
            $table->string('po_number');
            $table->string('color');
            $table->decimal('fabric_width', 5, 2); // 45", 58", 60"
            $table->decimal('shrinkage_percentage', 5, 2)->default(3.00);
            $table->integer('ply_count');
            $table->decimal('marker_length', 8, 2);
            $table->decimal('marker_efficiency', 5, 2)->default(85.00);
            $table->json('size_breakdown'); // Store size breakdown as JSON
            $table->decimal('total_fabric_required', 10, 2);
            $table->decimal('fabric_wastage', 10, 2)->default(0);
            $table->enum('status', ['planned', 'in_progress', 'completed', 'cancelled'])->default('planned');
            $table->date('cutting_date');
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->foreign('production_plan_id')->references('id')->on('production_plans')->onDelete('cascade');
            $table->index(['style_number', 'po_number']);
            $table->index(['cutting_date']);
            $table->index(['status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cutting_plans');
    }
};
