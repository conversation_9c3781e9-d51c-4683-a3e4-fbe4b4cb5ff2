<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('markers', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('cutting_plan_id');
            $table->string('marker_number')->unique();
            $table->string('style_number');
            $table->decimal('marker_length', 8, 2);
            $table->decimal('marker_width', 5, 2);
            $table->json('size_ratio'); // Store size ratio as JSON
            $table->integer('total_pieces_per_marker');
            $table->decimal('fabric_utilization', 5, 2);
            $table->decimal('efficiency_percentage', 5, 2);
            $table->string('marker_file_path')->nullable(); // Path to marker file
            $table->enum('status', ['draft', 'approved', 'used'])->default('draft');
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->foreign('cutting_plan_id')->references('id')->on('cutting_plans')->onDelete('cascade');
            $table->index(['style_number']);
            $table->index(['status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('markers');
    }
};
