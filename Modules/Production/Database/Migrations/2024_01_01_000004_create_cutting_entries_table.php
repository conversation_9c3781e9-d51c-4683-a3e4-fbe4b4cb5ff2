<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cutting_entries', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('cutting_plan_id');
            $table->unsignedBigInteger('marker_id')->nullable();
            $table->string('bundle_number')->unique();
            $table->string('style_number');
            $table->string('po_number');
            $table->string('color');
            $table->string('size');
            $table->integer('quantity');
            $table->decimal('fabric_consumed', 10, 2);
            $table->string('cutter_name');
            $table->datetime('cutting_datetime');
            $table->string('qr_code')->nullable(); // QR code for bundle tracking
            $table->enum('status', ['cut', 'sent_to_sewing', 'completed'])->default('cut');
            $table->text('defects')->nullable();
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->foreign('cutting_plan_id')->references('id')->on('cutting_plans')->onDelete('cascade');
            $table->foreign('marker_id')->references('id')->on('markers')->onDelete('set null');
            $table->index(['style_number', 'po_number']);
            $table->index(['bundle_number']);
            $table->index(['cutting_datetime']);
            $table->index(['status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cutting_entries');
    }
};
