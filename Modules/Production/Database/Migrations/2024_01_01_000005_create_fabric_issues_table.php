<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('fabric_issues', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('cutting_plan_id');
            $table->string('issue_number')->unique();
            $table->string('fabric_type');
            $table->string('color');
            $table->decimal('issued_quantity', 10, 2);
            $table->decimal('returned_quantity', 10, 2)->default(0);
            $table->decimal('consumed_quantity', 10, 2)->default(0);
            $table->decimal('wastage_quantity', 10, 2)->default(0);
            $table->string('issued_to'); // Person who received fabric
            $table->datetime('issue_datetime');
            $table->datetime('return_datetime')->nullable();
            $table->enum('status', ['issued', 'partially_returned', 'fully_returned', 'consumed'])->default('issued');
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->foreign('cutting_plan_id')->references('id')->on('cutting_plans')->onDelete('cascade');
            $table->index(['issue_number']);
            $table->index(['fabric_type', 'color']);
            $table->index(['issue_datetime']);
            $table->index(['status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('fabric_issues');
    }
};
