<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sewing_lines', function (Blueprint $table) {
            $table->id();
            $table->string('line_number')->unique();
            $table->string('line_name');
            $table->integer('total_operators');
            $table->integer('helpers')->default(0);
            $table->integer('daily_capacity');
            $table->decimal('target_efficiency', 5, 2)->default(85.00);
            $table->string('supervisor_name');
            $table->string('line_chief_name')->nullable();
            $table->json('machine_types'); // Store machine types as JSON
            $table->enum('status', ['active', 'inactive', 'maintenance'])->default('active');
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->index(['line_number']);
            $table->index(['status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sewing_lines');
    }
};
