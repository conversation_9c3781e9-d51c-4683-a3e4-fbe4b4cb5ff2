<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('operators', function (Blueprint $table) {
            $table->id();
            $table->string('operator_id')->unique();
            $table->string('name');
            $table->string('designation'); // Operator, Helper, Quality Checker, etc.
            $table->unsignedBigInteger('sewing_line_id')->nullable();
            $table->enum('skill_level', ['trainee', 'junior', 'senior', 'expert'])->default('junior');
            $table->decimal('hourly_rate', 8, 2)->default(0);
            $table->decimal('efficiency_rating', 5, 2)->default(85.00);
            $table->json('machine_skills')->nullable(); // Store machine skills as JSON
            $table->date('joining_date');
            $table->enum('status', ['active', 'inactive', 'on_leave'])->default('active');
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->foreign('sewing_line_id')->references('id')->on('sewing_lines')->onDelete('set null');
            $table->index(['operator_id']);
            $table->index(['sewing_line_id']);
            $table->index(['skill_level']);
            $table->index(['status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('operators');
    }
};
