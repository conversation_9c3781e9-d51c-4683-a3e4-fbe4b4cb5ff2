<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('bundle_tickets', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('cutting_entry_id');
            $table->string('ticket_number')->unique();
            $table->string('bundle_number');
            $table->string('style_number');
            $table->string('po_number');
            $table->string('color');
            $table->string('size');
            $table->integer('quantity');
            $table->decimal('smv', 8, 2); // Standard Minute Value
            $table->string('barcode')->nullable();
            $table->string('qr_code')->nullable();
            $table->datetime('issued_datetime');
            $table->unsignedBigInteger('sewing_line_id')->nullable();
            $table->enum('status', ['issued', 'in_sewing', 'completed', 'rejected'])->default('issued');
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->foreign('cutting_entry_id')->references('id')->on('cutting_entries')->onDelete('cascade');
            $table->foreign('sewing_line_id')->references('id')->on('sewing_lines')->onDelete('set null');
            $table->index(['ticket_number']);
            $table->index(['bundle_number']);
            $table->index(['style_number', 'po_number']);
            $table->index(['sewing_line_id']);
            $table->index(['status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('bundle_tickets');
    }
};
