<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sewing_entries', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('bundle_ticket_id');
            $table->unsignedBigInteger('sewing_line_id');
            $table->unsignedBigInteger('operator_id')->nullable();
            $table->datetime('input_datetime');
            $table->datetime('output_datetime')->nullable();
            $table->integer('input_quantity');
            $table->integer('output_quantity')->default(0);
            $table->integer('defect_quantity')->default(0);
            $table->decimal('actual_smv', 8, 2)->nullable();
            $table->decimal('efficiency_achieved', 5, 2)->nullable();
            $table->json('defect_types')->nullable(); // Store defect types as JSON
            $table->enum('status', ['input', 'in_progress', 'output', 'completed'])->default('input');
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->foreign('bundle_ticket_id')->references('id')->on('bundle_tickets')->onDelete('cascade');
            $table->foreign('sewing_line_id')->references('id')->on('sewing_lines')->onDelete('cascade');
            $table->foreign('operator_id')->references('id')->on('operators')->onDelete('set null');
            $table->index(['sewing_line_id', 'input_datetime']);
            $table->index(['operator_id']);
            $table->index(['status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sewing_entries');
    }
};
