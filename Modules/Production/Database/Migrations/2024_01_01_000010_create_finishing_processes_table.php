<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('finishing_processes', function (Blueprint $table) {
            $table->id();
            $table->string('process_name');
            $table->string('process_code')->unique();
            $table->text('description')->nullable();
            $table->decimal('standard_time_minutes', 8, 2)->default(0); // Standard time for process
            $table->integer('sequence_order')->default(1);
            $table->enum('process_type', ['buttoning', 'trimming', 'folding', 'poly_bagging', 'labeling', 'measurement', 'quality_check'])->default('quality_check');
            $table->boolean('is_mandatory')->default(true);
            $table->enum('status', ['active', 'inactive'])->default('active');
            $table->timestamps();

            $table->index(['process_code']);
            $table->index(['process_type']);
            $table->index(['sequence_order']);
            $table->index(['status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('finishing_processes');
    }
};
