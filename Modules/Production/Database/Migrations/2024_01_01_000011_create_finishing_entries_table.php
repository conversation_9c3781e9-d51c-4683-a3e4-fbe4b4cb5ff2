<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('finishing_entries', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('sewing_entry_id');
            $table->unsignedBigInteger('finishing_process_id');
            $table->string('bundle_number');
            $table->string('style_number');
            $table->string('po_number');
            $table->string('color');
            $table->string('size');
            $table->integer('input_quantity');
            $table->integer('output_quantity')->default(0);
            $table->integer('defect_quantity')->default(0);
            $table->integer('rework_quantity')->default(0);
            $table->datetime('start_datetime');
            $table->datetime('end_datetime')->nullable();
            $table->string('operator_name');
            $table->decimal('actual_time_minutes', 8, 2)->nullable();
            $table->decimal('efficiency_achieved', 5, 2)->nullable();
            $table->json('measurements')->nullable(); // Store measurements as JSON
            $table->json('defect_details')->nullable(); // Store defect details as JSON
            $table->enum('qc_status', ['pass', 'fail', 'rework', 'pending'])->default('pending');
            $table->enum('status', ['input', 'in_progress', 'completed', 'rejected'])->default('input');
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->foreign('sewing_entry_id')->references('id')->on('sewing_entries')->onDelete('cascade');
            $table->foreign('finishing_process_id')->references('id')->on('finishing_processes')->onDelete('cascade');
            $table->index(['bundle_number']);
            $table->index(['style_number', 'po_number']);
            $table->index(['start_datetime']);
            $table->index(['qc_status']);
            $table->index(['status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('finishing_entries');
    }
};
