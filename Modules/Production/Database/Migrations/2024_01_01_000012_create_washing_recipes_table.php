<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('washing_recipes', function (Blueprint $table) {
            $table->id();
            $table->string('recipe_name');
            $table->string('recipe_code')->unique();
            $table->enum('wash_type', ['enzyme_wash', 'acid_wash', 'stone_wash', 'softener_treatment', 'bleach_wash', 'normal_wash'])->default('normal_wash');
            $table->text('description')->nullable();
            $table->json('chemicals_required'); // Store chemicals and quantities as JSON
            $table->decimal('water_temperature', 5, 2)->default(40.00); // Temperature in Celsius
            $table->integer('wash_time_minutes')->default(60);
            $table->integer('rinse_cycles')->default(3);
            $table->decimal('expected_shrinkage', 5, 2)->default(3.00); // Expected shrinkage percentage
            $table->json('process_steps'); // Store detailed process steps as JSON
            $table->enum('garment_type', ['denim_pants', 'denim_jacket', 'denim_shirt', 'other'])->default('denim_pants');
            $table->enum('status', ['active', 'inactive', 'testing'])->default('active');
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->index(['recipe_code']);
            $table->index(['wash_type']);
            $table->index(['garment_type']);
            $table->index(['status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('washing_recipes');
    }
};
