<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('washing_entries', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('finishing_entry_id');
            $table->unsignedBigInteger('washing_recipe_id');
            $table->string('wash_lot_number')->unique();
            $table->string('bundle_number');
            $table->string('style_number');
            $table->string('po_number');
            $table->string('color');
            $table->string('size');
            $table->integer('input_quantity');
            $table->integer('output_quantity')->default(0);
            $table->integer('damage_quantity')->default(0);
            $table->json('pre_wash_measurements')->nullable(); // Store pre-wash measurements as JSON
            $table->json('post_wash_measurements')->nullable(); // Store post-wash measurements as JSON
            $table->decimal('actual_shrinkage', 5, 2)->nullable();
            $table->json('chemical_consumption')->nullable(); // Store actual chemical consumption as JSON
            $table->decimal('wash_cost', 10, 2)->default(0);
            $table->datetime('wash_start_datetime');
            $table->datetime('wash_end_datetime')->nullable();
            $table->string('operator_name');
            $table->enum('wash_quality', ['excellent', 'good', 'acceptable', 'poor'])->default('good');
            $table->enum('status', ['in_wash', 'completed', 'rejected', 'rework'])->default('in_wash');
            $table->text('damage_details')->nullable();
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->foreign('finishing_entry_id')->references('id')->on('finishing_entries')->onDelete('cascade');
            $table->foreign('washing_recipe_id')->references('id')->on('washing_recipes')->onDelete('cascade');
            $table->index(['wash_lot_number']);
            $table->index(['bundle_number']);
            $table->index(['style_number', 'po_number']);
            $table->index(['wash_start_datetime']);
            $table->index(['status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('washing_entries');
    }
};
