<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('aql_charts', function (Blueprint $table) {
            $table->id();
            $table->string('aql_level'); // 1.0, 1.5, 2.5, 4.0, 6.5, 10.0
            $table->integer('lot_size_min');
            $table->integer('lot_size_max');
            $table->integer('sample_size');
            $table->integer('acceptance_number');
            $table->integer('rejection_number');
            $table->enum('inspection_level', ['I', 'II', 'III'])->default('II');
            $table->enum('inspection_type', ['normal', 'tightened', 'reduced'])->default('normal');
            $table->text('description')->nullable();
            $table->timestamps();

            $table->index(['aql_level']);
            $table->index(['lot_size_min', 'lot_size_max']);
            $table->index(['inspection_level', 'inspection_type']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('aql_charts');
    }
};
