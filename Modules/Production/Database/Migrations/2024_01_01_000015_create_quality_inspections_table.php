<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('quality_inspections', function (Blueprint $table) {
            $table->id();
            $table->string('inspection_number')->unique();
            $table->morphs('inspectable'); // Polymorphic relation to sewing_entries, finishing_entries, washing_entries
            $table->unsignedBigInteger('aql_chart_id')->nullable();
            $table->string('bundle_number');
            $table->string('style_number');
            $table->string('po_number');
            $table->string('color');
            $table->string('size');
            $table->enum('inspection_stage', ['inline', 'end_line', 'pre_final', 'final', 'audit'])->default('inline');
            $table->integer('lot_quantity');
            $table->integer('sample_size');
            $table->integer('inspected_quantity');
            $table->integer('defect_quantity')->default(0);
            $table->integer('major_defects')->default(0);
            $table->integer('minor_defects')->default(0);
            $table->integer('critical_defects')->default(0);
            $table->decimal('defect_percentage', 5, 2)->default(0);
            $table->enum('inspection_result', ['pass', 'fail', 'conditional_pass'])->default('pass');
            $table->string('inspector_name');
            $table->datetime('inspection_datetime');
            $table->json('defect_details')->nullable(); // Store detailed defect information as JSON
            $table->text('corrective_actions')->nullable();
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->foreign('aql_chart_id')->references('id')->on('aql_charts')->onDelete('set null');
            $table->index(['inspection_number']);
            $table->index(['bundle_number']);
            $table->index(['style_number', 'po_number']);
            $table->index(['inspection_stage']);
            $table->index(['inspection_result']);
            $table->index(['inspection_datetime']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('quality_inspections');
    }
};
