<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('defect_logs', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('quality_inspection_id');
            $table->string('defect_code');
            $table->string('defect_name');
            $table->enum('defect_category', ['sewing', 'cutting', 'finishing', 'washing', 'fabric', 'measurement', 'other'])->default('sewing');
            $table->enum('defect_severity', ['critical', 'major', 'minor'])->default('minor');
            $table->text('defect_description');
            $table->string('defect_location')->nullable(); // Where on garment
            $table->integer('defect_count')->default(1);
            $table->string('detected_by');
            $table->datetime('detection_datetime');
            $table->enum('action_taken', ['rework', 'reject', 'accept', 'downgrade'])->default('rework');
            $table->text('corrective_action')->nullable();
            $table->string('responsible_operator')->nullable();
            $table->decimal('cost_impact', 10, 2)->default(0);
            $table->enum('status', ['open', 'resolved', 'closed'])->default('open');
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->foreign('quality_inspection_id')->references('id')->on('quality_inspections')->onDelete('cascade');
            $table->index(['defect_code']);
            $table->index(['defect_category']);
            $table->index(['defect_severity']);
            $table->index(['detection_datetime']);
            $table->index(['status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('defect_logs');
    }
};
