<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('cutting_plans', function (Blueprint $table) {
            if (!Schema::hasColumn('cutting_plans', 'cutting_efficiency')) {
                $table->decimal('cutting_efficiency', 5, 2)->default(85.00)->after('marker_efficiency');
            }
            if (!Schema::hasColumn('cutting_plans', 'fabric_consumption_per_piece')) {
                $table->decimal('fabric_consumption_per_piece', 8, 2)->nullable()->after('total_fabric_required');
            }
            if (!Schema::hasColumn('cutting_plans', 'total_pieces')) {
                $table->integer('total_pieces')->default(0)->after('fabric_consumption_per_piece');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('cutting_plans', function (Blueprint $table) {
            if (Schema::hasColumn('cutting_plans', 'cutting_efficiency')) {
                $table->dropColumn('cutting_efficiency');
            }
            if (Schema::hasColumn('cutting_plans', 'fabric_consumption_per_piece')) {
                $table->dropColumn('fabric_consumption_per_piece');
            }
            if (Schema::hasColumn('cutting_plans', 'total_pieces')) {
                $table->dropColumn('total_pieces');
            }
        });
    }
};
