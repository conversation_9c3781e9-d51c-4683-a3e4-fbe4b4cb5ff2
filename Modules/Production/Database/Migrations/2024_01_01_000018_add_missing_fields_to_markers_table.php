<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('markers', function (Blueprint $table) {
            if (!Schema::hasColumn('markers', 'marker_efficiency')) {
                $table->decimal('marker_efficiency', 5, 2)->default(85.00)->after('marker_width');
            }
            if (!Schema::hasColumn('markers', 'ply_count')) {
                $table->integer('ply_count')->default(1)->after('marker_efficiency');
            }
            if (!Schema::hasColumn('markers', 'size_layout')) {
                $table->text('size_layout')->nullable()->after('ply_count');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('markers', function (Blueprint $table) {
            if (Schema::hasColumn('markers', 'marker_efficiency')) {
                $table->dropColumn('marker_efficiency');
            }
            if (Schema::hasColumn('markers', 'ply_count')) {
                $table->dropColumn('ply_count');
            }
            if (Schema::hasColumn('markers', 'size_layout')) {
                $table->dropColumn('size_layout');
            }
        });
    }
};
