<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('fabric_issues', function (Blueprint $table) {
            if (!Schema::hasColumn('fabric_issues', 'unit')) {
                $table->string('unit', 20)->default('yards')->after('wastage_quantity');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('fabric_issues', function (Blueprint $table) {
            if (Schema::hasColumn('fabric_issues', 'unit')) {
                $table->dropColumn('unit');
            }
        });
    }
};
