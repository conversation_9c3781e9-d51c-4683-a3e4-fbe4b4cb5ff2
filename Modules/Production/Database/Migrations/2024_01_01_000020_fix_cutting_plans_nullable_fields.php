<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('cutting_plans', function (Blueprint $table) {
            // Make marker_length nullable
            $table->decimal('marker_length', 8, 2)->nullable()->change();
            
            // Make production_plan_id nullable
            $table->unsignedBigInteger('production_plan_id')->nullable()->change();
            
            // Make ply_count have a default value
            $table->integer('ply_count')->default(1)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('cutting_plans', function (Blueprint $table) {
            // Revert changes
            $table->decimal('marker_length', 8, 2)->nullable(false)->change();
            $table->unsignedBigInteger('production_plan_id')->nullable(false)->change();
            $table->integer('ply_count')->default(null)->change();
        });
    }
};
