<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('markers', function (Blueprint $table) {
            // Make size_ratio nullable
            $table->json('size_ratio')->nullable()->change();
            
            // Make other fields nullable or add defaults
            $table->integer('total_pieces_per_marker')->default(0)->change();
            $table->decimal('fabric_utilization', 5, 2)->default(90.00)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('markers', function (Blueprint $table) {
            // Revert changes
            $table->json('size_ratio')->nullable(false)->change();
            $table->integer('total_pieces_per_marker')->default(null)->change();
            $table->decimal('fabric_utilization', 5, 2)->default(null)->change();
        });
    }
};
