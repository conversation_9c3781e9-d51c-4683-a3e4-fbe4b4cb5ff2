<?php

namespace Modules\Production\Database\Seeders;

use Illuminate\Database\Seeder;
use Modules\Production\App\Models\AqlChart;

class AqlChartSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $aqlCharts = [
            // AQL 1.0 Normal Inspection Level II
            ['aql_level' => '1.0', 'lot_size_min' => 2, 'lot_size_max' => 8, 'sample_size' => 2, 'acceptance_number' => 0, 'rejection_number' => 1, 'inspection_level' => 'II', 'inspection_type' => 'normal'],
            ['aql_level' => '1.0', 'lot_size_min' => 9, 'lot_size_max' => 15, 'sample_size' => 3, 'acceptance_number' => 0, 'rejection_number' => 1, 'inspection_level' => 'II', 'inspection_type' => 'normal'],
            ['aql_level' => '1.0', 'lot_size_min' => 16, 'lot_size_max' => 25, 'sample_size' => 5, 'acceptance_number' => 0, 'rejection_number' => 1, 'inspection_level' => 'II', 'inspection_type' => 'normal'],
            ['aql_level' => '1.0', 'lot_size_min' => 26, 'lot_size_max' => 50, 'sample_size' => 8, 'acceptance_number' => 0, 'rejection_number' => 1, 'inspection_level' => 'II', 'inspection_type' => 'normal'],
            ['aql_level' => '1.0', 'lot_size_min' => 51, 'lot_size_max' => 90, 'sample_size' => 13, 'acceptance_number' => 0, 'rejection_number' => 1, 'inspection_level' => 'II', 'inspection_type' => 'normal'],
            ['aql_level' => '1.0', 'lot_size_min' => 91, 'lot_size_max' => 150, 'sample_size' => 20, 'acceptance_number' => 0, 'rejection_number' => 1, 'inspection_level' => 'II', 'inspection_type' => 'normal'],
            ['aql_level' => '1.0', 'lot_size_min' => 151, 'lot_size_max' => 280, 'sample_size' => 32, 'acceptance_number' => 1, 'rejection_number' => 2, 'inspection_level' => 'II', 'inspection_type' => 'normal'],
            ['aql_level' => '1.0', 'lot_size_min' => 281, 'lot_size_max' => 500, 'sample_size' => 50, 'acceptance_number' => 1, 'rejection_number' => 2, 'inspection_level' => 'II', 'inspection_type' => 'normal'],
            ['aql_level' => '1.0', 'lot_size_min' => 501, 'lot_size_max' => 1200, 'sample_size' => 80, 'acceptance_number' => 2, 'rejection_number' => 3, 'inspection_level' => 'II', 'inspection_type' => 'normal'],
            ['aql_level' => '1.0', 'lot_size_min' => 1201, 'lot_size_max' => 3200, 'sample_size' => 125, 'acceptance_number' => 3, 'rejection_number' => 4, 'inspection_level' => 'II', 'inspection_type' => 'normal'],

            // AQL 1.5 Normal Inspection Level II
            ['aql_level' => '1.5', 'lot_size_min' => 2, 'lot_size_max' => 8, 'sample_size' => 2, 'acceptance_number' => 0, 'rejection_number' => 1, 'inspection_level' => 'II', 'inspection_type' => 'normal'],
            ['aql_level' => '1.5', 'lot_size_min' => 9, 'lot_size_max' => 15, 'sample_size' => 3, 'acceptance_number' => 0, 'rejection_number' => 1, 'inspection_level' => 'II', 'inspection_type' => 'normal'],
            ['aql_level' => '1.5', 'lot_size_min' => 16, 'lot_size_max' => 25, 'sample_size' => 5, 'acceptance_number' => 0, 'rejection_number' => 1, 'inspection_level' => 'II', 'inspection_type' => 'normal'],
            ['aql_level' => '1.5', 'lot_size_min' => 26, 'lot_size_max' => 50, 'sample_size' => 8, 'acceptance_number' => 0, 'rejection_number' => 1, 'inspection_level' => 'II', 'inspection_type' => 'normal'],
            ['aql_level' => '1.5', 'lot_size_min' => 51, 'lot_size_max' => 90, 'sample_size' => 13, 'acceptance_number' => 1, 'rejection_number' => 2, 'inspection_level' => 'II', 'inspection_type' => 'normal'],
            ['aql_level' => '1.5', 'lot_size_min' => 91, 'lot_size_max' => 150, 'sample_size' => 20, 'acceptance_number' => 1, 'rejection_number' => 2, 'inspection_level' => 'II', 'inspection_type' => 'normal'],
            ['aql_level' => '1.5', 'lot_size_min' => 151, 'lot_size_max' => 280, 'sample_size' => 32, 'acceptance_number' => 2, 'rejection_number' => 3, 'inspection_level' => 'II', 'inspection_type' => 'normal'],
            ['aql_level' => '1.5', 'lot_size_min' => 281, 'lot_size_max' => 500, 'sample_size' => 50, 'acceptance_number' => 2, 'rejection_number' => 3, 'inspection_level' => 'II', 'inspection_type' => 'normal'],
            ['aql_level' => '1.5', 'lot_size_min' => 501, 'lot_size_max' => 1200, 'sample_size' => 80, 'acceptance_number' => 3, 'rejection_number' => 4, 'inspection_level' => 'II', 'inspection_type' => 'normal'],
            ['aql_level' => '1.5', 'lot_size_min' => 1201, 'lot_size_max' => 3200, 'sample_size' => 125, 'acceptance_number' => 5, 'rejection_number' => 6, 'inspection_level' => 'II', 'inspection_type' => 'normal'],

            // AQL 2.5 Normal Inspection Level II
            ['aql_level' => '2.5', 'lot_size_min' => 2, 'lot_size_max' => 8, 'sample_size' => 2, 'acceptance_number' => 0, 'rejection_number' => 1, 'inspection_level' => 'II', 'inspection_type' => 'normal'],
            ['aql_level' => '2.5', 'lot_size_min' => 9, 'lot_size_max' => 15, 'sample_size' => 3, 'acceptance_number' => 0, 'rejection_number' => 1, 'inspection_level' => 'II', 'inspection_type' => 'normal'],
            ['aql_level' => '2.5', 'lot_size_min' => 16, 'lot_size_max' => 25, 'sample_size' => 5, 'acceptance_number' => 1, 'rejection_number' => 2, 'inspection_level' => 'II', 'inspection_type' => 'normal'],
            ['aql_level' => '2.5', 'lot_size_min' => 26, 'lot_size_max' => 50, 'sample_size' => 8, 'acceptance_number' => 1, 'rejection_number' => 2, 'inspection_level' => 'II', 'inspection_type' => 'normal'],
            ['aql_level' => '2.5', 'lot_size_min' => 51, 'lot_size_max' => 90, 'sample_size' => 13, 'acceptance_number' => 1, 'rejection_number' => 2, 'inspection_level' => 'II', 'inspection_type' => 'normal'],
            ['aql_level' => '2.5', 'lot_size_min' => 91, 'lot_size_max' => 150, 'sample_size' => 20, 'acceptance_number' => 2, 'rejection_number' => 3, 'inspection_level' => 'II', 'inspection_type' => 'normal'],
            ['aql_level' => '2.5', 'lot_size_min' => 151, 'lot_size_max' => 280, 'sample_size' => 32, 'acceptance_number' => 3, 'rejection_number' => 4, 'inspection_level' => 'II', 'inspection_type' => 'normal'],
            ['aql_level' => '2.5', 'lot_size_min' => 281, 'lot_size_max' => 500, 'sample_size' => 50, 'acceptance_number' => 5, 'rejection_number' => 6, 'inspection_level' => 'II', 'inspection_type' => 'normal'],
            ['aql_level' => '2.5', 'lot_size_min' => 501, 'lot_size_max' => 1200, 'sample_size' => 80, 'acceptance_number' => 7, 'rejection_number' => 8, 'inspection_level' => 'II', 'inspection_type' => 'normal'],
            ['aql_level' => '2.5', 'lot_size_min' => 1201, 'lot_size_max' => 3200, 'sample_size' => 125, 'acceptance_number' => 10, 'rejection_number' => 11, 'inspection_level' => 'II', 'inspection_type' => 'normal'],

            // AQL 4.0 Normal Inspection Level II
            ['aql_level' => '4.0', 'lot_size_min' => 2, 'lot_size_max' => 8, 'sample_size' => 2, 'acceptance_number' => 0, 'rejection_number' => 1, 'inspection_level' => 'II', 'inspection_type' => 'normal'],
            ['aql_level' => '4.0', 'lot_size_min' => 9, 'lot_size_max' => 15, 'sample_size' => 3, 'acceptance_number' => 1, 'rejection_number' => 2, 'inspection_level' => 'II', 'inspection_type' => 'normal'],
            ['aql_level' => '4.0', 'lot_size_min' => 16, 'lot_size_max' => 25, 'sample_size' => 5, 'acceptance_number' => 1, 'rejection_number' => 2, 'inspection_level' => 'II', 'inspection_type' => 'normal'],
            ['aql_level' => '4.0', 'lot_size_min' => 26, 'lot_size_max' => 50, 'sample_size' => 8, 'acceptance_number' => 1, 'rejection_number' => 2, 'inspection_level' => 'II', 'inspection_type' => 'normal'],
            ['aql_level' => '4.0', 'lot_size_min' => 51, 'lot_size_max' => 90, 'sample_size' => 13, 'acceptance_number' => 2, 'rejection_number' => 3, 'inspection_level' => 'II', 'inspection_type' => 'normal'],
            ['aql_level' => '4.0', 'lot_size_min' => 91, 'lot_size_max' => 150, 'sample_size' => 20, 'acceptance_number' => 3, 'rejection_number' => 4, 'inspection_level' => 'II', 'inspection_type' => 'normal'],
            ['aql_level' => '4.0', 'lot_size_min' => 151, 'lot_size_max' => 280, 'sample_size' => 32, 'acceptance_number' => 5, 'rejection_number' => 6, 'inspection_level' => 'II', 'inspection_type' => 'normal'],
            ['aql_level' => '4.0', 'lot_size_min' => 281, 'lot_size_max' => 500, 'sample_size' => 50, 'acceptance_number' => 7, 'rejection_number' => 8, 'inspection_level' => 'II', 'inspection_type' => 'normal'],
            ['aql_level' => '4.0', 'lot_size_min' => 501, 'lot_size_max' => 1200, 'sample_size' => 80, 'acceptance_number' => 10, 'rejection_number' => 11, 'inspection_level' => 'II', 'inspection_type' => 'normal'],
            ['aql_level' => '4.0', 'lot_size_min' => 1201, 'lot_size_max' => 3200, 'sample_size' => 125, 'acceptance_number' => 14, 'rejection_number' => 15, 'inspection_level' => 'II', 'inspection_type' => 'normal']
        ];

        foreach ($aqlCharts as $chartData) {
            AqlChart::create($chartData);
        }
    }
}
