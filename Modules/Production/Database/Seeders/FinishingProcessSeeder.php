<?php

namespace Modules\Production\Database\Seeders;

use Illuminate\Database\Seeder;
use Modules\Production\App\Models\FinishingProcess;

class FinishingProcessSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $processes = [
            [
                'process_name' => 'Thread Trimming',
                'process_code' => 'TRM001',
                'description' => 'Remove excess threads from garment',
                'standard_time_minutes' => 2.5,
                'sequence_order' => 1,
                'process_type' => 'trimming',
                'is_mandatory' => true,
                'status' => 'active'
            ],
            [
                'process_name' => 'Button Attachment',
                'process_code' => 'BTN001',
                'description' => 'Attach buttons to garment',
                'standard_time_minutes' => 3.0,
                'sequence_order' => 2,
                'process_type' => 'buttoning',
                'is_mandatory' => true,
                'status' => 'active'
            ],
            [
                'process_name' => 'Final Measurement Check',
                'process_code' => 'MSR001',
                'description' => 'Check garment measurements against spec',
                'standard_time_minutes' => 1.5,
                'sequence_order' => 3,
                'process_type' => 'measurement',
                'is_mandatory' => true,
                'status' => 'active'
            ],
            [
                'process_name' => 'Quality Inspection',
                'process_code' => 'QC001',
                'description' => 'Final quality check before packing',
                'standard_time_minutes' => 2.0,
                'sequence_order' => 4,
                'process_type' => 'quality_check',
                'is_mandatory' => true,
                'status' => 'active'
            ],
            [
                'process_name' => 'Folding',
                'process_code' => 'FLD001',
                'description' => 'Fold garment according to customer requirements',
                'standard_time_minutes' => 1.0,
                'sequence_order' => 5,
                'process_type' => 'folding',
                'is_mandatory' => true,
                'status' => 'active'
            ],
            [
                'process_name' => 'Poly Bagging',
                'process_code' => 'PLY001',
                'description' => 'Pack garment in poly bag',
                'standard_time_minutes' => 0.5,
                'sequence_order' => 6,
                'process_type' => 'poly_bagging',
                'is_mandatory' => true,
                'status' => 'active'
            ],
            [
                'process_name' => 'Labeling',
                'process_code' => 'LBL001',
                'description' => 'Attach size and care labels',
                'standard_time_minutes' => 1.5,
                'sequence_order' => 7,
                'process_type' => 'labeling',
                'is_mandatory' => true,
                'status' => 'active'
            ]
        ];

        foreach ($processes as $processData) {
            FinishingProcess::create($processData);
        }
    }
}
