<?php

namespace Modules\Production\Database\Seeders;

use Illuminate\Database\Seeder;
use Modules\Production\App\Models\Operator;
use Modules\Production\App\Models\SewingLine;

class OperatorSeeder extends Seeder
{
    public function run(): void
    {
        $sewingLines = SewingLine::all();
        
        foreach ($sewingLines as $line) {
            for ($i = 1; $i <= $line->total_operators; $i++) {
                Operator::create([
                    'operator_id' => 'OP' . str_pad($line->id * 100 + $i, 4, '0', STR_PAD_LEFT),
                    'name' => 'Operator ' . $i . ' - Line ' . $line->line_number,
                    'designation' => $i <= 2 ? 'Quality Checker' : 'Operator',
                    'sewing_line_id' => $line->id,
                    'skill_level' => ['trainee', 'junior', 'senior', 'expert'][rand(0, 3)],
                    'hourly_rate' => rand(8, 15),
                    'efficiency_rating' => rand(75, 95),
                    'machine_skills' => ['Single Needle', 'Overlock'],
                    'joining_date' => now()->subDays(rand(30, 365)),
                    'status' => 'active'
                ]);
            }
        }
    }
}
