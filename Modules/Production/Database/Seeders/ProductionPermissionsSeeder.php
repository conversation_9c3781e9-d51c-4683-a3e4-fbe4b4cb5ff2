<?php

namespace Modules\Production\Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class ProductionPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create permissions for Production module
        $permissions = [
            // Dashboard permissions
            'production.dashboard.view',
            
            // Planning permissions
            'production.planning.view',
            'production.planning.create',
            'production.planning.edit',
            'production.planning.delete',
            'production.planning.approve',
            'production.planning.export',
            
            // Cutting permissions
            'production.cutting.view',
            'production.cutting.create',
            'production.cutting.edit',
            'production.cutting.delete',
            'production.cutting.marker-planning',
            'production.cutting.fabric-issues',
            
            // Sewing permissions
            'production.sewing.view',
            'production.sewing.create',
            'production.sewing.edit',
            'production.sewing.delete',
            'production.sewing.line-tracking',
            'production.sewing.efficiency',
            
            // Finishing permissions
            'production.finishing.view',
            'production.finishing.create',
            'production.finishing.edit',
            'production.finishing.delete',
            'production.finishing.processes',
            
            // Washing permissions
            'production.washing.view',
            'production.washing.create',
            'production.washing.edit',
            'production.washing.delete',
            'production.washing.recipes',
            
            // Quality permissions
            'production.quality.view',
            'production.quality.create',
            'production.quality.edit',
            'production.quality.delete',
            'production.quality.aql-charts',
            'production.quality.reports',
            
            // Export permissions
            'production.exports'
        ];

        // Create permissions
        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission, 'guard_name' => 'web']);
        }

        // Create roles and assign permissions
        $this->createRoles();
    }

    /**
     * Create roles and assign permissions.
     */
    private function createRoles(): void
    {
        // Admin role - all permissions
        $admin = Role::firstOrCreate(['name' => 'admin', 'guard_name' => 'web']);
        $admin->givePermissionTo(Permission::all());

        // Production Manager role
        $productionManager = Role::firstOrCreate(['name' => 'production-manager', 'guard_name' => 'web']);
        $productionManager->givePermissionTo([
            'production.dashboard.view',
            'production.planning.view',
            'production.planning.create',
            'production.planning.edit',
            'production.planning.approve',
            'production.planning.export',
            'production.cutting.view',
            'production.cutting.create',
            'production.cutting.edit',
            'production.cutting.marker-planning',
            'production.cutting.fabric-issues',
            'production.sewing.view',
            'production.sewing.create',
            'production.sewing.edit',
            'production.sewing.line-tracking',
            'production.sewing.efficiency',
            'production.finishing.view',
            'production.finishing.create',
            'production.finishing.edit',
            'production.finishing.processes',
            'production.washing.view',
            'production.washing.create',
            'production.washing.edit',
            'production.washing.recipes',
            'production.quality.view',
            'production.quality.create',
            'production.quality.edit',
            'production.quality.aql-charts',
            'production.quality.reports',
            'production.exports'
        ]);

        // Cutting Operator role
        $cuttingOperator = Role::firstOrCreate(['name' => 'cutting-operator', 'guard_name' => 'web']);
        $cuttingOperator->givePermissionTo([
            'production.dashboard.view',
            'production.cutting.view',
            'production.cutting.create',
            'production.cutting.edit',
            'production.cutting.marker-planning',
            'production.cutting.fabric-issues'
        ]);

        // Sewing Supervisor role
        $sewingSupervisor = Role::firstOrCreate(['name' => 'sewing-supervisor', 'guard_name' => 'web']);
        $sewingSupervisor->givePermissionTo([
            'production.dashboard.view',
            'production.sewing.view',
            'production.sewing.create',
            'production.sewing.edit',
            'production.sewing.line-tracking',
            'production.sewing.efficiency'
        ]);

        // Finishing QC role
        $finishingQc = Role::firstOrCreate(['name' => 'finishing-qc', 'guard_name' => 'web']);
        $finishingQc->givePermissionTo([
            'production.dashboard.view',
            'production.finishing.view',
            'production.finishing.create',
            'production.finishing.edit',
            'production.quality.view',
            'production.quality.create',
            'production.quality.edit'
        ]);

        // Washing Manager role
        $washingManager = Role::firstOrCreate(['name' => 'washing-manager', 'guard_name' => 'web']);
        $washingManager->givePermissionTo([
            'production.dashboard.view',
            'production.washing.view',
            'production.washing.create',
            'production.washing.edit',
            'production.washing.recipes',
            'production.quality.view',
            'production.quality.create',
            'production.quality.edit'
        ]);
    }
}
