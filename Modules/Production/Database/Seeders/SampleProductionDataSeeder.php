<?php

namespace Modules\Production\Database\Seeders;

use Illuminate\Database\Seeder;
use Modules\Production\App\Models\ProductionPlan;
use Modules\Production\App\Models\CuttingPlan;
use Modules\Production\App\Models\SewingLine;

class SampleProductionDataSeeder extends Seeder
{
    public function run(): void
    {
        // Create sample production plans
        $productionPlans = [
            [
                'plan_number' => 'PP' . date('Ymd') . '0001',
                'style_number' => 'DN001',
                'po_number' => 'PO2024001',
                'target_quantity' => 1000,
                'start_date' => now()->addDays(1),
                'end_date' => now()->addDays(15),
                'line_capacity' => 300,
                'efficiency_percentage' => 85.00,
                'fabric_requirements' => [
                    ['fabric_type' => 'Denim 12oz', 'quantity' => 2500, 'unit' => 'yards'],
                    ['fabric_type' => 'Pocket Lining', 'quantity' => 500, 'unit' => 'yards']
                ],
                'accessories_requirements' => [
                    ['accessory_type' => 'Rivets', 'quantity' => 6000, 'unit' => 'pieces'],
                    ['accessory_type' => 'Buttons', 'quantity' => 1000, 'unit' => 'pieces'],
                    ['accessory_type' => 'Zippers', 'quantity' => 1000, 'unit' => 'pieces']
                ],
                'fabric_wastage_percentage' => 5.00,
                'status' => 'approved'
            ],
            [
                'plan_number' => 'PP' . date('Ymd') . '0002',
                'style_number' => 'SH001',
                'po_number' => 'PO2024002',
                'target_quantity' => 800,
                'start_date' => now()->addDays(3),
                'end_date' => now()->addDays(18),
                'line_capacity' => 250,
                'efficiency_percentage' => 82.00,
                'fabric_requirements' => [
                    ['fabric_type' => 'Cotton Twill', 'quantity' => 1800, 'unit' => 'yards'],
                    ['fabric_type' => 'Interfacing', 'quantity' => 200, 'unit' => 'yards']
                ],
                'accessories_requirements' => [
                    ['accessory_type' => 'Buttons', 'quantity' => 6400, 'unit' => 'pieces'],
                    ['accessory_type' => 'Labels', 'quantity' => 800, 'unit' => 'pieces']
                ],
                'fabric_wastage_percentage' => 4.50,
                'status' => 'in_progress'
            ]
        ];

        foreach ($productionPlans as $planData) {
            $plan = ProductionPlan::create($planData);
            
            // Create cutting plan for each production plan
            CuttingPlan::create([
                'production_plan_id' => $plan->id,
                'cutting_number' => 'CT' . date('Ymd') . str_pad($plan->id, 4, '0', STR_PAD_LEFT),
                'style_number' => $plan->style_number,
                'po_number' => $plan->po_number,
                'color' => 'Indigo Blue',
                'fabric_width' => 58.00,
                'shrinkage_percentage' => 3.00,
                'ply_count' => 50,
                'marker_length' => 4.5,
                'marker_efficiency' => 85.00,
                'size_breakdown' => [
                    ['size' => 'S', 'quantity' => 150],
                    ['size' => 'M', 'quantity' => 300],
                    ['size' => 'L', 'quantity' => 350],
                    ['size' => 'XL', 'quantity' => 200]
                ],
                'total_fabric_required' => 2500.00,
                'fabric_wastage' => 125.00,
                'status' => 'planned',
                'cutting_date' => now()->addDays(2)
            ]);
        }
    }
}
