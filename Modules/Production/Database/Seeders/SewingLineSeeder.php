<?php

namespace Modules\Production\Database\Seeders;

use Illuminate\Database\Seeder;
use Modules\Production\App\Models\SewingLine;

class SewingLineSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $sewingLines = [
            [
                'line_number' => 'L001',
                'line_name' => 'Denim Line 1',
                'total_operators' => 25,
                'helpers' => 3,
                'daily_capacity' => 300,
                'target_efficiency' => 85.00,
                'supervisor_name' => '<PERSON>',
                'line_chief_name' => '<PERSON>',
                'machine_types' => [
                    'Single Needle' => 15,
                    'Overlock' => 8,
                    'Flat Lock' => 2
                ],
                'status' => 'active'
            ],
            [
                'line_number' => 'L002',
                'line_name' => 'Denim Line 2',
                'total_operators' => 30,
                'helpers' => 4,
                'daily_capacity' => 350,
                'target_efficiency' => 88.00,
                'supervisor_name' => '<PERSON>',
                'line_chief_name' => '<PERSON>',
                'machine_types' => [
                    'Single Needle' => 18,
                    'Overlock' => 10,
                    'Flat Lock' => 2
                ],
                'status' => 'active'
            ],
            [
                'line_number' => 'L003',
                'line_name' => 'Shirt Line 1',
                'total_operators' => 20,
                'helpers' => 2,
                'daily_capacity' => 250,
                'target_efficiency' => 82.00,
                'supervisor_name' => 'Michael Brown',
                'line_chief_name' => 'Lisa Anderson',
                'machine_types' => [
                    'Single Needle' => 12,
                    'Overlock' => 6,
                    'Button Hole' => 2
                ],
                'status' => 'active'
            ],
            [
                'line_number' => 'L004',
                'line_name' => 'Shirt Line 2',
                'total_operators' => 22,
                'helpers' => 3,
                'daily_capacity' => 280,
                'target_efficiency' => 84.00,
                'supervisor_name' => 'Robert Taylor',
                'line_chief_name' => 'Jennifer White',
                'machine_types' => [
                    'Single Needle' => 14,
                    'Overlock' => 6,
                    'Button Hole' => 2
                ],
                'status' => 'active'
            ],
            [
                'line_number' => 'L005',
                'line_name' => 'Sample Line',
                'total_operators' => 8,
                'helpers' => 1,
                'daily_capacity' => 50,
                'target_efficiency' => 75.00,
                'supervisor_name' => 'Emily Garcia',
                'line_chief_name' => null,
                'machine_types' => [
                    'Single Needle' => 5,
                    'Overlock' => 3
                ],
                'status' => 'active'
            ]
        ];

        foreach ($sewingLines as $lineData) {
            SewingLine::create($lineData);
        }
    }
}
