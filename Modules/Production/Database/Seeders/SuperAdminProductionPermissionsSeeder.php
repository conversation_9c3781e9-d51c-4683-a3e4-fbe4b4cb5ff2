<?php

namespace Modules\Production\Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class SuperAdminProductionPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get or create super admin role
        $superAdmin = Role::firstOrCreate([
            'name' => 'super-admin',
            'guard_name' => 'web'
        ]);

        // Alternative names for super admin role (check common variations)
        $possibleSuperAdminRoles = [
            'super-admin',
            'superadmin', 
            'super_admin',
            'admin',
            'administrator'
        ];

        $superAdminRole = null;
        foreach ($possibleSuperAdminRoles as $roleName) {
            $role = Role::where('name', $roleName)->first();
            if ($role) {
                $superAdminRole = $role;
                break;
            }
        }

        // If no existing super admin role found, create one
        if (!$superAdminRole) {
            $superAdminRole = Role::create([
                'name' => 'super-admin',
                'guard_name' => 'web'
            ]);
            
            $this->command->info('Created new super-admin role');
        } else {
            $this->command->info("Found existing super admin role: {$superAdminRole->name}");
        }

        // Get all production permissions
        $productionPermissions = Permission::where('name', 'like', 'production.%')->get();

        if ($productionPermissions->isEmpty()) {
            $this->command->warn('No production permissions found. Please run ProductionPermissionsSeeder first.');
            return;
        }

        // Assign all production permissions to super admin
        $superAdminRole->givePermissionTo($productionPermissions);

        $this->command->info("Assigned {$productionPermissions->count()} production permissions to {$superAdminRole->name} role");

        // Display assigned permissions
        $this->command->info('Production permissions assigned:');
        foreach ($productionPermissions as $permission) {
            $this->command->line("  - {$permission->name}");
        }

        // Also ensure super admin has all general permissions if they exist
        $allPermissions = Permission::all();
        $superAdminRole->syncPermissions($allPermissions);
        
        $this->command->info("Super admin now has access to all {$allPermissions->count()} permissions in the system");
    }
}
