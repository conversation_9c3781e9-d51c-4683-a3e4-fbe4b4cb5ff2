<?php

namespace Modules\Production\Database\Seeders;

use Illuminate\Database\Seeder;
use Modules\Production\App\Models\WashingRecipe;

class WashingRecipeSeeder extends Seeder
{
    public function run(): void
    {
        $recipes = [
            [
                'recipe_name' => 'Standard Enzyme Wash',
                'recipe_code' => 'ENZ001',
                'wash_type' => 'enzyme_wash',
                'description' => 'Standard enzyme wash for denim pants',
                'chemicals_required' => [
                    ['chemical' => 'Cellulase Enzyme', 'quantity' => 2.5, 'unit' => 'g/l'],
                    ['chemical' => 'Sodium Carbonate', 'quantity' => 1.0, 'unit' => 'g/l'],
                    ['chemical' => 'Wetting Agent', 'quantity' => 0.5, 'unit' => 'g/l']
                ],
                'water_temperature' => 55.0,
                'wash_time_minutes' => 45,
                'rinse_cycles' => 3,
                'expected_shrinkage' => 3.5,
                'process_steps' => [
                    'Fill machine with water at 55°C',
                    'Add chemicals in sequence',
                    'Load garments',
                    'Run wash cycle for 45 minutes',
                    'Drain and rinse 3 times',
                    'Final spin and unload'
                ],
                'garment_type' => 'denim_pants',
                'status' => 'active'
            ],
            [
                'recipe_name' => 'Light Stone Wash',
                'recipe_code' => 'STN001',
                'wash_type' => 'stone_wash',
                'description' => 'Light stone wash for vintage look',
                'chemicals_required' => [
                    ['chemical' => 'Pumice Stone', 'quantity' => 1.5, 'unit' => 'kg/kg'],
                    ['chemical' => 'Anti-back Staining', 'quantity' => 1.0, 'unit' => 'g/l'],
                    ['chemical' => 'Softener', 'quantity' => 2.0, 'unit' => 'g/l']
                ],
                'water_temperature' => 40.0,
                'wash_time_minutes' => 60,
                'rinse_cycles' => 4,
                'expected_shrinkage' => 4.0,
                'process_steps' => [
                    'Load pumice stones',
                    'Fill with water at 40°C',
                    'Add anti-back staining agent',
                    'Load garments',
                    'Run wash cycle for 60 minutes',
                    'Remove stones',
                    'Add softener and rinse'
                ],
                'garment_type' => 'denim_pants',
                'status' => 'active'
            ],
            [
                'recipe_name' => 'Acid Wash Heavy',
                'recipe_code' => 'ACD001',
                'wash_type' => 'acid_wash',
                'description' => 'Heavy acid wash for distressed look',
                'chemicals_required' => [
                    ['chemical' => 'Potassium Permanganate', 'quantity' => 3.0, 'unit' => 'g/l'],
                    ['chemical' => 'Sodium Bisulfite', 'quantity' => 2.0, 'unit' => 'g/l'],
                    ['chemical' => 'Neutralizing Agent', 'quantity' => 1.5, 'unit' => 'g/l']
                ],
                'water_temperature' => 60.0,
                'wash_time_minutes' => 30,
                'rinse_cycles' => 5,
                'expected_shrinkage' => 5.0,
                'process_steps' => [
                    'Pre-wet garments',
                    'Apply permanganate solution',
                    'Wash at 60°C for 30 minutes',
                    'Neutralize with bisulfite',
                    'Rinse thoroughly 5 times',
                    'Final neutralization check'
                ],
                'garment_type' => 'denim_jacket',
                'status' => 'active'
            ]
        ];

        foreach ($recipes as $recipeData) {
            WashingRecipe::create($recipeData);
        }
    }
}
