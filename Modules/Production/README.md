# Garments Production Module

A comprehensive Laravel 10+ module designed specifically for woven garments factories that produce denim pants and shirts. This module provides complete production tracking from planning to quality control.

## 🚀 Features

### 📊 Production Dashboard
- Real-time production monitoring and analytics
- Daily production summary (cut, sew, wash, finish quantities)
- Interactive charts with Chart.js integration
- Line efficiency monitoring
- Production bottleneck identification
- WIP (Work in Progress) levels tracking

### 📋 Production Planning
- Create and manage production plans linked to orders, styles, and POs
- Calculate line capacity vs order target with efficiency metrics
- Generate fabric & accessories requirement summaries with wastage calculations
- Track production timeline and milestones
- Approval workflow for production plans

### ✂️ Cutting Section (Woven Fabric Specific)
- Marker planning with fabric width (45", 58", 60"), shrinkage percentage, ply count
- Cutting entry tracking by size, color, PO number, bundle number
- Fabric issue/return tracking with wastage calculations
- Cutting efficiency and summary reports
- Bundle generation with QR codes

### 🧵 Sewing Section
- Bundle ticket generation with barcode/QR code support
- Line input/output tracking with hourly production logs
- Operator-wise efficiency calculations based on SMV (Standard Minute Value)
- WIP reports by line, style, and date
- Line balancing and bottleneck analysis

### ✨ Finishing Section
- Input/output logging for finishing operations
- Process tracking: buttoning, trimming, folding, poly bagging, labeling
- Final measurement verification and QC pass/fail status
- Finishing efficiency and defect tracking

### 🌊 Washing Section (Denim Specific)
- Washing types: enzyme wash, acid wash, stone wash, softener treatment
- Pre-wash and post-wash measurement tracking
- Wash loss/damage logging with cost implications
- Chemical consumption tracking
- Washing recipe management

### 🔍 Quality Control
- Multi-stage inspections: inline, end-line, pre-final, final
- AQL (Acceptable Quality Level) chart inspection logic with statistical sampling
- Defect categorization: sewing defects, washing defects, finishing defects
- Quality reports and trend analysis
- Rejection tracking and rework management

## 📦 Installation

### Prerequisites
- Laravel 10+
- PHP 8.1+
- MySQL 8.0+
- Spatie Laravel Permission package
- nwidart/laravel-modules package

### Step 1: Install Required Packages

```bash
# Install Laravel Modules package
composer require nwidart/laravel-modules

# Install Spatie Permission package
composer require spatie/laravel-permission

# Install export packages
composer require barryvdh/laravel-dompdf
composer require maatwebsite/laravel-excel
```

### Step 2: Publish Module Configuration

```bash
php artisan vendor:publish --provider="Nwidart\Modules\LaravelModulesServiceProvider"
php artisan vendor:publish --provider="Spatie\Permission\PermissionServiceProvider"
```

### Step 3: Run Migrations

```bash
# Run Spatie Permission migrations
php artisan migrate

# Run Production module migrations
php artisan module:migrate Production
```

### Step 4: Seed Database

```bash
# Seed production module data
php artisan module:seed Production
```

### Step 5: Assign Super Admin Permissions

```bash
# Assign all production permissions to super admin role
php artisan production:assign-super-admin

# Or assign to a specific user by email
php artisan production:assign-super-admin --email=<EMAIL>

# Or assign to a specific user by ID
php artisan production:assign-super-admin --user-id=1
```

### Step 6: Enable Module

```bash
php artisan module:enable Production
```

## 🔧 Configuration

### Environment Variables

Add these to your `.env` file:

```env
# PDF Export Configuration
DOMPDF_ENABLE_REMOTE=true
DOMPDF_ENABLE_CSS_FLOAT=true

# Excel Export Configuration
EXCEL_CACHE_DRIVER=file
```

### Module Configuration

The module configuration is located in `config/modules.php`. Ensure the following settings:

```php
'namespace' => 'Modules',
'stubs' => [
    'enabled' => false,
],
'paths' => [
    'modules' => base_path('Modules'),
],
```

## 🎯 Usage

### Accessing the Module

After installation, the Production module will be available in the sidebar navigation under "Garments Production" with the following sections:

1. **Dashboard** - Overview of production metrics
2. **Production Planning** - Create and manage production plans
3. **Cutting Section** - Manage cutting operations
4. **Sewing Section** - Track sewing line operations
5. **Finishing Section** - Monitor finishing processes
6. **Washing (Denim)** - Manage washing operations
7. **Quality Control** - Quality inspections and reports

### User Roles and Permissions

The module includes predefined roles:

- **super-admin** - Complete access to all production features and system administration
- **admin** - Full access to all production features
- **production-manager** - Comprehensive production management access
- **cutting-operator** - Cutting section access
- **sewing-supervisor** - Sewing section management
- **finishing-qc** - Finishing and quality control access
- **washing-manager** - Washing operations management

#### Super Admin Setup

To assign super admin permissions to a user:

```bash
# Interactive assignment
php artisan production:assign-super-admin

# Assign to specific user by email
php artisan production:assign-super-admin --email=<EMAIL>

# Assign to specific user by ID
php artisan production:assign-super-admin --user-id=1

# Use custom role name
php artisan production:assign-super-admin --role-name=administrator
```

#### Quick Super Admin Assignment

You can also use the standalone PHP script:

```bash
php assign-super-admin-production.php
```

### Creating a Production Plan

1. Navigate to Production Planning
2. Click "New Production Plan"
3. Fill in order details, target quantities, dates
4. Add fabric and accessories requirements
5. Set efficiency targets and wastage percentages
6. Save and approve the plan

### Workflow Overview

1. **Planning** → Create production plan with requirements
2. **Cutting** → Plan markers, cut fabric, issue materials
3. **Sewing** → Generate bundle tickets, track line production
4. **Finishing** → Process garments through finishing operations
5. **Washing** → Apply washing treatments (for denim)
6. **Quality** → Inspect at multiple stages using AQL standards

## 📊 Reports and Analytics

### Available Reports

- Production Summary (PDF/Excel)
- Cutting Efficiency Report
- Sewing Line Performance
- Quality Analysis Report
- Defect Trend Analysis
- WIP Status Report

### Export Options

All major reports support:
- PDF export for printing and sharing
- Excel export for data analysis
- Real-time dashboard updates

## 🔒 Security

### Permission-Based Access Control

The module uses Spatie Laravel Permission for granular access control:

```php
// Check permissions in controllers
$this->authorize('production.planning.create');

// Check permissions in Blade templates
@can('production.cutting.view')
    <!-- Content -->
@endcan
```

### Route Protection

All routes are protected with appropriate middleware:

```php
Route::group([
    'middleware' => ['web', 'auth', 'can:production.planning.view']
], function () {
    // Protected routes
});
```

## 🧪 Testing

### Running Tests

```bash
# Run all module tests
php artisan test Modules/Production/Tests

# Run specific test
php artisan test Modules/Production/Tests/Feature/ProductionPlanTest.php
```

### Test Coverage

The module includes tests for:
- Production plan CRUD operations
- Cutting plan calculations
- Sewing efficiency calculations
- Quality inspection logic
- Permission-based access control

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## 📝 License

This module is open-sourced software licensed under the [MIT license](LICENSE).

## 🆘 Support

For support and questions:

1. Check the documentation
2. Review existing issues
3. Create a new issue with detailed information
4. Include steps to reproduce any bugs

## 🔄 Version History

### v1.0.0
- Initial release
- Complete production workflow
- Dashboard and analytics
- Quality control with AQL charts
- Export functionality
- Permission-based access control

---

**Built with ❤️ for the garments industry**
