<?php

namespace Modules\Production\Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Modules\Production\App\Models\ProductionPlan;
use App\Models\User;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class ProductionPlanTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $productionManager;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create permissions
        Permission::create(['name' => 'production.planning.view']);
        Permission::create(['name' => 'production.planning.create']);
        Permission::create(['name' => 'production.planning.edit']);
        Permission::create(['name' => 'production.planning.delete']);
        
        // Create role
        $this->productionManager = Role::create(['name' => 'production-manager']);
        $this->productionManager->givePermissionTo([
            'production.planning.view',
            'production.planning.create',
            'production.planning.edit',
            'production.planning.delete'
        ]);
        
        // Create user
        $this->user = User::factory()->create();
        $this->user->assignRole('production-manager');
    }

    /** @test */
    public function authenticated_user_can_view_production_plans_index()
    {
        $this->actingAs($this->user)
            ->get(route('production.planning.index'))
            ->assertStatus(200)
            ->assertViewIs('production::planning.index');
    }

    /** @test */
    public function authenticated_user_can_create_production_plan()
    {
        $planData = [
            'style_number' => 'DN001',
            'po_number' => 'PO2024001',
            'target_quantity' => 1000,
            'start_date' => now()->addDays(1)->format('Y-m-d'),
            'end_date' => now()->addDays(15)->format('Y-m-d'),
            'line_capacity' => 300,
            'efficiency_percentage' => 85.00,
            'fabric_wastage_percentage' => 5.00,
            'status' => 'draft'
        ];

        $this->actingAs($this->user)
            ->post(route('production.planning.store'), $planData)
            ->assertRedirect();

        $this->assertDatabaseHas('production_plans', [
            'style_number' => 'DN001',
            'po_number' => 'PO2024001',
            'target_quantity' => 1000
        ]);
    }

    /** @test */
    public function production_plan_generates_unique_plan_number()
    {
        $plan = ProductionPlan::create([
            'style_number' => 'DN001',
            'po_number' => 'PO2024001',
            'target_quantity' => 1000,
            'start_date' => now()->addDays(1),
            'end_date' => now()->addDays(15),
            'line_capacity' => 300,
            'efficiency_percentage' => 85.00,
            'fabric_wastage_percentage' => 5.00,
            'status' => 'draft'
        ]);

        $this->assertNotNull($plan->plan_number);
        $this->assertStringStartsWith('PP', $plan->plan_number);
    }

    /** @test */
    public function production_plan_calculates_daily_target_correctly()
    {
        $plan = ProductionPlan::create([
            'style_number' => 'DN001',
            'po_number' => 'PO2024001',
            'target_quantity' => 1000,
            'start_date' => now(),
            'end_date' => now()->addDays(9), // 10 days total
            'line_capacity' => 300,
            'efficiency_percentage' => 85.00,
            'fabric_wastage_percentage' => 5.00,
            'status' => 'draft'
        ]);

        $this->assertEquals(100, $plan->daily_target); // 1000 / 10 days
    }

    /** @test */
    public function production_plan_calculates_total_fabric_requirement_with_wastage()
    {
        $plan = ProductionPlan::create([
            'style_number' => 'DN001',
            'po_number' => 'PO2024001',
            'target_quantity' => 1000,
            'start_date' => now(),
            'end_date' => now()->addDays(14),
            'line_capacity' => 300,
            'efficiency_percentage' => 85.00,
            'fabric_requirements' => [
                ['fabric_type' => 'Denim', 'quantity' => 2000, 'unit' => 'yards']
            ],
            'fabric_wastage_percentage' => 5.00,
            'status' => 'draft'
        ]);

        $this->assertEquals(2100, $plan->total_fabric_requirement); // 2000 + 5% wastage
    }

    /** @test */
    public function unauthorized_user_cannot_access_production_plans()
    {
        $unauthorizedUser = User::factory()->create();

        $this->actingAs($unauthorizedUser)
            ->get(route('production.planning.index'))
            ->assertStatus(403);
    }

    /** @test */
    public function production_plan_validation_requires_mandatory_fields()
    {
        $this->actingAs($this->user)
            ->post(route('production.planning.store'), [])
            ->assertSessionHasErrors([
                'style_number',
                'po_number',
                'target_quantity',
                'start_date',
                'end_date',
                'line_capacity',
                'efficiency_percentage',
                'fabric_wastage_percentage',
                'status'
            ]);
    }

    /** @test */
    public function production_plan_end_date_must_be_after_start_date()
    {
        $planData = [
            'style_number' => 'DN001',
            'po_number' => 'PO2024001',
            'target_quantity' => 1000,
            'start_date' => now()->addDays(10)->format('Y-m-d'),
            'end_date' => now()->addDays(5)->format('Y-m-d'), // Before start date
            'line_capacity' => 300,
            'efficiency_percentage' => 85.00,
            'fabric_wastage_percentage' => 5.00,
            'status' => 'draft'
        ];

        $this->actingAs($this->user)
            ->post(route('production.planning.store'), $planData)
            ->assertSessionHasErrors(['end_date']);
    }
}
