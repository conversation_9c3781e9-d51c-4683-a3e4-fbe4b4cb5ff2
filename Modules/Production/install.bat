@echo off
echo 🏭 Installing Garments Production Module...
echo ==========================================

REM Check if <PERSON><PERSON> is installed
if not exist "artisan" (
    echo ❌ Error: <PERSON><PERSON> not found. Please run this script from your Laravel root directory.
    pause
    exit /b 1
)

echo 📦 Installing required packages...

REM Install Laravel Modules
echo Installing Laravel Modules package...
composer require nwidart/laravel-modules

REM Install Spatie Permission
echo Installing Spatie Permission package...
composer require spatie/laravel-permission

REM Install export packages
echo Installing PDF export package...
composer require barryvdh/laravel-dompdf

echo Installing Excel export package...
composer require maatwebsite/laravel-excel

echo 📋 Publishing configurations...

REM Publish module configuration
php artisan vendor:publish --provider="Nwidart\Modules\LaravelModulesServiceProvider" --force

REM Publish permission configuration
php artisan vendor:publish --provider="Spatie\Permission\PermissionServiceProvider" --force

echo 🗄️ Running migrations...

REM Run core Laravel migrations first
php artisan migrate

REM Run module migrations
php artisan module:migrate Production

echo 🌱 Seeding database...

REM Seed the production module
php artisan module:seed Production

echo 🔐 Assigning super admin permissions...

REM Assign super admin permissions
php artisan production:assign-super-admin

echo 🔧 Enabling module...

REM Enable the production module
php artisan module:enable Production

echo 🧹 Clearing caches...

REM Clear all caches
php artisan config:clear
php artisan cache:clear
php artisan route:clear
php artisan view:clear

echo ✅ Installation completed successfully!
echo.
echo 🎯 Next Steps:
echo 1. Add the following to your .env file:
echo    DOMPDF_ENABLE_REMOTE=true
echo    DOMPDF_ENABLE_CSS_FLOAT=true
echo    EXCEL_CACHE_DRIVER=file
echo.
echo 2. Assign production roles to users:
echo    php artisan tinker
echo    $user = User::find(1);
echo    $user->assignRole('production-manager');
echo.
echo 3. Access the module at: /production/dashboard
echo.
echo 📚 Documentation: See README.md for detailed usage instructions
echo.
echo 🎉 Happy Manufacturing!
pause
