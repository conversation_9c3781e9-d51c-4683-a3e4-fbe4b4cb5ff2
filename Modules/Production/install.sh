#!/bin/bash

# Garments Production Module Installation Script
# This script automates the installation of the Production module

echo "🏭 Installing Garments Production Module..."
echo "=========================================="

# Check if <PERSON><PERSON> is installed
if [ ! -f "artisan" ]; then
    echo "❌ Error: Lara<PERSON> not found. Please run this script from your Laravel root directory."
    exit 1
fi

echo "📦 Installing required packages..."

# Install Laravel Modules if not already installed
if ! composer show nwidart/laravel-modules > /dev/null 2>&1; then
    echo "Installing Laravel Modules package..."
    composer require nwidart/laravel-modules
fi

# Install Spatie Permission if not already installed
if ! composer show spatie/laravel-permission > /dev/null 2>&1; then
    echo "Installing Spatie Permission package..."
    composer require spatie/laravel-permission
fi

# Install export packages
if ! composer show barryvdh/laravel-dompdf > /dev/null 2>&1; then
    echo "Installing PDF export package..."
    composer require barryvdh/laravel-dompdf
fi

if ! composer show maatwebsite/laravel-excel > /dev/null 2>&1; then
    echo "Installing Excel export package..."
    composer require maatwebsite/laravel-excel
fi

echo "📋 Publishing configurations..."

# Publish module configuration
php artisan vendor:publish --provider="Nwidart\Modules\LaravelModulesServiceProvider" --force

# Publish permission configuration
php artisan vendor:publish --provider="Spatie\Permission\PermissionServiceProvider" --force

echo "🗄️ Running migrations..."

# Run core Laravel migrations first
php artisan migrate

# Run module migrations
php artisan module:migrate Production

echo "🌱 Seeding database..."

# Seed the production module
php artisan module:seed Production

echo "🔐 Assigning super admin permissions..."

# Assign super admin permissions
php artisan production:assign-super-admin

echo "🔧 Enabling module..."

# Enable the production module
php artisan module:enable Production

echo "🧹 Clearing caches..."

# Clear all caches
php artisan config:clear
php artisan cache:clear
php artisan route:clear
php artisan view:clear

echo "✅ Installation completed successfully!"
echo ""
echo "🎯 Next Steps:"
echo "1. Add the following to your .env file:"
echo "   DOMPDF_ENABLE_REMOTE=true"
echo "   DOMPDF_ENABLE_CSS_FLOAT=true"
echo "   EXCEL_CACHE_DRIVER=file"
echo ""
echo "2. Assign production roles to users:"
echo "   php artisan tinker"
echo "   \$user = User::find(1);"
echo "   \$user->assignRole('production-manager');"
echo ""
echo "3. Access the module at: /production/dashboard"
echo ""
echo "📚 Documentation: See README.md for detailed usage instructions"
echo ""
echo "🎉 Happy Manufacturing!"
