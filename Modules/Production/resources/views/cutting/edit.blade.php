@extends('layouts.master')

@section('title', 'Edit Cutting Plan')

@section('main_content')
<div class="erp-state-overview-section">
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="page-title">Edit Cutting Plan</h2>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="{{ route('production.cutting.index') }}">Cutting Section</a></li>
                                <li class="breadcrumb-item"><a href="{{ route('production.cutting.show', $cuttingPlan) }}">{{ $cuttingPlan->cutting_number }}</a></li>
                                <li class="breadcrumb-item active">Edit</li>
                            </ol>
                        </nav>
                    </div>
                    <div>
                        <a href="{{ route('production.cutting.show', $cuttingPlan) }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Details
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Edit Form -->
        <form method="POST" action="{{ route('production.cutting.update', $cuttingPlan) }}">
            @csrf
            @method('PUT')
            
            <div class="row">
                <div class="col-md-8">
                    <!-- Basic Information -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Basic Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Cutting Number</label>
                                        <input type="text" class="form-control" value="{{ $cuttingPlan->cutting_number }}" readonly>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Status</label>
                                        <select name="status" class="form-select @error('status') is-invalid @enderror">
                                            <option value="planned" {{ $cuttingPlan->status == 'planned' ? 'selected' : '' }}>Planned</option>
                                            <option value="in_progress" {{ $cuttingPlan->status == 'in_progress' ? 'selected' : '' }}>In Progress</option>
                                            <option value="completed" {{ $cuttingPlan->status == 'completed' ? 'selected' : '' }}>Completed</option>
                                            <option value="cancelled" {{ $cuttingPlan->status == 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                                        </select>
                                        @error('status')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Style Number <span class="text-danger">*</span></label>
                                        <input type="text" name="style_number" class="form-control @error('style_number') is-invalid @enderror" 
                                               value="{{ old('style_number', $cuttingPlan->style_number) }}" required>
                                        @error('style_number')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">PO Number <span class="text-danger">*</span></label>
                                        <input type="text" name="po_number" class="form-control @error('po_number') is-invalid @enderror" 
                                               value="{{ old('po_number', $cuttingPlan->po_number) }}" required>
                                        @error('po_number')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">Color <span class="text-danger">*</span></label>
                                        <input type="text" name="color" class="form-control @error('color') is-invalid @enderror" 
                                               value="{{ old('color', $cuttingPlan->color) }}" required>
                                        @error('color')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">Total Pieces <span class="text-danger">*</span></label>
                                        <input type="number" name="total_pieces" class="form-control @error('total_pieces') is-invalid @enderror" 
                                               value="{{ old('total_pieces', $cuttingPlan->total_pieces) }}" required min="1">
                                        @error('total_pieces')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">Cutting Date <span class="text-danger">*</span></label>
                                        <input type="date" name="cutting_date" class="form-control @error('cutting_date') is-invalid @enderror" 
                                               value="{{ old('cutting_date', $cuttingPlan->cutting_date->format('Y-m-d')) }}" required>
                                        @error('cutting_date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Fabric Details -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Fabric Details</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">Total Fabric Required (yards) <span class="text-danger">*</span></label>
                                        <input type="number" name="total_fabric_required" class="form-control @error('total_fabric_required') is-invalid @enderror" 
                                               value="{{ old('total_fabric_required', $cuttingPlan->total_fabric_required) }}" required min="0" step="0.01">
                                        @error('total_fabric_required')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">Fabric Width (inches) <span class="text-danger">*</span></label>
                                        <select name="fabric_width" class="form-select @error('fabric_width') is-invalid @enderror" required>
                                            <option value="">Select Width</option>
                                            <option value="45" {{ old('fabric_width', $cuttingPlan->fabric_width) == '45' ? 'selected' : '' }}>45"</option>
                                            <option value="58" {{ old('fabric_width', $cuttingPlan->fabric_width) == '58' ? 'selected' : '' }}>58"</option>
                                            <option value="60" {{ old('fabric_width', $cuttingPlan->fabric_width) == '60' ? 'selected' : '' }}>60"</option>
                                        </select>
                                        @error('fabric_width')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">Shrinkage Percentage <span class="text-danger">*</span></label>
                                        <input type="number" name="shrinkage_percentage" class="form-control @error('shrinkage_percentage') is-invalid @enderror" 
                                               value="{{ old('shrinkage_percentage', $cuttingPlan->shrinkage_percentage) }}" required min="0" max="20" step="0.1">
                                        @error('shrinkage_percentage')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Ply Count <span class="text-danger">*</span></label>
                                        <input type="number" name="ply_count" class="form-control @error('ply_count') is-invalid @enderror" 
                                               value="{{ old('ply_count', $cuttingPlan->ply_count) }}" required min="1" max="100">
                                        @error('ply_count')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Fabric Consumption per Piece</label>
                                        <input type="number" name="fabric_consumption_per_piece" class="form-control @error('fabric_consumption_per_piece') is-invalid @enderror" 
                                               value="{{ old('fabric_consumption_per_piece', $cuttingPlan->fabric_consumption_per_piece) }}" step="0.01" min="0">
                                        @error('fabric_consumption_per_piece')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Size Breakdown -->
                    <div class="card mb-4">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">Size Breakdown</h5>
                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="addSizeRow()">
                                <i class="fas fa-plus"></i> Add Size
                            </button>
                        </div>
                        <div class="card-body">
                            <div id="size-breakdown">
                                @if($cuttingPlan->size_breakdown && is_array($cuttingPlan->size_breakdown))
                                @foreach($cuttingPlan->size_breakdown as $size => $quantity)
                                <div class="size-row row mb-2">
                                    <div class="col-md-5">
                                        <input type="text" name="size_breakdown_sizes[]" class="form-control"
                                               placeholder="Size (e.g., S, M, L, XL)" value="{{ is_string($size) ? $size : '' }}">
                                    </div>
                                    <div class="col-md-5">
                                        <input type="number" name="size_breakdown_quantities[]" class="form-control"
                                               placeholder="Quantity" value="{{ is_numeric($quantity) ? $quantity : '' }}" min="1">
                                    </div>
                                    <div class="col-md-2">
                                        <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeSizeRow(this)">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                                @endforeach
                                @endif
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <!-- Actions -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Actions</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Update Cutting Plan
                                </button>
                                <a href="{{ route('production.cutting.show', $cuttingPlan) }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times"></i> Cancel
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Notes -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Notes</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label">Special Instructions</label>
                                <textarea name="notes" class="form-control @error('notes') is-invalid @enderror" rows="4" 
                                          placeholder="Enter any special instructions or notes...">{{ old('notes', $cuttingPlan->notes) }}</textarea>
                                @error('notes')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
@endsection

@push('js')
<script>
function addSizeRow() {
    const container = document.getElementById('size-breakdown');
    const row = document.createElement('div');
    row.className = 'size-row row mb-2';
    row.innerHTML = `
        <div class="col-md-5">
            <input type="text" name="size_breakdown_sizes[]" class="form-control" placeholder="Size (e.g., S, M, L, XL)">
        </div>
        <div class="col-md-5">
            <input type="number" name="size_breakdown_quantities[]" class="form-control" placeholder="Quantity" min="1">
        </div>
        <div class="col-md-2">
            <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeSizeRow(this)">
                <i class="fas fa-trash"></i>
            </button>
        </div>
    `;
    container.appendChild(row);
}

function removeSizeRow(button) {
    button.closest('.size-row').remove();
}
</script>
@endpush
