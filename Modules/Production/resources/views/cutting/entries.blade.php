@extends('layouts.master')

@section('title', 'Cutting Entries')

@section('main_content')
<div class="erp-state-overview-section">
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="page-title">Cutting Entries</h2>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="{{ route('production.cutting.index') }}">Cutting Section</a></li>
                                <li class="breadcrumb-item active">Cutting Entries</li>
                            </ol>
                        </nav>
                    </div>
                    <div>
                        <a href="{{ route('production.cutting.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Cutting
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Create New Entry Form -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Create New Cutting Entry</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="{{ route('production.cutting.entries.store') }}">
                            @csrf
                            
                            <div class="mb-3">
                                <label class="form-label">Cutting Plan <span class="text-danger">*</span></label>
                                <select name="cutting_plan_id" class="form-select @error('cutting_plan_id') is-invalid @enderror" required>
                                    <option value="">Select Cutting Plan</option>
                                    @foreach($cuttingPlans as $plan)
                                    <option value="{{ $plan->id }}" {{ old('cutting_plan_id') == $plan->id ? 'selected' : '' }}>
                                        {{ $plan->cutting_number }} - {{ $plan->style_number }}
                                    </option>
                                    @endforeach
                                </select>
                                @error('cutting_plan_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Style Number <span class="text-danger">*</span></label>
                                        <input type="text" name="style_number" class="form-control @error('style_number') is-invalid @enderror" 
                                               value="{{ old('style_number') }}" required>
                                        @error('style_number')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">PO Number <span class="text-danger">*</span></label>
                                        <input type="text" name="po_number" class="form-control @error('po_number') is-invalid @enderror" 
                                               value="{{ old('po_number') }}" required>
                                        @error('po_number')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Color <span class="text-danger">*</span></label>
                                        <input type="text" name="color" class="form-control @error('color') is-invalid @enderror" 
                                               value="{{ old('color') }}" required>
                                        @error('color')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Size <span class="text-danger">*</span></label>
                                        <input type="text" name="size" class="form-control @error('size') is-invalid @enderror" 
                                               value="{{ old('size') }}" required>
                                        @error('size')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Quantity <span class="text-danger">*</span></label>
                                        <input type="number" name="quantity" class="form-control @error('quantity') is-invalid @enderror" 
                                               value="{{ old('quantity') }}" required min="1">
                                        @error('quantity')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Fabric Consumed <span class="text-danger">*</span></label>
                                        <input type="number" name="fabric_consumed" class="form-control @error('fabric_consumed') is-invalid @enderror" 
                                               value="{{ old('fabric_consumed') }}" required min="0" step="0.01">
                                        @error('fabric_consumed')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Cutter Name <span class="text-danger">*</span></label>
                                <input type="text" name="cutter_name" class="form-control @error('cutter_name') is-invalid @enderror" 
                                       value="{{ old('cutter_name') }}" required>
                                @error('cutter_name')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Status</label>
                                <select name="status" class="form-select @error('status') is-invalid @enderror">
                                    <option value="cut" {{ old('status', 'cut') == 'cut' ? 'selected' : '' }}>Cut</option>
                                    <option value="sent_to_sewing" {{ old('status') == 'sent_to_sewing' ? 'selected' : '' }}>Sent to Sewing</option>
                                    <option value="completed" {{ old('status') == 'completed' ? 'selected' : '' }}>Completed</option>
                                </select>
                                @error('status')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Defects</label>
                                <textarea name="defects" class="form-control @error('defects') is-invalid @enderror" rows="2" 
                                          placeholder="Any defects found...">{{ old('defects') }}</textarea>
                                @error('defects')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Notes</label>
                                <textarea name="notes" class="form-control @error('notes') is-invalid @enderror" rows="2" 
                                          placeholder="Additional notes...">{{ old('notes') }}</textarea>
                                @error('notes')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-plus"></i> Create Entry
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Cutting Entries List -->
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">Cutting Entries</h5>
                        <div class="d-flex gap-2">
                            <!-- Filters -->
                            <form method="GET" action="{{ route('production.cutting.entries') }}" class="d-flex gap-2">
                                <input type="text" name="bundle_number" class="form-control form-control-sm" 
                                       placeholder="Bundle Number" value="{{ request('bundle_number') }}">
                                <input type="text" name="style_number" class="form-control form-control-sm" 
                                       placeholder="Style Number" value="{{ request('style_number') }}">
                                <select name="status" class="form-select form-select-sm">
                                    <option value="">All Status</option>
                                    <option value="cut" {{ request('status') == 'cut' ? 'selected' : '' }}>Cut</option>
                                    <option value="sent_to_sewing" {{ request('status') == 'sent_to_sewing' ? 'selected' : '' }}>Sent to Sewing</option>
                                    <option value="completed" {{ request('status') == 'completed' ? 'selected' : '' }}>Completed</option>
                                </select>
                                <button type="submit" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-search"></i>
                                </button>
                            </form>
                        </div>
                    </div>
                    <div class="card-body">
                        @if($cuttingEntries->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Bundle Number</th>
                                        <th>Style</th>
                                        <th>PO Number</th>
                                        <th>Size</th>
                                        <th>Quantity</th>
                                        <th>Fabric Used</th>
                                        <th>Cutter</th>
                                        <th>Status</th>
                                        <th>Cut Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($cuttingEntries as $entry)
                                    <tr>
                                        <td>
                                            <strong>{{ $entry->bundle_number }}</strong>
                                            @if($entry->qr_code)
                                            <br>
                                            <small class="text-muted">QR: Available</small>
                                            @endif
                                        </td>
                                        <td>{{ $entry->style_number }}</td>
                                        <td>{{ $entry->po_number }}</td>
                                        <td>{{ $entry->size }}</td>
                                        <td>{{ number_format($entry->quantity) }}</td>
                                        <td>{{ number_format($entry->fabric_consumed, 2) }} yards</td>
                                        <td>{{ $entry->cutter_name }}</td>
                                        <td>
                                            <span class="badge bg-{{ 
                                                $entry->status == 'cut' ? 'primary' : 
                                                ($entry->status == 'sent_to_sewing' ? 'warning' : 'success') 
                                            }}">
                                                {{ ucfirst(str_replace('_', ' ', $entry->status)) }}
                                            </span>
                                        </td>
                                        <td>{{ $entry->cutting_datetime->format('M d, Y H:i') }}</td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button type="button" class="btn btn-outline-primary" title="View Details">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                @if($entry->status == 'cut')
                                                <button type="button" class="btn btn-outline-success" title="Send to Sewing" 
                                                        onclick="sendToSewing({{ $entry->id }})">
                                                    <i class="fas fa-arrow-right"></i>
                                                </button>
                                                @endif
                                                @if($entry->qr_code)
                                                <button type="button" class="btn btn-outline-info" title="Show QR Code" 
                                                        onclick="showQRCode('{{ $entry->qr_code }}')">
                                                    <i class="fas fa-qrcode"></i>
                                                </button>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="d-flex justify-content-center">
                            {{ $cuttingEntries->links() }}
                        </div>
                        @else
                        <div class="text-center py-5">
                            <i class="fas fa-cut fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No Cutting Entries Found</h5>
                            <p class="text-muted">Start by creating cutting entries from cutting plans.</p>
                        </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('css')
<style>
.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.badge {
    font-size: 0.75em;
}
</style>
@endpush

@push('js')
<script>
function sendToSewing(entryId) {
    if (confirm('Are you sure you want to send this bundle to sewing?')) {
        // This would update the status to 'sent_to_sewing'
        alert('Send to sewing functionality would be implemented here for entry ID: ' + entryId);
    }
}

function showQRCode(qrCode) {
    // This would show a modal with the QR code
    alert('QR Code: ' + qrCode);
}

// Auto-populate fields when cutting plan is selected
$('select[name="cutting_plan_id"]').on('change', function() {
    const planId = $(this).val();
    if (planId) {
        // You could make an AJAX call here to get plan details
        // and auto-populate style_number, po_number, color fields
    }
});
</script>
@endpush
