@extends('layouts.master')

@section('title', 'Fabric Issue Details')

@section('main_content')
<div class="erp-state-overview-section">
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="page-title">Fabric Issue Details</h2>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="{{ route('production.cutting.index') }}">Cutting Section</a></li>
                                <li class="breadcrumb-item"><a href="{{ route('production.cutting.fabric-issues') }}">Fabric Issues</a></li>
                                <li class="breadcrumb-item active">{{ $fabricIssue->issue_number }}</li>
                            </ol>
                        </nav>
                    </div>
                    <div class="d-flex gap-2">
                        <a href="{{ route('production.cutting.fabric-issues') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> Back to List
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Issue Details -->
            <div class="col-md-8">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Issue Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>Issue Number:</strong></td>
                                        <td>{{ $fabricIssue->issue_number }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Cutting Plan:</strong></td>
                                        <td>
                                            <a href="{{ route('production.cutting.show', $fabricIssue->cuttingPlan) }}" class="text-decoration-none">
                                                {{ $fabricIssue->cuttingPlan->cutting_number }}
                                            </a>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Style Number:</strong></td>
                                        <td>{{ $fabricIssue->cuttingPlan->style_number }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>PO Number:</strong></td>
                                        <td>{{ $fabricIssue->cuttingPlan->po_number }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Fabric Type:</strong></td>
                                        <td>{{ $fabricIssue->fabric_type }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Color:</strong></td>
                                        <td>{{ $fabricIssue->color }}</td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>Issued To:</strong></td>
                                        <td>{{ $fabricIssue->issued_to }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Issue Date:</strong></td>
                                        <td>{{ $fabricIssue->issue_datetime ? $fabricIssue->issue_datetime->format('M d, Y H:i') : 'Not set' }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Unit:</strong></td>
                                        <td>{{ $fabricIssue->unit ?? 'yards' }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Status:</strong></td>
                                        <td>
                                            <span class="badge bg-{{ 
                                                $fabricIssue->status == 'issued' ? 'primary' : 
                                                ($fabricIssue->status == 'partially_returned' ? 'warning' : 
                                                ($fabricIssue->status == 'fully_returned' ? 'success' : 'info')) 
                                            }} fs-6">
                                                {{ ucfirst(str_replace('_', ' ', $fabricIssue->status)) }}
                                            </span>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quantity Information -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Quantity Details</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4 class="text-primary">{{ number_format($fabricIssue->issued_quantity, 2) }}</h4>
                                    <small class="text-muted">Issued Quantity</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4 class="text-success">{{ number_format($fabricIssue->returned_quantity, 2) }}</h4>
                                    <small class="text-muted">Returned Quantity</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4 class="text-warning">{{ number_format($fabricIssue->wastage_quantity, 2) }}</h4>
                                    <small class="text-muted">Wastage Quantity</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4 class="text-info">{{ number_format($fabricIssue->issued_quantity - $fabricIssue->returned_quantity - $fabricIssue->wastage_quantity, 2) }}</h4>
                                    <small class="text-muted">Outstanding</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mt-4">
                            <div class="col-md-6">
                                <div class="progress mb-2" style="height: 25px;">
                                    @php
                                        $returnPercentage = $fabricIssue->issued_quantity > 0 ? ($fabricIssue->returned_quantity / $fabricIssue->issued_quantity) * 100 : 0;
                                    @endphp
                                    <div class="progress-bar bg-success" role="progressbar" style="width: {{ $returnPercentage }}%">
                                        {{ number_format($returnPercentage, 1) }}% Returned
                                    </div>
                                </div>
                                <small class="text-muted">Return Progress</small>
                            </div>
                            <div class="col-md-6">
                                <div class="progress mb-2" style="height: 25px;">
                                    @php
                                        $wastagePercentage = $fabricIssue->issued_quantity > 0 ? ($fabricIssue->wastage_quantity / $fabricIssue->issued_quantity) * 100 : 0;
                                    @endphp
                                    <div class="progress-bar bg-warning" role="progressbar" style="width: {{ $wastagePercentage }}%">
                                        {{ number_format($wastagePercentage, 1) }}% Wastage
                                    </div>
                                </div>
                                <small class="text-muted">Wastage Rate</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Notes -->
                @if($fabricIssue->notes || $fabricIssue->return_notes)
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Notes</h5>
                    </div>
                    <div class="card-body">
                        @if($fabricIssue->notes)
                        <div class="mb-3">
                            <label class="form-label"><strong>Issue Notes:</strong></label>
                            <div class="alert alert-info">
                                {{ $fabricIssue->notes }}
                            </div>
                        </div>
                        @endif
                        
                        @if($fabricIssue->return_notes)
                        <div class="mb-3">
                            <label class="form-label"><strong>Return Notes:</strong></label>
                            <div class="alert alert-success">
                                {{ $fabricIssue->return_notes }}
                            </div>
                        </div>
                        @endif
                    </div>
                </div>
                @endif
            </div>

            <!-- Sidebar Actions -->
            <div class="col-md-4">
                <!-- Return Form -->
                @if($fabricIssue->status == 'issued' || $fabricIssue->status == 'partially_returned')
                <div class="card mb-4" id="return-form">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Record Return</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="{{ route('production.cutting.fabric-issues.return', $fabricIssue) }}">
                            @csrf
                            
                            <div class="mb-3">
                                <label class="form-label">Returned Quantity <span class="text-danger">*</span></label>
                                <input type="number" name="returned_quantity" class="form-control @error('returned_quantity') is-invalid @enderror" 
                                       value="{{ old('returned_quantity') }}" required min="0" max="{{ $fabricIssue->issued_quantity - $fabricIssue->returned_quantity }}" step="0.01">
                                <small class="form-text text-muted">
                                    Max: {{ number_format($fabricIssue->issued_quantity - $fabricIssue->returned_quantity, 2) }} {{ $fabricIssue->unit ?? 'yards' }}
                                </small>
                                @error('returned_quantity')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Wastage Quantity</label>
                                <input type="number" name="wastage_quantity" class="form-control @error('wastage_quantity') is-invalid @enderror" 
                                       value="{{ old('wastage_quantity') }}" min="0" step="0.01">
                                @error('wastage_quantity')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Return Notes</label>
                                <textarea name="return_notes" class="form-control @error('return_notes') is-invalid @enderror" rows="3" 
                                          placeholder="Enter return notes...">{{ old('return_notes') }}</textarea>
                                @error('return_notes')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-undo"></i> Record Return
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                @endif

                <!-- Status Information -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Status Information</h5>
                    </div>
                    <div class="card-body text-center">
                        <div class="mb-3">
                            <span class="badge bg-{{ 
                                $fabricIssue->status == 'issued' ? 'primary' : 
                                ($fabricIssue->status == 'partially_returned' ? 'warning' : 
                                ($fabricIssue->status == 'fully_returned' ? 'success' : 'info')) 
                            }} fs-4">
                                {{ ucfirst(str_replace('_', ' ', $fabricIssue->status)) }}
                            </span>
                        </div>
                        
                        @php
                            $outstandingQty = $fabricIssue->issued_quantity - $fabricIssue->returned_quantity - $fabricIssue->wastage_quantity;
                        @endphp
                        @if($outstandingQty > 0)
                        <div class="mb-3">
                            <h6>Outstanding</h6>
                            <h3 class="text-warning">{{ number_format($outstandingQty, 2) }}</h3>
                            <small class="text-muted">{{ $fabricIssue->unit ?? 'yards' }}</small>
                        </div>
                        @endif
                    </div>
                </div>

                <!-- Actions -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Actions</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="{{ route('production.cutting.show', $fabricIssue->cuttingPlan) }}" class="btn btn-outline-primary">
                                <i class="fas fa-eye"></i> View Cutting Plan
                            </a>
                            
                            <a href="{{ route('production.cutting.fabric-issues') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-list"></i> Back to Issues
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('css')
<style>
.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.badge {
    font-size: 0.75em;
}

.table-borderless td {
    border: none;
    padding: 0.25rem 0.5rem;
}

.progress {
    background-color: #e9ecef;
}
</style>
@endpush
