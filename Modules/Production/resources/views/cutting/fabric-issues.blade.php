@extends('layouts.master')

@section('title', 'Fabric Issues')

@section('main_content')
<div class="erp-state-overview-section">
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="page-title">Fabric Issues</h2>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="{{ route('production.cutting.index') }}">Cutting Section</a></li>
                                <li class="breadcrumb-item active">Fabric Issues</li>
                            </ol>
                        </nav>
                    </div>
                    <div>
                        <a href="{{ route('production.cutting.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Cutting
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Issue Fabric Form -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Issue New Fabric</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="{{ route('production.cutting.fabric-issues.store') }}">
                            @csrf
                            
                            <div class="mb-3">
                                <label class="form-label">Cutting Plan <span class="text-danger">*</span></label>
                                <select name="cutting_plan_id" class="form-select @error('cutting_plan_id') is-invalid @enderror" required>
                                    <option value="">Select Cutting Plan</option>
                                    @foreach($cuttingPlans as $plan)
                                    <option value="{{ $plan->id }}" {{ old('cutting_plan_id', request('cutting_plan')) == $plan->id ? 'selected' : '' }}>
                                        {{ $plan->cutting_number }} - {{ $plan->style_number }}
                                    </option>
                                    @endforeach
                                </select>
                                @error('cutting_plan_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Fabric Type <span class="text-danger">*</span></label>
                                <input type="text" name="fabric_type" class="form-control @error('fabric_type') is-invalid @enderror" 
                                       value="{{ old('fabric_type') }}" required placeholder="e.g., Cotton, Polyester, Denim">
                                @error('fabric_type')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Color <span class="text-danger">*</span></label>
                                <input type="text" name="color" class="form-control @error('color') is-invalid @enderror" 
                                       value="{{ old('color') }}" required>
                                @error('color')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Issued Quantity <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="number" name="issued_quantity" class="form-control @error('issued_quantity') is-invalid @enderror" 
                                           value="{{ old('issued_quantity') }}" required min="0" step="0.01">
                                    <select name="unit" class="form-select @error('unit') is-invalid @enderror" style="max-width: 100px;">
                                        <option value="yards" {{ old('unit', 'yards') == 'yards' ? 'selected' : '' }}>Yards</option>
                                        <option value="meters" {{ old('unit') == 'meters' ? 'selected' : '' }}>Meters</option>
                                        <option value="pieces" {{ old('unit') == 'pieces' ? 'selected' : '' }}>Pieces</option>
                                    </select>
                                </div>
                                @error('issued_quantity')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                @error('unit')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Issued To <span class="text-danger">*</span></label>
                                <input type="text" name="issued_to" class="form-control @error('issued_to') is-invalid @enderror" 
                                       value="{{ old('issued_to') }}" required placeholder="Person receiving fabric">
                                @error('issued_to')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Notes</label>
                                <textarea name="notes" class="form-control @error('notes') is-invalid @enderror" rows="3" 
                                          placeholder="Any special instructions or notes...">{{ old('notes') }}</textarea>
                                @error('notes')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-plus"></i> Issue Fabric
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Fabric Issues List -->
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">Fabric Issues History</h5>
                        <div class="d-flex gap-2">
                            <!-- Filters -->
                            <form method="GET" action="{{ route('production.cutting.fabric-issues') }}" class="d-flex gap-2">
                                <input type="text" name="issue_number" class="form-control form-control-sm" 
                                       placeholder="Issue Number" value="{{ request('issue_number') }}">
                                <input type="text" name="fabric_type" class="form-control form-control-sm" 
                                       placeholder="Fabric Type" value="{{ request('fabric_type') }}">
                                <select name="status" class="form-select form-select-sm">
                                    <option value="">All Status</option>
                                    <option value="issued" {{ request('status') == 'issued' ? 'selected' : '' }}>Issued</option>
                                    <option value="partially_returned" {{ request('status') == 'partially_returned' ? 'selected' : '' }}>Partially Returned</option>
                                    <option value="fully_returned" {{ request('status') == 'fully_returned' ? 'selected' : '' }}>Fully Returned</option>
                                    <option value="consumed" {{ request('status') == 'consumed' ? 'selected' : '' }}>Consumed</option>
                                </select>
                                <button type="submit" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-search"></i>
                                </button>
                            </form>
                        </div>
                    </div>
                    <div class="card-body">
                        @if($fabricIssues->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Issue Number</th>
                                        <th>Cutting Plan</th>
                                        <th>Fabric Type</th>
                                        <th>Color</th>
                                        <th>Issued Qty</th>
                                        <th>Returned Qty</th>
                                        <th>Wastage</th>
                                        <th>Issued To</th>
                                        <th>Status</th>
                                        <th>Issue Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($fabricIssues as $issue)
                                    <tr>
                                        <td>{{ $issue->issue_number }}</td>
                                        <td>
                                            <a href="{{ route('production.cutting.show', $issue->cuttingPlan) }}" class="text-decoration-none">
                                                {{ $issue->cuttingPlan->cutting_number }}
                                            </a>
                                        </td>
                                        <td>{{ $issue->fabric_type }}</td>
                                        <td>{{ $issue->color }}</td>
                                        <td>{{ number_format($issue->issued_quantity, 2) }} {{ $issue->unit ?? 'yards' }}</td>
                                        <td>{{ number_format($issue->returned_quantity, 2) }} {{ $issue->unit ?? 'yards' }}</td>
                                        <td>{{ number_format($issue->wastage_quantity, 2) }} {{ $issue->unit ?? 'yards' }}</td>
                                        <td>{{ $issue->issued_to }}</td>
                                        <td>
                                            <span class="badge bg-{{ 
                                                $issue->status == 'issued' ? 'primary' : 
                                                ($issue->status == 'partially_returned' ? 'warning' : 
                                                ($issue->status == 'fully_returned' ? 'success' : 'info')) 
                                            }}">
                                                {{ ucfirst(str_replace('_', ' ', $issue->status)) }}
                                            </span>
                                        </td>
                                        <td>{{ $issue->issue_datetime->format('M d, Y H:i') }}</td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="{{ route('production.cutting.fabric-issues.show', $issue) }}" class="btn btn-outline-primary" title="View Details">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                @if($issue->status == 'issued' || $issue->status == 'partially_returned')
                                                <a href="{{ route('production.cutting.fabric-issues.show', $issue) }}#return-form" class="btn btn-outline-success" title="Record Return">
                                                    <i class="fas fa-undo"></i>
                                                </a>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="d-flex justify-content-center">
                            {{ $fabricIssues->links() }}
                        </div>
                        @else
                        <div class="text-center py-5">
                            <i class="fas fa-boxes fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No Fabric Issues Found</h5>
                            <p class="text-muted">Start by issuing fabric to cutting teams.</p>
                        </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('css')
<style>
.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.badge {
    font-size: 0.75em;
}

.input-group .form-select {
    border-left: 0;
}
</style>
@endpush

@push('js')
<script>
// Fabric issues functionality
document.addEventListener('DOMContentLoaded', function() {
    // Auto-populate fields when cutting plan is selected
    const cuttingPlanSelect = document.querySelector('select[name="cutting_plan_id"]');
    if (cuttingPlanSelect) {
        cuttingPlanSelect.addEventListener('change', function() {
            // You could make an AJAX call here to get plan details
            // and auto-populate related fields
        });
    }
});
</script>
@endpush
