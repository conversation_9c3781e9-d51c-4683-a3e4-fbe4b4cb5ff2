@extends('layouts.master')

@section('title', 'Cutting Section')

@section('main_content')
<div class="erp-state-overview-section">
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h2 class="page-title">Cutting Section</h2>
                    <div class="d-flex gap-2">
                        @can('production.cutting.create')
                        <a href="{{ route('production.cutting.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> New Cutting Plan
                        </a>
                        @endcan
                        @can('production.cutting.marker-planning')
                        <a href="{{ route('production.cutting.marker-planning') }}" class="btn btn-outline-info">
                            <i class="fas fa-ruler"></i> Marker Planning
                        </a>
                        @endcan
                        @can('production.cutting.fabric-issues')
                        <a href="{{ route('production.cutting.fabric-issues') }}" class="btn btn-outline-warning">
                            <i class="fas fa-boxes"></i> Fabric Issues
                        </a>
                        @endcan
                        @can('production.exports')
                        <div class="dropdown">
                            <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-download"></i> Export
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{{ route('production.exports.cutting-report.pdf', request()->query()) }}">
                                    <i class="fas fa-file-pdf text-danger"></i> PDF Report
                                </a></li>
                            </ul>
                        </div>
                        @endcan
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="GET" action="{{ route('production.cutting.index') }}" class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">Status</label>
                        <select name="status" class="form-select">
                            <option value="">All Status</option>
                            <option value="planned" {{ request('status') == 'planned' ? 'selected' : '' }}>Planned</option>
                            <option value="in_progress" {{ request('status') == 'in_progress' ? 'selected' : '' }}>In Progress</option>
                            <option value="completed" {{ request('status') == 'completed' ? 'selected' : '' }}>Completed</option>
                            <option value="cancelled" {{ request('status') == 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Style Number</label>
                        <input type="text" name="style_number" class="form-control" value="{{ request('style_number') }}" placeholder="Enter style number">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">PO Number</label>
                        <input type="text" name="po_number" class="form-control" value="{{ request('po_number') }}" placeholder="Enter PO number">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Cutting Date</label>
                        <input type="date" name="cutting_date" class="form-control" value="{{ request('cutting_date') }}">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Sort By</label>
                        <select name="sort_by" class="form-select">
                            <option value="cutting_date" {{ request('sort_by') == 'cutting_date' ? 'selected' : '' }}>Cutting Date</option>
                            <option value="created_at" {{ request('sort_by') == 'created_at' ? 'selected' : '' }}>Created Date</option>
                            <option value="style_number" {{ request('sort_by') == 'style_number' ? 'selected' : '' }}>Style Number</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Sort Direction</label>
                        <select name="sort_direction" class="form-select">
                            <option value="desc" {{ request('sort_direction') == 'desc' ? 'selected' : '' }}>Descending</option>
                            <option value="asc" {{ request('sort_direction') == 'asc' ? 'selected' : '' }}>Ascending</option>
                        </select>
                    </div>
                    <div class="col-md-6 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="fas fa-search"></i> Filter
                        </button>
                        <a href="{{ route('production.cutting.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i> Clear
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Cutting Plans Table -->
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>Cutting Number</th>
                                <th>Style Number</th>
                                <th>PO Number</th>
                                <th>Color</th>
                                <th>Total Pieces</th>
                                <th>Fabric Required</th>
                                <th>Cutting Date</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($cuttingPlans as $plan)
                            <tr>
                                <td>
                                    <a href="{{ route('production.cutting.show', $plan) }}" class="text-decoration-none">
                                        {{ $plan->cutting_number }}
                                    </a>
                                </td>
                                <td>{{ $plan->style_number }}</td>
                                <td>{{ $plan->po_number }}</td>
                                <td>{{ $plan->color }}</td>
                                <td>{{ number_format($plan->total_pieces) }}</td>
                                <td>{{ number_format($plan->total_fabric_required, 2) }} yards</td>
                                <td>{{ $plan->cutting_date->format('M d, Y') }}</td>
                                <td>
                                    <span class="badge bg-{{ 
                                        $plan->status == 'completed' ? 'success' : 
                                        ($plan->status == 'in_progress' ? 'primary' : 
                                        ($plan->status == 'cancelled' ? 'danger' : 'secondary')) 
                                    }}">
                                        {{ ucfirst(str_replace('_', ' ', $plan->status)) }}
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        @can('production.cutting.view')
                                        <a href="{{ route('production.cutting.show', $plan) }}" class="btn btn-outline-primary" title="View">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        @endcan
                                        @can('production.cutting.edit')
                                        <a href="{{ route('production.cutting.edit', $plan) }}" class="btn btn-outline-warning" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        @endcan
                                        @can('production.cutting.delete')
                                        @if($plan->status != 'in_progress')
                                        <form method="POST" action="{{ route('production.cutting.destroy', $plan) }}" class="d-inline">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-outline-danger" title="Delete" onclick="return confirm('Are you sure you want to delete this cutting plan?')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                        @endif
                                        @endcan
                                    </div>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="9" class="text-center py-4">
                                    <div class="text-muted">
                                        <i class="fas fa-cut fa-3x mb-3"></i>
                                        <p>No cutting plans found.</p>
                                        @can('production.cutting.create')
                                        <a href="{{ route('production.cutting.create') }}" class="btn btn-primary">
                                            Create First Cutting Plan
                                        </a>
                                        @endcan
                                    </div>
                                </td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                @if($cuttingPlans->hasPages())
                <div class="d-flex justify-content-center mt-4">
                    {{ $cuttingPlans->links() }}
                </div>
                @endif
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="row mt-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <h4>{{ $cuttingPlans->where('status', 'planned')->count() }}</h4>
                        <small>Planned</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body text-center">
                        <h4>{{ $cuttingPlans->where('status', 'in_progress')->count() }}</h4>
                        <small>In Progress</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <h4>{{ $cuttingPlans->where('status', 'completed')->count() }}</h4>
                        <small>Completed</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <h4>{{ number_format($cuttingPlans->sum('total_pieces')) }}</h4>
                        <small>Total Pieces</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('css')
<style>
.table th {
    font-weight: 600;
    border-bottom: 2px solid #dee2e6;
}
.btn-group-sm > .btn {
    padding: 0.25rem 0.5rem;
}
</style>
@endpush
