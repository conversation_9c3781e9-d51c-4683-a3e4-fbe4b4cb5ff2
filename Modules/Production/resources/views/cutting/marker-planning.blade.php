@extends('layouts.master')

@section('title', 'Marker Planning')

@section('main_content')
<div class="erp-state-overview-section">
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="page-title">Marker Planning</h2>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="{{ route('production.cutting.index') }}">Cutting Section</a></li>
                                <li class="breadcrumb-item active">Marker Planning</li>
                            </ol>
                        </nav>
                    </div>
                    <div>
                        <a href="{{ route('production.cutting.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Cutting
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Cutting Plans Selection -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Select Cutting Plan</h5>
                    </div>
                    <div class="card-body">
                        <form method="GET" action="{{ route('production.cutting.marker-planning') }}">
                            <div class="mb-3">
                                <label class="form-label">Cutting Plan</label>
                                <select name="cutting_plan_id" class="form-select" onchange="this.form.submit()">
                                    <option value="">Select a cutting plan</option>
                                    @foreach($cuttingPlans as $plan)
                                    <option value="{{ $plan->id }}" {{ request('cutting_plan_id') == $plan->id ? 'selected' : '' }}>
                                        {{ $plan->cutting_number }} - {{ $plan->style_number }}
                                    </option>
                                    @endforeach
                                </select>
                            </div>
                        </form>

                        @if($selectedPlan)
                        <div class="mt-4">
                            <h6>Plan Details</h6>
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>Style:</strong></td>
                                    <td>{{ $selectedPlan->style_number }}</td>
                                </tr>
                                <tr>
                                    <td><strong>PO:</strong></td>
                                    <td>{{ $selectedPlan->po_number }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Color:</strong></td>
                                    <td>{{ $selectedPlan->color }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Total Pieces:</strong></td>
                                    <td>{{ $selectedPlan->total_pieces }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Fabric Width:</strong></td>
                                    <td>{{ $selectedPlan->fabric_width }}"</td>
                                </tr>
                            </table>
                        </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Marker Creation Form -->
            <div class="col-md-8">
                @if($selectedPlan)
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Create New Marker</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="{{ route('production.cutting.markers.store') }}">
                            @csrf
                            <input type="hidden" name="cutting_plan_id" value="{{ $selectedPlan->id }}">
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Marker Length (inches) <span class="text-danger">*</span></label>
                                        <input type="number" name="marker_length" class="form-control @error('marker_length') is-invalid @enderror" 
                                               value="{{ old('marker_length') }}" required min="1" step="0.1">
                                        @error('marker_length')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Marker Efficiency (%) <span class="text-danger">*</span></label>
                                        <input type="number" name="marker_efficiency" class="form-control @error('marker_efficiency') is-invalid @enderror" 
                                               value="{{ old('marker_efficiency', '85') }}" required min="50" max="100" step="0.1">
                                        @error('marker_efficiency')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Ply Count <span class="text-danger">*</span></label>
                                        <input type="number" name="ply_count" class="form-control @error('ply_count') is-invalid @enderror" 
                                               value="{{ old('ply_count', $selectedPlan->ply_count) }}" required min="1" max="100">
                                        @error('ply_count')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Fabric Utilization (%)</label>
                                        <input type="number" name="fabric_utilization" class="form-control @error('fabric_utilization') is-invalid @enderror" 
                                               value="{{ old('fabric_utilization', '90') }}" min="50" max="100" step="0.1">
                                        @error('fabric_utilization')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Size Layout</label>
                                <textarea name="size_layout" class="form-control @error('size_layout') is-invalid @enderror" rows="3" 
                                          placeholder="Describe the size layout in the marker...">{{ old('size_layout') }}</textarea>
                                @error('size_layout')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Notes</label>
                                <textarea name="notes" class="form-control @error('notes') is-invalid @enderror" rows="2" 
                                          placeholder="Any special notes or instructions...">{{ old('notes') }}</textarea>
                                @error('notes')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="d-flex justify-content-end">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Create Marker
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Existing Markers -->
                @if($selectedPlan->markers->count() > 0)
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Existing Markers</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Marker Number</th>
                                        <th>Length</th>
                                        <th>Efficiency</th>
                                        <th>Ply Count</th>
                                        <th>Status</th>
                                        <th>Created</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($selectedPlan->markers as $marker)
                                    <tr>
                                        <td>{{ $marker->marker_number }}</td>
                                        <td>{{ $marker->marker_length }}"</td>
                                        <td>{{ $marker->marker_efficiency }}%</td>
                                        <td>{{ $marker->ply_count }}</td>
                                        <td>
                                            <span class="badge bg-{{ $marker->status == 'active' ? 'success' : 'secondary' }}">
                                                {{ ucfirst($marker->status) }}
                                            </span>
                                        </td>
                                        <td>{{ $marker->created_at->format('M d, Y') }}</td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button type="button" class="btn btn-outline-primary" title="View Details">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button type="button" class="btn btn-outline-secondary" title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                @endif

                @else
                <div class="card">
                    <div class="card-body text-center py-5">
                        <i class="fas fa-cut fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Select a Cutting Plan</h5>
                        <p class="text-muted">Choose a cutting plan from the left panel to start creating markers.</p>
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection

@push('css')
<style>
.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.table-sm td {
    padding: 0.25rem 0.5rem;
}

.badge {
    font-size: 0.75em;
}
</style>
@endpush
