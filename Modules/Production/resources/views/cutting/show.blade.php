@extends('layouts.master')

@section('title', 'Cutting Plan Details')

@section('main_content')
<div class="erp-state-overview-section">
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="page-title">Cutting Plan Details</h2>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="{{ route('production.cutting.index') }}">Cutting Section</a></li>
                                <li class="breadcrumb-item active">{{ $cuttingPlan->cutting_number }}</li>
                            </ol>
                        </nav>
                    </div>
                    <div class="d-flex gap-2">
                        @can('production.cutting.edit')
                        <a href="{{ route('production.cutting.edit', $cuttingPlan) }}" class="btn btn-warning">
                            <i class="fas fa-edit"></i> Edit Plan
                        </a>
                        @endcan
                        @can('production.cutting.marker-planning')
                        <a href="{{ route('production.cutting.marker-planning') }}?cutting_plan={{ $cuttingPlan->id }}" class="btn btn-info">
                            <i class="fas fa-ruler"></i> Marker Planning
                        </a>
                        @endcan
                        @can('production.cutting.fabric-issues')
                        <a href="{{ route('production.cutting.fabric-issues') }}?cutting_plan={{ $cuttingPlan->id }}" class="btn btn-success">
                            <i class="fas fa-boxes"></i> Fabric Issues
                        </a>
                        @endcan
                        <a href="{{ route('production.cutting.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> Back to List
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Plan Overview -->
        <div class="row mb-4">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Cutting Plan Overview</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>Cutting Number:</strong></td>
                                        <td>{{ $cuttingPlan->cutting_number }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Style Number:</strong></td>
                                        <td>{{ $cuttingPlan->style_number }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>PO Number:</strong></td>
                                        <td>{{ $cuttingPlan->po_number }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Color:</strong></td>
                                        <td>{{ $cuttingPlan->color }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Total Pieces:</strong></td>
                                        <td>{{ number_format($cuttingPlan->total_pieces) }}</td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>Cutting Date:</strong></td>
                                        <td>{{ $cuttingPlan->cutting_date ? $cuttingPlan->cutting_date->format('M d, Y') : 'Not set' }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Fabric Required:</strong></td>
                                        <td>{{ number_format($cuttingPlan->total_fabric_required, 2) }} yards</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Fabric Width:</strong></td>
                                        <td>{{ $cuttingPlan->fabric_width }}"</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Shrinkage %:</strong></td>
                                        <td>{{ number_format($cuttingPlan->shrinkage_percentage, 1) }}%</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Ply Count:</strong></td>
                                        <td>{{ $cuttingPlan->ply_count }}</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Status & Progress</h5>
                    </div>
                    <div class="card-body text-center">
                        <div class="mb-3">
                            <span class="badge bg-{{ 
                                $cuttingPlan->status == 'completed' ? 'success' : 
                                ($cuttingPlan->status == 'in_progress' ? 'primary' : 
                                ($cuttingPlan->status == 'cancelled' ? 'danger' : 'secondary')) 
                            }} fs-6">
                                {{ ucfirst(str_replace('_', ' ', $cuttingPlan->status)) }}
                            </span>
                        </div>
                        <div class="progress mb-3" style="height: 20px;">
                            <div class="progress-bar" role="progressbar" style="width: {{ $metrics['cutting_progress'] ?? 0 }}%">
                                {{ number_format($metrics['cutting_progress'] ?? 0, 1) }}%
                            </div>
                        </div>
                        <small class="text-muted">Cutting Progress</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Size Breakdown -->
        @if($cuttingPlan->size_breakdown)
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Size Breakdown</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Size</th>
                                <th>Quantity</th>
                                <th>Percentage</th>
                                <th>Cut Quantity</th>
                                <th>Remaining</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($cuttingPlan->size_breakdown as $size => $quantity)
                            @php
                                $cutQuantity = $cuttingPlan->cuttingEntries->where('size', $size)->sum('quantity');
                                $percentage = ($quantity / $cuttingPlan->total_pieces) * 100;
                            @endphp
                            <tr>
                                <td><strong>{{ $size }}</strong></td>
                                <td>{{ number_format($quantity) }}</td>
                                <td>{{ number_format($percentage, 1) }}%</td>
                                <td>{{ number_format($cutQuantity) }}</td>
                                <td>{{ number_format($quantity - $cutQuantity) }}</td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        @endif

        <!-- Markers -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Markers</h5>
                @can('production.cutting.marker-planning')
                <a href="{{ route('production.cutting.marker-planning') }}?cutting_plan={{ $cuttingPlan->id }}" class="btn btn-sm btn-outline-primary">
                    <i class="fas fa-plus"></i> Add Marker
                </a>
                @endcan
            </div>
            <div class="card-body">
                @if($cuttingPlan->markers->count() > 0)
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Marker Number</th>
                                <th>Length</th>
                                <th>Width</th>
                                <th>Efficiency</th>
                                <th>Pieces per Marker</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($cuttingPlan->markers as $marker)
                            <tr>
                                <td>{{ $marker->marker_number }}</td>
                                <td>{{ number_format($marker->marker_length, 2) }} yards</td>
                                <td>{{ number_format($marker->marker_width, 2) }}"</td>
                                <td>{{ number_format($marker->efficiency_percentage, 1) }}%</td>
                                <td>{{ number_format($marker->pieces_per_marker) }}</td>
                                <td>
                                    <span class="badge bg-{{ $marker->status == 'approved' ? 'success' : 'secondary' }}">
                                        {{ ucfirst($marker->status) }}
                                    </span>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                @else
                <div class="text-center py-4">
                    <i class="fas fa-ruler fa-3x text-muted mb-3"></i>
                    <p class="text-muted">No markers created yet.</p>
                    @can('production.cutting.marker-planning')
                    <a href="{{ route('production.cutting.marker-planning') }}?cutting_plan={{ $cuttingPlan->id }}" class="btn btn-primary">
                        Create First Marker
                    </a>
                    @endcan
                </div>
                @endif
            </div>
        </div>

        <!-- Cutting Entries -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Cutting Entries</h5>
                @can('production.cutting.create')
                <a href="{{ route('production.cutting.entries') }}?cutting_plan={{ $cuttingPlan->id }}" class="btn btn-sm btn-outline-primary">
                    <i class="fas fa-plus"></i> Add Entry
                </a>
                @endcan
            </div>
            <div class="card-body">
                @if($cuttingPlan->cuttingEntries->count() > 0)
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Bundle Number</th>
                                <th>Size</th>
                                <th>Quantity</th>
                                <th>Defects</th>
                                <th>Cutting Date</th>
                                <th>Cutter</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($cuttingPlan->cuttingEntries as $entry)
                            <tr>
                                <td>{{ $entry->bundle_number }}</td>
                                <td>{{ $entry->size }}</td>
                                <td>{{ number_format($entry->quantity) }}</td>
                                <td>
                                    <span class="badge bg-{{ !empty($entry->defects) ? 'danger' : 'success' }}">
                                        {{ !empty($entry->defects) ? 'Yes' : 'No' }}
                                    </span>
                                </td>
                                <td>{{ $entry->cutting_datetime ? $entry->cutting_datetime->format('M d, Y H:i') : 'Not set' }}</td>
                                <td>{{ $entry->cutter_name }}</td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                @else
                <div class="text-center py-4">
                    <i class="fas fa-cut fa-3x text-muted mb-3"></i>
                    <p class="text-muted">No cutting entries recorded yet.</p>
                    @can('production.cutting.create')
                    <a href="{{ route('production.cutting.entries') }}?cutting_plan={{ $cuttingPlan->id }}" class="btn btn-primary">
                        Record First Entry
                    </a>
                    @endcan
                </div>
                @endif
            </div>
        </div>

        <!-- Fabric Issues -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Fabric Issues</h5>
                @can('production.cutting.fabric-issues')
                <a href="{{ route('production.cutting.fabric-issues') }}?cutting_plan={{ $cuttingPlan->id }}" class="btn btn-sm btn-outline-primary">
                    <i class="fas fa-plus"></i> Issue Fabric
                </a>
                @endcan
            </div>
            <div class="card-body">
                @if($cuttingPlan->fabricIssues->count() > 0)
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Issue Number</th>
                                <th>Fabric Type</th>
                                <th>Issued Quantity</th>
                                <th>Returned Quantity</th>
                                <th>Wastage</th>
                                <th>Issue Date</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($cuttingPlan->fabricIssues as $issue)
                            <tr>
                                <td>{{ $issue->issue_number }}</td>
                                <td>{{ $issue->fabric_type }}</td>
                                <td>{{ number_format($issue->issued_quantity, 2) }} {{ $issue->unit }}</td>
                                <td>{{ number_format($issue->returned_quantity, 2) }} {{ $issue->unit }}</td>
                                <td>{{ number_format($issue->wastage_quantity, 2) }} {{ $issue->unit }}</td>
                                <td>{{ $issue->issue_datetime ? $issue->issue_datetime->format('M d, Y H:i') : 'Not set' }}</td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                @else
                <div class="text-center py-4">
                    <i class="fas fa-boxes fa-3x text-muted mb-3"></i>
                    <p class="text-muted">No fabric issues recorded yet.</p>
                    @can('production.cutting.fabric-issues')
                    <a href="{{ route('production.cutting.fabric-issues') }}?cutting_plan={{ $cuttingPlan->id }}" class="btn btn-primary">
                        Issue First Fabric
                    </a>
                    @endcan
                </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection

@push('css')
<style>
.table th {
    font-weight: 600;
    border-bottom: 2px solid #dee2e6;
}
.progress {
    background-color: #e9ecef;
}
</style>
@endpush
