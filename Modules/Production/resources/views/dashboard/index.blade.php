@extends('layouts.master')

@section('title', 'Production Dashboard')

@section('main_content')
<div class="erp-state-overview-section">
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h2 class="page-title">Garments Production Dashboard</h2>
                    <div class="d-flex gap-2">
                        <input type="date" class="form-control" id="start_date" value="{{ $dateRange['start_date']->format('Y-m-d') }}">
                        <input type="date" class="form-control" id="end_date" value="{{ $dateRange['end_date']->format('Y-m-d') }}">
                        <button class="btn btn-primary" onclick="refreshDashboard()">
                            <i class="fas fa-sync-alt"></i> Refresh
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Production Summary Cards -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">Cutting</h6>
                                <h3 class="mb-0">{{ number_format($dailyProduction['cutting']) }}</h3>
                                <small>Pieces Cut</small>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-cut fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">Sewing</h6>
                                <h3 class="mb-0">{{ number_format($dailyProduction['sewing']) }}</h3>
                                <small>Pieces Sewn</small>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-tshirt fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">Finishing</h6>
                                <h3 class="mb-0">{{ number_format($dailyProduction['finishing']) }}</h3>
                                <small>Pieces Finished</small>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-check-circle fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">Washing</h6>
                                <h3 class="mb-0">{{ number_format($dailyProduction['washing']) }}</h3>
                                <small>Pieces Washed</small>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-tint fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row -->
        <div class="row mb-4">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Daily Production Trend</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="productionTrendChart" height="100"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Quality Metrics</h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6">
                                <h4 class="text-success">{{ $qualityMetrics['pass_rate'] }}%</h4>
                                <small class="text-muted">Pass Rate</small>
                            </div>
                            <div class="col-6">
                                <h4 class="text-danger">{{ $qualityMetrics['fail_rate'] }}%</h4>
                                <small class="text-muted">Fail Rate</small>
                            </div>
                        </div>
                        <hr>
                        <div class="text-center">
                            <h5>{{ number_format($qualityMetrics['avg_defect_rate'], 2) }}%</h5>
                            <small class="text-muted">Avg Defect Rate</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Line Efficiency and WIP -->
        <div class="row mb-4">
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Line Efficiency</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Line</th>
                                        <th>Target</th>
                                        <th>Actual</th>
                                        <th>Variance</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($lineEfficiency as $line)
                                    <tr>
                                        <td>{{ $line['line_number'] }}</td>
                                        <td>{{ $line['target_efficiency'] }}%</td>
                                        <td>{{ $line['actual_efficiency'] }}%</td>
                                        <td class="{{ $line['variance'] >= 0 ? 'text-success' : 'text-danger' }}">
                                            {{ $line['variance'] > 0 ? '+' : '' }}{{ $line['variance'] }}%
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">WIP Levels</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6 mb-3">
                                <div class="text-center">
                                    <h4 class="text-primary">{{ number_format($wipLevels['cutting_wip']) }}</h4>
                                    <small class="text-muted">Cutting WIP</small>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="text-center">
                                    <h4 class="text-success">{{ number_format($wipLevels['sewing_wip']) }}</h4>
                                    <small class="text-muted">Sewing WIP</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="text-center">
                                    <h4 class="text-info">{{ number_format($wipLevels['finishing_wip']) }}</h4>
                                    <small class="text-muted">Finishing WIP</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="text-center">
                                    <h4 class="text-warning">{{ number_format($wipLevels['washing_wip']) }}</h4>
                                    <small class="text-muted">Washing WIP</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bottlenecks -->
        @if(count($bottlenecks) > 0)
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-danger text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-exclamation-triangle"></i> Production Bottlenecks
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Line</th>
                                        <th>WIP Count</th>
                                        <th>Capacity</th>
                                        <th>Action Required</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($bottlenecks as $bottleneck)
                                    <tr>
                                        <td>{{ $bottleneck['line_number'] }}</td>
                                        <td><span class="badge bg-danger">{{ $bottleneck['wip_count'] }}</span></td>
                                        <td>{{ $bottleneck['daily_capacity'] }}</td>
                                        <td>
                                            <small class="text-muted">High WIP - Review line balance</small>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @endif
    </div>
</div>
@endsection

@push('css')
<style>
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border-radius: 0.5rem;
}
.opacity-75 {
    opacity: 0.75;
}
</style>
@endpush

@push('js')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Production Trend Chart
const ctx = document.getElementById('productionTrendChart').getContext('2d');
const chartData = @json($chartData);

new Chart(ctx, {
    type: 'line',
    data: {
        labels: chartData.daily_trend.map(item => item.date),
        datasets: [{
            label: 'Cutting',
            data: chartData.daily_trend.map(item => item.cutting),
            borderColor: '#007bff',
            backgroundColor: 'rgba(0, 123, 255, 0.1)',
            tension: 0.4
        }, {
            label: 'Sewing',
            data: chartData.daily_trend.map(item => item.sewing),
            borderColor: '#28a745',
            backgroundColor: 'rgba(40, 167, 69, 0.1)',
            tension: 0.4
        }, {
            label: 'Finishing',
            data: chartData.daily_trend.map(item => item.finishing),
            borderColor: '#17a2b8',
            backgroundColor: 'rgba(23, 162, 184, 0.1)',
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true
            }
        },
        plugins: {
            legend: {
                position: 'top'
            }
        }
    }
});

function refreshDashboard() {
    const startDate = document.getElementById('start_date').value;
    const endDate = document.getElementById('end_date').value;
    
    window.location.href = `{{ route('production.dashboard.index') }}?start_date=${startDate}&end_date=${endDate}`;
}
</script>
@endpush
