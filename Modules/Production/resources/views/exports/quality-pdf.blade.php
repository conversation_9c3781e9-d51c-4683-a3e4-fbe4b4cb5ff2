<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Quality Control Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            margin: 0;
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 10px;
        }
        .company-name {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .report-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .report-date {
            font-size: 12px;
            color: #666;
        }
        .summary-section {
            margin-bottom: 20px;
        }
        .summary-title {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 10px;
            background-color: #f5f5f5;
            padding: 5px;
        }
        .summary-stats {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
        }
        .stat-box {
            text-align: center;
            border: 1px solid #ddd;
            padding: 10px;
            width: 23%;
        }
        .stat-value {
            font-size: 16px;
            font-weight: bold;
            color: #333;
        }
        .stat-label {
            font-size: 10px;
            color: #666;
            margin-top: 5px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f5f5f5;
            font-weight: bold;
            font-size: 11px;
        }
        td {
            font-size: 10px;
        }
        .text-center {
            text-align: center;
        }
        .text-right {
            text-align: right;
        }
        .badge {
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 9px;
            font-weight: bold;
        }
        .badge-success {
            background-color: #d4edda;
            color: #155724;
        }
        .badge-warning {
            background-color: #fff3cd;
            color: #856404;
        }
        .badge-danger {
            background-color: #f8d7da;
            color: #721c24;
        }
        .badge-primary {
            background-color: #d1ecf1;
            color: #0c5460;
        }
        .badge-secondary {
            background-color: #e2e3e5;
            color: #383d41;
        }
        .defect-badges {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 10px;
        }
        .quality-chart {
            margin-bottom: 20px;
        }
        .chart-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }
        .chart-item {
            text-align: center;
            border: 1px solid #ddd;
            padding: 8px;
            width: 18%;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="company-name">{{ config('app.name', 'Garments Factory') }}</div>
        <div class="report-title">Quality Control Report</div>
        <div class="report-date">Generated on: {{ now()->format('F d, Y H:i:s') }}</div>
        @if(request('start_date') || request('end_date'))
        <div class="report-date">
            Period: {{ request('start_date') ? \Carbon\Carbon::parse(request('start_date'))->format('M d, Y') : 'Beginning' }} 
            to {{ request('end_date') ? \Carbon\Carbon::parse(request('end_date'))->format('M d, Y') : 'Present' }}
        </div>
        @endif
    </div>

    <!-- Summary Statistics -->
    <div class="summary-section">
        <div class="summary-title">Quality Summary</div>
        <div class="summary-stats">
            <div class="stat-box">
                <div class="stat-value">{{ $qualityInspections->where('inspection_result', 'pass')->count() }}</div>
                <div class="stat-label">Pass</div>
            </div>
            <div class="stat-box">
                <div class="stat-value">{{ $qualityInspections->where('inspection_result', 'fail')->count() }}</div>
                <div class="stat-label">Fail</div>
            </div>
            <div class="stat-box">
                <div class="stat-value">{{ $qualityInspections->where('inspection_result', 'conditional_pass')->count() }}</div>
                <div class="stat-label">Conditional</div>
            </div>
            <div class="stat-box">
                <div class="stat-value">{{ number_format($qualityInspections->avg('defect_percentage'), 2) }}%</div>
                <div class="stat-label">Avg Defect Rate</div>
            </div>
        </div>
    </div>

    <!-- Inspection Stage Analysis -->
    <div class="summary-section">
        <div class="summary-title">Inspection Stage Analysis</div>
        <table>
            <thead>
                <tr>
                    <th>Stage</th>
                    <th>Total Inspections</th>
                    <th>Pass</th>
                    <th>Fail</th>
                    <th>Conditional</th>
                    <th>Pass Rate</th>
                    <th>Avg Defect %</th>
                </tr>
            </thead>
            <tbody>
                @foreach($qualityInspections->groupBy('inspection_stage') as $stage => $stageInspections)
                @php
                    $passCount = $stageInspections->where('inspection_result', 'pass')->count();
                    $failCount = $stageInspections->where('inspection_result', 'fail')->count();
                    $conditionalCount = $stageInspections->where('inspection_result', 'conditional_pass')->count();
                    $totalCount = $stageInspections->count();
                    $passRate = $totalCount > 0 ? ($passCount / $totalCount) * 100 : 0;
                    $avgDefectRate = $stageInspections->avg('defect_percentage');
                @endphp
                <tr>
                    <td>{{ ucfirst(str_replace('_', ' ', $stage)) }}</td>
                    <td class="text-center">{{ $totalCount }}</td>
                    <td class="text-center">{{ $passCount }}</td>
                    <td class="text-center">{{ $failCount }}</td>
                    <td class="text-center">{{ $conditionalCount }}</td>
                    <td class="text-center">{{ number_format($passRate, 1) }}%</td>
                    <td class="text-center">{{ number_format($avgDefectRate, 2) }}%</td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    <!-- Defect Analysis -->
    <div class="summary-section">
        <div class="summary-title">Defect Analysis</div>
        <div class="quality-chart">
            <div class="chart-row">
                <div class="chart-item">
                    <div class="stat-value">{{ $qualityInspections->sum('critical_defects') }}</div>
                    <div class="stat-label">Critical Defects</div>
                </div>
                <div class="chart-item">
                    <div class="stat-value">{{ $qualityInspections->sum('major_defects') }}</div>
                    <div class="stat-label">Major Defects</div>
                </div>
                <div class="chart-item">
                    <div class="stat-value">{{ $qualityInspections->sum('minor_defects') }}</div>
                    <div class="stat-label">Minor Defects</div>
                </div>
                <div class="chart-item">
                    <div class="stat-value">{{ $qualityInspections->sum('defect_quantity') }}</div>
                    <div class="stat-label">Total Defects</div>
                </div>
                <div class="chart-item">
                    <div class="stat-value">{{ $qualityInspections->sum('sample_size') }}</div>
                    <div class="stat-label">Total Inspected</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Inspections -->
    <div class="summary-section">
        <div class="summary-title">Detailed Quality Inspections</div>
        <table>
            <thead>
                <tr>
                    <th>Inspection #</th>
                    <th>Bundle</th>
                    <th>Stage</th>
                    <th>Lot Qty</th>
                    <th>Sample</th>
                    <th>Defects</th>
                    <th>Defect %</th>
                    <th>Inspector</th>
                    <th>Result</th>
                    <th>Date</th>
                </tr>
            </thead>
            <tbody>
                @foreach($qualityInspections as $inspection)
                <tr>
                    <td>{{ $inspection->inspection_number }}</td>
                    <td>{{ $inspection->bundle_number }}</td>
                    <td>{{ ucfirst(str_replace('_', ' ', $inspection->inspection_stage)) }}</td>
                    <td class="text-center">{{ number_format($inspection->lot_quantity) }}</td>
                    <td class="text-center">{{ number_format($inspection->sample_size) }}</td>
                    <td class="text-center">
                        <div class="defect-badges">
                            @if($inspection->critical_defects > 0)
                            <span class="badge badge-danger">C: {{ $inspection->critical_defects }}</span>
                            @endif
                            @if($inspection->major_defects > 0)
                            <span class="badge badge-warning">M: {{ $inspection->major_defects }}</span>
                            @endif
                            @if($inspection->minor_defects > 0)
                            <span class="badge badge-primary">m: {{ $inspection->minor_defects }}</span>
                            @endif
                            @if($inspection->defect_quantity == 0)
                            <span class="badge badge-success">No Defects</span>
                            @endif
                        </div>
                    </td>
                    <td class="text-center">
                        <span class="badge {{ $inspection->defect_percentage > 4 ? 'badge-danger' : ($inspection->defect_percentage > 2.5 ? 'badge-warning' : 'badge-success') }}">
                            {{ number_format($inspection->defect_percentage, 2) }}%
                        </span>
                    </td>
                    <td>{{ $inspection->inspector_name }}</td>
                    <td class="text-center">
                        <span class="badge {{ 
                            $inspection->inspection_result == 'pass' ? 'badge-success' : 
                            ($inspection->inspection_result == 'fail' ? 'badge-danger' : 'badge-warning') 
                        }}">
                            {{ ucfirst(str_replace('_', ' ', $inspection->inspection_result)) }}
                        </span>
                    </td>
                    <td class="text-center">{{ $inspection->inspection_datetime->format('M d, Y') }}</td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    <!-- Inspector Performance -->
    <div class="summary-section">
        <div class="summary-title">Inspector Performance</div>
        <table>
            <thead>
                <tr>
                    <th>Inspector</th>
                    <th>Total Inspections</th>
                    <th>Pass Rate</th>
                    <th>Avg Defect Rate</th>
                    <th>Critical Defects Found</th>
                    <th>Major Defects Found</th>
                </tr>
            </thead>
            <tbody>
                @foreach($qualityInspections->groupBy('inspector_name') as $inspector => $inspectorInspections)
                @php
                    $passCount = $inspectorInspections->where('inspection_result', 'pass')->count();
                    $totalCount = $inspectorInspections->count();
                    $passRate = $totalCount > 0 ? ($passCount / $totalCount) * 100 : 0;
                    $avgDefectRate = $inspectorInspections->avg('defect_percentage');
                    $criticalDefects = $inspectorInspections->sum('critical_defects');
                    $majorDefects = $inspectorInspections->sum('major_defects');
                @endphp
                <tr>
                    <td>{{ $inspector }}</td>
                    <td class="text-center">{{ $totalCount }}</td>
                    <td class="text-center">{{ number_format($passRate, 1) }}%</td>
                    <td class="text-center">{{ number_format($avgDefectRate, 2) }}%</td>
                    <td class="text-center">{{ $criticalDefects }}</td>
                    <td class="text-center">{{ $majorDefects }}</td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    <!-- Footer -->
    <div class="footer">
        <p>This report was generated automatically by the Quality Control Management System.</p>
        <p>Report contains {{ $qualityInspections->count() }} quality inspections performed by {{ $qualityInspections->groupBy('inspector_name')->count() }} inspectors.</p>
        <p>Overall Quality Performance: {{ number_format(($qualityInspections->where('inspection_result', 'pass')->count() / $qualityInspections->count()) * 100, 1) }}% Pass Rate</p>
    </div>
</body>
</html>
