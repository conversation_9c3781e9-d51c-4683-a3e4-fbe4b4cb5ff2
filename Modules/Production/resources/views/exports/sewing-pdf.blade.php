<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Sewing Production Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            margin: 0;
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 10px;
        }
        .company-name {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .report-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .report-date {
            font-size: 12px;
            color: #666;
        }
        .summary-section {
            margin-bottom: 20px;
        }
        .summary-title {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 10px;
            background-color: #f5f5f5;
            padding: 5px;
        }
        .summary-stats {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
        }
        .stat-box {
            text-align: center;
            border: 1px solid #ddd;
            padding: 10px;
            width: 23%;
        }
        .stat-value {
            font-size: 16px;
            font-weight: bold;
            color: #333;
        }
        .stat-label {
            font-size: 10px;
            color: #666;
            margin-top: 5px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f5f5f5;
            font-weight: bold;
            font-size: 11px;
        }
        td {
            font-size: 10px;
        }
        .text-center {
            text-align: center;
        }
        .text-right {
            text-align: right;
        }
        .badge {
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 9px;
            font-weight: bold;
        }
        .badge-success {
            background-color: #d4edda;
            color: #155724;
        }
        .badge-warning {
            background-color: #fff3cd;
            color: #856404;
        }
        .badge-danger {
            background-color: #f8d7da;
            color: #721c24;
        }
        .badge-primary {
            background-color: #d1ecf1;
            color: #0c5460;
        }
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 10px;
        }
        .page-break {
            page-break-before: always;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="company-name">{{ config('app.name', 'Garments Factory') }}</div>
        <div class="report-title">Sewing Production Report</div>
        <div class="report-date">Generated on: {{ now()->format('F d, Y H:i:s') }}</div>
        @if(request('start_date') || request('end_date'))
        <div class="report-date">
            Period: {{ request('start_date') ? \Carbon\Carbon::parse(request('start_date'))->format('M d, Y') : 'Beginning' }} 
            to {{ request('end_date') ? \Carbon\Carbon::parse(request('end_date'))->format('M d, Y') : 'Present' }}
        </div>
        @endif
    </div>

    <!-- Summary Statistics -->
    <div class="summary-section">
        <div class="summary-title">Production Summary</div>
        <div class="summary-stats">
            <div class="stat-box">
                <div class="stat-value">{{ $sewingEntries->where('status', 'input')->count() }}</div>
                <div class="stat-label">Input Entries</div>
            </div>
            <div class="stat-box">
                <div class="stat-value">{{ $sewingEntries->where('status', 'in_progress')->count() }}</div>
                <div class="stat-label">In Progress</div>
            </div>
            <div class="stat-box">
                <div class="stat-value">{{ $sewingEntries->where('status', 'completed')->count() }}</div>
                <div class="stat-label">Completed</div>
            </div>
            <div class="stat-box">
                <div class="stat-value">{{ number_format($sewingEntries->sum('output_quantity')) }}</div>
                <div class="stat-label">Total Output</div>
            </div>
        </div>
    </div>

    <!-- Efficiency Summary -->
    <div class="summary-section">
        <div class="summary-title">Efficiency Analysis</div>
        <table>
            <thead>
                <tr>
                    <th>Line</th>
                    <th>Total Entries</th>
                    <th>Total Output</th>
                    <th>Average Efficiency</th>
                    <th>Total Defects</th>
                    <th>Defect Rate</th>
                </tr>
            </thead>
            <tbody>
                @foreach($sewingEntries->groupBy('sewing_line_id') as $lineId => $lineEntries)
                @php
                    $line = $lineEntries->first()->sewingLine;
                    $totalOutput = $lineEntries->sum('output_quantity');
                    $avgEfficiency = $lineEntries->where('efficiency_achieved', '>', 0)->avg('efficiency_achieved');
                    $totalDefects = $lineEntries->sum('defect_quantity');
                    $defectRate = $totalOutput > 0 ? ($totalDefects / $totalOutput) * 100 : 0;
                @endphp
                <tr>
                    <td>{{ $line->line_number ?? 'N/A' }} - {{ $line->line_name ?? 'Unknown' }}</td>
                    <td class="text-center">{{ $lineEntries->count() }}</td>
                    <td class="text-center">{{ number_format($totalOutput) }}</td>
                    <td class="text-center">{{ $avgEfficiency ? number_format($avgEfficiency, 1) . '%' : 'N/A' }}</td>
                    <td class="text-center">{{ number_format($totalDefects) }}</td>
                    <td class="text-center">{{ number_format($defectRate, 2) }}%</td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    <!-- Detailed Entries -->
    <div class="summary-section">
        <div class="summary-title">Detailed Sewing Entries</div>
        <table>
            <thead>
                <tr>
                    <th>Bundle Ticket</th>
                    <th>Line</th>
                    <th>Operator</th>
                    <th>Input Qty</th>
                    <th>Output Qty</th>
                    <th>Defects</th>
                    <th>Efficiency</th>
                    <th>Input Date</th>
                    <th>Status</th>
                </tr>
            </thead>
            <tbody>
                @foreach($sewingEntries as $entry)
                <tr>
                    <td>{{ $entry->bundleTicket->ticket_number ?? 'N/A' }}</td>
                    <td>{{ $entry->sewingLine->line_number ?? 'N/A' }}</td>
                    <td>{{ $entry->operator->operator_id ?? 'N/A' }}</td>
                    <td class="text-center">{{ number_format($entry->input_quantity) }}</td>
                    <td class="text-center">{{ number_format($entry->output_quantity ?? 0) }}</td>
                    <td class="text-center">{{ number_format($entry->defect_quantity ?? 0) }}</td>
                    <td class="text-center">
                        @if($entry->efficiency_achieved)
                        <span class="badge {{ $entry->efficiency_achieved >= 85 ? 'badge-success' : ($entry->efficiency_achieved >= 70 ? 'badge-warning' : 'badge-danger') }}">
                            {{ number_format($entry->efficiency_achieved, 1) }}%
                        </span>
                        @else
                        N/A
                        @endif
                    </td>
                    <td class="text-center">{{ $entry->input_datetime->format('M d, Y H:i') }}</td>
                    <td class="text-center">
                        <span class="badge {{ 
                            $entry->status == 'completed' ? 'badge-success' : 
                            ($entry->status == 'in_progress' ? 'badge-primary' : 
                            ($entry->status == 'output' ? 'badge-warning' : 'badge-secondary')) 
                        }}">
                            {{ ucfirst(str_replace('_', ' ', $entry->status)) }}
                        </span>
                    </td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    <!-- Footer -->
    <div class="footer">
        <p>This report was generated automatically by the Production Management System.</p>
        <p>Report contains {{ $sewingEntries->count() }} sewing entries across {{ $sewingEntries->groupBy('sewing_line_id')->count() }} production lines.</p>
    </div>
</body>
</html>
