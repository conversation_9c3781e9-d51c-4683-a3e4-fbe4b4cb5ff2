@extends('layouts.master')

@section('title', 'Create Finishing Entry')

@section('main_content')
<div class="erp-state-overview-section">
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="page-title">Create Finishing Entry</h2>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="{{ route('production.finishing.index') }}">Finishing Section</a></li>
                                <li class="breadcrumb-item active">Create Entry</li>
                            </ol>
                        </nav>
                    </div>
                    <div>
                        <a href="{{ route('production.finishing.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> Back to List
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Create Form -->
        <form method="POST" action="{{ route('production.finishing.store') }}">
            @csrf
            
            <div class="row">
                <div class="col-md-8">
                    <!-- Basic Information -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Basic Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Sewing Entry <span class="text-danger">*</span></label>
                                        <select name="sewing_entry_id" class="form-select @error('sewing_entry_id') is-invalid @enderror" required>
                                            <option value="">Select Sewing Entry</option>
                                            @forelse($sewingEntries as $entry)
                                            <option value="{{ $entry->id }}" {{ old('sewing_entry_id') == $entry->id ? 'selected' : '' }}>
                                                {{ $entry->bundleTicket->bundle_number }} - {{ $entry->bundleTicket->style_number }}
                                            </option>
                                            @empty
                                            <option value="" disabled>No completed sewing entries available</option>
                                            @endforelse
                                        </select>
                                        @if($sewingEntries->count() == 0)
                                        <div class="form-text text-warning">
                                            <i class="fas fa-exclamation-triangle"></i>
                                            No completed sewing entries available. Please complete sewing operations first.
                                            <a href="{{ route('production.sewing.index') }}" class="text-primary">Go to Sewing</a>
                                        </div>
                                        @endif
                                        @error('sewing_entry_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Operator Name <span class="text-danger">*</span></label>
                                        <input type="text" name="operator_name" class="form-control @error('operator_name') is-invalid @enderror"
                                               value="{{ old('operator_name') }}" required placeholder="Enter operator name">
                                        @error('operator_name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Finishing Process <span class="text-danger">*</span></label>
                                        <select name="finishing_process_id" class="form-select @error('finishing_process_id') is-invalid @enderror" required>
                                            <option value="">Select Finishing Process</option>
                                            @foreach($finishingProcesses as $process)
                                            <option value="{{ $process->id }}" {{ old('finishing_process_id') == $process->id ? 'selected' : '' }}>
                                                {{ $process->process_name }} ({{ $process->process_code }})
                                            </option>
                                            @endforeach
                                        </select>
                                        @error('finishing_process_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Status <span class="text-danger">*</span></label>
                                        <select name="status" class="form-select @error('status') is-invalid @enderror" required>
                                            <option value="input" {{ old('status') == 'input' ? 'selected' : '' }}>Input</option>
                                            <option value="in_progress" {{ old('status') == 'in_progress' ? 'selected' : '' }}>In Progress</option>
                                            <option value="completed" {{ old('status') == 'completed' ? 'selected' : '' }}>Completed</option>
                                            <option value="rejected" {{ old('status') == 'rejected' ? 'selected' : '' }}>Rejected</option>
                                        </select>
                                        @error('status')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quantity Information -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Quantity Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label class="form-label">Input Quantity <span class="text-danger">*</span></label>
                                        <input type="number" name="input_quantity" class="form-control @error('input_quantity') is-invalid @enderror" 
                                               value="{{ old('input_quantity') }}" required min="1">
                                        @error('input_quantity')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label class="form-label">Output Quantity</label>
                                        <input type="number" name="output_quantity" class="form-control @error('output_quantity') is-invalid @enderror" 
                                               value="{{ old('output_quantity') }}" min="0">
                                        @error('output_quantity')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label class="form-label">Defect Quantity</label>
                                        <input type="number" name="defect_quantity" class="form-control @error('defect_quantity') is-invalid @enderror" 
                                               value="{{ old('defect_quantity', 0) }}" min="0">
                                        @error('defect_quantity')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label class="form-label">Rework Quantity</label>
                                        <input type="number" name="rework_quantity" class="form-control @error('rework_quantity') is-invalid @enderror" 
                                               value="{{ old('rework_quantity', 0) }}" min="0">
                                        @error('rework_quantity')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quality Control -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Quality Control</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">QC Status <span class="text-danger">*</span></label>
                                        <select name="qc_status" class="form-select @error('qc_status') is-invalid @enderror" required>
                                            <option value="pending" {{ old('qc_status') == 'pending' ? 'selected' : '' }}>Pending</option>
                                            <option value="pass" {{ old('qc_status') == 'pass' ? 'selected' : '' }}>Pass</option>
                                            <option value="fail" {{ old('qc_status') == 'fail' ? 'selected' : '' }}>Fail</option>
                                            <option value="rework" {{ old('qc_status') == 'rework' ? 'selected' : '' }}>Rework</option>
                                        </select>
                                        @error('qc_status')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">QC Inspector</label>
                                        <input type="text" name="qc_inspector" class="form-control @error('qc_inspector') is-invalid @enderror" 
                                               value="{{ old('qc_inspector') }}" placeholder="Enter inspector name">
                                        @error('qc_inspector')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">QC Date & Time</label>
                                        <input type="datetime-local" name="qc_datetime" class="form-control @error('qc_datetime') is-invalid @enderror" 
                                               value="{{ old('qc_datetime') }}">
                                        @error('qc_datetime')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="mb-3">
                                        <label class="form-label">QC Remarks</label>
                                        <textarea name="qc_remarks" class="form-control @error('qc_remarks') is-invalid @enderror" rows="3" 
                                                  placeholder="Enter QC remarks or defect details...">{{ old('qc_remarks') }}</textarea>
                                        @error('qc_remarks')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Timing Information -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Timing Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Start Date & Time <span class="text-danger">*</span></label>
                                        <input type="datetime-local" name="start_datetime" class="form-control @error('start_datetime') is-invalid @enderror" 
                                               value="{{ old('start_datetime', now()->format('Y-m-d\TH:i')) }}" required>
                                        @error('start_datetime')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">End Date & Time</label>
                                        <input type="datetime-local" name="end_datetime" class="form-control @error('end_datetime') is-invalid @enderror" 
                                               value="{{ old('end_datetime') }}">
                                        @error('end_datetime')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <!-- Actions -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Actions</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Create Finishing Entry
                                </button>
                                <a href="{{ route('production.finishing.index') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times"></i> Cancel
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Process Info -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Process Info</h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle"></i> Finishing Processes:</h6>
                                <ul class="mb-0">
                                    @foreach($finishingProcesses->take(5) as $process)
                                    <li>{{ $process->process_name }}</li>
                                    @endforeach
                                    @if($finishingProcesses->count() > 5)
                                    <li><em>...and {{ $finishingProcesses->count() - 5 }} more</em></li>
                                    @endif
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Notes -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Notes</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label">Additional Notes</label>
                                <textarea name="notes" class="form-control @error('notes') is-invalid @enderror" rows="4" 
                                          placeholder="Enter any additional notes...">{{ old('notes') }}</textarea>
                                @error('notes')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
@endsection

@push('js')
<script>
// Auto-calculate output quantity based on input minus defects and rework
document.addEventListener('DOMContentLoaded', function() {
    const inputQtyInput = document.querySelector('input[name="input_quantity"]');
    const defectQtyInput = document.querySelector('input[name="defect_quantity"]');
    const reworkQtyInput = document.querySelector('input[name="rework_quantity"]');
    const outputQtyInput = document.querySelector('input[name="output_quantity"]');

    function calculateOutput() {
        const inputQty = parseFloat(inputQtyInput.value) || 0;
        const defectQty = parseFloat(defectQtyInput.value) || 0;
        const reworkQty = parseFloat(reworkQtyInput.value) || 0;

        if (inputQty > 0) {
            const outputQty = inputQty - defectQty - reworkQty;
            if (outputQty >= 0) {
                outputQtyInput.value = outputQty;
            }
        }
    }

    [inputQtyInput, defectQtyInput, reworkQtyInput].forEach(input => {
        if (input) {
            input.addEventListener('input', calculateOutput);
        }
    });
});
</script>
@endpush
