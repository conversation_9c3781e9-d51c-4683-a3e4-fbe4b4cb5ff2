@extends('layouts.master')

@section('title', 'Edit Finishing Entry')

@section('main_content')
<div class="erp-state-overview-section">
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="page-title">Edit Finishing Entry</h2>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="{{ route('production.finishing.index') }}">Finishing Section</a></li>
                                <li class="breadcrumb-item"><a href="{{ route('production.finishing.show', $finishingEntry) }}">{{ $finishingEntry->bundle_number }}</a></li>
                                <li class="breadcrumb-item active">Edit</li>
                            </ol>
                        </nav>
                    </div>
                    <div>
                        <a href="{{ route('production.finishing.show', $finishingEntry) }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Details
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Edit Form -->
        <form method="POST" action="{{ route('production.finishing.update', $finishingEntry) }}">
            @csrf
            @method('PUT')
            
            <div class="row">
                <div class="col-md-8">
                    <!-- Basic Information -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Basic Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Bundle Number</label>
                                        <input type="text" class="form-control" value="{{ $finishingEntry->bundle_number }}" readonly>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Style Number</label>
                                        <input type="text" class="form-control" value="{{ $finishingEntry->style_number }}" readonly>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Finishing Process <span class="text-danger">*</span></label>
                                        <select name="finishing_process_id" class="form-select @error('finishing_process_id') is-invalid @enderror" required>
                                            <option value="">Select Finishing Process</option>
                                            @foreach($finishingProcesses as $process)
                                            <option value="{{ $process->id }}" {{ old('finishing_process_id', $finishingEntry->finishing_process_id) == $process->id ? 'selected' : '' }}>
                                                {{ $process->process_name }} ({{ $process->process_code }})
                                            </option>
                                            @endforeach
                                        </select>
                                        @error('finishing_process_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Operator Name <span class="text-danger">*</span></label>
                                        <input type="text" name="operator_name" class="form-control @error('operator_name') is-invalid @enderror" 
                                               value="{{ old('operator_name', $finishingEntry->operator_name) }}" required>
                                        @error('operator_name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quantity Information -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Quantity Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label class="form-label">Input Quantity</label>
                                        <input type="number" class="form-control" value="{{ $finishingEntry->input_quantity }}" readonly>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label class="form-label">Output Quantity</label>
                                        <input type="number" name="output_quantity" class="form-control @error('output_quantity') is-invalid @enderror" 
                                               value="{{ old('output_quantity', $finishingEntry->output_quantity) }}" min="0" id="output_quantity">
                                        @error('output_quantity')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label class="form-label">Defect Quantity</label>
                                        <input type="number" name="defect_quantity" class="form-control @error('defect_quantity') is-invalid @enderror" 
                                               value="{{ old('defect_quantity', $finishingEntry->defect_quantity) }}" min="0" id="defect_quantity">
                                        @error('defect_quantity')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label class="form-label">Rework Quantity</label>
                                        <input type="number" name="rework_quantity" class="form-control @error('rework_quantity') is-invalid @enderror" 
                                               value="{{ old('rework_quantity', $finishingEntry->rework_quantity) }}" min="0" id="rework_quantity">
                                        @error('rework_quantity')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quality Control -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Quality Control</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">QC Status <span class="text-danger">*</span></label>
                                        <select name="qc_status" class="form-select @error('qc_status') is-invalid @enderror" required>
                                            <option value="pending" {{ old('qc_status', $finishingEntry->qc_status) == 'pending' ? 'selected' : '' }}>Pending</option>
                                            <option value="pass" {{ old('qc_status', $finishingEntry->qc_status) == 'pass' ? 'selected' : '' }}>Pass</option>
                                            <option value="fail" {{ old('qc_status', $finishingEntry->qc_status) == 'fail' ? 'selected' : '' }}>Fail</option>
                                            <option value="rework" {{ old('qc_status', $finishingEntry->qc_status) == 'rework' ? 'selected' : '' }}>Rework</option>
                                        </select>
                                        @error('qc_status')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Status <span class="text-danger">*</span></label>
                                        <select name="status" class="form-select @error('status') is-invalid @enderror" required>
                                            <option value="input" {{ old('status', $finishingEntry->status) == 'input' ? 'selected' : '' }}>Input</option>
                                            <option value="in_progress" {{ old('status', $finishingEntry->status) == 'in_progress' ? 'selected' : '' }}>In Progress</option>
                                            <option value="completed" {{ old('status', $finishingEntry->status) == 'completed' ? 'selected' : '' }}>Completed</option>
                                            <option value="rejected" {{ old('status', $finishingEntry->status) == 'rejected' ? 'selected' : '' }}>Rejected</option>
                                        </select>
                                        @error('status')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Defect Details</label>
                                <textarea name="defect_details" class="form-control @error('defect_details') is-invalid @enderror" rows="3" 
                                          placeholder="Enter defect details...">{{ old('defect_details', is_array($finishingEntry->defect_details) ? implode(', ', $finishingEntry->defect_details) : $finishingEntry->defect_details) }}</textarea>
                                @error('defect_details')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Notes</label>
                                <textarea name="notes" class="form-control @error('notes') is-invalid @enderror" rows="3" 
                                          placeholder="Enter additional notes...">{{ old('notes', $finishingEntry->notes) }}</textarea>
                                @error('notes')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Timing Information -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Timing Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Start Date & Time</label>
                                        <input type="datetime-local" name="start_datetime" class="form-control @error('start_datetime') is-invalid @enderror" 
                                               value="{{ old('start_datetime', $finishingEntry->start_datetime ? $finishingEntry->start_datetime->format('Y-m-d\TH:i') : '') }}">
                                        @error('start_datetime')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">End Date & Time</label>
                                        <input type="datetime-local" name="end_datetime" class="form-control @error('end_datetime') is-invalid @enderror" 
                                               value="{{ old('end_datetime', $finishingEntry->end_datetime ? $finishingEntry->end_datetime->format('Y-m-d\TH:i') : '') }}">
                                        @error('end_datetime')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <!-- Actions -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Actions</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Update Entry
                                </button>
                                <a href="{{ route('production.finishing.show', $finishingEntry) }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times"></i> Cancel
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Current Information -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Current Status</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label"><strong>Current Status:</strong></label>
                                <div>
                                    <span class="badge bg-{{ 
                                        $finishingEntry->status == 'completed' ? 'success' : 
                                        ($finishingEntry->status == 'in_progress' ? 'primary' : 
                                        ($finishingEntry->status == 'rejected' ? 'danger' : 'secondary')) 
                                    }}">
                                        {{ ucfirst(str_replace('_', ' ', $finishingEntry->status)) }}
                                    </span>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label"><strong>QC Status:</strong></label>
                                <div>
                                    <span class="badge bg-{{ 
                                        $finishingEntry->qc_status == 'pass' ? 'success' : 
                                        ($finishingEntry->qc_status == 'fail' ? 'danger' : 
                                        ($finishingEntry->qc_status == 'rework' ? 'warning' : 'secondary')) 
                                    }}">
                                        {{ ucfirst($finishingEntry->qc_status) }}
                                    </span>
                                </div>
                            </div>
                            
                            @if($finishingEntry->efficiency_achieved)
                            <div class="mb-3">
                                <label class="form-label"><strong>Current Efficiency:</strong></label>
                                <div class="progress" style="height: 20px;">
                                    <div class="progress-bar bg-info" role="progressbar" style="width: {{ min($finishingEntry->efficiency_achieved, 100) }}%">
                                        {{ number_format($finishingEntry->efficiency_achieved, 1) }}%
                                    </div>
                                </div>
                            </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
@endsection

@push('css')
<style>
.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.badge {
    font-size: 0.75em;
}
</style>
@endpush

@push('js')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const outputQtyInput = document.getElementById('output_quantity');
    const defectQtyInput = document.getElementById('defect_quantity');
    const reworkQtyInput = document.getElementById('rework_quantity');

    function validateQuantities() {
        const inputQty = parseInt(document.querySelector('input[value="{{ $finishingEntry->input_quantity }}"]').value) || 0;
        const outputQty = parseInt(outputQtyInput.value) || 0;
        const defectQty = parseInt(defectQtyInput.value) || 0;
        const reworkQty = parseInt(reworkQtyInput.value) || 0;

        // Validate that output + defect + rework doesn't exceed input
        const total = outputQty + defectQty + reworkQty;
        if (total > inputQty) {
            alert('Total of output, defect, and rework quantities cannot exceed input quantity (' + inputQty + ')');
            return false;
        }
        
        return true;
    }

    [outputQtyInput, defectQtyInput, reworkQtyInput].forEach(input => {
        if (input) {
            input.addEventListener('change', validateQuantities);
        }
    });

    // Form validation on submit
    document.querySelector('form').addEventListener('submit', function(e) {
        if (!validateQuantities()) {
            e.preventDefault();
        }
    });
});
</script>
@endpush
