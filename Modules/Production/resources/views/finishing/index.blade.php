@extends('layouts.master')

@section('title', 'Finishing Section')

@section('main_content')
<div class="erp-state-overview-section">
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h2 class="page-title">Finishing Section</h2>
                    <div class="d-flex gap-2">
                        @can('production.finishing.create')
                        <a href="{{ route('production.finishing.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> New Finishing Entry
                        </a>
                        @endcan
                        @can('production.finishing.processes')
                        <a href="{{ route('production.finishing.processes') }}" class="btn btn-outline-info">
                            <i class="fas fa-cogs"></i> Manage Processes
                        </a>
                        @endcan
                        @can('production.exports')
                        <div class="dropdown">
                            <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-download"></i> Export
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#">
                                    <i class="fas fa-file-pdf text-danger"></i> PDF Report
                                </a></li>
                            </ul>
                        </div>
                        @endcan
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="GET" action="{{ route('production.finishing.index') }}" class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">Bundle Number</label>
                        <input type="text" name="bundle_number" class="form-control" value="{{ request('bundle_number') }}" placeholder="Enter bundle number">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Style Number</label>
                        <input type="text" name="style_number" class="form-control" value="{{ request('style_number') }}" placeholder="Enter style number">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Finishing Process</label>
                        <select name="finishing_process_id" class="form-select">
                            <option value="">All Processes</option>
                            @foreach($finishingProcesses as $process)
                            <option value="{{ $process->id }}" {{ request('finishing_process_id') == $process->id ? 'selected' : '' }}>
                                {{ $process->process_name }}
                            </option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">QC Status</label>
                        <select name="qc_status" class="form-select">
                            <option value="">All QC Status</option>
                            <option value="pass" {{ request('qc_status') == 'pass' ? 'selected' : '' }}>Pass</option>
                            <option value="fail" {{ request('qc_status') == 'fail' ? 'selected' : '' }}>Fail</option>
                            <option value="rework" {{ request('qc_status') == 'rework' ? 'selected' : '' }}>Rework</option>
                            <option value="pending" {{ request('qc_status') == 'pending' ? 'selected' : '' }}>Pending</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Status</label>
                        <select name="status" class="form-select">
                            <option value="">All Status</option>
                            <option value="input" {{ request('status') == 'input' ? 'selected' : '' }}>Input</option>
                            <option value="in_progress" {{ request('status') == 'in_progress' ? 'selected' : '' }}>In Progress</option>
                            <option value="completed" {{ request('status') == 'completed' ? 'selected' : '' }}>Completed</option>
                            <option value="rejected" {{ request('status') == 'rejected' ? 'selected' : '' }}>Rejected</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Start Date</label>
                        <input type="date" name="start_date" class="form-control" value="{{ request('start_date') }}">
                    </div>
                    <div class="col-md-6 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="fas fa-search"></i> Filter
                        </button>
                        <a href="{{ route('production.finishing.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i> Clear
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Finishing Entries Table -->
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>Bundle Number</th>
                                <th>Style Number</th>
                                <th>Process</th>
                                <th>Input Qty</th>
                                <th>Output Qty</th>
                                <th>Defects</th>
                                <th>Rework</th>
                                <th>QC Status</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($finishingEntries as $entry)
                            <tr>
                                <td>
                                    <a href="{{ route('production.finishing.show', $entry) }}" class="text-decoration-none">
                                        {{ $entry->bundle_number }}
                                    </a>
                                </td>
                                <td>{{ $entry->style_number }}</td>
                                <td>
                                    {{ $entry->finishingProcess->process_name ?? 'N/A' }}
                                    <br>
                                    <small class="text-muted">{{ $entry->finishingProcess->process_code ?? '' }}</small>
                                </td>
                                <td>{{ number_format($entry->input_quantity) }}</td>
                                <td>{{ number_format($entry->output_quantity ?? 0) }}</td>
                                <td>
                                    <span class="badge bg-{{ $entry->defect_quantity > 0 ? 'danger' : 'success' }}">
                                        {{ number_format($entry->defect_quantity ?? 0) }}
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-{{ $entry->rework_quantity > 0 ? 'warning' : 'success' }}">
                                        {{ number_format($entry->rework_quantity ?? 0) }}
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-{{ 
                                        $entry->qc_status == 'pass' ? 'success' : 
                                        ($entry->qc_status == 'fail' ? 'danger' : 
                                        ($entry->qc_status == 'rework' ? 'warning' : 'secondary')) 
                                    }}">
                                        {{ ucfirst($entry->qc_status) }}
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-{{ 
                                        $entry->status == 'completed' ? 'success' : 
                                        ($entry->status == 'in_progress' ? 'primary' : 
                                        ($entry->status == 'rejected' ? 'danger' : 'secondary')) 
                                    }}">
                                        {{ ucfirst(str_replace('_', ' ', $entry->status)) }}
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        @can('production.finishing.view')
                                        <a href="{{ route('production.finishing.show', $entry) }}" class="btn btn-outline-primary" title="View">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        @endcan
                                        @can('production.finishing.edit')
                                        <a href="{{ route('production.finishing.edit', $entry) }}" class="btn btn-outline-warning" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        @endcan
                                        @can('production.finishing.delete')
                                        @if($entry->status != 'completed')
                                        <form method="POST" action="{{ route('production.finishing.destroy', $entry) }}" class="d-inline">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-outline-danger" title="Delete" onclick="return confirm('Are you sure you want to delete this finishing entry?')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                        @endif
                                        @endcan
                                    </div>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="10" class="text-center py-4">
                                    <div class="text-muted">
                                        <i class="fas fa-check-circle fa-3x mb-3"></i>
                                        <p>No finishing entries found.</p>
                                        @can('production.finishing.create')
                                        <a href="{{ route('production.finishing.create') }}" class="btn btn-primary">
                                            Create First Finishing Entry
                                        </a>
                                        @endcan
                                    </div>
                                </td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                @if($finishingEntries->hasPages())
                <div class="d-flex justify-content-center mt-4">
                    {{ $finishingEntries->links() }}
                </div>
                @endif
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="row mt-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <h4>{{ $finishingEntries->where('status', 'input')->count() }}</h4>
                        <small>Input</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body text-center">
                        <h4>{{ $finishingEntries->where('status', 'in_progress')->count() }}</h4>
                        <small>In Progress</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <h4>{{ $finishingEntries->where('qc_status', 'pass')->count() }}</h4>
                        <small>QC Pass</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <h4>{{ number_format($finishingEntries->sum('output_quantity')) }}</h4>
                        <small>Total Output</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('css')
<style>
.table th {
    font-weight: 600;
    border-bottom: 2px solid #dee2e6;
}
.btn-group-sm > .btn {
    padding: 0.25rem 0.5rem;
}
</style>
@endpush
