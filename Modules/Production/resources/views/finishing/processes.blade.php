@extends('layouts.master')

@section('title', 'Finishing Processes Management')

@section('main_content')
<div class="erp-state-overview-section">
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="page-title">Finishing Processes Management</h2>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="{{ route('production.finishing.index') }}">Finishing Section</a></li>
                                <li class="breadcrumb-item active">Processes</li>
                            </ol>
                        </nav>
                    </div>
                    <div class="d-flex gap-2">
                        @can('production.finishing.create')
                        <a href="{{ route('production.finishing.processes.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Add Process
                        </a>
                        @endcan
                        <a href="{{ route('production.finishing.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Finishing
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Process Categories -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <h4>{{ $finishingProcesses->where('category', 'trimming')->count() }}</h4>
                        <small>Trimming Processes</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <h4>{{ $finishingProcesses->where('category', 'pressing')->count() }}</h4>
                        <small>Pressing Processes</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <h4>{{ $finishingProcesses->where('category', 'packing')->count() }}</h4>
                        <small>Packing Processes</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body text-center">
                        <h4>{{ $finishingProcesses->where('is_active', true)->count() }}</h4>
                        <small>Active Processes</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Processes Table -->
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>Process Code</th>
                                <th>Process Name</th>
                                <th>Category</th>
                                <th>Description</th>
                                <th>Standard Time (min)</th>
                                <th>Sequence Order</th>
                                <th>Status</th>
                                <th>Usage Count</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($finishingProcesses as $process)
                            <tr>
                                <td>
                                    <strong>{{ $process->process_code }}</strong>
                                </td>
                                <td>{{ $process->process_name }}</td>
                                <td>
                                    <span class="badge bg-{{ 
                                        $process->category == 'trimming' ? 'primary' : 
                                        ($process->category == 'pressing' ? 'success' : 
                                        ($process->category == 'packing' ? 'info' : 'secondary')) 
                                    }}">
                                        {{ ucfirst($process->category) }}
                                    </span>
                                </td>
                                <td>
                                    <small>{{ Str::limit($process->description, 50) }}</small>
                                </td>
                                <td class="text-center">
                                    @if($process->standard_time_minutes)
                                    {{ number_format($process->standard_time_minutes, 1) }} min
                                    @else
                                    <span class="text-muted">N/A</span>
                                    @endif
                                </td>
                                <td class="text-center">{{ $process->sequence_order }}</td>
                                <td>
                                    <span class="badge bg-{{ $process->is_active ? 'success' : 'secondary' }}">
                                        {{ $process->is_active ? 'Active' : 'Inactive' }}
                                    </span>
                                </td>
                                <td class="text-center">
                                    <span class="badge bg-info">
                                        {{ $process->finishingEntries->count() }}
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ route('production.finishing.processes.show', $process) }}" class="btn btn-outline-primary" title="View">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        @can('production.finishing.edit')
                                        <a href="{{ route('production.finishing.processes.edit', $process) }}" class="btn btn-outline-warning" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        @endcan
                                        @can('production.finishing.delete')
                                        @if($process->finishingEntries->count() == 0)
                                        <form method="POST" action="{{ route('production.finishing.processes.destroy', $process) }}" class="d-inline">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-outline-danger" title="Delete" onclick="return confirm('Are you sure you want to delete this process?')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                        @endif
                                        @endcan
                                    </div>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="9" class="text-center py-4">
                                    <div class="text-muted">
                                        <i class="fas fa-cogs fa-3x mb-3"></i>
                                        <p>No finishing processes found.</p>
                                        @can('production.finishing.create')
                                        <a href="{{ route('production.finishing.processes.create') }}" class="btn btn-primary">
                                            Add First Process
                                        </a>
                                        @endcan
                                    </div>
                                </td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Process Flow Diagram -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Process Flow</h5>
            </div>
            <div class="card-body">
                <div class="process-flow">
                    @foreach($finishingProcesses->where('is_active', true)->sortBy('sequence_order') as $index => $process)
                    <div class="process-step">
                        <div class="step-number">{{ $process->sequence_order }}</div>
                        <div class="step-content">
                            <h6>{{ $process->process_name }}</h6>
                            <small class="text-muted">{{ $process->process_code }}</small>
                            @if($process->standard_time_minutes)
                            <br><small class="text-info">{{ $process->standard_time_minutes }} min</small>
                            @endif
                        </div>
                        @if(!$loop->last)
                        <div class="step-arrow">
                            <i class="fas fa-arrow-right"></i>
                        </div>
                        @endif
                    </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('css')
<style>
.process-flow {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 20px;
    padding: 20px 0;
}

.process-step {
    display: flex;
    align-items: center;
    gap: 10px;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #007bff;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 14px;
}

.step-content {
    text-align: center;
    min-width: 120px;
}

.step-content h6 {
    margin: 0;
    font-size: 12px;
    font-weight: 600;
}

.step-arrow {
    color: #6c757d;
    font-size: 18px;
}

.table th {
    font-weight: 600;
    border-bottom: 2px solid #dee2e6;
}

.btn-group-sm > .btn {
    padding: 0.25rem 0.5rem;
}

@media (max-width: 768px) {
    .process-flow {
        flex-direction: column;
    }
    
    .step-arrow {
        transform: rotate(90deg);
    }
}
</style>
@endpush
