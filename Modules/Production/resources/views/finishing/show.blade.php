@extends('layouts.master')

@section('title', 'Finishing Entry Details')

@section('main_content')
<div class="erp-state-overview-section">
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="page-title">Finishing Entry Details</h2>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="{{ route('production.finishing.index') }}">Finishing Section</a></li>
                                <li class="breadcrumb-item active">{{ $finishingEntry->bundle_number }}</li>
                            </ol>
                        </nav>
                    </div>
                    <div class="d-flex gap-2">
                        @can('production.finishing.edit')
                        <a href="{{ route('production.finishing.edit', $finishingEntry) }}" class="btn btn-warning">
                            <i class="fas fa-edit"></i> Edit Entry
                        </a>
                        @endcan
                        <a href="{{ route('production.finishing.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> Back to List
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Basic Information -->
            <div class="col-md-8">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Basic Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>Bundle Number:</strong></td>
                                        <td>{{ $finishingEntry->bundle_number }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Style Number:</strong></td>
                                        <td>{{ $finishingEntry->style_number }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>PO Number:</strong></td>
                                        <td>{{ $finishingEntry->po_number }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Color:</strong></td>
                                        <td>{{ $finishingEntry->color }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Size:</strong></td>
                                        <td>{{ $finishingEntry->size }}</td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>Finishing Process:</strong></td>
                                        <td>{{ $finishingEntry->finishingProcess->process_name ?? 'N/A' }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Process Code:</strong></td>
                                        <td>{{ $finishingEntry->finishingProcess->process_code ?? 'N/A' }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Operator Name:</strong></td>
                                        <td>{{ $finishingEntry->operator_name }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Start Date:</strong></td>
                                        <td>{{ $finishingEntry->start_datetime ? $finishingEntry->start_datetime->format('M d, Y H:i') : 'Not set' }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>End Date:</strong></td>
                                        <td>{{ $finishingEntry->end_datetime ? $finishingEntry->end_datetime->format('M d, Y H:i') : 'Not completed' }}</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quantity Information -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Quantity Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4 class="text-primary">{{ number_format($finishingEntry->input_quantity) }}</h4>
                                    <small class="text-muted">Input Quantity</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4 class="text-success">{{ number_format($finishingEntry->output_quantity ?? 0) }}</h4>
                                    <small class="text-muted">Output Quantity</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4 class="text-danger">{{ number_format($finishingEntry->defect_quantity ?? 0) }}</h4>
                                    <small class="text-muted">Defect Quantity</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4 class="text-warning">{{ number_format($finishingEntry->rework_quantity ?? 0) }}</h4>
                                    <small class="text-muted">Rework Quantity</small>
                                </div>
                            </div>
                        </div>
                        
                        @if($finishingEntry->output_quantity > 0)
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <div class="progress mb-2" style="height: 20px;">
                                    @php
                                        $goodQuantity = $finishingEntry->output_quantity - ($finishingEntry->defect_quantity ?? 0);
                                        $goodPercentage = ($goodQuantity / $finishingEntry->output_quantity) * 100;
                                    @endphp
                                    <div class="progress-bar bg-success" role="progressbar" style="width: {{ $goodPercentage }}%">
                                        {{ number_format($goodPercentage, 1) }}% Good
                                    </div>
                                </div>
                                <small class="text-muted">Quality Rate</small>
                            </div>
                            <div class="col-md-6">
                                @if($finishingEntry->efficiency_achieved)
                                <div class="progress mb-2" style="height: 20px;">
                                    <div class="progress-bar bg-info" role="progressbar" style="width: {{ min($finishingEntry->efficiency_achieved, 100) }}%">
                                        {{ number_format($finishingEntry->efficiency_achieved, 1) }}%
                                    </div>
                                </div>
                                <small class="text-muted">Efficiency Achieved</small>
                                @endif
                            </div>
                        </div>
                        @endif
                    </div>
                </div>

                <!-- Quality Control Information -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Quality Control</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label"><strong>QC Status:</strong></label>
                                    <div>
                                        <span class="badge bg-{{ 
                                            $finishingEntry->qc_status == 'pass' ? 'success' : 
                                            ($finishingEntry->qc_status == 'fail' ? 'danger' : 
                                            ($finishingEntry->qc_status == 'rework' ? 'warning' : 'secondary')) 
                                        }} fs-6">
                                            {{ ucfirst($finishingEntry->qc_status) }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label"><strong>Status:</strong></label>
                                    <div>
                                        <span class="badge bg-{{ 
                                            $finishingEntry->status == 'completed' ? 'success' : 
                                            ($finishingEntry->status == 'in_progress' ? 'primary' : 
                                            ($finishingEntry->status == 'rejected' ? 'danger' : 'secondary')) 
                                        }} fs-6">
                                            {{ ucfirst(str_replace('_', ' ', $finishingEntry->status)) }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        @if($finishingEntry->measurements)
                        <div class="mb-3">
                            <label class="form-label"><strong>Measurements:</strong></label>
                            <div class="table-responsive">
                                <table class="table table-sm table-bordered">
                                    <thead class="table-light">
                                        <tr>
                                            @foreach(array_keys($finishingEntry->measurements) as $measurement)
                                            <th>{{ ucfirst(str_replace('_', ' ', $measurement)) }}</th>
                                            @endforeach
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            @foreach($finishingEntry->measurements as $value)
                                            <td>{{ $value }}</td>
                                            @endforeach
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        @endif
                        
                        @if($finishingEntry->defect_details)
                        <div class="mb-3">
                            <label class="form-label"><strong>Defect Details:</strong></label>
                            <div class="alert alert-warning">
                                @if(is_array($finishingEntry->defect_details))
                                    <ul class="mb-0">
                                        @foreach($finishingEntry->defect_details as $defect)
                                        <li>{{ $defect }}</li>
                                        @endforeach
                                    </ul>
                                @else
                                    {{ $finishingEntry->defect_details }}
                                @endif
                            </div>
                        </div>
                        @endif
                        
                        @if($finishingEntry->notes)
                        <div class="mb-3">
                            <label class="form-label"><strong>Notes:</strong></label>
                            <div class="alert alert-info">
                                {{ $finishingEntry->notes }}
                            </div>
                        </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Sidebar Information -->
            <div class="col-md-4">
                <!-- Status Card -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Entry Status</h5>
                    </div>
                    <div class="card-body text-center">
                        <div class="mb-3">
                            <span class="badge bg-{{ 
                                $finishingEntry->status == 'completed' ? 'success' : 
                                ($finishingEntry->status == 'in_progress' ? 'primary' : 
                                ($finishingEntry->status == 'rejected' ? 'danger' : 'secondary')) 
                            }} fs-5">
                                {{ ucfirst(str_replace('_', ' ', $finishingEntry->status)) }}
                            </span>
                        </div>
                        @if($finishingEntry->actual_time_minutes)
                        <div class="mb-3">
                            <h6>Processing Time</h6>
                            <p class="text-muted">{{ number_format($finishingEntry->actual_time_minutes) }} minutes</p>
                        </div>
                        @endif
                    </div>
                </div>

                <!-- Source Information -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Source Information</h5>
                    </div>
                    <div class="card-body">
                        @if($finishingEntry->sewingEntry)
                        <div class="mb-3">
                            <label class="form-label"><strong>From Sewing Entry:</strong></label>
                            <p class="mb-1">Bundle: {{ $finishingEntry->sewingEntry->bundleTicket->bundle_number ?? 'N/A' }}</p>
                            <p class="mb-1">Output: {{ number_format($finishingEntry->sewingEntry->output_quantity ?? 0) }} pieces</p>
                            <p class="mb-0">
                                <a href="{{ route('production.sewing.show', $finishingEntry->sewingEntry) }}" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-eye"></i> View Sewing Entry
                                </a>
                            </p>
                        </div>
                        @endif
                    </div>
                </div>

                <!-- Actions -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Actions</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            @can('production.finishing.edit')
                            <a href="{{ route('production.finishing.edit', $finishingEntry) }}" class="btn btn-warning">
                                <i class="fas fa-edit"></i> Edit Entry
                            </a>
                            @endcan
                            
                            @if($finishingEntry->status == 'completed' && $finishingEntry->qc_status == 'pass')
                            <a href="{{ route('production.washing.create') }}?finishing_entry={{ $finishingEntry->id }}" class="btn btn-info">
                                <i class="fas fa-arrow-right"></i> Send to Washing
                            </a>
                            @endif
                            
                            <a href="{{ route('production.finishing.index') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-list"></i> Back to List
                            </a>
                            
                            @can('production.finishing.delete')
                            @if($finishingEntry->status != 'completed')
                            <form method="POST" action="{{ route('production.finishing.destroy', $finishingEntry) }}" class="d-inline">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn btn-outline-danger w-100" onclick="return confirm('Are you sure you want to delete this finishing entry?')">
                                    <i class="fas fa-trash"></i> Delete Entry
                                </button>
                            </form>
                            @endif
                            @endcan
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('css')
<style>
.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.badge {
    font-size: 0.75em;
}

.table-borderless td {
    border: none;
    padding: 0.25rem 0.5rem;
}

.progress {
    background-color: #e9ecef;
}
</style>
@endpush
