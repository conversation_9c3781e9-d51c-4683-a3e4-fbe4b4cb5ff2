@extends('layouts.master')

@section('title', 'Create Production Plan')

@section('main_content')
<div class="erp-state-overview-section">
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h2 class="page-title">Create Production Plan</h2>
                    <a href="{{ route('production.planning.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Back to List
                    </a>
                </div>
            </div>
        </div>

        <!-- Form -->
        <div class="card">
            <div class="card-body">
                <form method="POST" action="{{ route('production.planning.store') }}">
                    @csrf
                    
                    <div class="row">
                        <!-- Basic Information -->
                        <div class="col-md-6">
                            <h5 class="mb-3">Basic Information</h5>
                            
                            <div class="mb-3">
                                <label class="form-label">Order ID <small class="text-muted">(Optional)</small></label>
                                <input type="number" name="order_id" class="form-control @error('order_id') is-invalid @enderror" value="{{ old('order_id') }}">
                                @error('order_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Style Number <span class="text-danger">*</span></label>
                                <input type="text" name="style_number" class="form-control @error('style_number') is-invalid @enderror" value="{{ old('style_number') }}" required>
                                @error('style_number')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="mb-3">
                                <label class="form-label">PO Number <span class="text-danger">*</span></label>
                                <input type="text" name="po_number" class="form-control @error('po_number') is-invalid @enderror" value="{{ old('po_number') }}" required>
                                @error('po_number')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Target Quantity <span class="text-danger">*</span></label>
                                <input type="number" name="target_quantity" class="form-control @error('target_quantity') is-invalid @enderror" value="{{ old('target_quantity') }}" min="1" required>
                                @error('target_quantity')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Production Details -->
                        <div class="col-md-6">
                            <h5 class="mb-3">Production Details</h5>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Start Date <span class="text-danger">*</span></label>
                                        <input type="date" name="start_date" class="form-control @error('start_date') is-invalid @enderror" value="{{ old('start_date') }}" required>
                                        @error('start_date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">End Date <span class="text-danger">*</span></label>
                                        <input type="date" name="end_date" class="form-control @error('end_date') is-invalid @enderror" value="{{ old('end_date') }}" required>
                                        @error('end_date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Line Capacity <span class="text-danger">*</span></label>
                                        <input type="number" name="line_capacity" class="form-control @error('line_capacity') is-invalid @enderror" value="{{ old('line_capacity') }}" min="0" required>
                                        @error('line_capacity')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Efficiency % <span class="text-danger">*</span></label>
                                        <input type="number" name="efficiency_percentage" class="form-control @error('efficiency_percentage') is-invalid @enderror" value="{{ old('efficiency_percentage', 85) }}" min="0" max="200" step="0.01" required>
                                        @error('efficiency_percentage')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Fabric Wastage % <span class="text-danger">*</span></label>
                                <input type="number" name="fabric_wastage_percentage" class="form-control @error('fabric_wastage_percentage') is-invalid @enderror" value="{{ old('fabric_wastage_percentage', 5) }}" min="0" max="50" step="0.01" required>
                                @error('fabric_wastage_percentage')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Status <span class="text-danger">*</span></label>
                                <select name="status" class="form-select @error('status') is-invalid @enderror" required>
                                    <option value="draft" {{ old('status') == 'draft' ? 'selected' : '' }}>Draft</option>
                                    <option value="approved" {{ old('status') == 'approved' ? 'selected' : '' }}>Approved</option>
                                </select>
                                @error('status')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Fabric Requirements -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <h5 class="mb-3">Fabric Requirements</h5>
                            <div id="fabric-requirements">
                                <div class="fabric-requirement-row row mb-2">
                                    <div class="col-md-4">
                                        <input type="text" name="fabric_requirements[0][fabric_type]" class="form-control" placeholder="Fabric Type">
                                    </div>
                                    <div class="col-md-3">
                                        <input type="number" name="fabric_requirements[0][quantity]" class="form-control" placeholder="Quantity" step="0.01">
                                    </div>
                                    <div class="col-md-3">
                                        <input type="text" name="fabric_requirements[0][unit]" class="form-control" placeholder="Unit (yards/meters)">
                                    </div>
                                    <div class="col-md-2">
                                        <button type="button" class="btn btn-outline-danger remove-fabric-row" disabled>
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <button type="button" class="btn btn-outline-primary" id="add-fabric-row">
                                <i class="fas fa-plus"></i> Add Fabric
                            </button>
                        </div>
                    </div>

                    <!-- Accessories Requirements -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <h5 class="mb-3">Accessories Requirements</h5>
                            <div id="accessories-requirements">
                                <div class="accessory-requirement-row row mb-2">
                                    <div class="col-md-4">
                                        <input type="text" name="accessories_requirements[0][accessory_type]" class="form-control" placeholder="Accessory Type">
                                    </div>
                                    <div class="col-md-3">
                                        <input type="number" name="accessories_requirements[0][quantity]" class="form-control" placeholder="Quantity" step="0.01">
                                    </div>
                                    <div class="col-md-3">
                                        <input type="text" name="accessories_requirements[0][unit]" class="form-control" placeholder="Unit">
                                    </div>
                                    <div class="col-md-2">
                                        <button type="button" class="btn btn-outline-danger remove-accessory-row" disabled>
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <button type="button" class="btn btn-outline-primary" id="add-accessory-row">
                                <i class="fas fa-plus"></i> Add Accessory
                            </button>
                        </div>
                    </div>

                    <!-- Notes -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="mb-3">
                                <label class="form-label">Notes</label>
                                <textarea name="notes" class="form-control @error('notes') is-invalid @enderror" rows="3">{{ old('notes') }}</textarea>
                                @error('notes')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Submit Buttons -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Create Production Plan
                                </button>
                                <a href="{{ route('production.planning.index') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times"></i> Cancel
                                </a>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('js')
<script>
let fabricRowIndex = 1;
let accessoryRowIndex = 1;

// Add fabric requirement row
document.getElementById('add-fabric-row').addEventListener('click', function() {
    const container = document.getElementById('fabric-requirements');
    const newRow = document.createElement('div');
    newRow.className = 'fabric-requirement-row row mb-2';
    newRow.innerHTML = `
        <div class="col-md-4">
            <input type="text" name="fabric_requirements[${fabricRowIndex}][fabric_type]" class="form-control" placeholder="Fabric Type">
        </div>
        <div class="col-md-3">
            <input type="number" name="fabric_requirements[${fabricRowIndex}][quantity]" class="form-control" placeholder="Quantity" step="0.01">
        </div>
        <div class="col-md-3">
            <input type="text" name="fabric_requirements[${fabricRowIndex}][unit]" class="form-control" placeholder="Unit (yards/meters)">
        </div>
        <div class="col-md-2">
            <button type="button" class="btn btn-outline-danger remove-fabric-row">
                <i class="fas fa-trash"></i>
            </button>
        </div>
    `;
    container.appendChild(newRow);
    fabricRowIndex++;
    updateRemoveButtons();
});

// Add accessory requirement row
document.getElementById('add-accessory-row').addEventListener('click', function() {
    const container = document.getElementById('accessories-requirements');
    const newRow = document.createElement('div');
    newRow.className = 'accessory-requirement-row row mb-2';
    newRow.innerHTML = `
        <div class="col-md-4">
            <input type="text" name="accessories_requirements[${accessoryRowIndex}][accessory_type]" class="form-control" placeholder="Accessory Type">
        </div>
        <div class="col-md-3">
            <input type="number" name="accessories_requirements[${accessoryRowIndex}][quantity]" class="form-control" placeholder="Quantity" step="0.01">
        </div>
        <div class="col-md-3">
            <input type="text" name="accessories_requirements[${accessoryRowIndex}][unit]" class="form-control" placeholder="Unit">
        </div>
        <div class="col-md-2">
            <button type="button" class="btn btn-outline-danger remove-accessory-row">
                <i class="fas fa-trash"></i>
            </button>
        </div>
    `;
    container.appendChild(newRow);
    accessoryRowIndex++;
    updateRemoveButtons();
});

// Remove fabric/accessory rows
document.addEventListener('click', function(e) {
    if (e.target.closest('.remove-fabric-row')) {
        e.target.closest('.fabric-requirement-row').remove();
        updateRemoveButtons();
    }
    if (e.target.closest('.remove-accessory-row')) {
        e.target.closest('.accessory-requirement-row').remove();
        updateRemoveButtons();
    }
});

function updateRemoveButtons() {
    const fabricRows = document.querySelectorAll('.fabric-requirement-row');
    const accessoryRows = document.querySelectorAll('.accessory-requirement-row');
    
    fabricRows.forEach((row, index) => {
        const removeBtn = row.querySelector('.remove-fabric-row');
        removeBtn.disabled = fabricRows.length === 1;
    });
    
    accessoryRows.forEach((row, index) => {
        const removeBtn = row.querySelector('.remove-accessory-row');
        removeBtn.disabled = accessoryRows.length === 1;
    });
}
</script>
@endpush
