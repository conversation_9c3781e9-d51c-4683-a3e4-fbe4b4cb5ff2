@extends('layouts.master')

@section('title', 'Edit Production Plan')

@section('main_content')
<div class="erp-state-overview-section">
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="page-title">Edit Production Plan</h2>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="{{ route('production.planning.index') }}">Production Planning</a></li>
                                <li class="breadcrumb-item"><a href="{{ route('production.planning.show', $productionPlan) }}">{{ $productionPlan->plan_number }}</a></li>
                                <li class="breadcrumb-item active">Edit</li>
                            </ol>
                        </nav>
                    </div>
                    <div>
                        <a href="{{ route('production.planning.show', $productionPlan) }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Details
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Edit Form -->
        <form method="POST" action="{{ route('production.planning.update', $productionPlan) }}">
            @csrf
            @method('PUT')
            
            <div class="row">
                <div class="col-md-8">
                    <!-- Basic Information -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Basic Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Plan Number</label>
                                        <input type="text" class="form-control" value="{{ $productionPlan->plan_number }}" readonly>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Status</label>
                                        <select name="status" class="form-select @error('status') is-invalid @enderror">
                                            <option value="draft" {{ $productionPlan->status == 'draft' ? 'selected' : '' }}>Draft</option>
                                            <option value="approved" {{ $productionPlan->status == 'approved' ? 'selected' : '' }}>Approved</option>
                                            <option value="in_progress" {{ $productionPlan->status == 'in_progress' ? 'selected' : '' }}>In Progress</option>
                                            <option value="completed" {{ $productionPlan->status == 'completed' ? 'selected' : '' }}>Completed</option>
                                            <option value="cancelled" {{ $productionPlan->status == 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                                        </select>
                                        @error('status')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Style Number <span class="text-danger">*</span></label>
                                        <input type="text" name="style_number" class="form-control @error('style_number') is-invalid @enderror" 
                                               value="{{ old('style_number', $productionPlan->style_number) }}" required>
                                        @error('style_number')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">PO Number <span class="text-danger">*</span></label>
                                        <input type="text" name="po_number" class="form-control @error('po_number') is-invalid @enderror" 
                                               value="{{ old('po_number', $productionPlan->po_number) }}" required>
                                        @error('po_number')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">Target Quantity <span class="text-danger">*</span></label>
                                        <input type="number" name="target_quantity" class="form-control @error('target_quantity') is-invalid @enderror" 
                                               value="{{ old('target_quantity', $productionPlan->target_quantity) }}" required min="1">
                                        @error('target_quantity')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">Start Date <span class="text-danger">*</span></label>
                                        <input type="date" name="start_date" class="form-control @error('start_date') is-invalid @enderror" 
                                               value="{{ old('start_date', $productionPlan->start_date->format('Y-m-d')) }}" required>
                                        @error('start_date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">End Date <span class="text-danger">*</span></label>
                                        <input type="date" name="end_date" class="form-control @error('end_date') is-invalid @enderror" 
                                               value="{{ old('end_date', $productionPlan->end_date->format('Y-m-d')) }}" required>
                                        @error('end_date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">Line Capacity (pcs/day) <span class="text-danger">*</span></label>
                                        <input type="number" name="line_capacity" class="form-control @error('line_capacity') is-invalid @enderror" 
                                               value="{{ old('line_capacity', $productionPlan->line_capacity) }}" required min="1">
                                        @error('line_capacity')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">Efficiency Target (%) <span class="text-danger">*</span></label>
                                        <input type="number" name="efficiency_percentage" class="form-control @error('efficiency_percentage') is-invalid @enderror" 
                                               value="{{ old('efficiency_percentage', $productionPlan->efficiency_percentage) }}" required min="1" max="100" step="0.1">
                                        @error('efficiency_percentage')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">SMV (Standard Minute Value)</label>
                                        <input type="number" name="smv" class="form-control @error('smv') is-invalid @enderror" 
                                               value="{{ old('smv', $productionPlan->smv) }}" step="0.1" min="0">
                                        @error('smv')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Fabric Requirements -->
                    <div class="card mb-4">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">Fabric Requirements</h5>
                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="addFabricRow()">
                                <i class="fas fa-plus"></i> Add Fabric
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label">Fabric Wastage Percentage <span class="text-danger">*</span></label>
                                    <input type="number" name="fabric_wastage_percentage" class="form-control @error('fabric_wastage_percentage') is-invalid @enderror" 
                                           value="{{ old('fabric_wastage_percentage', $productionPlan->fabric_wastage_percentage) }}" required min="0" max="50" step="0.1">
                                    @error('fabric_wastage_percentage')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div id="fabric-requirements">
                                @if($productionPlan->fabric_requirements)
                                @foreach($productionPlan->fabric_requirements as $index => $fabric)
                                <div class="fabric-row row mb-2">
                                    <div class="col-md-4">
                                        <input type="text" name="fabric_requirements[{{ $index }}][fabric_type]" class="form-control" 
                                               placeholder="Fabric Type" value="{{ $fabric['fabric_type'] ?? '' }}">
                                    </div>
                                    <div class="col-md-3">
                                        <input type="number" name="fabric_requirements[{{ $index }}][quantity]" class="form-control" 
                                               placeholder="Quantity" value="{{ $fabric['quantity'] ?? '' }}" step="0.01">
                                    </div>
                                    <div class="col-md-3">
                                        <select name="fabric_requirements[{{ $index }}][unit]" class="form-select">
                                            <option value="yards" {{ ($fabric['unit'] ?? '') == 'yards' ? 'selected' : '' }}>Yards</option>
                                            <option value="meters" {{ ($fabric['unit'] ?? '') == 'meters' ? 'selected' : '' }}>Meters</option>
                                            <option value="kg" {{ ($fabric['unit'] ?? '') == 'kg' ? 'selected' : '' }}>Kg</option>
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeFabricRow(this)">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                                @endforeach
                                @endif
                            </div>
                        </div>
                    </div>

                    <!-- Accessories Requirements -->
                    <div class="card mb-4">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">Accessories Requirements</h5>
                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="addAccessoryRow()">
                                <i class="fas fa-plus"></i> Add Accessory
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label">Accessories Wastage Percentage <span class="text-danger">*</span></label>
                                    <input type="number" name="accessories_wastage_percentage" class="form-control @error('accessories_wastage_percentage') is-invalid @enderror" 
                                           value="{{ old('accessories_wastage_percentage', $productionPlan->accessories_wastage_percentage) }}" required min="0" max="50" step="0.1">
                                    @error('accessories_wastage_percentage')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div id="accessories-requirements">
                                @if($productionPlan->accessories_requirements)
                                @foreach($productionPlan->accessories_requirements as $index => $accessory)
                                <div class="accessory-row row mb-2">
                                    <div class="col-md-4">
                                        <input type="text" name="accessories_requirements[{{ $index }}][accessory_type]" class="form-control" 
                                               placeholder="Accessory Type" value="{{ $accessory['accessory_type'] ?? '' }}">
                                    </div>
                                    <div class="col-md-3">
                                        <input type="number" name="accessories_requirements[{{ $index }}][quantity]" class="form-control" 
                                               placeholder="Quantity" value="{{ $accessory['quantity'] ?? '' }}">
                                    </div>
                                    <div class="col-md-3">
                                        <select name="accessories_requirements[{{ $index }}][unit]" class="form-select">
                                            <option value="pcs" {{ ($accessory['unit'] ?? '') == 'pcs' ? 'selected' : '' }}>Pieces</option>
                                            <option value="sets" {{ ($accessory['unit'] ?? '') == 'sets' ? 'selected' : '' }}>Sets</option>
                                            <option value="yards" {{ ($accessory['unit'] ?? '') == 'yards' ? 'selected' : '' }}>Yards</option>
                                            <option value="meters" {{ ($accessory['unit'] ?? '') == 'meters' ? 'selected' : '' }}>Meters</option>
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeAccessoryRow(this)">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                                @endforeach
                                @endif
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <!-- Actions -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Actions</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Update Production Plan
                                </button>
                                <a href="{{ route('production.planning.show', $productionPlan) }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times"></i> Cancel
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Notes -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Notes</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label">Special Instructions</label>
                                <textarea name="notes" class="form-control @error('notes') is-invalid @enderror" rows="4" 
                                          placeholder="Enter any special instructions or notes...">{{ old('notes', $productionPlan->notes) }}</textarea>
                                @error('notes')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
@endsection

@push('js')
<script>
let fabricIndex = {{ $productionPlan->fabric_requirements ? count($productionPlan->fabric_requirements) : 0 }};
let accessoryIndex = {{ $productionPlan->accessories_requirements ? count($productionPlan->accessories_requirements) : 0 }};

function addFabricRow() {
    const container = document.getElementById('fabric-requirements');
    const row = document.createElement('div');
    row.className = 'fabric-row row mb-2';
    row.innerHTML = `
        <div class="col-md-4">
            <input type="text" name="fabric_requirements[${fabricIndex}][fabric_type]" class="form-control" placeholder="Fabric Type">
        </div>
        <div class="col-md-3">
            <input type="number" name="fabric_requirements[${fabricIndex}][quantity]" class="form-control" placeholder="Quantity" step="0.01">
        </div>
        <div class="col-md-3">
            <select name="fabric_requirements[${fabricIndex}][unit]" class="form-select">
                <option value="yards">Yards</option>
                <option value="meters">Meters</option>
                <option value="kg">Kg</option>
            </select>
        </div>
        <div class="col-md-2">
            <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeFabricRow(this)">
                <i class="fas fa-trash"></i>
            </button>
        </div>
    `;
    container.appendChild(row);
    fabricIndex++;
}

function removeFabricRow(button) {
    button.closest('.fabric-row').remove();
}

function addAccessoryRow() {
    const container = document.getElementById('accessories-requirements');
    const row = document.createElement('div');
    row.className = 'accessory-row row mb-2';
    row.innerHTML = `
        <div class="col-md-4">
            <input type="text" name="accessories_requirements[${accessoryIndex}][accessory_type]" class="form-control" placeholder="Accessory Type">
        </div>
        <div class="col-md-3">
            <input type="number" name="accessories_requirements[${accessoryIndex}][quantity]" class="form-control" placeholder="Quantity">
        </div>
        <div class="col-md-3">
            <select name="accessories_requirements[${accessoryIndex}][unit]" class="form-select">
                <option value="pcs">Pieces</option>
                <option value="sets">Sets</option>
                <option value="yards">Yards</option>
                <option value="meters">Meters</option>
            </select>
        </div>
        <div class="col-md-2">
            <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeAccessoryRow(this)">
                <i class="fas fa-trash"></i>
            </button>
        </div>
    `;
    container.appendChild(row);
    accessoryIndex++;
}

function removeAccessoryRow(button) {
    button.closest('.accessory-row').remove();
}
</script>
@endpush
