@extends('layouts.master')

@section('title', 'Production Planning')

@section('main_content')
<div class="erp-state-overview-section">
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h2 class="page-title">Production Planning</h2>
                    <div class="d-flex gap-2">
                        @can('production.planning.create')
                        <a href="{{ route('production.planning.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> New Production Plan
                        </a>
                        @endcan
                        @can('production.planning.export')
                        <div class="dropdown">
                            <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-download"></i> Export
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{{ route('production.planning.export.pdf', request()->query()) }}">
                                    <i class="fas fa-file-pdf text-danger"></i> PDF
                                </a></li>
                                <li><a class="dropdown-item" href="{{ route('production.planning.export.excel', request()->query()) }}">
                                    <i class="fas fa-file-excel text-success"></i> Excel
                                </a></li>
                            </ul>
                        </div>
                        @endcan
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="GET" action="{{ route('production.planning.index') }}" class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">Status</label>
                        <select name="status" class="form-select">
                            <option value="">All Status</option>
                            <option value="draft" {{ request('status') == 'draft' ? 'selected' : '' }}>Draft</option>
                            <option value="approved" {{ request('status') == 'approved' ? 'selected' : '' }}>Approved</option>
                            <option value="in_progress" {{ request('status') == 'in_progress' ? 'selected' : '' }}>In Progress</option>
                            <option value="completed" {{ request('status') == 'completed' ? 'selected' : '' }}>Completed</option>
                            <option value="cancelled" {{ request('status') == 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Style Number</label>
                        <input type="text" name="style_number" class="form-control" value="{{ request('style_number') }}" placeholder="Enter style number">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">PO Number</label>
                        <input type="text" name="po_number" class="form-control" value="{{ request('po_number') }}" placeholder="Enter PO number">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Start Date</label>
                        <input type="date" name="start_date" class="form-control" value="{{ request('start_date') }}">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">End Date</label>
                        <input type="date" name="end_date" class="form-control" value="{{ request('end_date') }}">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Sort By</label>
                        <select name="sort_by" class="form-select">
                            <option value="created_at" {{ request('sort_by') == 'created_at' ? 'selected' : '' }}>Created Date</option>
                            <option value="start_date" {{ request('sort_by') == 'start_date' ? 'selected' : '' }}>Start Date</option>
                            <option value="end_date" {{ request('sort_by') == 'end_date' ? 'selected' : '' }}>End Date</option>
                            <option value="target_quantity" {{ request('sort_by') == 'target_quantity' ? 'selected' : '' }}>Target Quantity</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Sort Direction</label>
                        <select name="sort_direction" class="form-select">
                            <option value="desc" {{ request('sort_direction') == 'desc' ? 'selected' : '' }}>Descending</option>
                            <option value="asc" {{ request('sort_direction') == 'asc' ? 'selected' : '' }}>Ascending</option>
                        </select>
                    </div>
                    <div class="col-md-3 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="fas fa-search"></i> Filter
                        </button>
                        <a href="{{ route('production.planning.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i> Clear
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Production Plans Table -->
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>Plan Number</th>
                                <th>Style Number</th>
                                <th>PO Number</th>
                                <th>Target Qty</th>
                                <th>Start Date</th>
                                <th>End Date</th>
                                <th>Efficiency</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($productionPlans as $plan)
                            <tr>
                                <td>
                                    <a href="{{ route('production.planning.show', $plan) }}" class="text-decoration-none">
                                        {{ $plan->plan_number }}
                                    </a>
                                </td>
                                <td>{{ $plan->style_number }}</td>
                                <td>{{ $plan->po_number }}</td>
                                <td>{{ number_format($plan->target_quantity) }}</td>
                                <td>{{ $plan->start_date->format('M d, Y') }}</td>
                                <td>{{ $plan->end_date->format('M d, Y') }}</td>
                                <td>{{ $plan->efficiency_percentage }}%</td>
                                <td>
                                    <span class="badge bg-{{ 
                                        $plan->status == 'approved' ? 'success' : 
                                        ($plan->status == 'in_progress' ? 'primary' : 
                                        ($plan->status == 'completed' ? 'info' : 
                                        ($plan->status == 'cancelled' ? 'danger' : 'secondary'))) 
                                    }}">
                                        {{ ucfirst(str_replace('_', ' ', $plan->status)) }}
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        @can('production.planning.view')
                                        <a href="{{ route('production.planning.show', $plan) }}" class="btn btn-outline-primary" title="View">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        @endcan
                                        @can('production.planning.edit')
                                        <a href="{{ route('production.planning.edit', $plan) }}" class="btn btn-outline-warning" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        @endcan
                                        @can('production.planning.approve')
                                        @if($plan->status == 'draft')
                                        <form method="POST" action="{{ route('production.planning.approve', $plan) }}" class="d-inline">
                                            @csrf
                                            <button type="submit" class="btn btn-outline-success" title="Approve" onclick="return confirm('Are you sure you want to approve this plan?')">
                                                <i class="fas fa-check"></i>
                                            </button>
                                        </form>
                                        @endif
                                        @endcan
                                        @can('production.planning.delete')
                                        @if($plan->status == 'draft')
                                        <form method="POST" action="{{ route('production.planning.destroy', $plan) }}" class="d-inline">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-outline-danger" title="Delete" onclick="return confirm('Are you sure you want to delete this plan?')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                        @endif
                                        @endcan
                                    </div>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="9" class="text-center py-4">
                                    <div class="text-muted">
                                        <i class="fas fa-inbox fa-3x mb-3"></i>
                                        <p>No production plans found.</p>
                                        @can('production.planning.create')
                                        <a href="{{ route('production.planning.create') }}" class="btn btn-primary">
                                            Create First Production Plan
                                        </a>
                                        @endcan
                                    </div>
                                </td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                @if($productionPlans->hasPages())
                <div class="d-flex justify-content-center mt-4">
                    {{ $productionPlans->links() }}
                </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection

@push('css')
<style>
.table th {
    font-weight: 600;
    border-bottom: 2px solid #dee2e6;
}
.btn-group-sm > .btn {
    padding: 0.25rem 0.5rem;
}
</style>
@endpush
