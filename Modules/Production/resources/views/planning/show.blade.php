@extends('layouts.master')

@section('title', 'Production Plan Details')

@section('main_content')
<div class="erp-state-overview-section">
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="page-title">Production Plan Details</h2>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="{{ route('production.planning.index') }}">Production Planning</a></li>
                                <li class="breadcrumb-item active">{{ $productionPlan->plan_number }}</li>
                            </ol>
                        </nav>
                    </div>
                    <div class="d-flex gap-2">
                        @can('production.planning.edit')
                        <a href="{{ route('production.planning.edit', $productionPlan) }}" class="btn btn-warning">
                            <i class="fas fa-edit"></i> Edit Plan
                        </a>
                        @endcan
                        @can('production.planning.approve')
                        @if($productionPlan->status == 'draft')
                        <form method="POST" action="{{ route('production.planning.approve', $productionPlan) }}" class="d-inline">
                            @csrf
                            <button type="submit" class="btn btn-success" onclick="return confirm('Are you sure you want to approve this production plan?')">
                                <i class="fas fa-check"></i> Approve Plan
                            </button>
                        </form>
                        @endif
                        @endcan
                        @can('production.planning.export')
                        <div class="dropdown">
                            <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-download"></i> Export
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{{ route('production.planning.export.pdf', $productionPlan) }}">
                                    <i class="fas fa-file-pdf text-danger"></i> PDF
                                </a></li>
                                <li><a class="dropdown-item" href="{{ route('production.planning.export.excel', $productionPlan) }}">
                                    <i class="fas fa-file-excel text-success"></i> Excel
                                </a></li>
                            </ul>
                        </div>
                        @endcan
                        <a href="{{ route('production.planning.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> Back to List
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Plan Overview -->
        <div class="row mb-4">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Plan Overview</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>Plan Number:</strong></td>
                                        <td>{{ $productionPlan->plan_number }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Style Number:</strong></td>
                                        <td>{{ $productionPlan->style_number }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>PO Number:</strong></td>
                                        <td>{{ $productionPlan->po_number }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Target Quantity:</strong></td>
                                        <td>{{ number_format($productionPlan->target_quantity) }} pcs</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Daily Target:</strong></td>
                                        <td>{{ number_format($productionPlan->daily_target) }} pcs</td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>Start Date:</strong></td>
                                        <td>{{ $productionPlan->start_date->format('M d, Y') }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>End Date:</strong></td>
                                        <td>{{ $productionPlan->end_date->format('M d, Y') }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Duration:</strong></td>
                                        <td>{{ $productionPlan->duration_days }} days</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Line Capacity:</strong></td>
                                        <td>{{ number_format($productionPlan->line_capacity) }} pcs/day</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Efficiency Target:</strong></td>
                                        <td>{{ number_format($productionPlan->efficiency_percentage, 1) }}%</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Status & Progress</h5>
                    </div>
                    <div class="card-body text-center">
                        <div class="mb-3">
                            <span class="badge bg-{{ 
                                $productionPlan->status == 'approved' ? 'success' : 
                                ($productionPlan->status == 'in_progress' ? 'primary' : 
                                ($productionPlan->status == 'completed' ? 'info' : 'secondary')) 
                            }} fs-6">
                                {{ ucfirst(str_replace('_', ' ', $productionPlan->status)) }}
                            </span>
                        </div>
                        <div class="progress mb-3" style="height: 20px;">
                            <div class="progress-bar" role="progressbar" style="width: {{ $productionPlan->completion_percentage }}%">
                                {{ number_format($productionPlan->completion_percentage, 1) }}%
                            </div>
                        </div>
                        <small class="text-muted">Production Progress</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Fabric Requirements -->
        @if($productionPlan->fabric_requirements)
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Fabric Requirements</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Fabric Type</th>
                                <th>Required Quantity</th>
                                <th>Unit</th>
                                <th>Wastage %</th>
                                <th>Total with Wastage</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($productionPlan->fabric_requirements as $fabric)
                            <tr>
                                <td>{{ $fabric['fabric_type'] ?? 'N/A' }}</td>
                                <td>{{ number_format($fabric['quantity'] ?? 0, 2) }}</td>
                                <td>{{ $fabric['unit'] ?? 'yards' }}</td>
                                <td>{{ number_format($productionPlan->fabric_wastage_percentage, 1) }}%</td>
                                <td>{{ number_format(($fabric['quantity'] ?? 0) * (1 + $productionPlan->fabric_wastage_percentage / 100), 2) }}</td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        @endif

        <!-- Accessories Requirements -->
        @if($productionPlan->accessories_requirements)
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Accessories Requirements</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Accessory Type</th>
                                <th>Required Quantity</th>
                                <th>Unit</th>
                                <th>Wastage %</th>
                                <th>Total with Wastage</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($productionPlan->accessories_requirements as $accessory)
                            <tr>
                                <td>{{ $accessory['accessory_type'] ?? 'N/A' }}</td>
                                <td>{{ number_format($accessory['quantity'] ?? 0) }}</td>
                                <td>{{ $accessory['unit'] ?? 'pcs' }}</td>
                                <td>{{ number_format($productionPlan->accessories_wastage_percentage, 1) }}%</td>
                                <td>{{ number_format(($accessory['quantity'] ?? 0) * (1 + $productionPlan->accessories_wastage_percentage / 100)) }}</td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        @endif

        <!-- Related Records -->
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Cutting Plans</h5>
                    </div>
                    <div class="card-body">
                        @if($productionPlan->cuttingPlans->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Cutting Number</th>
                                        <th>Color</th>
                                        <th>Pieces</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($productionPlan->cuttingPlans as $cutting)
                                    <tr>
                                        <td><a href="{{ route('production.cutting.show', $cutting) }}">{{ $cutting->cutting_number }}</a></td>
                                        <td>{{ $cutting->color }}</td>
                                        <td>{{ number_format($cutting->total_pieces) }}</td>
                                        <td><span class="badge bg-secondary">{{ ucfirst($cutting->status) }}</span></td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                        @else
                        <p class="text-muted">No cutting plans created yet.</p>
                        @endif
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Production Timeline</h5>
                    </div>
                    <div class="card-body">
                        <div class="timeline">
                            <div class="timeline-item">
                                <div class="timeline-marker bg-primary"></div>
                                <div class="timeline-content">
                                    <h6>Plan Created</h6>
                                    <small class="text-muted">{{ $productionPlan->created_at->format('M d, Y H:i') }}</small>
                                </div>
                            </div>
                            @if($productionPlan->approved_at)
                            <div class="timeline-item">
                                <div class="timeline-marker bg-success"></div>
                                <div class="timeline-content">
                                    <h6>Plan Approved</h6>
                                    <small class="text-muted">{{ $productionPlan->approved_at->format('M d, Y H:i') }}</small>
                                </div>
                            </div>
                            @endif
                            <div class="timeline-item">
                                <div class="timeline-marker bg-info"></div>
                                <div class="timeline-content">
                                    <h6>Production Start</h6>
                                    <small class="text-muted">{{ $productionPlan->start_date->format('M d, Y') }}</small>
                                </div>
                            </div>
                            <div class="timeline-item">
                                <div class="timeline-marker bg-warning"></div>
                                <div class="timeline-content">
                                    <h6>Target Completion</h6>
                                    <small class="text-muted">{{ $productionPlan->end_date->format('M d, Y') }}</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('css')
<style>
.timeline {
    position: relative;
    padding-left: 30px;
}
.timeline-item {
    position: relative;
    margin-bottom: 20px;
}
.timeline-marker {
    position: absolute;
    left: -35px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
}
.timeline::before {
    content: '';
    position: absolute;
    left: -30px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}
</style>
@endpush
