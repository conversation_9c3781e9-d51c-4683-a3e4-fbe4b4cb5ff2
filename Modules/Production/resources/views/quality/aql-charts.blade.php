@extends('layouts.master')

@section('title', 'AQL Charts Management')

@section('main_content')
<div class="erp-state-overview-section">
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="page-title">AQL Charts Management</h2>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="{{ route('production.quality.index') }}">Quality Control</a></li>
                                <li class="breadcrumb-item active">AQL Charts</li>
                            </ol>
                        </nav>
                    </div>
                    <div class="d-flex gap-2">
                        @can('production.quality.create')
                        <a href="{{ route('production.quality.aql-charts.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Add AQL Chart
                        </a>
                        @endcan
                        <a href="{{ route('production.quality.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Quality
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- AQL Level Summary -->
        <div class="row mb-4">
            <div class="col-md-2">
                <div class="card bg-danger text-white">
                    <div class="card-body text-center">
                        <h4>{{ $aqlCharts->where('aql_level', '0.065')->count() }}</h4>
                        <small>AQL 0.065</small>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card bg-warning text-white">
                    <div class="card-body text-center">
                        <h4>{{ $aqlCharts->where('aql_level', '0.10')->count() }}</h4>
                        <small>AQL 0.10</small>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <h4>{{ $aqlCharts->where('aql_level', '0.15')->count() }}</h4>
                        <small>AQL 0.15</small>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <h4>{{ $aqlCharts->where('aql_level', '0.25')->count() }}</h4>
                        <small>AQL 0.25</small>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <h4>{{ $aqlCharts->where('aql_level', '0.40')->count() }}</h4>
                        <small>AQL 0.40</small>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card bg-secondary text-white">
                    <div class="card-body text-center">
                        <h4>{{ $aqlCharts->where('is_active', true)->count() }}</h4>
                        <small>Active Charts</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- AQL Charts Table -->
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>AQL Level</th>
                                <th>Lot Size Range</th>
                                <th>Sample Size</th>
                                <th>Acceptance Number</th>
                                <th>Rejection Number</th>
                                <th>Inspection Level</th>
                                <th>Status</th>
                                <th>Usage Count</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($aqlCharts as $chart)
                            <tr>
                                <td>
                                    <span class="badge bg-{{ 
                                        $chart->aql_level <= 0.10 ? 'danger' : 
                                        ($chart->aql_level <= 0.25 ? 'warning' : 'success') 
                                    }} fs-6">
                                        {{ $chart->aql_level }}
                                    </span>
                                </td>
                                <td>
                                    {{ number_format($chart->lot_size_from) }} - {{ number_format($chart->lot_size_to) }}
                                </td>
                                <td class="text-center">
                                    <strong>{{ $chart->sample_size }}</strong>
                                </td>
                                <td class="text-center">
                                    <span class="badge bg-success">{{ $chart->acceptance_number }}</span>
                                </td>
                                <td class="text-center">
                                    <span class="badge bg-danger">{{ $chart->rejection_number }}</span>
                                </td>
                                <td class="text-center">
                                    <span class="badge bg-info">{{ strtoupper($chart->inspection_level) }}</span>
                                </td>
                                <td>
                                    <span class="badge bg-{{ $chart->is_active ? 'success' : 'secondary' }}">
                                        {{ $chart->is_active ? 'Active' : 'Inactive' }}
                                    </span>
                                </td>
                                <td class="text-center">
                                    <span class="badge bg-info">
                                        {{ $chart->qualityInspections->count() }}
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ route('production.quality.aql-charts.show', $chart) }}" class="btn btn-outline-primary" title="View">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        @can('production.quality.edit')
                                        <a href="{{ route('production.quality.aql-charts.edit', $chart) }}" class="btn btn-outline-warning" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        @endcan
                                        @can('production.quality.delete')
                                        @if($chart->qualityInspections->count() == 0)
                                        <form method="POST" action="{{ route('production.quality.aql-charts.destroy', $chart) }}" class="d-inline">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-outline-danger" title="Delete" onclick="return confirm('Are you sure you want to delete this AQL chart?')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                        @endif
                                        @endcan
                                    </div>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="9" class="text-center py-4">
                                    <div class="text-muted">
                                        <i class="fas fa-chart-bar fa-3x mb-3"></i>
                                        <p>No AQL charts found.</p>
                                        @can('production.quality.create')
                                        <a href="{{ route('production.quality.aql-charts.create') }}" class="btn btn-primary">
                                            Add First AQL Chart
                                        </a>
                                        @endcan
                                    </div>
                                </td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- AQL Information -->
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">AQL Level Guide</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>AQL Level</th>
                                        <th>Quality Standard</th>
                                        <th>Typical Use</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td><span class="badge bg-danger">0.065</span></td>
                                        <td>Very Strict</td>
                                        <td>Critical defects, safety items</td>
                                    </tr>
                                    <tr>
                                        <td><span class="badge bg-warning">0.10</span></td>
                                        <td>Strict</td>
                                        <td>High-end fashion, luxury items</td>
                                    </tr>
                                    <tr>
                                        <td><span class="badge bg-primary">0.15</span></td>
                                        <td>Normal</td>
                                        <td>Standard garments, major defects</td>
                                    </tr>
                                    <tr>
                                        <td><span class="badge bg-info">0.25</span></td>
                                        <td>General</td>
                                        <td>General production, minor defects</td>
                                    </tr>
                                    <tr>
                                        <td><span class="badge bg-success">0.40</span></td>
                                        <td>Relaxed</td>
                                        <td>Basic items, cosmetic defects</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Inspection Levels</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle"></i> Inspection Level Guidelines:</h6>
                            <ul class="mb-0">
                                <li><strong>Level I:</strong> Reduced inspection for trusted suppliers</li>
                                <li><strong>Level II:</strong> Normal inspection (most common)</li>
                                <li><strong>Level III:</strong> Tightened inspection for quality issues</li>
                            </ul>
                        </div>
                        
                        <div class="mt-3">
                            <h6>Sample Size Calculation:</h6>
                            <p class="small text-muted">
                                Sample size is determined by lot size and inspection level according to 
                                MIL-STD-105E (ANSI/ASQ Z1.4) standard. The acceptance and rejection 
                                numbers are based on the AQL level and sample size.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Usage Statistics -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">AQL Usage Statistics</h5>
            </div>
            <div class="card-body">
                <canvas id="aqlUsageChart" height="100"></canvas>
            </div>
        </div>
    </div>
</div>
@endsection

@push('js')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// AQL Usage Chart
const ctx = document.getElementById('aqlUsageChart').getContext('2d');
new Chart(ctx, {
    type: 'bar',
    data: {
        labels: @json($aqlCharts->pluck('aql_level')),
        datasets: [{
            label: 'Usage Count',
            data: @json($aqlCharts->map(function($chart) { return $chart->qualityInspections->count(); })),
            backgroundColor: [
                'rgba(220, 53, 69, 0.8)',
                'rgba(255, 193, 7, 0.8)',
                'rgba(13, 110, 253, 0.8)',
                'rgba(13, 202, 240, 0.8)',
                'rgba(25, 135, 84, 0.8)'
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});
</script>
@endpush

@push('css')
<style>
.table th {
    font-weight: 600;
    border-bottom: 2px solid #dee2e6;
}
.btn-group-sm > .btn {
    padding: 0.25rem 0.5rem;
}
</style>
@endpush
