@extends('layouts.master')

@section('title', 'Create Quality Inspection')

@section('main_content')
<div class="erp-state-overview-section">
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="page-title">Create Quality Inspection</h2>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="{{ route('production.quality.index') }}">Quality Control</a></li>
                                <li class="breadcrumb-item active">Create Inspection</li>
                            </ol>
                        </nav>
                    </div>
                    <div>
                        <a href="{{ route('production.quality.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> Back to List
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Create Form -->
        <form method="POST" action="{{ route('production.quality.store') }}">
            @csrf
            
            <div class="row">
                <div class="col-md-8">
                    <!-- Basic Information -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Basic Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">Inspection Type <span class="text-danger">*</span></label>
                                        <select name="inspectable_type" class="form-select @error('inspectable_type') is-invalid @enderror" required id="inspectable_type">
                                            <option value="sewing" {{ old('inspectable_type', $inspectableType) == 'sewing' ? 'selected' : '' }}>Sewing</option>
                                            <option value="finishing" {{ old('inspectable_type', $inspectableType) == 'finishing' ? 'selected' : '' }}>Finishing</option>
                                            <option value="washing" {{ old('inspectable_type', $inspectableType) == 'washing' ? 'selected' : '' }}>Washing</option>
                                        </select>
                                        @error('inspectable_type')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">Item to Inspect <span class="text-danger">*</span></label>
                                        <select name="inspectable_id" class="form-select @error('inspectable_id') is-invalid @enderror" required id="inspectable_id">
                                            <option value="">Select Item</option>
                                            @forelse($inspectableItems as $item)
                                                @if($inspectableType == 'sewing')
                                                <option value="{{ $item->id }}" {{ old('inspectable_id') == $item->id ? 'selected' : '' }}
                                                        data-bundle="{{ $item->bundleTicket->bundle_number }}"
                                                        data-style="{{ $item->bundleTicket->style_number }}"
                                                        data-quantity="{{ $item->output_quantity }}">
                                                    {{ $item->bundleTicket->bundle_number }} - {{ $item->bundleTicket->style_number }}
                                                </option>
                                                @else
                                                <option value="{{ $item->id }}" {{ old('inspectable_id') == $item->id ? 'selected' : '' }}
                                                        data-bundle="{{ $item->bundle_number }}"
                                                        data-style="{{ $item->style_number }}"
                                                        data-quantity="{{ $item->output_quantity }}">
                                                    {{ $item->bundle_number }} - {{ $item->style_number }}
                                                </option>
                                                @endif
                                            @empty
                                            <option value="" disabled>No items available for inspection</option>
                                            @endforelse
                                        </select>
                                        @if($inspectableItems->count() == 0)
                                        <div class="form-text text-warning">
                                            <i class="fas fa-exclamation-triangle"></i>
                                            No completed {{ $inspectableType }} entries available for inspection.
                                            <a href="{{ route('production.' . $inspectableType . '.index') }}" class="text-primary">Go to {{ ucfirst($inspectableType) }}</a>
                                        </div>
                                        @endif
                                        @error('inspectable_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">Inspection Stage <span class="text-danger">*</span></label>
                                        <select name="inspection_stage" class="form-select @error('inspection_stage') is-invalid @enderror" required>
                                            <option value="">Select Inspection Stage</option>
                                            <option value="inline" {{ old('inspection_stage') == 'inline' ? 'selected' : '' }}>Inline</option>
                                            <option value="end_line" {{ old('inspection_stage') == 'end_line' ? 'selected' : '' }}>End Line</option>
                                            <option value="pre_final" {{ old('inspection_stage') == 'pre_final' ? 'selected' : '' }}>Pre Final</option>
                                            <option value="final" {{ old('inspection_stage') == 'final' ? 'selected' : '' }}>Final</option>
                                            <option value="audit" {{ old('inspection_stage') == 'audit' ? 'selected' : '' }}>Audit</option>
                                        </select>
                                        @error('inspection_stage')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Bundle Number</label>
                                        <input type="text" name="bundle_number" class="form-control"
                                               value="{{ old('bundle_number') }}" readonly id="bundle_number_display">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Inspected Quantity <span class="text-danger">*</span></label>
                                        <input type="number" name="inspected_quantity" class="form-control @error('inspected_quantity') is-invalid @enderror"
                                               value="{{ old('inspected_quantity') }}" required min="1" id="inspected_quantity">
                                        @error('inspected_quantity')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Inspector Name <span class="text-danger">*</span></label>
                                        <input type="text" name="inspector_name" class="form-control @error('inspector_name') is-invalid @enderror" 
                                               value="{{ old('inspector_name') }}" required placeholder="Enter inspector name">
                                        @error('inspector_name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Inspection Date & Time <span class="text-danger">*</span></label>
                                        <input type="datetime-local" name="inspection_datetime" class="form-control @error('inspection_datetime') is-invalid @enderror" 
                                               value="{{ old('inspection_datetime', now()->format('Y-m-d\TH:i')) }}" required>
                                        @error('inspection_datetime')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- AQL Information -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">AQL Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">Lot Quantity <span class="text-danger">*</span></label>
                                        <input type="number" name="lot_quantity" class="form-control @error('lot_quantity') is-invalid @enderror" 
                                               value="{{ old('lot_quantity') }}" required min="1" id="lot_quantity">
                                        @error('lot_quantity')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">AQL Chart</label>
                                        <select name="aql_chart_id" class="form-select @error('aql_chart_id') is-invalid @enderror" id="aql_chart">
                                            <option value="">Select AQL Chart</option>
                                            @foreach($aqlCharts as $chart)
                                            <option value="{{ $chart->id }}" {{ old('aql_chart_id') == $chart->id ? 'selected' : '' }}
                                                    data-sample-size="{{ $chart->sample_size }}" 
                                                    data-acceptance="{{ $chart->acceptance_number }}"
                                                    data-rejection="{{ $chart->rejection_number }}">
                                                AQL {{ $chart->aql_level }} ({{ $chart->lot_size_from }}-{{ $chart->lot_size_to }})
                                            </option>
                                            @endforeach
                                        </select>
                                        @error('aql_chart_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">Sample Size <span class="text-danger">*</span></label>
                                        <input type="number" name="sample_size" class="form-control @error('sample_size') is-invalid @enderror" 
                                               value="{{ old('sample_size') }}" required min="1" id="sample_size">
                                        @error('sample_size')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Defect Information -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Defect Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label class="form-label">Critical Defects</label>
                                        <input type="number" name="critical_defects" class="form-control @error('critical_defects') is-invalid @enderror" 
                                               value="{{ old('critical_defects', 0) }}" min="0" class="defect-input">
                                        @error('critical_defects')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label class="form-label">Major Defects</label>
                                        <input type="number" name="major_defects" class="form-control @error('major_defects') is-invalid @enderror" 
                                               value="{{ old('major_defects', 0) }}" min="0" class="defect-input">
                                        @error('major_defects')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label class="form-label">Minor Defects</label>
                                        <input type="number" name="minor_defects" class="form-control @error('minor_defects') is-invalid @enderror" 
                                               value="{{ old('minor_defects', 0) }}" min="0" class="defect-input">
                                        @error('minor_defects')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label class="form-label">Total Defects</label>
                                        <input type="number" name="defect_quantity" class="form-control @error('defect_quantity') is-invalid @enderror" 
                                               value="{{ old('defect_quantity', 0) }}" min="0" id="total_defects" readonly>
                                        @error('defect_quantity')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Defect Percentage</label>
                                        <input type="number" name="defect_percentage" class="form-control @error('defect_percentage') is-invalid @enderror" 
                                               value="{{ old('defect_percentage', 0) }}" step="0.01" min="0" max="100" id="defect_percentage" readonly>
                                        @error('defect_percentage')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Inspection Result <span class="text-danger">*</span></label>
                                        <select name="inspection_result" class="form-select @error('inspection_result') is-invalid @enderror" required id="inspection_result">
                                            <option value="">Select Result</option>
                                            <option value="pass" {{ old('inspection_result') == 'pass' ? 'selected' : '' }}>Pass</option>
                                            <option value="fail" {{ old('inspection_result') == 'fail' ? 'selected' : '' }}>Fail</option>
                                            <option value="conditional_pass" {{ old('inspection_result') == 'conditional_pass' ? 'selected' : '' }}>Conditional Pass</option>
                                        </select>
                                        @error('inspection_result')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <!-- Actions -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Actions</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Create Inspection
                                </button>
                                <a href="{{ route('production.quality.index') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times"></i> Cancel
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- AQL Guide -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">AQL Guide</h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle"></i> AQL Levels:</h6>
                                <ul class="mb-0">
                                    <li><strong>0.065:</strong> Very strict</li>
                                    <li><strong>0.10:</strong> Strict</li>
                                    <li><strong>0.15:</strong> Normal</li>
                                    <li><strong>0.25:</strong> General</li>
                                    <li><strong>0.40:</strong> Relaxed</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Inspection Notes -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Inspection Notes</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label">Defect Details</label>
                                <textarea name="defect_details" class="form-control @error('defect_details') is-invalid @enderror" rows="4" 
                                          placeholder="Enter defect details and locations...">{{ old('defect_details') }}</textarea>
                                @error('defect_details')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Remarks</label>
                                <textarea name="remarks" class="form-control @error('remarks') is-invalid @enderror" rows="3" 
                                          placeholder="Enter inspection remarks...">{{ old('remarks') }}</textarea>
                                @error('remarks')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
@endsection

@push('js')
<script>
// Auto-calculate defects and percentages
document.addEventListener('DOMContentLoaded', function() {
    const defectInputs = document.querySelectorAll('.defect-input');
    const totalDefectsInput = document.getElementById('total_defects');
    const sampleSizeInput = document.getElementById('sample_size');
    const defectPercentageInput = document.getElementById('defect_percentage');
    const aqlChartSelect = document.getElementById('aql_chart');
    const lotQuantityInput = document.getElementById('lot_quantity');

    function calculateTotals() {
        let totalDefects = 0;
        defectInputs.forEach(input => {
            totalDefects += parseInt(input.value) || 0;
        });
        
        totalDefectsInput.value = totalDefects;
        
        const sampleSize = parseInt(sampleSizeInput.value) || 0;
        if (sampleSize > 0) {
            const defectPercentage = (totalDefects / sampleSize) * 100;
            defectPercentageInput.value = defectPercentage.toFixed(2);
        }
    }

    // Auto-populate sample size based on AQL chart selection
    aqlChartSelect.addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        if (selectedOption.value) {
            const sampleSize = selectedOption.getAttribute('data-sample-size');
            if (sampleSize) {
                sampleSizeInput.value = sampleSize;
                calculateTotals();
            }
        }
    });

    // Handle inspectable type change
    const inspectableTypeSelect = document.getElementById('inspectable_type');
    const inspectableIdSelect = document.getElementById('inspectable_id');
    const bundleNumberDisplay = document.getElementById('bundle_number_display');
    const inspectedQuantityInput = document.getElementById('inspected_quantity');

    inspectableTypeSelect.addEventListener('change', function() {
        // Reload page with new inspectable type
        const url = new URL(window.location);
        url.searchParams.set('inspectable_type', this.value);
        window.location.href = url.toString();
    });

    // Handle inspectable item selection
    inspectableIdSelect.addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        if (selectedOption.value) {
            const bundleNumber = selectedOption.getAttribute('data-bundle');
            const quantity = selectedOption.getAttribute('data-quantity');

            bundleNumberDisplay.value = bundleNumber || '';
            inspectedQuantityInput.value = quantity || '';

            // Update lot quantity for AQL calculation
            if (lotQuantityInput) {
                lotQuantityInput.value = quantity || '';
            }
        } else {
            bundleNumberDisplay.value = '';
            inspectedQuantityInput.value = '';
            if (lotQuantityInput) {
                lotQuantityInput.value = '';
            }
        }
    });

    // Calculate totals when defect inputs change
    defectInputs.forEach(input => {
        input.addEventListener('input', calculateTotals);
    });

    // Recalculate when sample size changes
    sampleSizeInput.addEventListener('input', calculateTotals);
});
</script>
@endpush
