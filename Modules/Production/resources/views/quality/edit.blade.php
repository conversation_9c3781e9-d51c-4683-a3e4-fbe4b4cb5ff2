@extends('layouts.master')

@section('title', 'Edit Quality Inspection')

@section('main_content')
<div class="erp-state-overview-section">
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="page-title">Edit Quality Inspection</h2>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="{{ route('production.quality.index') }}">Quality Control</a></li>
                                <li class="breadcrumb-item"><a href="{{ route('production.quality.show', $qualityInspection) }}">{{ $qualityInspection->inspection_number }}</a></li>
                                <li class="breadcrumb-item active">Edit</li>
                            </ol>
                        </nav>
                    </div>
                    <div>
                        <a href="{{ route('production.quality.show', $qualityInspection) }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Details
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Edit Form -->
        <form method="POST" action="{{ route('production.quality.update', $qualityInspection) }}">
            @csrf
            @method('PUT')
            
            <div class="row">
                <div class="col-md-8">
                    <!-- Basic Information -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Inspection Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Inspection Number</label>
                                        <input type="text" class="form-control" value="{{ $qualityInspection->inspection_number }}" readonly>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Inspector Name <span class="text-danger">*</span></label>
                                        <input type="text" name="inspector_name" class="form-control @error('inspector_name') is-invalid @enderror" 
                                               value="{{ old('inspector_name', $qualityInspection->inspector_name) }}" required>
                                        @error('inspector_name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Inspection Stage <span class="text-danger">*</span></label>
                                        <select name="inspection_stage" class="form-select @error('inspection_stage') is-invalid @enderror" required>
                                            <option value="">Select Inspection Stage</option>
                                            <option value="inline" {{ old('inspection_stage', $qualityInspection->inspection_stage) == 'inline' ? 'selected' : '' }}>Inline</option>
                                            <option value="end_line" {{ old('inspection_stage', $qualityInspection->inspection_stage) == 'end_line' ? 'selected' : '' }}>End Line</option>
                                            <option value="pre_final" {{ old('inspection_stage', $qualityInspection->inspection_stage) == 'pre_final' ? 'selected' : '' }}>Pre Final</option>
                                            <option value="final" {{ old('inspection_stage', $qualityInspection->inspection_stage) == 'final' ? 'selected' : '' }}>Final</option>
                                            <option value="audit" {{ old('inspection_stage', $qualityInspection->inspection_stage) == 'audit' ? 'selected' : '' }}>Audit</option>
                                        </select>
                                        @error('inspection_stage')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">AQL Chart</label>
                                        <select name="aql_chart_id" class="form-select @error('aql_chart_id') is-invalid @enderror">
                                            <option value="">Select AQL Chart</option>
                                            @foreach($aqlCharts as $chart)
                                            <option value="{{ $chart->id }}" {{ old('aql_chart_id', $qualityInspection->aql_chart_id) == $chart->id ? 'selected' : '' }}
                                                    data-sample-size="{{ $chart->sample_size }}">
                                                {{ $chart->chart_name }} ({{ $chart->aql_level }})
                                            </option>
                                            @endforeach
                                        </select>
                                        @error('aql_chart_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quantity Information -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Quantity Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label class="form-label">Lot Quantity <span class="text-danger">*</span></label>
                                        <input type="number" name="lot_quantity" class="form-control @error('lot_quantity') is-invalid @enderror" 
                                               value="{{ old('lot_quantity', $qualityInspection->lot_quantity) }}" required min="1" id="lot_quantity">
                                        @error('lot_quantity')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label class="form-label">Sample Size <span class="text-danger">*</span></label>
                                        <input type="number" name="sample_size" class="form-control @error('sample_size') is-invalid @enderror" 
                                               value="{{ old('sample_size', $qualityInspection->sample_size) }}" required min="1" id="sample_size">
                                        @error('sample_size')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label class="form-label">Inspected Quantity <span class="text-danger">*</span></label>
                                        <input type="number" name="inspected_quantity" class="form-control @error('inspected_quantity') is-invalid @enderror" 
                                               value="{{ old('inspected_quantity', $qualityInspection->inspected_quantity) }}" required min="1" id="inspected_quantity">
                                        @error('inspected_quantity')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label class="form-label">Total Defects</label>
                                        <input type="number" name="defect_quantity" class="form-control @error('defect_quantity') is-invalid @enderror" 
                                               value="{{ old('defect_quantity', $qualityInspection->defect_quantity) }}" min="0" id="defect_quantity">
                                        @error('defect_quantity')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Defect Breakdown -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Defect Breakdown</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">Critical Defects</label>
                                        <input type="number" name="critical_defects" class="form-control @error('critical_defects') is-invalid @enderror" 
                                               value="{{ old('critical_defects', $qualityInspection->critical_defects) }}" min="0" id="critical_defects">
                                        @error('critical_defects')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">Major Defects</label>
                                        <input type="number" name="major_defects" class="form-control @error('major_defects') is-invalid @enderror" 
                                               value="{{ old('major_defects', $qualityInspection->major_defects) }}" min="0" id="major_defects">
                                        @error('major_defects')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">Minor Defects</label>
                                        <input type="number" name="minor_defects" class="form-control @error('minor_defects') is-invalid @enderror" 
                                               value="{{ old('minor_defects', $qualityInspection->minor_defects) }}" min="0" id="minor_defects">
                                        @error('minor_defects')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Defect Details</label>
                                <textarea name="defect_details" class="form-control @error('defect_details') is-invalid @enderror" rows="3" 
                                          placeholder="Enter detailed defect information...">{{ old('defect_details', is_array($qualityInspection->defect_details) ? implode(', ', $qualityInspection->defect_details) : $qualityInspection->defect_details) }}</textarea>
                                @error('defect_details')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Actions & Notes -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Actions & Notes</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label">Corrective Actions</label>
                                <textarea name="corrective_actions" class="form-control @error('corrective_actions') is-invalid @enderror" rows="3" 
                                          placeholder="Enter corrective actions taken...">{{ old('corrective_actions', $qualityInspection->corrective_actions) }}</textarea>
                                @error('corrective_actions')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Notes</label>
                                <textarea name="notes" class="form-control @error('notes') is-invalid @enderror" rows="3" 
                                          placeholder="Enter additional notes...">{{ old('notes', $qualityInspection->notes) }}</textarea>
                                @error('notes')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <!-- Actions -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Actions</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Update Inspection
                                </button>
                                <a href="{{ route('production.quality.show', $qualityInspection) }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times"></i> Cancel
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Current Status -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Current Status</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label"><strong>Current Result:</strong></label>
                                <div>
                                    <span class="badge bg-{{ 
                                        $qualityInspection->inspection_result == 'pass' ? 'success' : 
                                        ($qualityInspection->inspection_result == 'fail' ? 'danger' : 
                                        ($qualityInspection->inspection_result == 'conditional_pass' ? 'warning' : 'secondary')) 
                                    }}">
                                        {{ ucfirst(str_replace('_', ' ', $qualityInspection->inspection_result)) }}
                                    </span>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label"><strong>Defect Percentage:</strong></label>
                                <div class="progress" style="height: 20px;">
                                    <div class="progress-bar bg-{{ $qualityInspection->defect_percentage > 4 ? 'danger' : ($qualityInspection->defect_percentage > 2.5 ? 'warning' : 'success') }}" 
                                         role="progressbar" style="width: {{ min($qualityInspection->defect_percentage, 100) }}%">
                                        {{ number_format($qualityInspection->defect_percentage, 1) }}%
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Source Information -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Source Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label"><strong>Bundle Number:</strong></label>
                                <p class="mb-1">{{ $qualityInspection->bundle_number }}</p>
                            </div>
                            <div class="mb-3">
                                <label class="form-label"><strong>Style Number:</strong></label>
                                <p class="mb-1">{{ $qualityInspection->style_number }}</p>
                            </div>
                            <div class="mb-3">
                                <label class="form-label"><strong>Inspection Type:</strong></label>
                                <p class="mb-1">{{ ucfirst($qualityInspection->inspectable_type) }}</p>
                            </div>
                            @if($qualityInspection->inspectable_id)
                            <div class="mb-3">
                                <a href="{{ route('production.' . $qualityInspection->inspectable_type . '.show', $qualityInspection->inspectable_id) }}" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-eye"></i> View Source
                                </a>
                            </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
@endsection

@push('css')
<style>
.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.badge {
    font-size: 0.75em;
}
</style>
@endpush

@push('js')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const criticalInput = document.getElementById('critical_defects');
    const majorInput = document.getElementById('major_defects');
    const minorInput = document.getElementById('minor_defects');
    const totalDefectsInput = document.getElementById('defect_quantity');
    const inspectedQtyInput = document.getElementById('inspected_quantity');

    function calculateTotalDefects() {
        const critical = parseInt(criticalInput.value) || 0;
        const major = parseInt(majorInput.value) || 0;
        const minor = parseInt(minorInput.value) || 0;
        const total = critical + major + minor;
        
        totalDefectsInput.value = total;
        
        // Validate against inspected quantity
        const inspectedQty = parseInt(inspectedQtyInput.value) || 0;
        if (total > inspectedQty) {
            alert('Total defects cannot exceed inspected quantity');
            return false;
        }
        
        return true;
    }

    [criticalInput, majorInput, minorInput].forEach(input => {
        if (input) {
            input.addEventListener('change', calculateTotalDefects);
        }
    });

    // Auto-populate sample size based on AQL chart selection
    const aqlChartSelect = document.querySelector('select[name="aql_chart_id"]');
    const sampleSizeInput = document.getElementById('sample_size');

    if (aqlChartSelect) {
        aqlChartSelect.addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            if (selectedOption.value) {
                const sampleSize = selectedOption.getAttribute('data-sample-size');
                if (sampleSize && sampleSizeInput) {
                    sampleSizeInput.value = sampleSize;
                }
            }
        });
    }

    // Form validation on submit
    document.querySelector('form').addEventListener('submit', function(e) {
        if (!calculateTotalDefects()) {
            e.preventDefault();
        }
    });
});
</script>
@endpush
