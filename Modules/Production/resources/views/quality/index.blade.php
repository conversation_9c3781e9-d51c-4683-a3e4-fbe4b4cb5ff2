@extends('layouts.master')

@section('title', 'Quality Control')

@section('main_content')
<div class="erp-state-overview-section">
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h2 class="page-title">Quality Control</h2>
                    <div class="d-flex gap-2">
                        @can('production.quality.create')
                        <a href="{{ route('production.quality.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> New Inspection
                        </a>
                        @endcan
                        @can('production.quality.aql-charts')
                        <a href="{{ route('production.quality.aql-charts') }}" class="btn btn-outline-info">
                            <i class="fas fa-chart-bar"></i> AQL Charts
                        </a>
                        @endcan
                        @can('production.quality.reports')
                        <a href="{{ route('production.quality.reports') }}" class="btn btn-outline-success">
                            <i class="fas fa-chart-line"></i> Reports
                        </a>
                        @endcan
                        @can('production.exports')
                        <div class="dropdown">
                            <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-download"></i> Export
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{{ route('production.exports.quality-report.pdf', request()->query()) }}">
                                    <i class="fas fa-file-pdf text-danger"></i> PDF Report
                                </a></li>
                            </ul>
                        </div>
                        @endcan
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="GET" action="{{ route('production.quality.index') }}" class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">Inspection Number</label>
                        <input type="text" name="inspection_number" class="form-control" value="{{ request('inspection_number') }}" placeholder="Enter inspection number">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Bundle Number</label>
                        <input type="text" name="bundle_number" class="form-control" value="{{ request('bundle_number') }}" placeholder="Enter bundle number">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Inspection Stage</label>
                        <select name="inspection_stage" class="form-select">
                            <option value="">All Stages</option>
                            <option value="inline" {{ request('inspection_stage') == 'inline' ? 'selected' : '' }}>Inline</option>
                            <option value="end_line" {{ request('inspection_stage') == 'end_line' ? 'selected' : '' }}>End Line</option>
                            <option value="pre_final" {{ request('inspection_stage') == 'pre_final' ? 'selected' : '' }}>Pre Final</option>
                            <option value="final" {{ request('inspection_stage') == 'final' ? 'selected' : '' }}>Final</option>
                            <option value="audit" {{ request('inspection_stage') == 'audit' ? 'selected' : '' }}>Audit</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Inspection Result</label>
                        <select name="inspection_result" class="form-select">
                            <option value="">All Results</option>
                            <option value="pass" {{ request('inspection_result') == 'pass' ? 'selected' : '' }}>Pass</option>
                            <option value="fail" {{ request('inspection_result') == 'fail' ? 'selected' : '' }}>Fail</option>
                            <option value="conditional_pass" {{ request('inspection_result') == 'conditional_pass' ? 'selected' : '' }}>Conditional Pass</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Inspector Name</label>
                        <input type="text" name="inspector_name" class="form-control" value="{{ request('inspector_name') }}" placeholder="Enter inspector name">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Inspection Date</label>
                        <input type="date" name="inspection_date" class="form-control" value="{{ request('inspection_date') }}">
                    </div>
                    <div class="col-md-6 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="fas fa-search"></i> Filter
                        </button>
                        <a href="{{ route('production.quality.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i> Clear
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Quality Inspections Table -->
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>Inspection #</th>
                                <th>Bundle Number</th>
                                <th>Stage</th>
                                <th>Lot Qty</th>
                                <th>Sample Size</th>
                                <th>Defects</th>
                                <th>Defect %</th>
                                <th>Inspector</th>
                                <th>Result</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($qualityInspections as $inspection)
                            <tr>
                                <td>
                                    <a href="{{ route('production.quality.show', $inspection) }}" class="text-decoration-none">
                                        {{ $inspection->inspection_number }}
                                    </a>
                                    <br>
                                    <small class="text-muted">{{ $inspection->inspection_datetime->format('M d, Y H:i') }}</small>
                                </td>
                                <td>{{ $inspection->bundle_number }}</td>
                                <td>
                                    <span class="badge bg-secondary">
                                        {{ ucfirst(str_replace('_', ' ', $inspection->inspection_stage)) }}
                                    </span>
                                </td>
                                <td>{{ number_format($inspection->lot_quantity) }}</td>
                                <td>{{ number_format($inspection->sample_size) }}</td>
                                <td>
                                    <div class="d-flex flex-column">
                                        @if($inspection->critical_defects > 0)
                                        <span class="badge bg-danger mb-1">C: {{ $inspection->critical_defects }}</span>
                                        @endif
                                        @if($inspection->major_defects > 0)
                                        <span class="badge bg-warning mb-1">M: {{ $inspection->major_defects }}</span>
                                        @endif
                                        @if($inspection->minor_defects > 0)
                                        <span class="badge bg-info">m: {{ $inspection->minor_defects }}</span>
                                        @endif
                                        @if($inspection->defect_quantity == 0)
                                        <span class="badge bg-success">No Defects</span>
                                        @endif
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-{{ $inspection->defect_percentage > 4 ? 'danger' : ($inspection->defect_percentage > 2.5 ? 'warning' : 'success') }}">
                                        {{ number_format($inspection->defect_percentage, 2) }}%
                                    </span>
                                </td>
                                <td>{{ $inspection->inspector_name }}</td>
                                <td>
                                    <span class="badge bg-{{ 
                                        $inspection->inspection_result == 'pass' ? 'success' : 
                                        ($inspection->inspection_result == 'fail' ? 'danger' : 'warning') 
                                    }}">
                                        {{ ucfirst(str_replace('_', ' ', $inspection->inspection_result)) }}
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        @can('production.quality.view')
                                        <a href="{{ route('production.quality.show', $inspection) }}" class="btn btn-outline-primary" title="View">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        @endcan
                                        @can('production.quality.edit')
                                        <a href="{{ route('production.quality.edit', $inspection) }}" class="btn btn-outline-warning" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        @endcan
                                        @can('production.quality.delete')
                                        <form method="POST" action="{{ route('production.quality.destroy', $inspection) }}" class="d-inline">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-outline-danger" title="Delete" onclick="return confirm('Are you sure you want to delete this inspection?')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                        @endcan
                                    </div>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="10" class="text-center py-4">
                                    <div class="text-muted">
                                        <i class="fas fa-search fa-3x mb-3"></i>
                                        <p>No quality inspections found.</p>
                                        @can('production.quality.create')
                                        <a href="{{ route('production.quality.create') }}" class="btn btn-primary">
                                            Create First Inspection
                                        </a>
                                        @endcan
                                    </div>
                                </td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                @if($qualityInspections->hasPages())
                <div class="d-flex justify-content-center mt-4">
                    {{ $qualityInspections->links() }}
                </div>
                @endif
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="row mt-4">
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <h4>{{ $qualityInspections->where('inspection_result', 'pass')->count() }}</h4>
                        <small>Pass</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-danger text-white">
                    <div class="card-body text-center">
                        <h4>{{ $qualityInspections->where('inspection_result', 'fail')->count() }}</h4>
                        <small>Fail</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body text-center">
                        <h4>{{ $qualityInspections->where('inspection_result', 'conditional_pass')->count() }}</h4>
                        <small>Conditional</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <h4>{{ number_format($qualityInspections->avg('defect_percentage'), 1) }}%</h4>
                        <small>Avg Defect Rate</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('css')
<style>
.table th {
    font-weight: 600;
    border-bottom: 2px solid #dee2e6;
}
.btn-group-sm > .btn {
    padding: 0.25rem 0.5rem;
}
.badge {
    font-size: 0.75em;
}
</style>
@endpush
