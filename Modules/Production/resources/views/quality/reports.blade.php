@extends('layouts.master')

@section('title', 'Quality Reports')

@section('main_content')
<div class="erp-state-overview-section">
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="page-title">Quality Reports & Analytics</h2>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="{{ route('production.quality.index') }}">Quality Control</a></li>
                                <li class="breadcrumb-item active">Reports</li>
                            </ol>
                        </nav>
                    </div>
                    <div class="d-flex gap-2">
                        <a href="{{ route('production.quality.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Quality
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Report Filters -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="GET" action="{{ route('production.quality.reports') }}" class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">Date Range</label>
                        <select name="date_range" class="form-select">
                            <option value="today" {{ request('date_range') == 'today' ? 'selected' : '' }}>Today</option>
                            <option value="yesterday" {{ request('date_range') == 'yesterday' ? 'selected' : '' }}>Yesterday</option>
                            <option value="this_week" {{ request('date_range') == 'this_week' ? 'selected' : '' }}>This Week</option>
                            <option value="last_week" {{ request('date_range') == 'last_week' ? 'selected' : '' }}>Last Week</option>
                            <option value="this_month" {{ request('date_range') == 'this_month' ? 'selected' : '' }}>This Month</option>
                            <option value="custom" {{ request('date_range') == 'custom' ? 'selected' : '' }}>Custom Range</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">Start Date</label>
                        <input type="date" name="start_date" class="form-control" value="{{ request('start_date') }}">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">End Date</label>
                        <input type="date" name="end_date" class="form-control" value="{{ request('end_date') }}">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Inspection Stage</label>
                        <select name="inspection_stage" class="form-select">
                            <option value="">All Stages</option>
                            <option value="inline" {{ request('inspection_stage') == 'inline' ? 'selected' : '' }}>Inline</option>
                            <option value="end_line" {{ request('inspection_stage') == 'end_line' ? 'selected' : '' }}>End Line</option>
                            <option value="pre_final" {{ request('inspection_stage') == 'pre_final' ? 'selected' : '' }}>Pre Final</option>
                            <option value="final" {{ request('inspection_stage') == 'final' ? 'selected' : '' }}>Final</option>
                            <option value="audit" {{ request('inspection_stage') == 'audit' ? 'selected' : '' }}>Audit</option>
                        </select>
                    </div>
                    <div class="col-md-2 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-chart-line"></i> Generate
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Quality Metrics Summary -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <h3>{{ number_format($qualityMetrics['pass_rate'], 1) }}%</h3>
                        <p class="mb-0">Pass Rate</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-danger text-white">
                    <div class="card-body text-center">
                        <h3>{{ number_format($qualityMetrics['fail_rate'], 1) }}%</h3>
                        <p class="mb-0">Fail Rate</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body text-center">
                        <h3>{{ number_format($qualityMetrics['avg_defect_rate'], 2) }}%</h3>
                        <p class="mb-0">Avg Defect Rate</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <h3>{{ number_format($qualityMetrics['total_inspections']) }}</h3>
                        <p class="mb-0">Total Inspections</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Quality Trend</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="qualityTrendChart"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Defect Distribution</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="defectDistributionChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Stage-wise Analysis -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Stage-wise Quality Analysis</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>Inspection Stage</th>
                                <th>Total Inspections</th>
                                <th>Pass</th>
                                <th>Fail</th>
                                <th>Conditional</th>
                                <th>Pass Rate</th>
                                <th>Avg Defect Rate</th>
                                <th>Critical Defects</th>
                                <th>Major Defects</th>
                                <th>Minor Defects</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($stageAnalysis as $stage => $data)
                            <tr>
                                <td><strong>{{ ucfirst(str_replace('_', ' ', $stage)) }}</strong></td>
                                <td>{{ $data['total'] }}</td>
                                <td><span class="badge bg-success">{{ $data['pass'] }}</span></td>
                                <td><span class="badge bg-danger">{{ $data['fail'] }}</span></td>
                                <td><span class="badge bg-warning">{{ $data['conditional'] }}</span></td>
                                <td>
                                    <div class="progress" style="height: 20px;">
                                        <div class="progress-bar bg-success" style="width: {{ $data['pass_rate'] }}%">
                                            {{ number_format($data['pass_rate'], 1) }}%
                                        </div>
                                    </div>
                                </td>
                                <td>{{ number_format($data['avg_defect_rate'], 2) }}%</td>
                                <td><span class="badge bg-danger">{{ $data['critical_defects'] }}</span></td>
                                <td><span class="badge bg-warning">{{ $data['major_defects'] }}</span></td>
                                <td><span class="badge bg-info">{{ $data['minor_defects'] }}</span></td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Inspector Performance -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Inspector Performance Analysis</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>Inspector</th>
                                <th>Total Inspections</th>
                                <th>Pass Rate</th>
                                <th>Avg Defect Rate</th>
                                <th>Critical Defects Found</th>
                                <th>Major Defects Found</th>
                                <th>Performance Score</th>
                                <th>Rating</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($inspectorPerformance as $inspector => $data)
                            <tr>
                                <td><strong>{{ $inspector }}</strong></td>
                                <td>{{ $data['total_inspections'] }}</td>
                                <td>
                                    <span class="badge bg-{{ $data['pass_rate'] >= 90 ? 'success' : ($data['pass_rate'] >= 80 ? 'warning' : 'danger') }}">
                                        {{ number_format($data['pass_rate'], 1) }}%
                                    </span>
                                </td>
                                <td>{{ number_format($data['avg_defect_rate'], 2) }}%</td>
                                <td><span class="badge bg-danger">{{ $data['critical_defects'] }}</span></td>
                                <td><span class="badge bg-warning">{{ $data['major_defects'] }}</span></td>
                                <td>{{ number_format($data['performance_score'], 1) }}</td>
                                <td>
                                    @php
                                        $rating = 5;
                                        if ($data['performance_score'] < 60) $rating = 2;
                                        elseif ($data['performance_score'] < 70) $rating = 3;
                                        elseif ($data['performance_score'] < 85) $rating = 4;
                                    @endphp
                                    @for($i = 1; $i <= 5; $i++)
                                        <i class="fas fa-star text-{{ $i <= $rating ? 'warning' : 'muted' }}"></i>
                                    @endfor
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Export Options -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Export Reports</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="d-grid">
                            <a href="{{ route('production.exports.quality-report.pdf', request()->query()) }}" class="btn btn-danger" target="_blank">
                                <i class="fas fa-file-pdf"></i> Export PDF Report
                            </a>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="d-grid">
                            <a href="{{ route('production.exports.quality-report.excel', request()->query()) }}" class="btn btn-success">
                                <i class="fas fa-file-excel"></i> Export Excel Report
                            </a>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="d-grid">
                            <button class="btn btn-info" onclick="window.print()">
                                <i class="fas fa-print"></i> Print Report
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('js')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Quality Trend Chart
const trendCtx = document.getElementById('qualityTrendChart').getContext('2d');
new Chart(trendCtx, {
    type: 'line',
    data: {
        labels: @json($trendData['dates'] ?? []),
        datasets: [{
            label: 'Pass Rate (%)',
            data: @json($trendData['pass_rates'] ?? []),
            borderColor: 'rgb(25, 135, 84)',
            backgroundColor: 'rgba(25, 135, 84, 0.1)',
            tension: 0.1
        }, {
            label: 'Defect Rate (%)',
            data: @json($trendData['defect_rates'] ?? []),
            borderColor: 'rgb(220, 53, 69)',
            backgroundColor: 'rgba(220, 53, 69, 0.1)',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: true,
        scales: {
            y: {
                beginAtZero: true,
                max: 100
            }
        }
    }
});

// Defect Distribution Chart
const defectCtx = document.getElementById('defectDistributionChart').getContext('2d');
new Chart(defectCtx, {
    type: 'doughnut',
    data: {
        labels: ['Critical', 'Major', 'Minor'],
        datasets: [{
            data: @json($defectDistribution ?? []),
            backgroundColor: [
                'rgba(220, 53, 69, 0.8)',
                'rgba(255, 193, 7, 0.8)',
                'rgba(13, 202, 240, 0.8)'
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: true
    }
});
</script>
@endpush

@push('css')
<style>
.table th {
    font-weight: 600;
    border-bottom: 2px solid #dee2e6;
}
.progress {
    background-color: #e9ecef;
}
</style>
@endpush
