@extends('layouts.master')

@section('title', 'Quality Inspection Details')

@section('main_content')
<div class="erp-state-overview-section">
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="page-title">Quality Inspection Details</h2>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="{{ route('production.quality.index') }}">Quality Control</a></li>
                                <li class="breadcrumb-item active">{{ $qualityInspection->inspection_number }}</li>
                            </ol>
                        </nav>
                    </div>
                    <div class="d-flex gap-2">
                        @can('production.quality.edit')
                        <a href="{{ route('production.quality.edit', $qualityInspection) }}" class="btn btn-warning">
                            <i class="fas fa-edit"></i> Edit Inspection
                        </a>
                        @endcan
                        <a href="{{ route('production.quality.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> Back to List
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Basic Information -->
            <div class="col-md-8">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Inspection Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>Inspection Number:</strong></td>
                                        <td>{{ $qualityInspection->inspection_number }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Bundle Number:</strong></td>
                                        <td>{{ $qualityInspection->bundle_number }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Style Number:</strong></td>
                                        <td>{{ $qualityInspection->style_number }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>PO Number:</strong></td>
                                        <td>{{ $qualityInspection->po_number }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Color:</strong></td>
                                        <td>{{ $qualityInspection->color }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Size:</strong></td>
                                        <td>{{ $qualityInspection->size }}</td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>Inspection Type:</strong></td>
                                        <td>{{ ucfirst($qualityInspection->inspectable_type) }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Inspection Stage:</strong></td>
                                        <td>{{ ucfirst(str_replace('_', ' ', $qualityInspection->inspection_stage)) }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Inspector:</strong></td>
                                        <td>{{ $qualityInspection->inspector_name }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Inspection Date:</strong></td>
                                        <td>{{ $qualityInspection->inspection_datetime ? $qualityInspection->inspection_datetime->format('M d, Y H:i') : 'Not set' }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>AQL Chart:</strong></td>
                                        <td>{{ $qualityInspection->aqlChart->chart_name ?? 'Not specified' }}</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quantity Information -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Quantity & Results</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4 class="text-primary">{{ number_format($qualityInspection->lot_quantity) }}</h4>
                                    <small class="text-muted">Lot Quantity</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4 class="text-info">{{ number_format($qualityInspection->sample_size) }}</h4>
                                    <small class="text-muted">Sample Size</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4 class="text-warning">{{ number_format($qualityInspection->inspected_quantity) }}</h4>
                                    <small class="text-muted">Inspected Quantity</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4 class="text-danger">{{ number_format($qualityInspection->defect_quantity) }}</h4>
                                    <small class="text-muted">Total Defects</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mt-4">
                            <div class="col-md-4">
                                <div class="text-center">
                                    <h5 class="text-danger">{{ number_format($qualityInspection->critical_defects) }}</h5>
                                    <small class="text-muted">Critical Defects</small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center">
                                    <h5 class="text-warning">{{ number_format($qualityInspection->major_defects) }}</h5>
                                    <small class="text-muted">Major Defects</small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center">
                                    <h5 class="text-info">{{ number_format($qualityInspection->minor_defects) }}</h5>
                                    <small class="text-muted">Minor Defects</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mt-4">
                            <div class="col-md-6">
                                <div class="progress mb-2" style="height: 25px;">
                                    @php
                                        $passPercentage = 100 - $qualityInspection->defect_percentage;
                                    @endphp
                                    <div class="progress-bar bg-success" role="progressbar" style="width: {{ $passPercentage }}%">
                                        {{ number_format($passPercentage, 1) }}% Pass Rate
                                    </div>
                                </div>
                                <small class="text-muted">Quality Pass Rate</small>
                            </div>
                            <div class="col-md-6">
                                <div class="progress mb-2" style="height: 25px;">
                                    <div class="progress-bar bg-danger" role="progressbar" style="width: {{ $qualityInspection->defect_percentage }}%">
                                        {{ number_format($qualityInspection->defect_percentage, 1) }}% Defects
                                    </div>
                                </div>
                                <small class="text-muted">Defect Percentage</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Defect Details -->
                @if($qualityInspection->defect_details || $qualityInspection->defectLogs->count() > 0)
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Defect Details</h5>
                    </div>
                    <div class="card-body">
                        @if($qualityInspection->defect_details)
                        <div class="mb-3">
                            <label class="form-label"><strong>Defect Summary:</strong></label>
                            <div class="alert alert-warning">
                                @if(is_array($qualityInspection->defect_details))
                                    <ul class="mb-0">
                                        @foreach($qualityInspection->defect_details as $defect)
                                        <li>{{ $defect }}</li>
                                        @endforeach
                                    </ul>
                                @else
                                    {{ $qualityInspection->defect_details }}
                                @endif
                            </div>
                        </div>
                        @endif
                        
                        @if($qualityInspection->defectLogs->count() > 0)
                        <div class="mb-3">
                            <label class="form-label"><strong>Detailed Defect Logs:</strong></label>
                            <div class="table-responsive">
                                <table class="table table-sm table-bordered">
                                    <thead class="table-light">
                                        <tr>
                                            <th>Defect Type</th>
                                            <th>Severity</th>
                                            <th>Location</th>
                                            <th>Quantity</th>
                                            <th>Description</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($qualityInspection->defectLogs as $log)
                                        <tr>
                                            <td>{{ $log->defect_type }}</td>
                                            <td>
                                                <span class="badge bg-{{ 
                                                    $log->severity == 'critical' ? 'danger' : 
                                                    ($log->severity == 'major' ? 'warning' : 'info') 
                                                }}">
                                                    {{ ucfirst($log->severity) }}
                                                </span>
                                            </td>
                                            <td>{{ $log->defect_location }}</td>
                                            <td>{{ $log->defect_quantity }}</td>
                                            <td>{{ $log->description }}</td>
                                        </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        @endif
                    </div>
                </div>
                @endif

                <!-- Corrective Actions & Notes -->
                @if($qualityInspection->corrective_actions || $qualityInspection->notes)
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Actions & Notes</h5>
                    </div>
                    <div class="card-body">
                        @if($qualityInspection->corrective_actions)
                        <div class="mb-3">
                            <label class="form-label"><strong>Corrective Actions:</strong></label>
                            <div class="alert alert-info">
                                {{ $qualityInspection->corrective_actions }}
                            </div>
                        </div>
                        @endif
                        
                        @if($qualityInspection->notes)
                        <div class="mb-3">
                            <label class="form-label"><strong>Notes:</strong></label>
                            <div class="alert alert-secondary">
                                {{ $qualityInspection->notes }}
                            </div>
                        </div>
                        @endif
                    </div>
                </div>
                @endif
            </div>

            <!-- Sidebar Information -->
            <div class="col-md-4">
                <!-- Inspection Result -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Inspection Result</h5>
                    </div>
                    <div class="card-body text-center">
                        <div class="mb-3">
                            <span class="badge bg-{{ 
                                $qualityInspection->inspection_result == 'pass' ? 'success' : 
                                ($qualityInspection->inspection_result == 'fail' ? 'danger' : 
                                ($qualityInspection->inspection_result == 'conditional_pass' ? 'warning' : 'secondary')) 
                            }} fs-4">
                                {{ ucfirst(str_replace('_', ' ', $qualityInspection->inspection_result)) }}
                            </span>
                        </div>
                        <div class="mb-3">
                            <h6>Defect Percentage</h6>
                            <h3 class="text-{{ $qualityInspection->defect_percentage > 4 ? 'danger' : ($qualityInspection->defect_percentage > 2.5 ? 'warning' : 'success') }}">
                                {{ number_format($qualityInspection->defect_percentage, 2) }}%
                            </h3>
                        </div>
                    </div>
                </div>

                <!-- AQL Information -->
                @if($qualityInspection->aqlChart)
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">AQL Chart Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label"><strong>Chart Name:</strong></label>
                            <p class="mb-1">{{ $qualityInspection->aqlChart->chart_name }}</p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label"><strong>AQL Level:</strong></label>
                            <p class="mb-1">{{ $qualityInspection->aqlChart->aql_level }}</p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label"><strong>Inspection Level:</strong></label>
                            <p class="mb-1">{{ $qualityInspection->aqlChart->inspection_level }}</p>
                        </div>
                        @if($qualityInspection->aqlChart->acceptance_number !== null)
                        <div class="mb-3">
                            <label class="form-label"><strong>Acceptance Number:</strong></label>
                            <p class="mb-1">{{ $qualityInspection->aqlChart->acceptance_number }}</p>
                        </div>
                        @endif
                        @if($qualityInspection->aqlChart->rejection_number !== null)
                        <div class="mb-3">
                            <label class="form-label"><strong>Rejection Number:</strong></label>
                            <p class="mb-0">{{ $qualityInspection->aqlChart->rejection_number }}</p>
                        </div>
                        @endif
                    </div>
                </div>
                @endif

                <!-- Source Information -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Source Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label"><strong>Inspected Item:</strong></label>
                            <p class="mb-1">{{ ucfirst($qualityInspection->inspectable_type) }} Entry</p>
                            @if($qualityInspection->inspectable_id)
                            <p class="mb-0">
                                <a href="{{ route('production.' . $qualityInspection->inspectable_type . '.show', $qualityInspection->inspectable_id) }}" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-eye"></i> View Source
                                </a>
                            </p>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Actions</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            @can('production.quality.edit')
                            <a href="{{ route('production.quality.edit', $qualityInspection) }}" class="btn btn-warning">
                                <i class="fas fa-edit"></i> Edit Inspection
                            </a>
                            @endcan
                            
                            <a href="{{ route('production.quality.index') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-list"></i> Back to List
                            </a>
                            
                            @can('production.quality.delete')
                            <form method="POST" action="{{ route('production.quality.destroy', $qualityInspection) }}" class="d-inline">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn btn-outline-danger w-100" onclick="return confirm('Are you sure you want to delete this quality inspection?')">
                                    <i class="fas fa-trash"></i> Delete Inspection
                                </button>
                            </form>
                            @endcan
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('css')
<style>
.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.badge {
    font-size: 0.75em;
}

.table-borderless td {
    border: none;
    padding: 0.25rem 0.5rem;
}

.progress {
    background-color: #e9ecef;
}
</style>
@endpush
