@extends('layouts.master')

@section('title', 'Bundle Tickets Management')

@section('main_content')
<div class="erp-state-overview-section">
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="page-title">Bundle Tickets Management</h2>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="{{ route('production.sewing.index') }}">Sewing Section</a></li>
                                <li class="breadcrumb-item active">Bundle Tickets</li>
                            </ol>
                        </nav>
                    </div>
                    <div class="d-flex gap-2">
                        @can('production.sewing.create')
                        <a href="{{ route('production.sewing.bundle-tickets.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Generate Tickets
                        </a>
                        @endcan
                        <a href="{{ route('production.sewing.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Sewing
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="GET" action="{{ route('production.sewing.bundle-tickets') }}" class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">Bundle Number</label>
                        <input type="text" name="bundle_number" class="form-control" value="{{ request('bundle_number') }}" placeholder="Enter bundle number">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Ticket Number</label>
                        <input type="text" name="ticket_number" class="form-control" value="{{ request('ticket_number') }}" placeholder="Enter ticket number">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Status</label>
                        <select name="status" class="form-select">
                            <option value="">All Status</option>
                            <option value="generated" {{ request('status') == 'generated' ? 'selected' : '' }}>Generated</option>
                            <option value="in_use" {{ request('status') == 'in_use' ? 'selected' : '' }}>In Use</option>
                            <option value="completed" {{ request('status') == 'completed' ? 'selected' : '' }}>Completed</option>
                            <option value="cancelled" {{ request('status') == 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                        </select>
                    </div>
                    <div class="col-md-3 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="fas fa-search"></i> Filter
                        </button>
                        <a href="{{ route('production.sewing.bundle-tickets') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i> Clear
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Bundle Tickets Table -->
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>Ticket Number</th>
                                <th>Bundle Number</th>
                                <th>Style</th>
                                <th>Size</th>
                                <th>Quantity</th>
                                <th>Generated Date</th>
                                <th>Status</th>
                                <th>QR Code</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($bundleTickets as $ticket)
                            <tr>
                                <td>
                                    <strong>{{ $ticket->ticket_number }}</strong>
                                    @if($ticket->barcode)
                                    <br>
                                    <small class="text-muted">{{ $ticket->barcode }}</small>
                                    @endif
                                </td>
                                <td>{{ $ticket->bundle_number }}</td>
                                <td>{{ $ticket->style_number }}</td>
                                <td>{{ $ticket->size }}</td>
                                <td>{{ number_format($ticket->quantity) }}</td>
                                <td>{{ $ticket->generated_datetime->format('M d, Y H:i') }}</td>
                                <td>
                                    <span class="badge bg-{{ 
                                        $ticket->status == 'completed' ? 'success' : 
                                        ($ticket->status == 'in_use' ? 'primary' : 
                                        ($ticket->status == 'cancelled' ? 'danger' : 'secondary')) 
                                    }}">
                                        {{ ucfirst(str_replace('_', ' ', $ticket->status)) }}
                                    </span>
                                </td>
                                <td class="text-center">
                                    @if($ticket->qr_code)
                                    <div class="qr-code-container">
                                        <img src="data:image/png;base64,{{ $ticket->qr_code }}" alt="QR Code" style="width: 50px; height: 50px;">
                                    </div>
                                    @else
                                    <span class="text-muted">No QR</span>
                                    @endif
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ route('production.sewing.bundle-tickets.show', $ticket) }}" class="btn btn-outline-primary" title="View">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('production.sewing.bundle-tickets.print', $ticket) }}" class="btn btn-outline-info" title="Print" target="_blank">
                                            <i class="fas fa-print"></i>
                                        </a>
                                        @if($ticket->status == 'generated')
                                        <a href="{{ route('production.sewing.bundle-tickets.edit', $ticket) }}" class="btn btn-outline-warning" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        @endif
                                        @can('production.sewing.delete')
                                        @if($ticket->status != 'in_use')
                                        <form method="POST" action="{{ route('production.sewing.bundle-tickets.destroy', $ticket) }}" class="d-inline">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-outline-danger" title="Delete" onclick="return confirm('Are you sure you want to delete this bundle ticket?')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                        @endif
                                        @endcan
                                    </div>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="9" class="text-center py-4">
                                    <div class="text-muted">
                                        <i class="fas fa-ticket-alt fa-3x mb-3"></i>
                                        <p>No bundle tickets found.</p>
                                        @can('production.sewing.create')
                                        <a href="{{ route('production.sewing.bundle-tickets.create') }}" class="btn btn-primary">
                                            Generate First Bundle Ticket
                                        </a>
                                        @endcan
                                    </div>
                                </td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                @if($bundleTickets->hasPages())
                <div class="d-flex justify-content-center mt-4">
                    {{ $bundleTickets->links() }}
                </div>
                @endif
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="row mt-4">
            <div class="col-md-3">
                <div class="card bg-secondary text-white">
                    <div class="card-body text-center">
                        <h4>{{ $bundleTickets->where('status', 'generated')->count() }}</h4>
                        <small>Generated</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <h4>{{ $bundleTickets->where('status', 'in_use')->count() }}</h4>
                        <small>In Use</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <h4>{{ $bundleTickets->where('status', 'completed')->count() }}</h4>
                        <small>Completed</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <h4>{{ number_format($bundleTickets->sum('quantity')) }}</h4>
                        <small>Total Pieces</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('css')
<style>
.qr-code-container {
    display: inline-block;
    padding: 5px;
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
}
.table th {
    font-weight: 600;
    border-bottom: 2px solid #dee2e6;
}
.btn-group-sm > .btn {
    padding: 0.25rem 0.5rem;
}
</style>
@endpush
