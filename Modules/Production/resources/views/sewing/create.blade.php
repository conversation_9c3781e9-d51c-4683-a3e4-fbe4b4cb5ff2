@extends('layouts.master')

@section('title', 'Create Sewing Entry')

@section('main_content')
<div class="erp-state-overview-section">
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="page-title">Create Sewing Entry</h2>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="{{ route('production.sewing.index') }}">Sewing Section</a></li>
                                <li class="breadcrumb-item active">Create Entry</li>
                            </ol>
                        </nav>
                    </div>
                    <div>
                        <a href="{{ route('production.sewing.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> Back to List
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Create Form -->
        <form method="POST" action="{{ route('production.sewing.store') }}">
            @csrf
            
            <div class="row">
                <div class="col-md-8">
                    <!-- Basic Information -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Basic Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Bundle Ticket <span class="text-danger">*</span></label>
                                        <select name="bundle_ticket_id" class="form-select @error('bundle_ticket_id') is-invalid @enderror" required>
                                            <option value="">Select Bundle Ticket</option>
                                            @forelse($bundleTickets as $ticket)
                                            <option value="{{ $ticket->id }}" {{ old('bundle_ticket_id') == $ticket->id ? 'selected' : '' }}>
                                                {{ $ticket->ticket_number }} - {{ $ticket->bundle_number }}
                                            </option>
                                            @empty
                                            <option value="" disabled>No bundle tickets available</option>
                                            @endforelse
                                        </select>
                                        @if($bundleTickets->count() == 0)
                                        <div class="form-text text-warning">
                                            <i class="fas fa-exclamation-triangle"></i>
                                            No bundle tickets available. Please create cutting entries and generate bundle tickets first.
                                            <a href="{{ route('production.cutting.entries') }}" class="text-primary">Go to Cutting Entries</a>
                                        </div>
                                        @endif
                                        @error('bundle_ticket_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Sewing Line <span class="text-danger">*</span></label>
                                        <select name="sewing_line_id" class="form-select @error('sewing_line_id') is-invalid @enderror" required>
                                            <option value="">Select Sewing Line</option>
                                            @foreach($sewingLines as $line)
                                            <option value="{{ $line->id }}" {{ old('sewing_line_id') == $line->id ? 'selected' : '' }}>
                                                {{ $line->line_number }} - {{ $line->line_name }}
                                            </option>
                                            @endforeach
                                        </select>
                                        @error('sewing_line_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Operator <span class="text-danger">*</span></label>
                                        <select name="operator_id" class="form-select @error('operator_id') is-invalid @enderror" required>
                                            <option value="">Select Operator</option>
                                            @foreach($operators as $operator)
                                            <option value="{{ $operator->id }}" {{ old('operator_id') == $operator->id ? 'selected' : '' }}>
                                                {{ $operator->operator_id }} - {{ $operator->name }}
                                            </option>
                                            @endforeach
                                        </select>
                                        @error('operator_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Status <span class="text-danger">*</span></label>
                                        <select name="status" class="form-select @error('status') is-invalid @enderror" required>
                                            <option value="input" {{ old('status') == 'input' ? 'selected' : '' }}>Input</option>
                                            <option value="in_progress" {{ old('status') == 'in_progress' ? 'selected' : '' }}>In Progress</option>
                                            <option value="output" {{ old('status') == 'output' ? 'selected' : '' }}>Output</option>
                                            <option value="completed" {{ old('status') == 'completed' ? 'selected' : '' }}>Completed</option>
                                        </select>
                                        @error('status')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quantity Information -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Quantity Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">Input Quantity <span class="text-danger">*</span></label>
                                        <input type="number" name="input_quantity" class="form-control @error('input_quantity') is-invalid @enderror" 
                                               value="{{ old('input_quantity') }}" required min="1">
                                        @error('input_quantity')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">Output Quantity</label>
                                        <input type="number" name="output_quantity" class="form-control @error('output_quantity') is-invalid @enderror" 
                                               value="{{ old('output_quantity') }}" min="0">
                                        @error('output_quantity')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">Defect Quantity</label>
                                        <input type="number" name="defect_quantity" class="form-control @error('defect_quantity') is-invalid @enderror" 
                                               value="{{ old('defect_quantity', 0) }}" min="0">
                                        @error('defect_quantity')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Timing Information -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Timing Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Input Date & Time <span class="text-danger">*</span></label>
                                        <input type="datetime-local" name="input_datetime" class="form-control @error('input_datetime') is-invalid @enderror" 
                                               value="{{ old('input_datetime', now()->format('Y-m-d\TH:i')) }}" required>
                                        @error('input_datetime')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Output Date & Time</label>
                                        <input type="datetime-local" name="output_datetime" class="form-control @error('output_datetime') is-invalid @enderror" 
                                               value="{{ old('output_datetime') }}">
                                        @error('output_datetime')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">SMV (Standard Minute Value)</label>
                                        <input type="number" name="smv" class="form-control @error('smv') is-invalid @enderror" 
                                               value="{{ old('smv') }}" step="0.1" min="0">
                                        @error('smv')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">Actual Minutes Worked</label>
                                        <input type="number" name="actual_minutes" class="form-control @error('actual_minutes') is-invalid @enderror" 
                                               value="{{ old('actual_minutes') }}" min="0">
                                        @error('actual_minutes')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">Efficiency Achieved (%)</label>
                                        <input type="number" name="efficiency_achieved" class="form-control @error('efficiency_achieved') is-invalid @enderror" 
                                               value="{{ old('efficiency_achieved') }}" step="0.1" min="0" max="200">
                                        @error('efficiency_achieved')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <!-- Actions -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Actions</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Create Sewing Entry
                                </button>
                                <a href="{{ route('production.sewing.index') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times"></i> Cancel
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Info -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Quick Info</h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle"></i> Tips:</h6>
                                <ul class="mb-0">
                                    <li>Select bundle ticket first to auto-populate details</li>
                                    <li>SMV helps calculate efficiency automatically</li>
                                    <li>Output quantity can be added later if status is "Input"</li>
                                    <li>Efficiency = (SMV × Output Qty) / Actual Minutes × 100</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Notes -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Notes</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label">Remarks</label>
                                <textarea name="remarks" class="form-control @error('remarks') is-invalid @enderror" rows="4" 
                                          placeholder="Enter any remarks or notes...">{{ old('remarks') }}</textarea>
                                @error('remarks')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
@endsection

@push('js')
<script>
// Auto-calculate efficiency when SMV, output quantity, and actual minutes are provided
document.addEventListener('DOMContentLoaded', function() {
    const smvInput = document.querySelector('input[name="smv"]');
    const outputQtyInput = document.querySelector('input[name="output_quantity"]');
    const actualMinutesInput = document.querySelector('input[name="actual_minutes"]');
    const efficiencyInput = document.querySelector('input[name="efficiency_achieved"]');

    function calculateEfficiency() {
        const smv = parseFloat(smvInput.value) || 0;
        const outputQty = parseFloat(outputQtyInput.value) || 0;
        const actualMinutes = parseFloat(actualMinutesInput.value) || 0;

        if (smv > 0 && outputQty > 0 && actualMinutes > 0) {
            const efficiency = (smv * outputQty) / actualMinutes * 100;
            efficiencyInput.value = efficiency.toFixed(1);
        }
    }

    [smvInput, outputQtyInput, actualMinutesInput].forEach(input => {
        if (input) {
            input.addEventListener('input', calculateEfficiency);
        }
    });
});
</script>
@endpush
