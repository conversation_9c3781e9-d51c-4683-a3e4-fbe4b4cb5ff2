@extends('layouts.master')

@section('title', 'Edit Sewing Entry')

@section('main_content')
<div class="erp-state-overview-section">
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="page-title">Edit Sewing Entry</h2>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="{{ route('production.sewing.index') }}">Sewing Section</a></li>
                                <li class="breadcrumb-item"><a href="{{ route('production.sewing.show', $sewingEntry) }}">{{ $sewingEntry->bundleTicket->bundle_number ?? 'N/A' }}</a></li>
                                <li class="breadcrumb-item active">Edit</li>
                            </ol>
                        </nav>
                    </div>
                    <div>
                        <a href="{{ route('production.sewing.show', $sewingEntry) }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Details
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Edit Form -->
        <form method="POST" action="{{ route('production.sewing.update', $sewingEntry) }}">
            @csrf
            @method('PUT')
            
            <div class="row">
                <div class="col-md-8">
                    <!-- Basic Information -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Basic Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Bundle Number</label>
                                        <input type="text" class="form-control" value="{{ $sewingEntry->bundleTicket->bundle_number ?? 'N/A' }}" readonly>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Style Number</label>
                                        <input type="text" class="form-control" value="{{ $sewingEntry->bundleTicket->style_number ?? 'N/A' }}" readonly>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Sewing Line <span class="text-danger">*</span></label>
                                        <select name="sewing_line_id" class="form-select @error('sewing_line_id') is-invalid @enderror" required>
                                            <option value="">Select Sewing Line</option>
                                            @foreach($sewingLines as $line)
                                            <option value="{{ $line->id }}" {{ old('sewing_line_id', $sewingEntry->sewing_line_id) == $line->id ? 'selected' : '' }}>
                                                {{ $line->line_name }} ({{ $line->line_code }})
                                            </option>
                                            @endforeach
                                        </select>
                                        @error('sewing_line_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Operator</label>
                                        <select name="operator_id" class="form-select @error('operator_id') is-invalid @enderror">
                                            <option value="">Select Operator</option>
                                            @foreach($operators as $operator)
                                            <option value="{{ $operator->id }}" {{ old('operator_id', $sewingEntry->operator_id) == $operator->id ? 'selected' : '' }}>
                                                {{ $operator->name }} ({{ $operator->operator_id }})
                                            </option>
                                            @endforeach
                                        </select>
                                        @error('operator_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Production Information -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Production Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label class="form-label">Input Quantity</label>
                                        <input type="number" class="form-control" value="{{ $sewingEntry->input_quantity }}" readonly>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label class="form-label">Output Quantity</label>
                                        <input type="number" name="output_quantity" class="form-control @error('output_quantity') is-invalid @enderror" 
                                               value="{{ old('output_quantity', $sewingEntry->output_quantity) }}" min="0" max="{{ $sewingEntry->input_quantity }}" id="output_quantity">
                                        @error('output_quantity')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label class="form-label">Defect Quantity</label>
                                        <input type="number" name="defect_quantity" class="form-control @error('defect_quantity') is-invalid @enderror" 
                                               value="{{ old('defect_quantity', $sewingEntry->defect_quantity) }}" min="0" id="defect_quantity">
                                        @error('defect_quantity')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label class="form-label">Actual SMV</label>
                                        <input type="number" name="actual_smv" class="form-control @error('actual_smv') is-invalid @enderror" 
                                               value="{{ old('actual_smv', $sewingEntry->actual_smv) }}" min="0" step="0.01">
                                        @error('actual_smv')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Status <span class="text-danger">*</span></label>
                                        <select name="status" class="form-select @error('status') is-invalid @enderror" required>
                                            <option value="input" {{ old('status', $sewingEntry->status) == 'input' ? 'selected' : '' }}>Input</option>
                                            <option value="in_progress" {{ old('status', $sewingEntry->status) == 'in_progress' ? 'selected' : '' }}>In Progress</option>
                                            <option value="output" {{ old('status', $sewingEntry->status) == 'output' ? 'selected' : '' }}>Output</option>
                                            <option value="completed" {{ old('status', $sewingEntry->status) == 'completed' ? 'selected' : '' }}>Completed</option>
                                        </select>
                                        @error('status')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Efficiency Achieved (%)</label>
                                        <input type="number" class="form-control" value="{{ number_format($sewingEntry->efficiency_achieved ?? 0, 2) }}" readonly>
                                        <small class="form-text text-muted">Calculated automatically based on SMV and processing time</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Defect Information -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Defect Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label">Defect Types</label>
                                <div class="row">
                                    @php
                                        $commonDefects = ['Broken stitch', 'Uneven seam', 'Wrong measurement', 'Fabric defect', 'Color variation', 'Missing button', 'Loose thread'];
                                        $selectedDefects = old('defect_types', $sewingEntry->defect_types ?? []);
                                    @endphp
                                    @foreach($commonDefects as $defect)
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="defect_types[]" value="{{ $defect }}" 
                                                   id="defect_{{ $loop->index }}" {{ in_array($defect, $selectedDefects) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="defect_{{ $loop->index }}">
                                                {{ $defect }}
                                            </label>
                                        </div>
                                    </div>
                                    @endforeach
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Notes</label>
                                <textarea name="notes" class="form-control @error('notes') is-invalid @enderror" rows="3" 
                                          placeholder="Enter additional notes...">{{ old('notes', $sewingEntry->notes) }}</textarea>
                                @error('notes')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <!-- Actions -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Actions</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Update Entry
                                </button>
                                <a href="{{ route('production.sewing.show', $sewingEntry) }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times"></i> Cancel
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Current Information -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Current Status</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label"><strong>Current Status:</strong></label>
                                <div>
                                    <span class="badge bg-{{ 
                                        $sewingEntry->status == 'completed' ? 'success' : 
                                        ($sewingEntry->status == 'in_progress' ? 'primary' : 'secondary') 
                                    }}">
                                        {{ ucfirst(str_replace('_', ' ', $sewingEntry->status)) }}
                                    </span>
                                </div>
                            </div>
                            
                            @if($sewingEntry->efficiency_achieved)
                            <div class="mb-3">
                                <label class="form-label"><strong>Current Efficiency:</strong></label>
                                <div class="progress" style="height: 20px;">
                                    <div class="progress-bar bg-info" role="progressbar" style="width: {{ min($sewingEntry->efficiency_achieved, 100) }}%">
                                        {{ number_format($sewingEntry->efficiency_achieved, 1) }}%
                                    </div>
                                </div>
                            </div>
                            @endif
                            
                            @if($sewingEntry->bundleTicket)
                            <div class="mb-3">
                                <label class="form-label"><strong>Bundle Info:</strong></label>
                                <p class="mb-1">Quantity: {{ number_format($sewingEntry->bundleTicket->quantity) }}</p>
                                @if($sewingEntry->bundleTicket->smv)
                                <p class="mb-1">SMV: {{ $sewingEntry->bundleTicket->smv }} min</p>
                                @endif
                            </div>
                            @endif
                        </div>
                    </div>

                    <!-- Processing Time -->
                    @if($sewingEntry->processing_time_minutes)
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Processing Time</h5>
                        </div>
                        <div class="card-body">
                            <div class="text-center">
                                <h4 class="text-primary">{{ number_format($sewingEntry->processing_time_minutes) }}</h4>
                                <small class="text-muted">Minutes</small>
                            </div>
                            @if($sewingEntry->bundleTicket && $sewingEntry->bundleTicket->smv && $sewingEntry->output_quantity)
                            <div class="mt-3">
                                <small class="text-muted">
                                    Standard: {{ number_format($sewingEntry->bundleTicket->smv * $sewingEntry->output_quantity) }} minutes
                                </small>
                            </div>
                            @endif
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </form>
    </div>
</div>
@endsection

@push('css')
<style>
.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.badge {
    font-size: 0.75em;
}
</style>
@endpush

@push('js')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const outputQtyInput = document.getElementById('output_quantity');
    const defectQtyInput = document.getElementById('defect_quantity');

    function validateQuantities() {
        const inputQty = parseInt(document.querySelector('input[value="{{ $sewingEntry->input_quantity }}"]').value) || 0;
        const outputQty = parseInt(outputQtyInput.value) || 0;
        const defectQty = parseInt(defectQtyInput.value) || 0;

        // Validate that output doesn't exceed input
        if (outputQty > inputQty) {
            alert('Output quantity cannot exceed input quantity (' + inputQty + ')');
            outputQtyInput.value = inputQty;
            return false;
        }
        
        // Validate that defects don't exceed output
        if (defectQty > outputQty) {
            alert('Defect quantity cannot exceed output quantity (' + outputQty + ')');
            defectQtyInput.value = outputQty;
            return false;
        }
        
        return true;
    }

    [outputQtyInput, defectQtyInput].forEach(input => {
        if (input) {
            input.addEventListener('change', validateQuantities);
        }
    });

    // Form validation on submit
    document.querySelector('form').addEventListener('submit', function(e) {
        if (!validateQuantities()) {
            e.preventDefault();
        }
    });
});
</script>
@endpush
