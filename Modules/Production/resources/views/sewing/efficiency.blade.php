@extends('layouts.master')

@section('title', 'Sewing Efficiency Analysis')

@section('main_content')
<div class="erp-state-overview-section">
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="page-title">Sewing Efficiency Analysis</h2>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="{{ route('production.sewing.index') }}">Sewing Section</a></li>
                                <li class="breadcrumb-item active">Efficiency Analysis</li>
                            </ol>
                        </nav>
                    </div>
                    <div class="d-flex gap-2">
                        <a href="{{ route('production.sewing.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Sewing
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="GET" action="{{ route('production.sewing.efficiency') }}" class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">Date Range</label>
                        <select name="date_range" class="form-select">
                            <option value="today" {{ request('date_range') == 'today' ? 'selected' : '' }}>Today</option>
                            <option value="yesterday" {{ request('date_range') == 'yesterday' ? 'selected' : '' }}>Yesterday</option>
                            <option value="this_week" {{ request('date_range') == 'this_week' ? 'selected' : '' }}>This Week</option>
                            <option value="last_week" {{ request('date_range') == 'last_week' ? 'selected' : '' }}>Last Week</option>
                            <option value="this_month" {{ request('date_range') == 'this_month' ? 'selected' : '' }}>This Month</option>
                            <option value="custom" {{ request('date_range') == 'custom' ? 'selected' : '' }}>Custom Range</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">Start Date</label>
                        <input type="date" name="start_date" class="form-control" value="{{ request('start_date') }}">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">End Date</label>
                        <input type="date" name="end_date" class="form-control" value="{{ request('end_date') }}">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Sewing Line</label>
                        <select name="sewing_line_id" class="form-select">
                            <option value="">All Lines</option>
                            @foreach($sewingLines as $line)
                            <option value="{{ $line->id }}" {{ request('sewing_line_id') == $line->id ? 'selected' : '' }}>
                                {{ $line->line_number }} - {{ $line->line_name }}
                            </option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-md-2 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i> Analyze
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Overall Efficiency Summary -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <h3>{{ number_format($overallMetrics['avg_efficiency'], 1) }}%</h3>
                        <p class="mb-0">Overall Efficiency</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <h3>{{ number_format($overallMetrics['total_output']) }}</h3>
                        <p class="mb-0">Total Output</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <h3>{{ number_format($overallMetrics['total_smv']) }}</h3>
                        <p class="mb-0">Total SMV</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body text-center">
                        <h3>{{ number_format($overallMetrics['defect_rate'], 2) }}%</h3>
                        <p class="mb-0">Defect Rate</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Efficiency Trend Chart -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Efficiency Trend</h5>
            </div>
            <div class="card-body">
                <canvas id="efficiencyTrendChart" height="100"></canvas>
            </div>
        </div>

        <!-- Line-wise Efficiency Analysis -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Line-wise Efficiency Analysis</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>Line</th>
                                <th>Entries</th>
                                <th>Total Input</th>
                                <th>Total Output</th>
                                <th>Avg Efficiency</th>
                                <th>Best Efficiency</th>
                                <th>Worst Efficiency</th>
                                <th>Defect Rate</th>
                                <th>Performance</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($lineEfficiency as $lineData)
                            <tr>
                                <td>
                                    <strong>{{ $lineData['line']->line_number }}</strong>
                                    <br>
                                    <small class="text-muted">{{ $lineData['line']->line_name }}</small>
                                </td>
                                <td>{{ $lineData['entries_count'] }}</td>
                                <td>{{ number_format($lineData['total_input']) }}</td>
                                <td>{{ number_format($lineData['total_output']) }}</td>
                                <td>
                                    <span class="badge bg-{{ $lineData['avg_efficiency'] >= 85 ? 'success' : ($lineData['avg_efficiency'] >= 70 ? 'warning' : 'danger') }}">
                                        {{ number_format($lineData['avg_efficiency'], 1) }}%
                                    </span>
                                </td>
                                <td>{{ number_format($lineData['best_efficiency'], 1) }}%</td>
                                <td>{{ number_format($lineData['worst_efficiency'], 1) }}%</td>
                                <td>
                                    <span class="badge bg-{{ $lineData['defect_rate'] <= 2 ? 'success' : ($lineData['defect_rate'] <= 5 ? 'warning' : 'danger') }}">
                                        {{ number_format($lineData['defect_rate'], 2) }}%
                                    </span>
                                </td>
                                <td>
                                    <div class="progress" style="height: 20px;">
                                        <div class="progress-bar bg-{{ $lineData['avg_efficiency'] >= 85 ? 'success' : ($lineData['avg_efficiency'] >= 70 ? 'warning' : 'danger') }}" 
                                             style="width: {{ min($lineData['avg_efficiency'], 100) }}%">
                                            {{ number_format($lineData['avg_efficiency'], 1) }}%
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Operator Efficiency Analysis -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Top Performing Operators</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>Operator</th>
                                <th>Line</th>
                                <th>Entries</th>
                                <th>Total Output</th>
                                <th>Avg Efficiency</th>
                                <th>Total SMV</th>
                                <th>Defect Rate</th>
                                <th>Rating</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($topOperators as $operatorData)
                            <tr>
                                <td>
                                    <strong>{{ $operatorData['operator']->operator_id }}</strong>
                                    <br>
                                    <small class="text-muted">{{ $operatorData['operator']->name }}</small>
                                </td>
                                <td>{{ $operatorData['operator']->sewingLine->line_number ?? 'N/A' }}</td>
                                <td>{{ $operatorData['entries_count'] }}</td>
                                <td>{{ number_format($operatorData['total_output']) }}</td>
                                <td>
                                    <span class="badge bg-{{ $operatorData['avg_efficiency'] >= 90 ? 'success' : ($operatorData['avg_efficiency'] >= 80 ? 'warning' : 'danger') }}">
                                        {{ number_format($operatorData['avg_efficiency'], 1) }}%
                                    </span>
                                </td>
                                <td>{{ number_format($operatorData['total_smv'], 1) }}</td>
                                <td>{{ number_format($operatorData['defect_rate'], 2) }}%</td>
                                <td>
                                    @php
                                        $rating = 5;
                                        if ($operatorData['avg_efficiency'] < 70) $rating = 2;
                                        elseif ($operatorData['avg_efficiency'] < 80) $rating = 3;
                                        elseif ($operatorData['avg_efficiency'] < 90) $rating = 4;
                                    @endphp
                                    @for($i = 1; $i <= 5; $i++)
                                        <i class="fas fa-star text-{{ $i <= $rating ? 'warning' : 'muted' }}"></i>
                                    @endfor
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Efficiency Distribution -->
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Efficiency Distribution</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="efficiencyDistributionChart"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">SMV vs Output Analysis</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="smvOutputChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('js')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Efficiency Trend Chart
const trendCtx = document.getElementById('efficiencyTrendChart').getContext('2d');
new Chart(trendCtx, {
    type: 'line',
    data: {
        labels: @json($trendData['dates'] ?? []),
        datasets: [{
            label: 'Average Efficiency',
            data: @json($trendData['efficiency'] ?? []),
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.1)',
            tension: 0.1
        }, {
            label: 'Target (85%)',
            data: Array(@json(count($trendData['dates'] ?? []))).fill(85),
            borderColor: 'rgb(255, 99, 132)',
            backgroundColor: 'rgba(255, 99, 132, 0.1)',
            borderDash: [5, 5]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                max: 100
            }
        }
    }
});

// Efficiency Distribution Chart
const distCtx = document.getElementById('efficiencyDistributionChart').getContext('2d');
new Chart(distCtx, {
    type: 'doughnut',
    data: {
        labels: ['Excellent (≥90%)', 'Good (80-89%)', 'Average (70-79%)', 'Poor (<70%)'],
        datasets: [{
            data: @json($efficiencyDistribution ?? []),
            backgroundColor: [
                'rgba(40, 167, 69, 0.8)',
                'rgba(255, 193, 7, 0.8)',
                'rgba(255, 152, 0, 0.8)',
                'rgba(220, 53, 69, 0.8)'
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: true
    }
});

// SMV vs Output Chart
const smvCtx = document.getElementById('smvOutputChart').getContext('2d');
new Chart(smvCtx, {
    type: 'scatter',
    data: {
        datasets: [{
            label: 'SMV vs Output',
            data: @json($smvOutputData ?? []),
            backgroundColor: 'rgba(54, 162, 235, 0.6)',
            borderColor: 'rgba(54, 162, 235, 1)'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: true,
        scales: {
            x: {
                title: {
                    display: true,
                    text: 'SMV'
                }
            },
            y: {
                title: {
                    display: true,
                    text: 'Output Quantity'
                }
            }
        }
    }
});
</script>
@endpush
