@extends('layouts.master')

@section('title', 'Sewing Section')

@section('main_content')
<div class="erp-state-overview-section">
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h2 class="page-title">Sewing Section</h2>
                    <div class="d-flex gap-2">
                        @can('production.sewing.create')
                        <a href="{{ route('production.sewing.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> New Sewing Entry
                        </a>
                        @endcan
                        @can('production.sewing.line-tracking')
                        <a href="{{ route('production.sewing.line-tracking') }}" class="btn btn-outline-info">
                            <i class="fas fa-chart-line"></i> Line Tracking
                        </a>
                        @endcan
                        @can('production.sewing.efficiency')
                        <a href="{{ route('production.sewing.efficiency') }}" class="btn btn-outline-success">
                            <i class="fas fa-tachometer-alt"></i> Efficiency
                        </a>
                        @endcan
                        @can('production.sewing.view')
                        <a href="{{ route('production.sewing.bundle-tickets') }}" class="btn btn-outline-warning">
                            <i class="fas fa-ticket-alt"></i> Bundle Tickets
                        </a>
                        @endcan
                        @can('production.exports')
                        <div class="dropdown">
                            <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-download"></i> Export
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{{ route('production.exports.sewing-report.pdf', request()->query()) }}">
                                    <i class="fas fa-file-pdf text-danger"></i> PDF Report
                                </a></li>
                            </ul>
                        </div>
                        @endcan
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="GET" action="{{ route('production.sewing.index') }}" class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">Sewing Line</label>
                        <select name="sewing_line_id" class="form-select">
                            <option value="">All Lines</option>
                            @foreach($sewingLines as $line)
                            <option value="{{ $line->id }}" {{ request('sewing_line_id') == $line->id ? 'selected' : '' }}>
                                {{ $line->line_number }} - {{ $line->line_name }}
                            </option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Operator</label>
                        <select name="operator_id" class="form-select">
                            <option value="">All Operators</option>
                            @foreach($operators as $operator)
                            <option value="{{ $operator->id }}" {{ request('operator_id') == $operator->id ? 'selected' : '' }}>
                                {{ $operator->operator_id }} - {{ $operator->name }}
                            </option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Status</label>
                        <select name="status" class="form-select">
                            <option value="">All Status</option>
                            <option value="input" {{ request('status') == 'input' ? 'selected' : '' }}>Input</option>
                            <option value="in_progress" {{ request('status') == 'in_progress' ? 'selected' : '' }}>In Progress</option>
                            <option value="output" {{ request('status') == 'output' ? 'selected' : '' }}>Output</option>
                            <option value="completed" {{ request('status') == 'completed' ? 'selected' : '' }}>Completed</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Input Date</label>
                        <input type="date" name="input_date" class="form-control" value="{{ request('input_date') }}">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Sort By</label>
                        <select name="sort_by" class="form-select">
                            <option value="input_datetime" {{ request('sort_by') == 'input_datetime' ? 'selected' : '' }}>Input Date</option>
                            <option value="output_datetime" {{ request('sort_by') == 'output_datetime' ? 'selected' : '' }}>Output Date</option>
                            <option value="efficiency_achieved" {{ request('sort_by') == 'efficiency_achieved' ? 'selected' : '' }}>Efficiency</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Sort Direction</label>
                        <select name="sort_direction" class="form-select">
                            <option value="desc" {{ request('sort_direction') == 'desc' ? 'selected' : '' }}>Descending</option>
                            <option value="asc" {{ request('sort_direction') == 'asc' ? 'selected' : '' }}>Ascending</option>
                        </select>
                    </div>
                    <div class="col-md-6 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="fas fa-search"></i> Filter
                        </button>
                        <a href="{{ route('production.sewing.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i> Clear
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Sewing Entries Table -->
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>Bundle Ticket</th>
                                <th>Line</th>
                                <th>Operator</th>
                                <th>Input Qty</th>
                                <th>Output Qty</th>
                                <th>Defects</th>
                                <th>Efficiency</th>
                                <th>Input Date</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($sewingEntries as $entry)
                            <tr>
                                <td>
                                    <a href="{{ route('production.sewing.show', $entry) }}" class="text-decoration-none">
                                        {{ $entry->bundleTicket->ticket_number ?? 'N/A' }}
                                    </a>
                                    <br>
                                    <small class="text-muted">{{ $entry->bundleTicket->bundle_number ?? '' }}</small>
                                </td>
                                <td>
                                    {{ $entry->sewingLine->line_number ?? 'N/A' }}
                                    <br>
                                    <small class="text-muted">{{ $entry->sewingLine->line_name ?? '' }}</small>
                                </td>
                                <td>
                                    {{ $entry->operator->operator_id ?? 'N/A' }}
                                    <br>
                                    <small class="text-muted">{{ $entry->operator->name ?? '' }}</small>
                                </td>
                                <td>{{ number_format($entry->input_quantity) }}</td>
                                <td>{{ number_format($entry->output_quantity ?? 0) }}</td>
                                <td>
                                    <span class="badge bg-{{ $entry->defect_quantity > 0 ? 'danger' : 'success' }}">
                                        {{ number_format($entry->defect_quantity ?? 0) }}
                                    </span>
                                </td>
                                <td>
                                    @if($entry->efficiency_achieved)
                                    <span class="badge bg-{{ $entry->efficiency_achieved >= 85 ? 'success' : ($entry->efficiency_achieved >= 70 ? 'warning' : 'danger') }}">
                                        {{ number_format($entry->efficiency_achieved, 1) }}%
                                    </span>
                                    @else
                                    <span class="text-muted">-</span>
                                    @endif
                                </td>
                                <td>{{ $entry->input_datetime->format('M d, Y H:i') }}</td>
                                <td>
                                    <span class="badge bg-{{ 
                                        $entry->status == 'completed' ? 'success' : 
                                        ($entry->status == 'in_progress' ? 'primary' : 
                                        ($entry->status == 'output' ? 'info' : 'secondary')) 
                                    }}">
                                        {{ ucfirst(str_replace('_', ' ', $entry->status)) }}
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        @can('production.sewing.view')
                                        <a href="{{ route('production.sewing.show', $entry) }}" class="btn btn-outline-primary" title="View">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        @endcan
                                        @can('production.sewing.edit')
                                        <a href="{{ route('production.sewing.edit', $entry) }}" class="btn btn-outline-warning" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        @endcan
                                        @can('production.sewing.delete')
                                        @if($entry->status != 'completed')
                                        <form method="POST" action="{{ route('production.sewing.destroy', $entry) }}" class="d-inline">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-outline-danger" title="Delete" onclick="return confirm('Are you sure you want to delete this sewing entry?')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                        @endif
                                        @endcan
                                    </div>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="10" class="text-center py-4">
                                    <div class="text-muted">
                                        <i class="fas fa-tshirt fa-3x mb-3"></i>
                                        <p>No sewing entries found.</p>
                                        @can('production.sewing.create')
                                        <a href="{{ route('production.sewing.create') }}" class="btn btn-primary">
                                            Create First Sewing Entry
                                        </a>
                                        @endcan
                                    </div>
                                </td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                @if($sewingEntries->hasPages())
                <div class="d-flex justify-content-center mt-4">
                    {{ $sewingEntries->links() }}
                </div>
                @endif
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="row mt-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <h4>{{ $sewingEntries->where('status', 'input')->count() }}</h4>
                        <small>Input</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body text-center">
                        <h4>{{ $sewingEntries->where('status', 'in_progress')->count() }}</h4>
                        <small>In Progress</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <h4>{{ $sewingEntries->where('status', 'completed')->count() }}</h4>
                        <small>Completed</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <h4>{{ number_format($sewingEntries->sum('output_quantity')) }}</h4>
                        <small>Total Output</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('css')
<style>
.table th {
    font-weight: 600;
    border-bottom: 2px solid #dee2e6;
}
.btn-group-sm > .btn {
    padding: 0.25rem 0.5rem;
}
</style>
@endpush
