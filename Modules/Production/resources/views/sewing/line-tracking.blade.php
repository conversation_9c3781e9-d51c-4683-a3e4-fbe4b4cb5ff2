@extends('layouts.master')

@section('title', 'Sewing Line Tracking')

@section('main_content')
<div class="erp-state-overview-section">
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="page-title">Sewing Line Tracking</h2>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="{{ route('production.sewing.index') }}">Sewing Section</a></li>
                                <li class="breadcrumb-item active">Line Tracking</li>
                            </ol>
                        </nav>
                    </div>
                    <div class="d-flex gap-2">
                        <a href="{{ route('production.sewing.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Sewing
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Date Filter -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="GET" action="{{ route('production.sewing.line-tracking') }}" class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">Tracking Date</label>
                        <input type="date" name="date" class="form-control" value="{{ request('date', now()->format('Y-m-d')) }}">
                    </div>
                    <div class="col-md-3 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="fas fa-search"></i> Update
                        </button>
                        <button type="button" class="btn btn-outline-info" onclick="autoRefresh()">
                            <i class="fas fa-sync"></i> Auto Refresh
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Line Performance Overview -->
        <div class="row mb-4">
            @foreach($sewingLines as $line)
            @php
                $todayEntries = $line->sewingEntries;
                $totalInput = $todayEntries->sum('input_quantity');
                $totalOutput = $todayEntries->sum('output_quantity');
                $totalDefects = $todayEntries->sum('defect_quantity');
                $avgEfficiency = $todayEntries->where('efficiency_achieved', '>', 0)->avg('efficiency_achieved');
                $defectRate = $totalOutput > 0 ? ($totalDefects / $totalOutput) * 100 : 0;
            @endphp
            <div class="col-md-6 col-lg-4 mb-3">
                <div class="card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="card-title mb-0">{{ $line->line_number }} - {{ $line->line_name }}</h6>
                        <span class="badge bg-{{ $line->status == 'active' ? 'success' : 'secondary' }}">
                            {{ ucfirst($line->status) }}
                        </span>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="metric">
                                    <div class="metric-value">{{ number_format($totalInput) }}</div>
                                    <div class="metric-label">Input</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="metric">
                                    <div class="metric-value">{{ number_format($totalOutput) }}</div>
                                    <div class="metric-label">Output</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row text-center mt-3">
                            <div class="col-6">
                                <div class="metric">
                                    <div class="metric-value text-{{ $avgEfficiency >= 85 ? 'success' : ($avgEfficiency >= 70 ? 'warning' : 'danger') }}">
                                        {{ $avgEfficiency ? number_format($avgEfficiency, 1) . '%' : 'N/A' }}
                                    </div>
                                    <div class="metric-label">Efficiency</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="metric">
                                    <div class="metric-value text-{{ $defectRate <= 2 ? 'success' : ($defectRate <= 5 ? 'warning' : 'danger') }}">
                                        {{ number_format($defectRate, 1) }}%
                                    </div>
                                    <div class="metric-label">Defect Rate</div>
                                </div>
                            </div>
                        </div>

                        <div class="progress mt-3" style="height: 8px;">
                            @php
                                $capacity = $line->daily_capacity ?? 300;
                                $progress = $capacity > 0 ? min(($totalOutput / $capacity) * 100, 100) : 0;
                            @endphp
                            <div class="progress-bar bg-{{ $progress >= 90 ? 'success' : ($progress >= 70 ? 'warning' : 'info') }}" 
                                 style="width: {{ $progress }}%"></div>
                        </div>
                        <small class="text-muted">{{ number_format($progress, 1) }}% of daily capacity</small>
                    </div>
                    <div class="card-footer">
                        <div class="d-flex justify-content-between">
                            <small class="text-muted">{{ $todayEntries->count() }} entries</small>
                            <small class="text-muted">{{ $line->operators->count() }} operators</small>
                        </div>
                    </div>
                </div>
            </div>
            @endforeach
        </div>

        <!-- Hourly Production Chart -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Hourly Production Tracking</h5>
            </div>
            <div class="card-body">
                <canvas id="hourlyProductionChart" height="100"></canvas>
            </div>
        </div>

        <!-- Real-time Line Status -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Real-time Line Status</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>Line</th>
                                <th>Current Bundle</th>
                                <th>Operator</th>
                                <th>Input Time</th>
                                <th>Progress</th>
                                <th>Expected Output</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($sewingLines as $line)
                            @php
                                $currentEntry = $line->sewingEntries->where('status', 'in_progress')->first() ?? 
                                               $line->sewingEntries->sortByDesc('input_datetime')->first();
                            @endphp
                            <tr>
                                <td>
                                    <strong>{{ $line->line_number }}</strong>
                                    <br>
                                    <small class="text-muted">{{ $line->line_name }}</small>
                                </td>
                                <td>
                                    @if($currentEntry)
                                    {{ $currentEntry->bundleTicket->bundle_number ?? 'N/A' }}
                                    <br>
                                    <small class="text-muted">{{ $currentEntry->bundleTicket->ticket_number ?? '' }}</small>
                                    @else
                                    <span class="text-muted">No active bundle</span>
                                    @endif
                                </td>
                                <td>
                                    @if($currentEntry)
                                    {{ $currentEntry->operator->name ?? 'N/A' }}
                                    <br>
                                    <small class="text-muted">{{ $currentEntry->operator->operator_id ?? '' }}</small>
                                    @else
                                    <span class="text-muted">-</span>
                                    @endif
                                </td>
                                <td>
                                    @if($currentEntry)
                                    {{ $currentEntry->input_datetime->format('H:i') }}
                                    <br>
                                    <small class="text-muted">{{ $currentEntry->input_datetime->diffForHumans() }}</small>
                                    @else
                                    <span class="text-muted">-</span>
                                    @endif
                                </td>
                                <td>
                                    @if($currentEntry)
                                    @php
                                        $progress = $currentEntry->input_quantity > 0 ? 
                                                   (($currentEntry->output_quantity ?? 0) / $currentEntry->input_quantity) * 100 : 0;
                                    @endphp
                                    <div class="progress" style="height: 20px;">
                                        <div class="progress-bar" style="width: {{ $progress }}%">
                                            {{ number_format($progress, 1) }}%
                                        </div>
                                    </div>
                                    @else
                                    <span class="text-muted">-</span>
                                    @endif
                                </td>
                                <td>
                                    @if($currentEntry)
                                    @php
                                        $hoursWorked = $currentEntry->input_datetime->diffInHours(now());
                                        $expectedOutput = $hoursWorked * ($line->hourly_capacity ?? 30);
                                    @endphp
                                    {{ number_format($expectedOutput) }}
                                    @else
                                    <span class="text-muted">-</span>
                                    @endif
                                </td>
                                <td>
                                    @if($currentEntry)
                                    <span class="badge bg-{{ 
                                        $currentEntry->status == 'completed' ? 'success' : 
                                        ($currentEntry->status == 'in_progress' ? 'primary' : 'secondary') 
                                    }}">
                                        {{ ucfirst(str_replace('_', ' ', $currentEntry->status)) }}
                                    </span>
                                    @else
                                    <span class="badge bg-secondary">Idle</span>
                                    @endif
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        @if($currentEntry)
                                        <a href="{{ route('production.sewing.show', $currentEntry) }}" class="btn btn-outline-primary" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        @endif
                                        <a href="{{ route('production.sewing.create') }}?line={{ $line->id }}" class="btn btn-outline-success" title="New Entry">
                                            <i class="fas fa-plus"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('css')
<style>
.metric {
    padding: 10px 0;
}
.metric-value {
    font-size: 1.5rem;
    font-weight: bold;
    line-height: 1;
}
.metric-label {
    font-size: 0.75rem;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}
.progress {
    background-color: #e9ecef;
}
</style>
@endpush

@push('js')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Hourly Production Chart
const ctx = document.getElementById('hourlyProductionChart').getContext('2d');
const hourlyChart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: @json($hourlyData['hours'] ?? []),
        datasets: [{
            label: 'Total Output',
            data: @json($hourlyData['output'] ?? []),
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.1)',
            tension: 0.1
        }, {
            label: 'Target',
            data: @json($hourlyData['target'] ?? []),
            borderColor: 'rgb(255, 99, 132)',
            backgroundColor: 'rgba(255, 99, 132, 0.1)',
            borderDash: [5, 5],
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// Auto refresh functionality
let autoRefreshInterval;
function autoRefresh() {
    if (autoRefreshInterval) {
        clearInterval(autoRefreshInterval);
        autoRefreshInterval = null;
        document.querySelector('[onclick="autoRefresh()"]').innerHTML = '<i class="fas fa-sync"></i> Auto Refresh';
    } else {
        autoRefreshInterval = setInterval(() => {
            window.location.reload();
        }, 30000); // Refresh every 30 seconds
        document.querySelector('[onclick="autoRefresh()"]').innerHTML = '<i class="fas fa-stop"></i> Stop Refresh';
    }
}
</script>
@endpush
