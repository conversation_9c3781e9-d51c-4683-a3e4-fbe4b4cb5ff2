@extends('layouts.master')

@section('title', 'Sewing Entry Details')

@section('main_content')
<div class="erp-state-overview-section">
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="page-title">Sewing Entry Details</h2>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="{{ route('production.sewing.index') }}">Sewing Section</a></li>
                                <li class="breadcrumb-item active">{{ $sewingEntry->bundleTicket->bundle_number ?? 'N/A' }}</li>
                            </ol>
                        </nav>
                    </div>
                    <div class="d-flex gap-2">
                        @can('production.sewing.edit')
                        <a href="{{ route('production.sewing.edit', $sewingEntry) }}" class="btn btn-warning">
                            <i class="fas fa-edit"></i> Edit Entry
                        </a>
                        @endcan
                        <a href="{{ route('production.sewing.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> Back to List
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Basic Information -->
            <div class="col-md-8">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Basic Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>Bundle Number:</strong></td>
                                        <td>{{ $sewingEntry->bundleTicket->bundle_number ?? 'N/A' }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Ticket Number:</strong></td>
                                        <td>{{ $sewingEntry->bundleTicket->ticket_number ?? 'N/A' }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Style Number:</strong></td>
                                        <td>{{ $sewingEntry->bundleTicket->style_number ?? 'N/A' }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>PO Number:</strong></td>
                                        <td>{{ $sewingEntry->bundleTicket->po_number ?? 'N/A' }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Color:</strong></td>
                                        <td>{{ $sewingEntry->bundleTicket->color ?? 'N/A' }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Size:</strong></td>
                                        <td>{{ $sewingEntry->bundleTicket->size ?? 'N/A' }}</td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>Sewing Line:</strong></td>
                                        <td>{{ $sewingEntry->sewingLine->line_name ?? 'N/A' }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Line Code:</strong></td>
                                        <td>{{ $sewingEntry->sewingLine->line_code ?? 'N/A' }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Operator:</strong></td>
                                        <td>{{ $sewingEntry->operator->name ?? 'N/A' }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Input Date:</strong></td>
                                        <td>{{ $sewingEntry->input_datetime ? $sewingEntry->input_datetime->format('M d, Y H:i') : 'Not set' }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Output Date:</strong></td>
                                        <td>{{ $sewingEntry->output_datetime ? $sewingEntry->output_datetime->format('M d, Y H:i') : 'Not completed' }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>SMV:</strong></td>
                                        <td>{{ $sewingEntry->bundleTicket->smv ?? 'N/A' }} minutes</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Production Information -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Production Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4 class="text-primary">{{ number_format($sewingEntry->input_quantity) }}</h4>
                                    <small class="text-muted">Input Quantity</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4 class="text-success">{{ number_format($sewingEntry->output_quantity ?? 0) }}</h4>
                                    <small class="text-muted">Output Quantity</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4 class="text-danger">{{ number_format($sewingEntry->defect_quantity ?? 0) }}</h4>
                                    <small class="text-muted">Defect Quantity</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4 class="text-info">{{ number_format($sewingEntry->good_quantity ?? 0) }}</h4>
                                    <small class="text-muted">Good Quantity</small>
                                </div>
                            </div>
                        </div>
                        
                        @if($sewingEntry->output_quantity > 0)
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <div class="progress mb-2" style="height: 20px;">
                                    @php
                                        $defectPercentage = $sewingEntry->defect_percentage ?? 0;
                                        $goodPercentage = 100 - $defectPercentage;
                                    @endphp
                                    <div class="progress-bar bg-success" role="progressbar" style="width: {{ $goodPercentage }}%">
                                        {{ number_format($goodPercentage, 1) }}% Good
                                    </div>
                                </div>
                                <small class="text-muted">Quality Rate</small>
                            </div>
                            <div class="col-md-6">
                                @if($sewingEntry->efficiency_achieved)
                                <div class="progress mb-2" style="height: 20px;">
                                    <div class="progress-bar bg-info" role="progressbar" style="width: {{ min($sewingEntry->efficiency_achieved, 100) }}%">
                                        {{ number_format($sewingEntry->efficiency_achieved, 1) }}%
                                    </div>
                                </div>
                                <small class="text-muted">Efficiency Achieved</small>
                                @endif
                            </div>
                        </div>
                        @endif
                        
                        @if($sewingEntry->processing_time_minutes)
                        <div class="row mt-3">
                            <div class="col-md-12">
                                <div class="alert alert-info">
                                    <strong>Processing Time:</strong> {{ number_format($sewingEntry->processing_time_minutes) }} minutes
                                    @if($sewingEntry->bundleTicket && $sewingEntry->bundleTicket->smv)
                                        (Standard: {{ $sewingEntry->bundleTicket->smv * $sewingEntry->output_quantity }} minutes)
                                    @endif
                                </div>
                            </div>
                        </div>
                        @endif
                    </div>
                </div>

                <!-- Finishing Entries -->
                @if($sewingEntry->finishingEntries->count() > 0)
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Finishing Entries</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Process</th>
                                        <th>Operator</th>
                                        <th>Input Qty</th>
                                        <th>Output Qty</th>
                                        <th>QC Status</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($sewingEntry->finishingEntries as $finishing)
                                    <tr>
                                        <td>{{ $finishing->finishingProcess->process_name ?? 'N/A' }}</td>
                                        <td>{{ $finishing->operator_name }}</td>
                                        <td>{{ number_format($finishing->input_quantity) }}</td>
                                        <td>{{ number_format($finishing->output_quantity ?? 0) }}</td>
                                        <td>
                                            <span class="badge bg-{{ 
                                                $finishing->qc_status == 'pass' ? 'success' : 
                                                ($finishing->qc_status == 'fail' ? 'danger' : 
                                                ($finishing->qc_status == 'rework' ? 'warning' : 'secondary')) 
                                            }}">
                                                {{ ucfirst($finishing->qc_status) }}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-{{ 
                                                $finishing->status == 'completed' ? 'success' : 
                                                ($finishing->status == 'in_progress' ? 'primary' : 'secondary') 
                                            }}">
                                                {{ ucfirst(str_replace('_', ' ', $finishing->status)) }}
                                            </span>
                                        </td>
                                        <td>
                                            <a href="{{ route('production.finishing.show', $finishing) }}" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                @endif

                <!-- Notes -->
                @if($sewingEntry->notes)
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Notes</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            {{ $sewingEntry->notes }}
                        </div>
                    </div>
                </div>
                @endif
            </div>

            <!-- Sidebar Information -->
            <div class="col-md-4">
                <!-- Status Card -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Entry Status</h5>
                    </div>
                    <div class="card-body text-center">
                        <div class="mb-3">
                            <span class="badge bg-{{ 
                                $sewingEntry->status == 'completed' ? 'success' : 
                                ($sewingEntry->status == 'in_progress' ? 'primary' : 'secondary') 
                            }} fs-5">
                                {{ ucfirst(str_replace('_', ' ', $sewingEntry->status)) }}
                            </span>
                        </div>
                        @if($sewingEntry->actual_smv)
                        <div class="mb-3">
                            <h6>Actual SMV</h6>
                            <p class="text-muted">{{ number_format($sewingEntry->actual_smv, 2) }} minutes</p>
                        </div>
                        @endif
                    </div>
                </div>

                <!-- Bundle Ticket Information -->
                @if($sewingEntry->bundleTicket)
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Bundle Ticket</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label"><strong>Ticket Number:</strong></label>
                            <p class="mb-1">{{ $sewingEntry->bundleTicket->ticket_number }}</p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label"><strong>Bundle Quantity:</strong></label>
                            <p class="mb-1">{{ number_format($sewingEntry->bundleTicket->quantity) }} pieces</p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label"><strong>Standard Minutes:</strong></label>
                            <p class="mb-1">{{ number_format($sewingEntry->bundleTicket->total_standard_minutes ?? 0) }} minutes</p>
                        </div>
                        @if($sewingEntry->bundleTicket->qr_code)
                        <div class="mb-3">
                            <label class="form-label"><strong>QR Code:</strong></label>
                            <p class="mb-0">
                                <button type="button" class="btn btn-sm btn-outline-info" onclick="showQRCode('{{ $sewingEntry->bundleTicket->qr_code }}')">
                                    <i class="fas fa-qrcode"></i> Show QR
                                </button>
                            </p>
                        </div>
                        @endif
                    </div>
                </div>
                @endif

                <!-- Actions -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Actions</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            @can('production.sewing.edit')
                            <a href="{{ route('production.sewing.edit', $sewingEntry) }}" class="btn btn-warning">
                                <i class="fas fa-edit"></i> Edit Entry
                            </a>
                            @endcan
                            
                            @if($sewingEntry->status == 'completed')
                            <a href="{{ route('production.finishing.create') }}?sewing_entry={{ $sewingEntry->id }}" class="btn btn-info">
                                <i class="fas fa-arrow-right"></i> Send to Finishing
                            </a>
                            @endif
                            
                            <a href="{{ route('production.sewing.index') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-list"></i> Back to List
                            </a>
                            
                            @can('production.sewing.delete')
                            @if($sewingEntry->status != 'completed')
                            <form method="POST" action="{{ route('production.sewing.destroy', $sewingEntry) }}" class="d-inline">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn btn-outline-danger w-100" onclick="return confirm('Are you sure you want to delete this sewing entry?')">
                                    <i class="fas fa-trash"></i> Delete Entry
                                </button>
                            </form>
                            @endif
                            @endcan
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('css')
<style>
.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.badge {
    font-size: 0.75em;
}

.table-borderless td {
    border: none;
    padding: 0.25rem 0.5rem;
}

.progress {
    background-color: #e9ecef;
}
</style>
@endpush

@push('js')
<script>
function showQRCode(qrCode) {
    // This would show a modal with the QR code
    alert('QR Code: ' + qrCode);
    // In a real implementation, you'd show this in a modal with a proper QR code image
}
</script>
@endpush
