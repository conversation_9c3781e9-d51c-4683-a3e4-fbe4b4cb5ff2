@extends('layouts.master')

@section('title', 'Create Washing Entry')

@section('main_content')
<div class="erp-state-overview-section">
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="page-title">Create Washing Entry</h2>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="{{ route('production.washing.index') }}">Washing Section</a></li>
                                <li class="breadcrumb-item active">Create Entry</li>
                            </ol>
                        </nav>
                    </div>
                    <div>
                        <a href="{{ route('production.washing.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> Back to List
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Create Form -->
        <form method="POST" action="{{ route('production.washing.store') }}">
            @csrf
            
            <div class="row">
                <div class="col-md-8">
                    <!-- Basic Information -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Basic Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Wash Lot Number <span class="text-danger">*</span></label>
                                        <input type="text" name="wash_lot_number" class="form-control @error('wash_lot_number') is-invalid @enderror" 
                                               value="{{ old('wash_lot_number') }}" required placeholder="Enter wash lot number">
                                        @error('wash_lot_number')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Bundle Number <span class="text-danger">*</span></label>
                                        <input type="text" name="bundle_number" class="form-control @error('bundle_number') is-invalid @enderror" 
                                               value="{{ old('bundle_number') }}" required placeholder="Enter bundle number">
                                        @error('bundle_number')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Washing Recipe <span class="text-danger">*</span></label>
                                        <select name="washing_recipe_id" class="form-select @error('washing_recipe_id') is-invalid @enderror" required>
                                            <option value="">Select Washing Recipe</option>
                                            @foreach($washingRecipes as $recipe)
                                            <option value="{{ $recipe->id }}" {{ old('washing_recipe_id') == $recipe->id ? 'selected' : '' }}>
                                                {{ $recipe->recipe_name }} ({{ $recipe->wash_type }})
                                            </option>
                                            @endforeach
                                        </select>
                                        @error('washing_recipe_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Status <span class="text-danger">*</span></label>
                                        <select name="status" class="form-select @error('status') is-invalid @enderror" required>
                                            <option value="in_wash" {{ old('status') == 'in_wash' ? 'selected' : '' }}>In Wash</option>
                                            <option value="completed" {{ old('status') == 'completed' ? 'selected' : '' }}>Completed</option>
                                            <option value="rejected" {{ old('status') == 'rejected' ? 'selected' : '' }}>Rejected</option>
                                            <option value="rework" {{ old('status') == 'rework' ? 'selected' : '' }}>Rework</option>
                                        </select>
                                        @error('status')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quantity Information -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Quantity Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label class="form-label">Input Quantity <span class="text-danger">*</span></label>
                                        <input type="number" name="input_quantity" class="form-control @error('input_quantity') is-invalid @enderror" 
                                               value="{{ old('input_quantity') }}" required min="1">
                                        @error('input_quantity')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label class="form-label">Output Quantity</label>
                                        <input type="number" name="output_quantity" class="form-control @error('output_quantity') is-invalid @enderror" 
                                               value="{{ old('output_quantity') }}" min="0">
                                        @error('output_quantity')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label class="form-label">Damage Quantity</label>
                                        <input type="number" name="damage_quantity" class="form-control @error('damage_quantity') is-invalid @enderror" 
                                               value="{{ old('damage_quantity', 0) }}" min="0">
                                        @error('damage_quantity')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label class="form-label">Damage Percentage</label>
                                        <input type="number" name="damage_percentage" class="form-control @error('damage_percentage') is-invalid @enderror" 
                                               value="{{ old('damage_percentage', 0) }}" step="0.1" min="0" max="100" readonly>
                                        @error('damage_percentage')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Wash Quality & Measurements -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Wash Quality & Measurements</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">Wash Quality <span class="text-danger">*</span></label>
                                        <select name="wash_quality" class="form-select @error('wash_quality') is-invalid @enderror" required>
                                            <option value="">Select Quality</option>
                                            <option value="excellent" {{ old('wash_quality') == 'excellent' ? 'selected' : '' }}>Excellent</option>
                                            <option value="good" {{ old('wash_quality') == 'good' ? 'selected' : '' }}>Good</option>
                                            <option value="acceptable" {{ old('wash_quality') == 'acceptable' ? 'selected' : '' }}>Acceptable</option>
                                            <option value="poor" {{ old('wash_quality') == 'poor' ? 'selected' : '' }}>Poor</option>
                                        </select>
                                        @error('wash_quality')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">Expected Shrinkage (%)</label>
                                        <input type="number" name="expected_shrinkage" class="form-control @error('expected_shrinkage') is-invalid @enderror" 
                                               value="{{ old('expected_shrinkage') }}" step="0.1" min="0" max="20">
                                        @error('expected_shrinkage')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">Actual Shrinkage (%)</label>
                                        <input type="number" name="actual_shrinkage" class="form-control @error('actual_shrinkage') is-invalid @enderror" 
                                               value="{{ old('actual_shrinkage') }}" step="0.1" min="0" max="20">
                                        @error('actual_shrinkage')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Pre-Wash Measurements</label>
                                        <textarea name="pre_wash_measurements" class="form-control @error('pre_wash_measurements') is-invalid @enderror" rows="3" 
                                                  placeholder="Enter pre-wash measurements (chest, length, etc.)">{{ old('pre_wash_measurements') }}</textarea>
                                        @error('pre_wash_measurements')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Post-Wash Measurements</label>
                                        <textarea name="post_wash_measurements" class="form-control @error('post_wash_measurements') is-invalid @enderror" rows="3" 
                                                  placeholder="Enter post-wash measurements (chest, length, etc.)">{{ old('post_wash_measurements') }}</textarea>
                                        @error('post_wash_measurements')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Timing Information -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Timing Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Wash Start Date & Time <span class="text-danger">*</span></label>
                                        <input type="datetime-local" name="wash_start_datetime" class="form-control @error('wash_start_datetime') is-invalid @enderror" 
                                               value="{{ old('wash_start_datetime', now()->format('Y-m-d\TH:i')) }}" required>
                                        @error('wash_start_datetime')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Wash End Date & Time</label>
                                        <input type="datetime-local" name="wash_end_datetime" class="form-control @error('wash_end_datetime') is-invalid @enderror" 
                                               value="{{ old('wash_end_datetime') }}">
                                        @error('wash_end_datetime')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <!-- Actions -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Actions</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Create Washing Entry
                                </button>
                                <a href="{{ route('production.washing.index') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times"></i> Cancel
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Wash Types -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Available Wash Types</h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-tint"></i> Wash Types:</h6>
                                <ul class="mb-0">
                                    @foreach($washingRecipes->unique('wash_type') as $recipe)
                                    <li>{{ ucfirst(str_replace('_', ' ', $recipe->wash_type)) }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Chemical Consumption -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Chemical Consumption</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label">Chemical Used</label>
                                <textarea name="chemical_consumption" class="form-control @error('chemical_consumption') is-invalid @enderror" rows="4" 
                                          placeholder="Enter chemical consumption details...">{{ old('chemical_consumption') }}</textarea>
                                @error('chemical_consumption')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Notes -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Notes</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label">Wash Notes</label>
                                <textarea name="wash_notes" class="form-control @error('wash_notes') is-invalid @enderror" rows="4" 
                                          placeholder="Enter wash notes or special instructions...">{{ old('wash_notes') }}</textarea>
                                @error('wash_notes')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
@endsection

@push('js')
<script>
// Auto-calculate damage percentage and output quantity
document.addEventListener('DOMContentLoaded', function() {
    const inputQtyInput = document.querySelector('input[name="input_quantity"]');
    const damageQtyInput = document.querySelector('input[name="damage_quantity"]');
    const damagePercentageInput = document.querySelector('input[name="damage_percentage"]');
    const outputQtyInput = document.querySelector('input[name="output_quantity"]');

    function calculateDamageAndOutput() {
        const inputQty = parseFloat(inputQtyInput.value) || 0;
        const damageQty = parseFloat(damageQtyInput.value) || 0;

        if (inputQty > 0) {
            // Calculate damage percentage
            const damagePercentage = (damageQty / inputQty) * 100;
            damagePercentageInput.value = damagePercentage.toFixed(1);
            
            // Calculate output quantity
            const outputQty = inputQty - damageQty;
            if (outputQty >= 0) {
                outputQtyInput.value = outputQty;
            }
        }
    }

    [inputQtyInput, damageQtyInput].forEach(input => {
        if (input) {
            input.addEventListener('input', calculateDamageAndOutput);
        }
    });
});
</script>
@endpush
