@extends('layouts.master')

@section('title', 'Washing Section (Denim)')

@section('main_content')
<div class="erp-state-overview-section">
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h2 class="page-title">Washing Section (Denim)</h2>
                    <div class="d-flex gap-2">
                        @can('production.washing.create')
                        <a href="{{ route('production.washing.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> New Washing Entry
                        </a>
                        @endcan
                        @can('production.washing.recipes')
                        <a href="{{ route('production.washing.recipes') }}" class="btn btn-outline-info">
                            <i class="fas fa-flask"></i> Washing Recipes
                        </a>
                        @endcan
                        @can('production.exports')
                        <div class="dropdown">
                            <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-download"></i> Export
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#">
                                    <i class="fas fa-file-pdf text-danger"></i> PDF Report
                                </a></li>
                            </ul>
                        </div>
                        @endcan
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="GET" action="{{ route('production.washing.index') }}" class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">Wash Lot Number</label>
                        <input type="text" name="wash_lot_number" class="form-control" value="{{ request('wash_lot_number') }}" placeholder="Enter wash lot number">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Bundle Number</label>
                        <input type="text" name="bundle_number" class="form-control" value="{{ request('bundle_number') }}" placeholder="Enter bundle number">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Washing Recipe</label>
                        <select name="washing_recipe_id" class="form-select">
                            <option value="">All Recipes</option>
                            @foreach($washingRecipes as $recipe)
                            <option value="{{ $recipe->id }}" {{ request('washing_recipe_id') == $recipe->id ? 'selected' : '' }}>
                                {{ $recipe->recipe_name }} ({{ $recipe->wash_type }})
                            </option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Wash Quality</label>
                        <select name="wash_quality" class="form-select">
                            <option value="">All Quality</option>
                            <option value="excellent" {{ request('wash_quality') == 'excellent' ? 'selected' : '' }}>Excellent</option>
                            <option value="good" {{ request('wash_quality') == 'good' ? 'selected' : '' }}>Good</option>
                            <option value="acceptable" {{ request('wash_quality') == 'acceptable' ? 'selected' : '' }}>Acceptable</option>
                            <option value="poor" {{ request('wash_quality') == 'poor' ? 'selected' : '' }}>Poor</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Status</label>
                        <select name="status" class="form-select">
                            <option value="">All Status</option>
                            <option value="in_wash" {{ request('status') == 'in_wash' ? 'selected' : '' }}>In Wash</option>
                            <option value="completed" {{ request('status') == 'completed' ? 'selected' : '' }}>Completed</option>
                            <option value="rejected" {{ request('status') == 'rejected' ? 'selected' : '' }}>Rejected</option>
                            <option value="rework" {{ request('status') == 'rework' ? 'selected' : '' }}>Rework</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Wash Date</label>
                        <input type="date" name="wash_date" class="form-control" value="{{ request('wash_date') }}">
                    </div>
                    <div class="col-md-6 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="fas fa-search"></i> Filter
                        </button>
                        <a href="{{ route('production.washing.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i> Clear
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Washing Entries Table -->
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>Wash Lot</th>
                                <th>Bundle Number</th>
                                <th>Recipe</th>
                                <th>Input Qty</th>
                                <th>Output Qty</th>
                                <th>Damage</th>
                                <th>Shrinkage</th>
                                <th>Quality</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($washingEntries as $entry)
                            <tr>
                                <td>
                                    <a href="{{ route('production.washing.show', $entry) }}" class="text-decoration-none">
                                        {{ $entry->wash_lot_number }}
                                    </a>
                                    <br>
                                    <small class="text-muted">
                                        @if($entry->wash_start_datetime)
                                        {{ $entry->wash_start_datetime->format('M d, Y') }}
                                        @endif
                                    </small>
                                </td>
                                <td>{{ $entry->bundle_number }}</td>
                                <td>
                                    {{ $entry->washingRecipe->recipe_name ?? 'N/A' }}
                                    <br>
                                    <small class="text-muted">{{ $entry->washingRecipe->wash_type ?? '' }}</small>
                                </td>
                                <td>{{ number_format($entry->input_quantity) }}</td>
                                <td>{{ number_format($entry->output_quantity ?? 0) }}</td>
                                <td>
                                    <span class="badge bg-{{ $entry->damage_quantity > 0 ? 'danger' : 'success' }}">
                                        {{ number_format($entry->damage_quantity ?? 0) }}
                                    </span>
                                    @if($entry->damage_quantity > 0)
                                    <br>
                                    <small class="text-muted">{{ number_format($entry->damage_percentage, 1) }}%</small>
                                    @endif
                                </td>
                                <td>
                                    @if($entry->actual_shrinkage)
                                    <span class="badge bg-info">
                                        {{ number_format($entry->actual_shrinkage, 1) }}%
                                    </span>
                                    @else
                                    <span class="text-muted">-</span>
                                    @endif
                                </td>
                                <td>
                                    <span class="badge bg-{{ 
                                        $entry->wash_quality == 'excellent' ? 'success' : 
                                        ($entry->wash_quality == 'good' ? 'primary' : 
                                        ($entry->wash_quality == 'acceptable' ? 'warning' : 'danger')) 
                                    }}">
                                        {{ ucfirst($entry->wash_quality) }}
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-{{ 
                                        $entry->status == 'completed' ? 'success' : 
                                        ($entry->status == 'in_wash' ? 'primary' : 
                                        ($entry->status == 'rejected' ? 'danger' : 'warning')) 
                                    }}">
                                        {{ ucfirst(str_replace('_', ' ', $entry->status)) }}
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        @can('production.washing.view')
                                        <a href="{{ route('production.washing.show', $entry) }}" class="btn btn-outline-primary" title="View">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        @endcan
                                        @can('production.washing.edit')
                                        <a href="{{ route('production.washing.edit', $entry) }}" class="btn btn-outline-warning" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        @endcan
                                        @can('production.washing.delete')
                                        @if($entry->status != 'completed')
                                        <form method="POST" action="{{ route('production.washing.destroy', $entry) }}" class="d-inline">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-outline-danger" title="Delete" onclick="return confirm('Are you sure you want to delete this washing entry?')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                        @endif
                                        @endcan
                                    </div>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="10" class="text-center py-4">
                                    <div class="text-muted">
                                        <i class="fas fa-tint fa-3x mb-3"></i>
                                        <p>No washing entries found.</p>
                                        @can('production.washing.create')
                                        <a href="{{ route('production.washing.create') }}" class="btn btn-primary">
                                            Create First Washing Entry
                                        </a>
                                        @endcan
                                    </div>
                                </td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                @if($washingEntries->hasPages())
                <div class="d-flex justify-content-center mt-4">
                    {{ $washingEntries->links() }}
                </div>
                @endif
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="row mt-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <h4>{{ $washingEntries->where('status', 'in_wash')->count() }}</h4>
                        <small>In Wash</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <h4>{{ $washingEntries->where('status', 'completed')->count() }}</h4>
                        <small>Completed</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body text-center">
                        <h4>{{ $washingEntries->where('wash_quality', 'excellent')->count() }}</h4>
                        <small>Excellent Quality</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <h4>{{ number_format($washingEntries->sum('output_quantity')) }}</h4>
                        <small>Total Washed</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('css')
<style>
.table th {
    font-weight: 600;
    border-bottom: 2px solid #dee2e6;
}
.btn-group-sm > .btn {
    padding: 0.25rem 0.5rem;
}
</style>
@endpush
