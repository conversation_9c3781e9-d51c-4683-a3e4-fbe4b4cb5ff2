@extends('layouts.master')

@section('title', 'Washing Recipes Management')

@section('main_content')
<div class="erp-state-overview-section">
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="page-title">Washing Recipes Management</h2>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item"><a href="{{ route('production.washing.index') }}">Washing Section</a></li>
                                <li class="breadcrumb-item active">Recipes</li>
                            </ol>
                        </nav>
                    </div>
                    <div class="d-flex gap-2">
                        @can('production.washing.create')
                        <a href="{{ route('production.washing.recipes.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Add Recipe
                        </a>
                        @endcan
                        <a href="{{ route('production.washing.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Washing
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recipe Categories -->
        <div class="row mb-4">
            <div class="col-md-2">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <h4>{{ $washingRecipes->where('wash_type', 'stone_wash')->count() }}</h4>
                        <small>Stone Wash</small>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <h4>{{ $washingRecipes->where('wash_type', 'acid_wash')->count() }}</h4>
                        <small>Acid Wash</small>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <h4>{{ $washingRecipes->where('wash_type', 'enzyme_wash')->count() }}</h4>
                        <small>Enzyme Wash</small>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card bg-warning text-white">
                    <div class="card-body text-center">
                        <h4>{{ $washingRecipes->where('wash_type', 'bleach_wash')->count() }}</h4>
                        <small>Bleach Wash</small>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card bg-secondary text-white">
                    <div class="card-body text-center">
                        <h4>{{ $washingRecipes->where('wash_type', 'normal_wash')->count() }}</h4>
                        <small>Normal Wash</small>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card bg-dark text-white">
                    <div class="card-body text-center">
                        <h4>{{ $washingRecipes->where('is_active', true)->count() }}</h4>
                        <small>Active</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recipes Table -->
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>Recipe Code</th>
                                <th>Recipe Name</th>
                                <th>Wash Type</th>
                                <th>Duration</th>
                                <th>Temperature</th>
                                <th>Expected Shrinkage</th>
                                <th>Status</th>
                                <th>Usage Count</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($washingRecipes as $recipe)
                            <tr>
                                <td>
                                    <strong>{{ $recipe->recipe_code }}</strong>
                                </td>
                                <td>{{ $recipe->recipe_name }}</td>
                                <td>
                                    <span class="badge bg-{{ 
                                        $recipe->wash_type == 'stone_wash' ? 'primary' : 
                                        ($recipe->wash_type == 'acid_wash' ? 'success' : 
                                        ($recipe->wash_type == 'enzyme_wash' ? 'info' : 
                                        ($recipe->wash_type == 'bleach_wash' ? 'warning' : 'secondary'))) 
                                    }}">
                                        {{ ucfirst(str_replace('_', ' ', $recipe->wash_type)) }}
                                    </span>
                                </td>
                                <td class="text-center">
                                    @if($recipe->duration_minutes)
                                    {{ number_format($recipe->duration_minutes) }} min
                                    @else
                                    <span class="text-muted">N/A</span>
                                    @endif
                                </td>
                                <td class="text-center">
                                    @if($recipe->temperature_celsius)
                                    {{ $recipe->temperature_celsius }}°C
                                    @else
                                    <span class="text-muted">N/A</span>
                                    @endif
                                </td>
                                <td class="text-center">
                                    @if($recipe->expected_shrinkage_percentage)
                                    {{ number_format($recipe->expected_shrinkage_percentage, 1) }}%
                                    @else
                                    <span class="text-muted">N/A</span>
                                    @endif
                                </td>
                                <td>
                                    <span class="badge bg-{{ $recipe->is_active ? 'success' : 'secondary' }}">
                                        {{ $recipe->is_active ? 'Active' : 'Inactive' }}
                                    </span>
                                </td>
                                <td class="text-center">
                                    <span class="badge bg-info">
                                        {{ $recipe->washingEntries->count() }}
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ route('production.washing.recipes.show', $recipe) }}" class="btn btn-outline-primary" title="View">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        @can('production.washing.edit')
                                        <a href="{{ route('production.washing.recipes.edit', $recipe) }}" class="btn btn-outline-warning" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        @endcan
                                        <a href="{{ route('production.washing.recipes.duplicate', $recipe) }}" class="btn btn-outline-info" title="Duplicate">
                                            <i class="fas fa-copy"></i>
                                        </a>
                                        @can('production.washing.delete')
                                        @if($recipe->washingEntries->count() == 0)
                                        <form method="POST" action="{{ route('production.washing.recipes.destroy', $recipe) }}" class="d-inline">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-outline-danger" title="Delete" onclick="return confirm('Are you sure you want to delete this recipe?')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                        @endif
                                        @endcan
                                    </div>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="9" class="text-center py-4">
                                    <div class="text-muted">
                                        <i class="fas fa-flask fa-3x mb-3"></i>
                                        <p>No washing recipes found.</p>
                                        @can('production.washing.create')
                                        <a href="{{ route('production.washing.recipes.create') }}" class="btn btn-primary">
                                            Add First Recipe
                                        </a>
                                        @endcan
                                    </div>
                                </td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Recipe Details Cards -->
        <div class="row mt-4">
            @foreach($washingRecipes->where('is_active', true)->take(3) as $recipe)
            <div class="col-md-4 mb-3">
                <div class="card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="card-title mb-0">{{ $recipe->recipe_name }}</h6>
                        <span class="badge bg-{{ 
                            $recipe->wash_type == 'stone_wash' ? 'primary' : 
                            ($recipe->wash_type == 'acid_wash' ? 'success' : 'info') 
                        }}">
                            {{ ucfirst(str_replace('_', ' ', $recipe->wash_type)) }}
                        </span>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="metric">
                                    <div class="metric-value">{{ $recipe->duration_minutes ?? 'N/A' }}</div>
                                    <div class="metric-label">Minutes</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="metric">
                                    <div class="metric-value">{{ $recipe->temperature_celsius ?? 'N/A' }}°</div>
                                    <div class="metric-label">Temperature</div>
                                </div>
                            </div>
                        </div>
                        
                        @if($recipe->chemical_formula)
                        <div class="mt-3">
                            <h6>Chemical Formula:</h6>
                            <small class="text-muted">{{ Str::limit($recipe->chemical_formula, 100) }}</small>
                        </div>
                        @endif
                        
                        <div class="mt-3">
                            <small class="text-muted">
                                Used {{ $recipe->washingEntries->count() }} times
                                @if($recipe->expected_shrinkage_percentage)
                                • {{ $recipe->expected_shrinkage_percentage }}% shrinkage
                                @endif
                            </small>
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="d-flex justify-content-between">
                            <a href="{{ route('production.washing.recipes.show', $recipe) }}" class="btn btn-sm btn-outline-primary">
                                View Details
                            </a>
                            <a href="{{ route('production.washing.create') }}?recipe={{ $recipe->id }}" class="btn btn-sm btn-primary">
                                Use Recipe
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
    </div>
</div>
@endsection

@push('css')
<style>
.metric {
    padding: 10px 0;
}
.metric-value {
    font-size: 1.2rem;
    font-weight: bold;
    line-height: 1;
}
.metric-label {
    font-size: 0.75rem;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}
.table th {
    font-weight: 600;
    border-bottom: 2px solid #dee2e6;
}
.btn-group-sm > .btn {
    padding: 0.25rem 0.5rem;
}
</style>
@endpush
