<?php

use Illuminate\Support\Facades\Route;
use Modules\Production\App\Http\Controllers\DashboardController;
use Modules\Production\App\Http\Controllers\PlanningController;
use Modules\Production\App\Http\Controllers\CuttingController;
use Modules\Production\App\Http\Controllers\SewingController;
use Modules\Production\App\Http\Controllers\FinishingController;
use Modules\Production\App\Http\Controllers\WashingController;
use Modules\Production\App\Http\Controllers\QualityController;

/*
|--------------------------------------------------------------------------
| Production Module Routes
|--------------------------------------------------------------------------
|
| All routes for the Garments Production Module with proper middleware
| and permission-based access control.
|
*/

Route::group([
    'prefix' => 'production',
    'as' => 'production.',
    'middleware' => ['web', 'auth']
], function () {

    // Production Dashboard
    Route::get('dashboard', [DashboardController::class, 'index'])
        ->name('dashboard.index')
        ->middleware('can:production.dashboard.view');

    // Production Planning Module
    Route::group(['prefix' => 'planning', 'as' => 'planning.'], function () {
        Route::get('/', [PlanningController::class, 'index'])
            ->name('index')
            ->middleware('can:production.planning.view');

        Route::get('create', [PlanningController::class, 'create'])
            ->name('create')
            ->middleware('can:production.planning.create');

        Route::post('/', [PlanningController::class, 'store'])
            ->name('store')
            ->middleware('can:production.planning.create');

        Route::get('{productionPlan}', [PlanningController::class, 'show'])
            ->name('show')
            ->middleware('can:production.planning.view');

        Route::get('{productionPlan}/edit', [PlanningController::class, 'edit'])
            ->name('edit')
            ->middleware('can:production.planning.edit');

        Route::put('{productionPlan}', [PlanningController::class, 'update'])
            ->name('update')
            ->middleware('can:production.planning.edit');

        Route::delete('{productionPlan}', [PlanningController::class, 'destroy'])
            ->name('destroy')
            ->middleware('can:production.planning.delete');

        // Additional planning routes
        Route::post('{productionPlan}/approve', [PlanningController::class, 'approve'])
            ->name('approve')
            ->middleware('can:production.planning.approve');

        Route::get('export/pdf', [PlanningController::class, 'exportPdf'])
            ->name('export.pdf')
            ->middleware('can:production.planning.export');

        Route::get('export/excel', [PlanningController::class, 'exportExcel'])
            ->name('export.excel')
            ->middleware('can:production.planning.export');
    });

    // Cutting Section Module
    Route::group(['prefix' => 'cutting', 'as' => 'cutting.'], function () {
        Route::get('/', [CuttingController::class, 'index'])
            ->name('index')
            ->middleware('can:production.cutting.view');

        Route::get('create', [CuttingController::class, 'create'])
            ->name('create')
            ->middleware('can:production.cutting.create');

        // Marker planning routes (must be before parameterized routes)
        Route::get('marker-planning', [CuttingController::class, 'markerPlanning'])
            ->name('marker-planning');

        Route::post('markers', [CuttingController::class, 'storeMarker'])
            ->name('markers.store');

        // Cutting entries routes (must be before parameterized routes)
        Route::get('entries', [CuttingController::class, 'entries'])
            ->name('entries')
            ->middleware('can:production.cutting.view');

        Route::post('entries', [CuttingController::class, 'storeEntry'])
            ->name('entries.store')
            ->middleware('can:production.cutting.create');

        // Fabric issue routes (must be before parameterized routes)
        Route::get('fabric-issues', [CuttingController::class, 'fabricIssues'])
            ->name('fabric-issues')
            ->middleware('can:production.cutting.fabric-issues');

        Route::post('fabric-issues', [CuttingController::class, 'storeFabricIssue'])
            ->name('fabric-issues.store')
            ->middleware('can:production.cutting.create');

        Route::get('fabric-issues/{fabricIssue}', [CuttingController::class, 'showFabricIssue'])
            ->name('fabric-issues.show')
            ->middleware('can:production.cutting.view');

        Route::post('fabric-issues/{fabricIssue}/return', [CuttingController::class, 'recordFabricReturn'])
            ->name('fabric-issues.return')
            ->middleware('can:production.cutting.edit');

        Route::post('/', [CuttingController::class, 'store'])
            ->name('store')
            ->middleware('can:production.cutting.create');

        // Parameterized routes (must be last)
        Route::get('{cuttingPlan}', [CuttingController::class, 'show'])
            ->name('show')
            ->middleware('can:production.cutting.view');

        Route::get('{cuttingPlan}/edit', [CuttingController::class, 'edit'])
            ->name('edit')
            ->middleware('can:production.cutting.edit');

        Route::put('{cuttingPlan}', [CuttingController::class, 'update'])
            ->name('update')
            ->middleware('can:production.cutting.edit');

        Route::delete('{cuttingPlan}', [CuttingController::class, 'destroy'])
            ->name('destroy')
            ->middleware('can:production.cutting.delete');
    });

    // Sewing Section Module
    Route::group(['prefix' => 'sewing', 'as' => 'sewing.'], function () {
        Route::get('/', [SewingController::class, 'index'])
            ->name('index')
            ->middleware('can:production.sewing.view');

        Route::get('create', [SewingController::class, 'create'])
            ->name('create')
            ->middleware('can:production.sewing.create');

        Route::post('/', [SewingController::class, 'store'])
            ->name('store')
            ->middleware('can:production.sewing.create');

        Route::get('{sewingEntry}', [SewingController::class, 'show'])
            ->name('show')
            ->middleware('can:production.sewing.view');

        Route::get('{sewingEntry}/edit', [SewingController::class, 'edit'])
            ->name('edit')
            ->middleware('can:production.sewing.edit');

        Route::put('{sewingEntry}', [SewingController::class, 'update'])
            ->name('update')
            ->middleware('can:production.sewing.edit');

        Route::delete('{sewingEntry}', [SewingController::class, 'destroy'])
            ->name('destroy')
            ->middleware('can:production.sewing.delete');

        // Line tracking routes
        Route::get('line-tracking', [SewingController::class, 'lineTracking'])
            ->name('line-tracking')
            ->middleware('can:production.sewing.line-tracking');

        Route::get('efficiency', [SewingController::class, 'efficiency'])
            ->name('efficiency')
            ->middleware('can:production.sewing.efficiency');

        // Bundle ticket routes
        Route::get('bundle-tickets', [SewingController::class, 'bundleTickets'])
            ->name('bundle-tickets')
            ->middleware('can:production.sewing.view');

        Route::post('bundle-tickets', [SewingController::class, 'storeBundleTicket'])
            ->name('bundle-tickets.store')
            ->middleware('can:production.sewing.create');
    });

    // Finishing Section Module
    Route::group(['prefix' => 'finishing', 'as' => 'finishing.'], function () {
        Route::get('/', [FinishingController::class, 'index'])
            ->name('index')
            ->middleware('can:production.finishing.view');

        Route::get('create', [FinishingController::class, 'create'])
            ->name('create')
            ->middleware('can:production.finishing.create');

        Route::post('/', [FinishingController::class, 'store'])
            ->name('store')
            ->middleware('can:production.finishing.create');

        Route::get('{finishingEntry}', [FinishingController::class, 'show'])
            ->name('show')
            ->middleware('can:production.finishing.view');

        Route::get('{finishingEntry}/edit', [FinishingController::class, 'edit'])
            ->name('edit')
            ->middleware('can:production.finishing.edit');

        Route::put('{finishingEntry}', [FinishingController::class, 'update'])
            ->name('update')
            ->middleware('can:production.finishing.edit');

        Route::delete('{finishingEntry}', [FinishingController::class, 'destroy'])
            ->name('destroy')
            ->middleware('can:production.finishing.delete');

        // Process management routes
        Route::get('processes', [FinishingController::class, 'processes'])
            ->name('processes');

        Route::post('processes', [FinishingController::class, 'storeProcess'])
            ->name('processes.store');
    });

    // Washing Section Module (Denim Specific)
    Route::group(['prefix' => 'washing', 'as' => 'washing.'], function () {
        Route::get('/', [WashingController::class, 'index'])
            ->name('index')
            ->middleware('can:production.washing.view');

        Route::get('create', [WashingController::class, 'create'])
            ->name('create')
            ->middleware('can:production.washing.create');

        Route::post('/', [WashingController::class, 'store'])
            ->name('store')
            ->middleware('can:production.washing.create');

        Route::get('{washingEntry}', [WashingController::class, 'show'])
            ->name('show')
            ->middleware('can:production.washing.view');

        Route::get('{washingEntry}/edit', [WashingController::class, 'edit'])
            ->name('edit')
            ->middleware('can:production.washing.edit');

        Route::put('{washingEntry}', [WashingController::class, 'update'])
            ->name('update')
            ->middleware('can:production.washing.edit');

        Route::delete('{washingEntry}', [WashingController::class, 'destroy'])
            ->name('destroy')
            ->middleware('can:production.washing.delete');

        // Recipe management routes
        Route::get('recipes', [WashingController::class, 'recipes'])
            ->name('recipes')
            ->middleware('can:production.washing.recipes');

        Route::post('recipes', [WashingController::class, 'storeRecipe'])
            ->name('recipes.store')
            ->middleware('can:production.washing.create');

        Route::get('recipes/{washingRecipe}/edit', [WashingController::class, 'editRecipe'])
            ->name('recipes.edit')
            ->middleware('can:production.washing.edit');

        Route::put('recipes/{washingRecipe}', [WashingController::class, 'updateRecipe'])
            ->name('recipes.update')
            ->middleware('can:production.washing.edit');
    });

    // Quality Control Module
    Route::group(['prefix' => 'quality', 'as' => 'quality.'], function () {
        Route::get('/', [QualityController::class, 'index'])
            ->name('index')
            ->middleware('can:production.quality.view');

        Route::get('create', [QualityController::class, 'create'])
            ->name('create')
            ->middleware('can:production.quality.create');

        Route::post('/', [QualityController::class, 'store'])
            ->name('store')
            ->middleware('can:production.quality.create');

        Route::get('{qualityInspection}', [QualityController::class, 'show'])
            ->name('show')
            ->middleware('can:production.quality.view');

        Route::get('{qualityInspection}/edit', [QualityController::class, 'edit'])
            ->name('edit')
            ->middleware('can:production.quality.edit');

        Route::put('{qualityInspection}', [QualityController::class, 'update'])
            ->name('update')
            ->middleware('can:production.quality.edit');

        Route::delete('{qualityInspection}', [QualityController::class, 'destroy'])
            ->name('destroy')
            ->middleware('can:production.quality.delete');

        // AQL Charts routes
        Route::get('aql-charts', [QualityController::class, 'aqlCharts'])
            ->name('aql-charts');

        Route::post('aql-charts', [QualityController::class, 'storeAqlChart'])
            ->name('aql-charts.store');

        // Quality reports routes
        Route::get('reports', [QualityController::class, 'reports'])
            ->name('reports')
            ->middleware('can:production.quality.reports');

        Route::get('reports/defect-analysis', [QualityController::class, 'defectAnalysis'])
            ->name('reports.defect-analysis')
            ->middleware('can:production.quality.reports');

        Route::get('reports/trend-analysis', [QualityController::class, 'trendAnalysis'])
            ->name('reports.trend-analysis')
            ->middleware('can:production.quality.reports');
    });

    // Export routes for all modules
    Route::group(['prefix' => 'exports', 'as' => 'exports.'], function () {
        Route::get('production-summary/pdf', [DashboardController::class, 'exportProductionSummaryPdf'])
            ->name('production-summary.pdf')
            ->middleware('can:production.exports');

        Route::get('production-summary/excel', [DashboardController::class, 'exportProductionSummaryExcel'])
            ->name('production-summary.excel')
            ->middleware('can:production.exports');

        Route::get('cutting-report/pdf', [CuttingController::class, 'exportPdf'])
            ->name('cutting-report.pdf')
            ->middleware('can:production.exports');

        Route::get('sewing-report/pdf', [SewingController::class, 'exportPdf'])
            ->name('sewing-report.pdf')
            ->middleware('can:production.exports');

        Route::get('quality-report/pdf', [QualityController::class, 'exportPdf'])
            ->name('quality-report.pdf')
            ->middleware('can:production.exports');
    });
});
