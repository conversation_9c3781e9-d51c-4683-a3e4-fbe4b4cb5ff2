# AI Marker Optimization Tool for Red Rooks Apparel

A comprehensive marker optimization system that uses AI-powered 2D bin-packing algorithms to arrange garment pattern pieces efficiently on fabric rolls, reducing material waste and improving production cost-effectiveness.

## 🎯 Overview

This tool integrates with the Red Rooks Apparel ERP system to provide:
- AI-powered pattern layout optimization
- Real-time fabric utilization analysis
- Interactive marker visualization
- Comprehensive reporting and analytics
- Export capabilities for production systems

## 🏗️ Architecture

```
ai-marker-optimization/
├── api/                          # Python FastAPI backend
├── frontend/                     # Vue.js + Fabric.js frontend
├── laravel-integration/          # Laravel ERP integration
├── docs/                        # Documentation
└── docker/                      # Docker configuration
```

## 🚀 Quick Start

### Prerequisites
- Python 3.9+
- Node.js 16+
- Laravel 10.x
- MySQL 8.0+
- Docker (optional)

### Backend Setup (Python FastAPI)
```bash
cd api
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
uvicorn main:app --reload --port 8001
```

### Frontend Setup (Vue.js)
```bash
cd frontend
npm install
npm run dev
```

### Laravel Integration
```bash
cd laravel-integration
php artisan migrate
php artisan serve
```

## 🧠 Core Features

### AI Optimization Algorithms
- **Genetic Algorithm**: Complex optimization with multiple constraints
- **Bottom-Left-Fill (BLF)**: Fast greedy approach for real-time optimization
- **No-Fit Polygon (NFP)**: Advanced nesting for irregular shapes

### Interactive Canvas
- Real-time pattern visualization
- Drag-and-drop pattern adjustment
- Zoom and pan controls
- Size-based color coding
- Export to PDF/SVG/PNG

### Efficiency Metrics
- Fabric utilization percentage
- Waste analysis
- Cost savings calculator
- Production planning insights

## 📊 API Endpoints

### Core Optimization
- `POST /api/v1/optimize-marker` - Main optimization endpoint
- `GET /api/v1/algorithms` - List available algorithms
- `POST /api/v1/validate-patterns` - Validate pattern data
- `GET /api/v1/health` - Health check

### Integration
- `GET /api/v1/optimization-history` - Retrieve past optimizations
- `POST /api/v1/export-marker` - Export marker layouts

## 🎨 Design System

Follows Red Rooks Apparel's design standards:
- **Primary Color**: #dc2626 (Red)
- **Secondary Color**: #ef4444 (Light Red)
- **Font**: Poppins
- **Icons**: Font Awesome with fallbacks

## 📈 Performance Targets

- **Optimization Speed**: < 5 seconds for typical garment orders
- **Fabric Utilization**: 85%+ efficiency target
- **API Response Time**: < 200ms for validation endpoints
- **Canvas Rendering**: 60fps for smooth interactions

## 🔧 Development

### Code Standards
- Python: PEP 8 formatting
- JavaScript: ESLint + Prettier
- PHP: PSR-12 standards
- Vue.js: Composition API with TypeScript

### Testing
- Python: pytest with 80%+ coverage
- Frontend: Vitest + Vue Test Utils
- Laravel: PHPUnit integration tests

## 📚 Documentation

- [API Documentation](docs/api-documentation.md)
- [Integration Guide](docs/integration-guide.md)
- [User Manual](docs/user-manual.md)
- [Algorithm Details](docs/algorithms.md)

## 🚢 Deployment

### Docker Deployment
```bash
docker-compose up -d
```

### Production Setup
- Python API: Gunicorn + Nginx
- Frontend: Static build served by Nginx
- Laravel: Standard PHP-FPM deployment

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Follow coding standards
4. Add comprehensive tests
5. Submit a pull request

## 📄 License

Proprietary software for Red Rooks Apparel.
All rights reserved.

## 🆘 Support

For technical support or questions:
- Email: <EMAIL>
- Documentation: [Internal Wiki](docs/)
- Issue Tracker: [GitHub Issues](issues/)
