# AI Marker Optimization Tool - Environment Configuration

# Application Settings
DEBUG=true
HOST=0.0.0.0
PORT=8001
SECRET_KEY=your-secret-key-here-change-in-production
LOG_LEVEL=INFO

# Security
ALLOWED_HOSTS=localhost,127.0.0.1,*.redrooksapparel.com
CORS_ORIGINS=http://localhost:3000,http://localhost:8000,https://app.redrooksapparel.com

# Laravel Integration
LARAVEL_API_URL=http://localhost:8000
LARAVEL_API_TOKEN=your-laravel-api-token

# Database
DATABASE_URL=postgresql://user:password@localhost:5432/marker_optimization

# Redis (for caching and rate limiting)
REDIS_URL=redis://localhost:6379/0

# Optimization Limits
MAX_OPTIMIZATION_TIME=300
MAX_PATTERN_PIECES=1000
MAX_FABRIC_WIDTH=200.0
MAX_FABRIC_LENGTH=1000.0

# Algorithm Defaults
DEFAULT_ALGORITHM=bottom_left_fill
DEFAULT_MAX_ITERATIONS=1000

# File Handling
MAX_FILE_SIZE=10485760
UPLOAD_DIR=uploads
EXPORT_DIR=exports

# Monitoring
ENABLE_METRICS=true
NOTIFICATION_WEBHOOK_URL=

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=3600

# Production Settings (uncomment for production)
# DEBUG=false
# LOG_LEVEL=WARNING
# ALLOWED_HOSTS=api.redrooksapparel.com
# CORS_ORIGINS=https://app.redrooksapparel.com
