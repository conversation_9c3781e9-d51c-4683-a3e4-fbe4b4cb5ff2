"""
Base classes for optimization algorithms
"""

import time
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Tuple, Optional
import logging

import numpy as np
from shapely.geometry import Polygon, Point
from shapely.affinity import rotate, translate

from app.models.optimization import (
    Pattern<PERSON><PERSON>ce, 
    PlacedPatternPiece, 
    MarkerLayout, 
    EfficiencyMetrics,
    OptimizationSettings
)

logger = logging.getLogger(__name__)


class OptimizationAlgorithm(ABC):
    """Base class for all optimization algorithms"""
    
    def __init__(self, fabric_width: float, fabric_length: Optional[float] = None):
        self.fabric_width = fabric_width
        self.fabric_length = fabric_length or 1000.0  # Default to large length
        self.fabric_area = self.fabric_width * self.fabric_length
        
        # Algorithm state
        self.placed_pieces: List[PlacedPatternPiece] = []
        self.failed_pieces: List[PatternPiece] = []
        self.execution_time = 0.0
        
    @abstractmethod
    def optimize(
        self, 
        pattern_pieces: List[PatternPiece], 
        settings: OptimizationSettings
    ) -> <PERSON><PERSON>[MarkerLayout, EfficiencyMetrics]:
        """
        Main optimization method to be implemented by subclasses
        
        Args:
            pattern_pieces: List of pattern pieces to place
            settings: Optimization settings and parameters
            
        Returns:
            Tuple of (<PERSON>erLayout, EfficiencyMetrics)
        """
        pass
    
    def create_polygon_from_piece(self, piece: PatternPiece) -> Polygon:
        """Convert pattern piece to Shapely polygon"""
        try:
            if piece.geometry.coordinates:
                # Use provided coordinates
                coords = piece.geometry.coordinates
                if len(coords) < 3:
                    raise ValueError(f"Polygon must have at least 3 points for piece {piece.piece_id}")
                
                # Ensure polygon is closed
                if coords[0] != coords[-1]:
                    coords.append(coords[0])
                
                polygon = Polygon(coords)
                
            elif piece.geometry.svg_path:
                # Parse SVG path (simplified implementation)
                # In production, use svgpathtools for proper SVG parsing
                polygon = self._create_rectangle_from_bounds(
                    piece.geometry.width, 
                    piece.geometry.height
                )
                
            else:
                # Fallback to bounding rectangle
                polygon = self._create_rectangle_from_bounds(
                    piece.geometry.width, 
                    piece.geometry.height
                )
            
            # Validate polygon
            if not polygon.is_valid:
                logger.warning(f"Invalid polygon for piece {piece.piece_id}, using bounding rectangle")
                polygon = self._create_rectangle_from_bounds(
                    piece.geometry.width, 
                    piece.geometry.height
                )
            
            return polygon
            
        except Exception as e:
            logger.error(f"Error creating polygon for piece {piece.piece_id}: {e}")
            # Fallback to rectangle
            return self._create_rectangle_from_bounds(
                piece.geometry.width, 
                piece.geometry.height
            )
    
    def _create_rectangle_from_bounds(self, width: float, height: float) -> Polygon:
        """Create a rectangle polygon from width and height"""
        return Polygon([
            (0, 0),
            (width, 0),
            (width, height),
            (0, height),
            (0, 0)
        ])
    
    def can_place_piece(
        self, 
        polygon: Polygon, 
        x: float, 
        y: float, 
        rotation: float = 0,
        min_spacing: float = 0.125
    ) -> bool:
        """
        Check if a piece can be placed at the given position
        
        Args:
            polygon: Shapely polygon of the piece
            x, y: Position coordinates
            rotation: Rotation angle in degrees
            min_spacing: Minimum spacing between pieces
            
        Returns:
            True if piece can be placed, False otherwise
        """
        try:
            # Apply transformations
            transformed_polygon = polygon
            if rotation != 0:
                transformed_polygon = rotate(transformed_polygon, rotation, origin='centroid')
            
            transformed_polygon = translate(transformed_polygon, x, y)
            
            # Check fabric boundaries
            bounds = transformed_polygon.bounds
            if (bounds[0] < 0 or bounds[1] < 0 or 
                bounds[2] > self.fabric_width or bounds[3] > self.fabric_length):
                return False
            
            # Check collision with existing pieces
            for placed_piece in self.placed_pieces:
                placed_polygon = self._get_placed_polygon(placed_piece)
                
                # Add minimum spacing buffer
                if min_spacing > 0:
                    buffered_polygon = placed_polygon.buffer(min_spacing)
                    if transformed_polygon.intersects(buffered_polygon):
                        return False
                else:
                    if transformed_polygon.intersects(placed_polygon):
                        return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error checking piece placement: {e}")
            return False
    
    def _get_placed_polygon(self, placed_piece: PlacedPatternPiece) -> Polygon:
        """Get the polygon for an already placed piece"""
        # This is a simplified implementation
        # In practice, you'd reconstruct the exact polygon from the piece data
        width = placed_piece.bounds['width']
        height = placed_piece.bounds['height']
        x = placed_piece.position['x']
        y = placed_piece.position['y']
        rotation = placed_piece.rotation
        
        polygon = self._create_rectangle_from_bounds(width, height)
        
        if rotation != 0:
            polygon = rotate(polygon, rotation, origin='centroid')
        
        polygon = translate(polygon, x, y)
        return polygon
    
    def place_piece(
        self, 
        piece: PatternPiece, 
        polygon: Polygon,
        x: float, 
        y: float, 
        rotation: float = 0
    ) -> PlacedPatternPiece:
        """
        Place a piece at the given position and add to placed pieces
        
        Args:
            piece: Original pattern piece
            polygon: Shapely polygon
            x, y: Position coordinates
            rotation: Rotation angle in degrees
            
        Returns:
            PlacedPatternPiece object
        """
        # Apply transformations to get final bounds
        transformed_polygon = polygon
        if rotation != 0:
            transformed_polygon = rotate(transformed_polygon, rotation, origin='centroid')
        
        transformed_polygon = translate(transformed_polygon, x, y)
        bounds = transformed_polygon.bounds
        
        placed_piece = PlacedPatternPiece(
            piece_id=piece.piece_id,
            name=piece.name,
            size=piece.size,
            position={"x": x, "y": y},
            rotation=rotation,
            bounds={
                "width": bounds[2] - bounds[0],
                "height": bounds[3] - bounds[1]
            },
            area=transformed_polygon.area
        )
        
        self.placed_pieces.append(placed_piece)
        return placed_piece
    
    def calculate_efficiency_metrics(self) -> EfficiencyMetrics:
        """Calculate efficiency metrics for the current layout"""
        total_fabric_area = self.fabric_area
        used_area = sum(piece.area for piece in self.placed_pieces)
        waste_area = total_fabric_area - used_area
        
        yield_percentage = (used_area / total_fabric_area) * 100 if total_fabric_area > 0 else 0
        waste_percentage = (waste_area / total_fabric_area) * 100 if total_fabric_area > 0 else 0
        
        return EfficiencyMetrics(
            total_fabric_area=total_fabric_area,
            used_area=used_area,
            waste_area=waste_area,
            yield_percentage=round(yield_percentage, 2),
            waste_percentage=round(waste_percentage, 2),
            pieces_placed=len(self.placed_pieces),
            pieces_failed=len(self.failed_pieces)
        )
    
    def create_marker_layout(self) -> MarkerLayout:
        """Create the final marker layout"""
        return MarkerLayout(
            fabric_dimensions={
                "width": self.fabric_width,
                "length": self.fabric_length
            },
            pattern_pieces=self.placed_pieces,
            grain_line_angle=0  # Default grain line
        )
    
    def get_rotation_angles(self, piece: PatternPiece, settings: OptimizationSettings) -> List[float]:
        """Get allowed rotation angles for a piece"""
        if not piece.rotation_allowed or not settings.allow_rotation:
            return [0]
        
        return settings.rotation_angles
    
    def sort_pieces_by_strategy(
        self, 
        pieces: List[PatternPiece], 
        strategy: str = "area_desc"
    ) -> List[PatternPiece]:
        """Sort pieces according to the specified strategy"""
        if strategy == "area_desc":
            return sorted(pieces, key=lambda p: p.geometry.area or (p.geometry.width * p.geometry.height), reverse=True)
        elif strategy == "area_asc":
            return sorted(pieces, key=lambda p: p.geometry.area or (p.geometry.width * p.geometry.height))
        elif strategy == "width_desc":
            return sorted(pieces, key=lambda p: p.geometry.width, reverse=True)
        elif strategy == "height_desc":
            return sorted(pieces, key=lambda p: p.geometry.height, reverse=True)
        elif strategy == "priority":
            return sorted(pieces, key=lambda p: (p.priority, -(p.geometry.area or (p.geometry.width * p.geometry.height))))
        else:
            return pieces
    
    def expand_pieces_by_quantity(self, pieces: List[PatternPiece]) -> List[PatternPiece]:
        """Expand pieces list based on quantity_needed"""
        expanded_pieces = []
        for piece in pieces:
            for i in range(piece.quantity_needed):
                # Create a copy with unique ID
                piece_copy = piece.copy()
                piece_copy.piece_id = f"{piece.piece_id}_{i+1}"
                expanded_pieces.append(piece_copy)
        return expanded_pieces
