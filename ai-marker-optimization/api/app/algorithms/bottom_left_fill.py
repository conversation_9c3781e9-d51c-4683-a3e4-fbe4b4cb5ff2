"""
Bottom-Left Fill (BLF) Algorithm Implementation
Fast greedy algorithm for marker optimization
"""

import time
import logging
from typing import List, Tuple, Optional

from app.algorithms.base import OptimizationAlgorithm
from app.models.optimization import (
    PatternPiece, 
    MarkerLayout, 
    EfficiencyMetrics,
    OptimizationSettings
)

logger = logging.getLogger(__name__)


class BottomLeftFillAlgorithm(OptimizationAlgorithm):
    """
    Bottom-Left Fill algorithm implementation
    
    This is a fast, greedy algorithm that places pieces starting from the bottom-left
    corner of the fabric, moving right and up as needed. It's ideal for real-time
    optimization and simple rectangular pieces.
    """
    
    def optimize(
        self, 
        pattern_pieces: List[PatternPiece], 
        settings: OptimizationSettings
    ) -> Tuple[MarkerLayout, EfficiencyMetrics]:
        """
        Optimize marker layout using Bottom-Left Fill algorithm
        
        Args:
            pattern_pieces: List of pattern pieces to place
            settings: Optimization settings
            
        Returns:
            Tuple of (MarkerLayout, EfficiencyMetrics)
        """
        start_time = time.time()
        
        # Reset state
        self.placed_pieces = []
        self.failed_pieces = []
        
        # Get algorithm parameters
        blf_params = settings.blf_params or {}
        sort_strategy = blf_params.get('sort_strategy', 'area_desc')
        rotation_step = blf_params.get('rotation_step', 90)
        
        # Expand pieces by quantity and sort
        expanded_pieces = self.expand_pieces_by_quantity(pattern_pieces)
        sorted_pieces = self.sort_pieces_by_strategy(expanded_pieces, sort_strategy)
        
        logger.info(f"Starting BLF optimization for {len(sorted_pieces)} pieces")
        
        # Place each piece using BLF strategy
        for piece in sorted_pieces:
            if self._place_piece_blf(piece, settings):
                logger.debug(f"Successfully placed piece {piece.piece_id}")
            else:
                logger.warning(f"Failed to place piece {piece.piece_id}")
                self.failed_pieces.append(piece)
        
        self.execution_time = time.time() - start_time
        
        # Create results
        marker_layout = self.create_marker_layout()
        efficiency_metrics = self.calculate_efficiency_metrics()
        
        logger.info(
            f"BLF optimization completed in {self.execution_time:.2f}s. "
            f"Placed: {len(self.placed_pieces)}, Failed: {len(self.failed_pieces)}, "
            f"Efficiency: {efficiency_metrics.yield_percentage:.1f}%"
        )
        
        return marker_layout, efficiency_metrics
    
    def _place_piece_blf(self, piece: PatternPiece, settings: OptimizationSettings) -> bool:
        """
        Place a single piece using Bottom-Left Fill strategy
        
        Args:
            piece: Pattern piece to place
            settings: Optimization settings
            
        Returns:
            True if piece was successfully placed, False otherwise
        """
        polygon = self.create_polygon_from_piece(piece)
        rotation_angles = self.get_rotation_angles(piece, settings)
        
        # Try each rotation angle
        for rotation in rotation_angles:
            # Try to find the bottom-left position for this rotation
            position = self._find_bottom_left_position(polygon, rotation, settings.minimum_spacing)
            
            if position:
                x, y = position
                self.place_piece(piece, polygon, x, y, rotation)
                return True
        
        return False
    
    def _find_bottom_left_position(
        self, 
        polygon, 
        rotation: float, 
        min_spacing: float
    ) -> Optional[Tuple[float, float]]:
        """
        Find the bottom-left position where the piece can be placed
        
        Args:
            polygon: Shapely polygon of the piece
            rotation: Rotation angle in degrees
            min_spacing: Minimum spacing between pieces
            
        Returns:
            Tuple of (x, y) coordinates if position found, None otherwise
        """
        from shapely.affinity import rotate as shapely_rotate
        
        # Apply rotation
        if rotation != 0:
            rotated_polygon = shapely_rotate(polygon, rotation, origin='centroid')
        else:
            rotated_polygon = polygon
        
        # Get piece dimensions after rotation
        bounds = rotated_polygon.bounds
        piece_width = bounds[2] - bounds[0]
        piece_height = bounds[3] - bounds[1]
        
        # Check if piece fits in fabric at all
        if piece_width > self.fabric_width or piece_height > self.fabric_length:
            return None
        
        # Grid search parameters
        grid_resolution = min(0.5, min_spacing)  # Search grid resolution
        max_x = self.fabric_width - piece_width
        max_y = self.fabric_length - piece_height
        
        # Bottom-left fill: start from bottom-left, move right then up
        for y in self._generate_y_positions(max_y, grid_resolution):
            for x in self._generate_x_positions(max_x, grid_resolution):
                if self.can_place_piece(polygon, x, y, rotation, min_spacing):
                    return (x, y)
        
        return None
    
    def _generate_y_positions(self, max_y: float, resolution: float):
        """Generate Y positions from bottom to top"""
        y = 0
        while y <= max_y:
            yield y
            y += resolution
    
    def _generate_x_positions(self, max_x: float, resolution: float):
        """Generate X positions from left to right"""
        x = 0
        while x <= max_x:
            yield x
            x += resolution
    
    def _get_bottom_left_candidates(self, polygon, rotation: float) -> List[Tuple[float, float]]:
        """
        Get candidate positions based on existing piece placements
        This is an optimization to avoid checking every grid position
        """
        from shapely.affinity import rotate as shapely_rotate
        
        candidates = [(0, 0)]  # Always try origin
        
        if rotation != 0:
            rotated_polygon = shapely_rotate(polygon, rotation, origin='centroid')
        else:
            rotated_polygon = polygon
        
        bounds = rotated_polygon.bounds
        piece_width = bounds[2] - bounds[0]
        piece_height = bounds[3] - bounds[1]
        
        # Add candidates based on existing pieces
        for placed_piece in self.placed_pieces:
            px = placed_piece.position['x']
            py = placed_piece.position['y']
            pw = placed_piece.bounds['width']
            ph = placed_piece.bounds['height']
            
            # Right edge of existing piece
            candidates.append((px + pw, py))
            
            # Top edge of existing piece
            candidates.append((px, py + ph))
            
            # Bottom-right corner
            candidates.append((px + pw, py))
            
            # Top-left corner
            candidates.append((px, py + ph))
        
        # Filter candidates that fit within fabric bounds
        valid_candidates = []
        for x, y in candidates:
            if (x + piece_width <= self.fabric_width and 
                y + piece_height <= self.fabric_length and
                x >= 0 and y >= 0):
                valid_candidates.append((x, y))
        
        # Sort by bottom-left preference (y first, then x)
        valid_candidates.sort(key=lambda pos: (pos[1], pos[0]))
        
        return valid_candidates
    
    def _optimize_fabric_length(self):
        """
        Optimize fabric length based on placed pieces
        This can be called after optimization to minimize fabric usage
        """
        if not self.placed_pieces:
            return
        
        # Find the maximum Y coordinate of all placed pieces
        max_y = 0
        for piece in self.placed_pieces:
            piece_max_y = piece.position['y'] + piece.bounds['height']
            max_y = max(max_y, piece_max_y)
        
        # Add some margin and update fabric length
        margin = 2.0  # 2 inch margin
        optimized_length = max_y + margin
        
        if optimized_length < self.fabric_length:
            self.fabric_length = optimized_length
            self.fabric_area = self.fabric_width * self.fabric_length
            logger.info(f"Optimized fabric length to {self.fabric_length:.2f} inches")


class ImprovedBottomLeftFillAlgorithm(BottomLeftFillAlgorithm):
    """
    Improved version of BLF with better placement strategies
    """
    
    def _find_bottom_left_position(
        self, 
        polygon, 
        rotation: float, 
        min_spacing: float
    ) -> Optional[Tuple[float, float]]:
        """
        Improved position finding using candidate-based approach
        """
        # First try candidate positions based on existing pieces
        candidates = self._get_bottom_left_candidates(polygon, rotation)
        
        for x, y in candidates:
            if self.can_place_piece(polygon, x, y, rotation, min_spacing):
                return (x, y)
        
        # Fallback to grid search if candidates don't work
        return super()._find_bottom_left_position(polygon, rotation, min_spacing)
    
    def optimize(
        self, 
        pattern_pieces: List[PatternPiece], 
        settings: OptimizationSettings
    ) -> Tuple[MarkerLayout, EfficiencyMetrics]:
        """
        Optimize with post-processing improvements
        """
        # Run base optimization
        marker_layout, efficiency_metrics = super().optimize(pattern_pieces, settings)
        
        # Post-processing optimizations
        self._optimize_fabric_length()
        
        # Recalculate metrics with optimized fabric length
        efficiency_metrics = self.calculate_efficiency_metrics()
        marker_layout = self.create_marker_layout()
        
        return marker_layout, efficiency_metrics
