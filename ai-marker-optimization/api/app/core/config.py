"""
Configuration settings for the AI Marker Optimization Tool
"""

import os
from functools import lru_cache
from typing import List, Optional

from pydantic import Field
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings"""
    
    # Application
    APP_NAME: str = "AI Marker Optimization Tool"
    VERSION: str = "1.0.0"
    DEBUG: bool = Field(default=False, env="DEBUG")
    
    # Server
    HOST: str = Field(default="0.0.0.0", env="HOST")
    PORT: int = Field(default=8001, env="PORT")
    
    # Security
    SECRET_KEY: str = Field(env="SECRET_KEY")
    ALLOWED_HOSTS: List[str] = Field(default=["localhost", "127.0.0.1"], env="ALLOWED_HOSTS")
    CORS_ORIGINS: List[str] = Field(
        default=["http://localhost:3000", "http://localhost:8000"],
        env="CORS_ORIGINS"
    )
    
    # Authentication
    LARAVEL_API_URL: str = Field(env="LARAVEL_API_URL")
    LARAVEL_API_TOKEN: str = Field(env="LARAVEL_API_TOKEN")
    
    # Database
    DATABASE_URL: str = Field(env="DATABASE_URL")
    
    # Redis
    REDIS_URL: str = Field(default="redis://localhost:6379", env="REDIS_URL")
    
    # Optimization settings
    MAX_OPTIMIZATION_TIME: int = Field(default=300, env="MAX_OPTIMIZATION_TIME")  # seconds
    MAX_PATTERN_PIECES: int = Field(default=1000, env="MAX_PATTERN_PIECES")
    MAX_FABRIC_WIDTH: float = Field(default=200.0, env="MAX_FABRIC_WIDTH")  # inches
    MAX_FABRIC_LENGTH: float = Field(default=1000.0, env="MAX_FABRIC_LENGTH")  # inches
    
    # Algorithm defaults
    DEFAULT_ALGORITHM: str = Field(default="bottom_left_fill", env="DEFAULT_ALGORITHM")
    DEFAULT_MAX_ITERATIONS: int = Field(default=1000, env="DEFAULT_MAX_ITERATIONS")
    DEFAULT_ROTATION_ANGLES: List[int] = Field(default=[0, 90, 180, 270])
    
    # File handling
    MAX_FILE_SIZE: int = Field(default=10 * 1024 * 1024, env="MAX_FILE_SIZE")  # 10MB
    UPLOAD_DIR: str = Field(default="uploads", env="UPLOAD_DIR")
    EXPORT_DIR: str = Field(default="exports", env="EXPORT_DIR")
    
    # Monitoring
    ENABLE_METRICS: bool = Field(default=True, env="ENABLE_METRICS")
    LOG_LEVEL: str = Field(default="INFO", env="LOG_LEVEL")
    
    # Rate limiting
    RATE_LIMIT_REQUESTS: int = Field(default=100, env="RATE_LIMIT_REQUESTS")
    RATE_LIMIT_WINDOW: int = Field(default=3600, env="RATE_LIMIT_WINDOW")  # seconds
    
    # External services
    NOTIFICATION_WEBHOOK_URL: Optional[str] = Field(default=None, env="NOTIFICATION_WEBHOOK_URL")
    
    class Config:
        env_file = ".env"
        case_sensitive = True


@lru_cache()
def get_settings() -> Settings:
    """Get cached settings instance"""
    return Settings()


# Algorithm configuration
ALGORITHM_CONFIGS = {
    "genetic": {
        "name": "Genetic Algorithm",
        "description": "Advanced optimization using evolutionary algorithms",
        "complexity": "high",
        "typical_time": "30-300 seconds",
        "best_for": "Complex layouts with many constraints",
        "parameters": {
            "population_size": {"default": 50, "min": 10, "max": 200},
            "generations": {"default": 100, "min": 10, "max": 1000},
            "mutation_rate": {"default": 0.1, "min": 0.01, "max": 0.5},
            "crossover_rate": {"default": 0.8, "min": 0.1, "max": 1.0}
        }
    },
    "bottom_left_fill": {
        "name": "Bottom-Left Fill",
        "description": "Fast greedy algorithm for quick optimization",
        "complexity": "low",
        "typical_time": "1-5 seconds",
        "best_for": "Real-time optimization and simple layouts",
        "parameters": {
            "sort_strategy": {"default": "area_desc", "options": ["area_desc", "area_asc", "width_desc", "height_desc"]},
            "rotation_step": {"default": 90, "min": 1, "max": 180}
        }
    },
    "no_fit_polygon": {
        "name": "No-Fit Polygon",
        "description": "Advanced nesting for irregular shapes",
        "complexity": "medium",
        "typical_time": "10-60 seconds",
        "best_for": "Irregular pattern pieces with complex shapes",
        "parameters": {
            "precision": {"default": 0.1, "min": 0.01, "max": 1.0},
            "simplification_tolerance": {"default": 0.05, "min": 0.001, "max": 0.5}
        }
    }
}

# Size color mapping for visualization
SIZE_COLORS = {
    "XXS": "#E8F5E8",  # Very light green
    "XS": "#E3F2FD",   # Light blue
    "S": "#2196F3",    # Blue
    "M": "#4CAF50",    # Green
    "L": "#FF9800",    # Orange
    "XL": "#F44336",   # Red
    "XXL": "#9C27B0",  # Purple
    "XXXL": "#795548"  # Brown
}

# Fabric specifications
FABRIC_SPECS = {
    "standard_widths": [36, 45, 54, 60, 72, 108],  # inches
    "grain_directions": ["lengthwise", "crosswise", "bias"],
    "default_seam_allowance": 0.625,  # 5/8 inch
    "minimum_spacing": 0.125  # 1/8 inch between pieces
}
