"""
Logging configuration for the AI Marker Optimization Tool
"""

import logging
import logging.config
import sys
from typing import Dict, Any

import structlog
from pythonjsonlogger import jsonlogger

from app.core.config import get_settings

settings = get_settings()


def setup_logging():
    """Configure structured logging for the application"""
    
    # Configure structlog
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer()
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )
    
    # Configure standard logging
    logging_config = {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "json": {
                "()": jsonlogger.JsonFormatter,
                "format": "%(asctime)s %(name)s %(levelname)s %(message)s"
            },
            "standard": {
                "format": "%(asctime)s [%(levelname)s] %(name)s: %(message)s"
            }
        },
        "handlers": {
            "console": {
                "class": "logging.StreamHandler",
                "level": settings.LOG_LEVEL,
                "formatter": "json" if not settings.DEBUG else "standard",
                "stream": sys.stdout
            },
            "file": {
                "class": "logging.handlers.RotatingFileHandler",
                "level": settings.LOG_LEVEL,
                "formatter": "json",
                "filename": "logs/app.log",
                "maxBytes": 10485760,  # 10MB
                "backupCount": 5
            }
        },
        "loggers": {
            "app": {
                "level": settings.LOG_LEVEL,
                "handlers": ["console", "file"] if not settings.DEBUG else ["console"],
                "propagate": False
            },
            "uvicorn": {
                "level": "INFO",
                "handlers": ["console"],
                "propagate": False
            },
            "uvicorn.access": {
                "level": "INFO",
                "handlers": ["console"],
                "propagate": False
            }
        },
        "root": {
            "level": settings.LOG_LEVEL,
            "handlers": ["console"]
        }
    }
    
    # Create logs directory if it doesn't exist
    import os
    os.makedirs("logs", exist_ok=True)
    
    # Apply logging configuration
    logging.config.dictConfig(logging_config)
    
    # Set up request ID context
    setup_request_context()


def setup_request_context():
    """Set up request context for logging"""
    
    def add_request_id(logger, method_name, event_dict):
        """Add request ID to log entries"""
        # This would be populated by middleware
        request_id = getattr(logger._context, 'request_id', None)
        if request_id:
            event_dict['request_id'] = request_id
        return event_dict
    
    # Add the processor to structlog
    current_processors = structlog.get_config()["processors"]
    current_processors.insert(-1, add_request_id)
    
    structlog.configure(processors=current_processors)


class OptimizationLogger:
    """
    Specialized logger for optimization operations
    """
    
    def __init__(self, optimization_id: str = None):
        self.logger = structlog.get_logger("optimization")
        self.optimization_id = optimization_id
    
    def log_optimization_start(
        self, 
        algorithm: str, 
        pieces_count: int, 
        fabric_width: float,
        **kwargs
    ):
        """Log optimization start"""
        self.logger.info(
            "Optimization started",
            optimization_id=self.optimization_id,
            algorithm=algorithm,
            pieces_count=pieces_count,
            fabric_width=fabric_width,
            **kwargs
        )
    
    def log_optimization_complete(
        self,
        execution_time: float,
        yield_percentage: float,
        pieces_placed: int,
        pieces_failed: int,
        **kwargs
    ):
        """Log optimization completion"""
        self.logger.info(
            "Optimization completed",
            optimization_id=self.optimization_id,
            execution_time_seconds=execution_time,
            yield_percentage=yield_percentage,
            pieces_placed=pieces_placed,
            pieces_failed=pieces_failed,
            **kwargs
        )
    
    def log_optimization_error(self, error: str, **kwargs):
        """Log optimization error"""
        self.logger.error(
            "Optimization failed",
            optimization_id=self.optimization_id,
            error=error,
            **kwargs
        )
    
    def log_piece_placement(
        self,
        piece_id: str,
        position: Dict[str, float],
        rotation: float,
        attempt_number: int = None
    ):
        """Log individual piece placement"""
        self.logger.debug(
            "Piece placed",
            optimization_id=self.optimization_id,
            piece_id=piece_id,
            position=position,
            rotation=rotation,
            attempt_number=attempt_number
        )
    
    def log_algorithm_metrics(self, metrics: Dict[str, Any]):
        """Log algorithm-specific metrics"""
        self.logger.info(
            "Algorithm metrics",
            optimization_id=self.optimization_id,
            **metrics
        )


class PerformanceLogger:
    """
    Logger for performance monitoring
    """
    
    def __init__(self):
        self.logger = structlog.get_logger("performance")
    
    def log_api_request(
        self,
        method: str,
        path: str,
        status_code: int,
        duration_ms: float,
        user_id: str = None
    ):
        """Log API request performance"""
        self.logger.info(
            "API request",
            method=method,
            path=path,
            status_code=status_code,
            duration_ms=duration_ms,
            user_id=user_id
        )
    
    def log_algorithm_performance(
        self,
        algorithm: str,
        pieces_count: int,
        execution_time: float,
        memory_usage_mb: float = None
    ):
        """Log algorithm performance metrics"""
        self.logger.info(
            "Algorithm performance",
            algorithm=algorithm,
            pieces_count=pieces_count,
            execution_time_seconds=execution_time,
            memory_usage_mb=memory_usage_mb
        )
    
    def log_database_query(
        self,
        query_type: str,
        duration_ms: float,
        rows_affected: int = None
    ):
        """Log database query performance"""
        self.logger.debug(
            "Database query",
            query_type=query_type,
            duration_ms=duration_ms,
            rows_affected=rows_affected
        )


class SecurityLogger:
    """
    Logger for security events
    """
    
    def __init__(self):
        self.logger = structlog.get_logger("security")
    
    def log_authentication_attempt(
        self,
        user_id: str = None,
        ip_address: str = None,
        success: bool = True,
        reason: str = None
    ):
        """Log authentication attempt"""
        self.logger.info(
            "Authentication attempt",
            user_id=user_id,
            ip_address=ip_address,
            success=success,
            reason=reason
        )
    
    def log_rate_limit_exceeded(
        self,
        client_id: str,
        endpoint: str,
        requests_count: int,
        limit: int
    ):
        """Log rate limit violation"""
        self.logger.warning(
            "Rate limit exceeded",
            client_id=client_id,
            endpoint=endpoint,
            requests_count=requests_count,
            limit=limit
        )
    
    def log_validation_failure(
        self,
        endpoint: str,
        user_id: str = None,
        validation_errors: list = None
    ):
        """Log validation failure"""
        self.logger.warning(
            "Validation failure",
            endpoint=endpoint,
            user_id=user_id,
            validation_errors=validation_errors
        )


def get_logger(name: str = None) -> structlog.BoundLogger:
    """
    Get a structured logger instance
    
    Args:
        name: Logger name (optional)
        
    Returns:
        Configured structlog logger
    """
    return structlog.get_logger(name or "app")
