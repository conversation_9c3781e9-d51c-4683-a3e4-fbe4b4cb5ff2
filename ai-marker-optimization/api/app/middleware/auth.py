"""
Authentication middleware for <PERSON>vel integration
"""

import logging
from typing import Dict, Any, Optional
import httpx
from fastapi import HTT<PERSON><PERSON>x<PERSON>, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from starlette.middleware.base import BaseHTTPMiddleware

from app.core.config import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()

security = HTTPBearer(auto_error=False)


class AuthMiddleware(BaseHTTPMiddleware):
    """
    Authentication middleware that validates Laravel Sanctum tokens
    """
    
    def __init__(self, app):
        super().__init__(app)
        self.excluded_paths = [
            "/docs",
            "/redoc",
            "/openapi.json",
            "/health",
            "/metrics",
            "/"
        ]
    
    async def dispatch(self, request: Request, call_next):
        """Process authentication for incoming requests"""
        
        # Skip authentication for excluded paths
        if any(request.url.path.startswith(path) for path in self.excluded_paths):
            return await call_next(request)
        
        # Extract authorization header
        authorization = request.headers.get("Authorization")
        if not authorization:
            raise HTTPException(
                status_code=401,
                detail="Authorization header required"
            )
        
        try:
            # Validate token with Laravel API
            user_data = await self._validate_token(authorization)
            
            # Add user data to request state
            request.state.user = user_data
            
            # Continue with request
            response = await call_next(request)
            return response
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Authentication error: {e}")
            raise HTTPException(
                status_code=401,
                detail="Authentication failed"
            )
    
    async def _validate_token(self, authorization: str) -> Dict[str, Any]:
        """
        Validate token with Laravel API
        
        Args:
            authorization: Authorization header value
            
        Returns:
            User data from Laravel API
        """
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{settings.LARAVEL_API_URL}/api/user",
                    headers={
                        "Authorization": authorization,
                        "Accept": "application/json"
                    },
                    timeout=5.0
                )
                
                if response.status_code == 200:
                    user_data = response.json()
                    logger.debug(f"Authenticated user: {user_data.get('id')}")
                    return user_data
                elif response.status_code == 401:
                    raise HTTPException(
                        status_code=401,
                        detail="Invalid or expired token"
                    )
                else:
                    logger.error(f"Laravel API error: {response.status_code}")
                    raise HTTPException(
                        status_code=401,
                        detail="Authentication service unavailable"
                    )
                    
        except httpx.TimeoutException:
            logger.error("Laravel API timeout")
            raise HTTPException(
                status_code=401,
                detail="Authentication service timeout"
            )
        except httpx.RequestError as e:
            logger.error(f"Laravel API request error: {e}")
            raise HTTPException(
                status_code=401,
                detail="Authentication service error"
            )


async def get_current_user(request: Request) -> Dict[str, Any]:
    """
    Dependency to get current authenticated user
    
    Args:
        request: FastAPI request object
        
    Returns:
        Current user data
    """
    if not hasattr(request.state, 'user'):
        raise HTTPException(
            status_code=401,
            detail="User not authenticated"
        )
    
    return request.state.user


async def get_optional_user(request: Request) -> Optional[Dict[str, Any]]:
    """
    Dependency to get current user if authenticated, None otherwise
    
    Args:
        request: FastAPI request object
        
    Returns:
        Current user data or None
    """
    return getattr(request.state, 'user', None)


class MockAuthMiddleware(BaseHTTPMiddleware):
    """
    Mock authentication middleware for development/testing
    """
    
    async def dispatch(self, request: Request, call_next):
        """Mock authentication that always succeeds"""
        
        # Skip authentication for excluded paths
        excluded_paths = ["/docs", "/redoc", "/openapi.json", "/health", "/metrics", "/"]
        if any(request.url.path.startswith(path) for path in excluded_paths):
            return await call_next(request)
        
        # Mock user data
        mock_user = {
            "id": 1,
            "name": "Test User",
            "email": "<EMAIL>",
            "company_id": 1,
            "role": "admin",
            "permissions": ["marker_optimization", "view_reports"]
        }
        
        request.state.user = mock_user
        
        response = await call_next(request)
        return response


def get_auth_middleware():
    """
    Get appropriate authentication middleware based on environment
    """
    if settings.DEBUG:
        logger.info("Using mock authentication middleware for development")
        return MockAuthMiddleware
    else:
        logger.info("Using Laravel Sanctum authentication middleware")
        return AuthMiddleware
