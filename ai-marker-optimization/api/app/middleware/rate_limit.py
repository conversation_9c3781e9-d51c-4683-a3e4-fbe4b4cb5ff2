"""
Rate limiting middleware
"""

import time
import logging
from typing import Dict, Any
from collections import defaultdict, deque
from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>, Request
from starlette.middleware.base import BaseHTTPMiddleware

from app.core.config import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


class RateLimitMiddleware(BaseHTTPMiddleware):
    """
    Simple in-memory rate limiting middleware
    In production, this should use Redis for distributed rate limiting
    """
    
    def __init__(self, app):
        super().__init__(app)
        self.requests: Dict[str, deque] = defaultdict(deque)
        self.max_requests = settings.RATE_LIMIT_REQUESTS
        self.window_seconds = settings.RATE_LIMIT_WINDOW
        
        # Different limits for different endpoints
        self.endpoint_limits = {
            "/api/v1/optimize-marker": {"requests": 10, "window": 3600},  # 10 per hour
            "/api/v1/validate-patterns": {"requests": 50, "window": 3600},  # 50 per hour
            "/api/v1/algorithms": {"requests": 100, "window": 3600},  # 100 per hour
        }
        
        self.excluded_paths = [
            "/health",
            "/metrics",
            "/docs",
            "/redoc",
            "/openapi.json"
        ]
    
    async def dispatch(self, request: Request, call_next):
        """Apply rate limiting to requests"""
        
        # Skip rate limiting for excluded paths
        if any(request.url.path.startswith(path) for path in self.excluded_paths):
            return await call_next(request)
        
        # Get client identifier
        client_id = self._get_client_id(request)
        
        # Get rate limit for this endpoint
        endpoint_limit = self._get_endpoint_limit(request.url.path)
        max_requests = endpoint_limit["requests"]
        window_seconds = endpoint_limit["window"]
        
        # Check rate limit
        current_time = time.time()
        
        # Clean old requests
        self._clean_old_requests(client_id, current_time, window_seconds)
        
        # Check if limit exceeded
        if len(self.requests[client_id]) >= max_requests:
            logger.warning(
                f"Rate limit exceeded for client {client_id}",
                extra={
                    "client_id": client_id,
                    "endpoint": request.url.path,
                    "requests_count": len(self.requests[client_id]),
                    "limit": max_requests
                }
            )
            
            # Calculate retry after
            oldest_request = self.requests[client_id][0]
            retry_after = int(window_seconds - (current_time - oldest_request))
            
            raise HTTPException(
                status_code=429,
                detail={
                    "message": "Rate limit exceeded",
                    "limit": max_requests,
                    "window_seconds": window_seconds,
                    "retry_after": retry_after
                },
                headers={"Retry-After": str(retry_after)}
            )
        
        # Record this request
        self.requests[client_id].append(current_time)
        
        # Add rate limit headers to response
        response = await call_next(request)
        
        remaining = max_requests - len(self.requests[client_id])
        reset_time = int(current_time + window_seconds)
        
        response.headers["X-RateLimit-Limit"] = str(max_requests)
        response.headers["X-RateLimit-Remaining"] = str(remaining)
        response.headers["X-RateLimit-Reset"] = str(reset_time)
        response.headers["X-RateLimit-Window"] = str(window_seconds)
        
        return response
    
    def _get_client_id(self, request: Request) -> str:
        """
        Get client identifier for rate limiting
        
        Uses user ID if authenticated, otherwise falls back to IP address
        """
        # Try to get user ID from authenticated user
        if hasattr(request.state, 'user') and request.state.user:
            user_id = request.state.user.get('id')
            if user_id:
                return f"user:{user_id}"
        
        # Fall back to IP address
        client_ip = request.client.host if request.client else "unknown"
        
        # Check for forwarded IP headers
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            client_ip = forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            client_ip = real_ip
        
        return f"ip:{client_ip}"
    
    def _get_endpoint_limit(self, path: str) -> Dict[str, int]:
        """Get rate limit configuration for endpoint"""
        
        # Check for exact match
        if path in self.endpoint_limits:
            return self.endpoint_limits[path]
        
        # Check for prefix match
        for endpoint_path, limits in self.endpoint_limits.items():
            if path.startswith(endpoint_path):
                return limits
        
        # Default limits
        return {
            "requests": self.max_requests,
            "window": self.window_seconds
        }
    
    def _clean_old_requests(self, client_id: str, current_time: float, window_seconds: int):
        """Remove requests outside the current window"""
        cutoff_time = current_time - window_seconds
        
        while (self.requests[client_id] and 
               self.requests[client_id][0] < cutoff_time):
            self.requests[client_id].popleft()
    
    def get_client_stats(self, client_id: str) -> Dict[str, Any]:
        """Get rate limiting stats for a client"""
        current_time = time.time()
        
        # Clean old requests first
        self._clean_old_requests(client_id, current_time, self.window_seconds)
        
        return {
            "client_id": client_id,
            "requests_in_window": len(self.requests[client_id]),
            "max_requests": self.max_requests,
            "window_seconds": self.window_seconds,
            "remaining": self.max_requests - len(self.requests[client_id])
        }


class RedisRateLimitMiddleware(BaseHTTPMiddleware):
    """
    Redis-based rate limiting middleware for production use
    """
    
    def __init__(self, app, redis_client=None):
        super().__init__(app)
        self.redis = redis_client
        self.max_requests = settings.RATE_LIMIT_REQUESTS
        self.window_seconds = settings.RATE_LIMIT_WINDOW
    
    async def dispatch(self, request: Request, call_next):
        """Apply Redis-based rate limiting"""
        
        if not self.redis:
            # Fall back to in-memory rate limiting
            logger.warning("Redis not available, falling back to in-memory rate limiting")
            return await call_next(request)
        
        # Skip rate limiting for excluded paths
        excluded_paths = ["/health", "/metrics", "/docs", "/redoc", "/openapi.json"]
        if any(request.url.path.startswith(path) for path in excluded_paths):
            return await call_next(request)
        
        client_id = self._get_client_id(request)
        key = f"rate_limit:{client_id}"
        
        try:
            # Use Redis sliding window
            current_time = time.time()
            
            # Remove old entries
            await self.redis.zremrangebyscore(
                key, 
                0, 
                current_time - self.window_seconds
            )
            
            # Count current requests
            current_requests = await self.redis.zcard(key)
            
            if current_requests >= self.max_requests:
                # Get oldest request for retry-after calculation
                oldest = await self.redis.zrange(key, 0, 0, withscores=True)
                if oldest:
                    retry_after = int(self.window_seconds - (current_time - oldest[0][1]))
                else:
                    retry_after = self.window_seconds
                
                raise HTTPException(
                    status_code=429,
                    detail={
                        "message": "Rate limit exceeded",
                        "limit": self.max_requests,
                        "window_seconds": self.window_seconds,
                        "retry_after": retry_after
                    },
                    headers={"Retry-After": str(retry_after)}
                )
            
            # Add current request
            await self.redis.zadd(key, {str(current_time): current_time})
            await self.redis.expire(key, self.window_seconds)
            
            # Continue with request
            response = await call_next(request)
            
            # Add rate limit headers
            remaining = self.max_requests - current_requests - 1
            reset_time = int(current_time + self.window_seconds)
            
            response.headers["X-RateLimit-Limit"] = str(self.max_requests)
            response.headers["X-RateLimit-Remaining"] = str(remaining)
            response.headers["X-RateLimit-Reset"] = str(reset_time)
            
            return response
            
        except Exception as e:
            logger.error(f"Redis rate limiting error: {e}")
            # Continue without rate limiting on Redis errors
            return await call_next(request)
    
    def _get_client_id(self, request: Request) -> str:
        """Get client identifier for rate limiting"""
        # Same implementation as in-memory version
        if hasattr(request.state, 'user') and request.state.user:
            user_id = request.state.user.get('id')
            if user_id:
                return f"user:{user_id}"
        
        client_ip = request.client.host if request.client else "unknown"
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            client_ip = forwarded_for.split(",")[0].strip()
        
        return f"ip:{client_ip}"
