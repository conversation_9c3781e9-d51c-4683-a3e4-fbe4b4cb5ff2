"""
Pydantic models for marker optimization requests and responses
"""

from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional, Union, Any
from uuid import UUID, uuid4

from pydantic import BaseModel, Field, validator, root_validator


class AlgorithmType(str, Enum):
    """Available optimization algorithms"""
    GENETIC = "genetic"
    BOTTOM_LEFT_FILL = "bottom_left_fill"
    NO_FIT_POLYGON = "no_fit_polygon"


class GrainDirection(str, Enum):
    """Fabric grain directions"""
    LENGTHWISE = "lengthwise"
    CROSSWISE = "crosswise"
    BIAS = "bias"


class PatternPieceGeometry(BaseModel):
    """Pattern piece geometric definition"""
    type: str = Field(..., description="Geometry type: 'polygon' or 'svg'")
    coordinates: Optional[List[List[float]]] = Field(None, description="Polygon coordinates [[x1,y1], [x2,y2], ...]")
    svg_path: Optional[str] = Field(None, description="SVG path data")
    width: float = Field(..., gt=0, description="Bounding box width in inches")
    height: float = Field(..., gt=0, description="Bounding box height in inches")
    area: Optional[float] = Field(None, description="Pattern piece area in square inches")
    
    @root_validator
    def validate_geometry(cls, values):
        """Ensure either coordinates or svg_path is provided"""
        if not values.get('coordinates') and not values.get('svg_path'):
            raise ValueError("Either coordinates or svg_path must be provided")
        return values


class PatternPiece(BaseModel):
    """Individual pattern piece definition"""
    piece_id: str = Field(..., description="Unique identifier for the pattern piece")
    name: str = Field(..., description="Human-readable name")
    size: str = Field(..., description="Garment size (XS, S, M, L, XL, etc.)")
    geometry: PatternPieceGeometry = Field(..., description="Geometric definition")
    quantity_needed: int = Field(..., gt=0, description="Number of pieces required")
    rotation_allowed: bool = Field(default=True, description="Whether piece can be rotated")
    grain_direction: Optional[GrainDirection] = Field(None, description="Required grain direction")
    fold_line: bool = Field(default=False, description="Whether piece should be placed on fold")
    priority: int = Field(default=1, description="Placement priority (1=highest)")
    
    @validator('size')
    def validate_size(cls, v):
        """Validate size format"""
        valid_sizes = ['XXS', 'XS', 'S', 'M', 'L', 'XL', 'XXL', 'XXXL']
        if v.upper() not in valid_sizes:
            raise ValueError(f"Size must be one of: {', '.join(valid_sizes)}")
        return v.upper()


class OptimizationSettings(BaseModel):
    """Algorithm-specific optimization settings"""
    algorithm_type: AlgorithmType = Field(default=AlgorithmType.BOTTOM_LEFT_FILL)
    max_iterations: int = Field(default=1000, gt=0, le=10000)
    rotation_angles: List[int] = Field(default=[0, 90, 180, 270])
    allow_rotation: bool = Field(default=True)
    minimum_spacing: float = Field(default=0.125, ge=0, description="Minimum spacing between pieces in inches")
    seam_allowance: float = Field(default=0.625, ge=0, description="Seam allowance in inches")
    
    # Algorithm-specific parameters
    genetic_params: Optional[Dict[str, Any]] = Field(None, description="Genetic algorithm parameters")
    blf_params: Optional[Dict[str, Any]] = Field(None, description="Bottom-left fill parameters")
    nfp_params: Optional[Dict[str, Any]] = Field(None, description="No-fit polygon parameters")


class MarkerOptimizationRequest(BaseModel):
    """Main optimization request model"""
    fabric_width: float = Field(..., gt=0, le=200, description="Fabric width in inches")
    fabric_length: Optional[float] = Field(None, gt=0, le=1000, description="Fabric length in inches (optional)")
    size_ratios: Dict[str, int] = Field(..., description="Size distribution e.g. {'S': 100, 'M': 200}")
    pattern_pieces: List[PatternPiece] = Field(..., min_items=1, max_items=1000)
    optimization_settings: OptimizationSettings = Field(default_factory=OptimizationSettings)
    
    # Metadata
    order_id: Optional[str] = Field(None, description="Associated order ID")
    company_id: Optional[str] = Field(None, description="Company identifier")
    user_id: Optional[str] = Field(None, description="User identifier")
    
    @validator('size_ratios')
    def validate_size_ratios(cls, v):
        """Validate size ratios"""
        if not v:
            raise ValueError("Size ratios cannot be empty")
        if any(qty <= 0 for qty in v.values()):
            raise ValueError("All size quantities must be positive")
        return v


class PlacedPatternPiece(BaseModel):
    """Pattern piece with placement information"""
    piece_id: str
    name: str
    size: str
    position: Dict[str, float] = Field(..., description="Position {x, y} in inches")
    rotation: float = Field(default=0, description="Rotation angle in degrees")
    bounds: Dict[str, float] = Field(..., description="Bounding box {width, height}")
    area: float = Field(..., description="Area in square inches")


class EfficiencyMetrics(BaseModel):
    """Optimization efficiency metrics"""
    total_fabric_area: float = Field(..., description="Total fabric area in square inches")
    used_area: float = Field(..., description="Area occupied by pattern pieces")
    waste_area: float = Field(..., description="Unused fabric area")
    yield_percentage: float = Field(..., ge=0, le=100, description="Fabric utilization percentage")
    waste_percentage: float = Field(..., ge=0, le=100, description="Waste percentage")
    pieces_placed: int = Field(..., description="Number of pieces successfully placed")
    pieces_failed: int = Field(..., description="Number of pieces that couldn't be placed")


class ProductionData(BaseModel):
    """Production planning data"""
    estimated_lays_needed: int = Field(..., description="Number of fabric lays required")
    fabric_utilization_per_lay: float = Field(..., description="Average utilization per lay")
    total_fabric_required_yards: float = Field(..., description="Total fabric needed in yards")
    cutting_time_estimate: Optional[float] = Field(None, description="Estimated cutting time in minutes")
    material_cost_estimate: Optional[float] = Field(None, description="Estimated material cost")


class MarkerLayout(BaseModel):
    """Complete marker layout definition"""
    fabric_dimensions: Dict[str, float] = Field(..., description="Fabric dimensions {width, length}")
    pattern_pieces: List[PlacedPatternPiece] = Field(..., description="Placed pattern pieces")
    grain_line_angle: float = Field(default=0, description="Grain line angle in degrees")


class OptimizationResponse(BaseModel):
    """Optimization result response"""
    optimization_id: UUID = Field(default_factory=uuid4)
    status: str = Field(default="success")
    execution_time_ms: int = Field(..., description="Execution time in milliseconds")
    algorithm_used: AlgorithmType
    
    # Results
    marker_layout: MarkerLayout
    efficiency_metrics: EfficiencyMetrics
    production_data: ProductionData
    
    # Metadata
    created_at: datetime = Field(default_factory=datetime.utcnow)
    request_parameters: Optional[Dict[str, Any]] = Field(None, description="Original request parameters")


class ValidationError(BaseModel):
    """Pattern validation error"""
    piece_id: str
    error_type: str
    message: str
    severity: str = Field(default="error")  # error, warning, info


class PatternValidationResponse(BaseModel):
    """Pattern validation result"""
    is_valid: bool
    errors: List[ValidationError] = Field(default_factory=list)
    warnings: List[ValidationError] = Field(default_factory=list)
    total_pieces: int
    total_area: float
    estimated_fabric_needed: Optional[float] = Field(None)


class AlgorithmInfo(BaseModel):
    """Algorithm information"""
    name: str
    algorithm_type: AlgorithmType
    description: str
    complexity: str
    typical_time: str
    best_for: str
    parameters: Dict[str, Any]


class OptimizationHistory(BaseModel):
    """Historical optimization record"""
    optimization_id: UUID
    created_at: datetime
    algorithm_used: AlgorithmType
    fabric_dimensions: Dict[str, float]
    efficiency_metrics: EfficiencyMetrics
    execution_time_ms: int
    order_id: Optional[str] = None
    user_id: Optional[str] = None
