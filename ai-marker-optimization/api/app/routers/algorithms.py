"""
Algorithm information API endpoints
"""

import logging
from typing import List, Dict, Any

from fastapi import APIRouter, HTTPException, Depends

from app.models.optimization import AlgorithmInfo, AlgorithmType
from app.services.optimization_service import OptimizationService
from app.core.config import ALGORITHM_CONFIGS
from app.middleware.auth import get_current_user

router = APIRouter()
logger = logging.getLogger(__name__)


@router.get("/algorithms", response_model=List[AlgorithmInfo])
async def get_available_algorithms(
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Get list of available optimization algorithms
    
    Returns information about all available algorithms including
    their parameters, complexity, and typical performance characteristics.
    """
    try:
        optimization_service = OptimizationService()
        algorithms_data = optimization_service.get_available_algorithms()
        
        algorithms = []
        for algo_data in algorithms_data:
            algorithm_info = AlgorithmInfo(
                name=algo_data["name"],
                algorithm_type=AlgorithmType(algo_data["algorithm_type"]),
                description=algo_data["description"],
                complexity=algo_data["complexity"],
                typical_time=algo_data["typical_time"],
                best_for=algo_data["best_for"],
                parameters=algo_data["parameters"]
            )
            algorithms.append(algorithm_info)
        
        logger.info(f"Retrieved {len(algorithms)} available algorithms")
        return algorithms
        
    except Exception as e:
        logger.error(f"Failed to retrieve algorithms: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to retrieve available algorithms"
        )


@router.get("/algorithms/{algorithm_type}", response_model=AlgorithmInfo)
async def get_algorithm_info(
    algorithm_type: AlgorithmType,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Get detailed information about a specific algorithm
    
    Args:
        algorithm_type: The algorithm type to get information for
        
    Returns:
        Detailed algorithm information including parameters and performance characteristics
    """
    try:
        optimization_service = OptimizationService()
        algo_data = optimization_service.get_algorithm_info(algorithm_type)
        
        algorithm_info = AlgorithmInfo(
            name=algo_data["name"],
            algorithm_type=algorithm_type,
            description=algo_data["description"],
            complexity=algo_data["complexity"],
            typical_time=algo_data["typical_time"],
            best_for=algo_data["best_for"],
            parameters=algo_data["parameters"]
        )
        
        logger.info(f"Retrieved information for algorithm: {algorithm_type.value}")
        return algorithm_info
        
    except ValueError as e:
        logger.warning(f"Algorithm not found: {algorithm_type.value}")
        raise HTTPException(
            status_code=404,
            detail=f"Algorithm '{algorithm_type.value}' not found"
        )
    except Exception as e:
        logger.error(f"Failed to retrieve algorithm info for {algorithm_type.value}: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to retrieve algorithm information"
        )


@router.post("/algorithms/{algorithm_type}/estimate")
async def estimate_optimization_time(
    algorithm_type: AlgorithmType,
    request: Dict[str, Any],
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Estimate optimization time and resource requirements
    
    Args:
        algorithm_type: The algorithm to estimate for
        request: Request containing pattern pieces and fabric dimensions
        
    Returns:
        Time and resource estimates for the optimization
    """
    try:
        # Validate request structure
        if "pattern_pieces" not in request or "fabric_width" not in request:
            raise HTTPException(
                status_code=400,
                detail="Request must include 'pattern_pieces' and 'fabric_width'"
            )
        
        # Simple validation of pattern pieces
        pattern_pieces = request["pattern_pieces"]
        fabric_width = request["fabric_width"]
        
        if not isinstance(pattern_pieces, list) or len(pattern_pieces) == 0:
            raise HTTPException(
                status_code=400,
                detail="Pattern pieces must be a non-empty list"
            )
        
        if not isinstance(fabric_width, (int, float)) or fabric_width <= 0:
            raise HTTPException(
                status_code=400,
                detail="Fabric width must be a positive number"
            )
        
        # Create simplified pattern pieces for estimation
        from app.models.optimization import PatternPiece, PatternPieceGeometry
        
        simplified_pieces = []
        for piece_data in pattern_pieces:
            try:
                geometry = PatternPieceGeometry(
                    type="polygon",
                    width=piece_data.get("width", 10),
                    height=piece_data.get("height", 10)
                )
                
                piece = PatternPiece(
                    piece_id=piece_data.get("piece_id", "unknown"),
                    name=piece_data.get("name", "Unknown Piece"),
                    size=piece_data.get("size", "M"),
                    geometry=geometry,
                    quantity_needed=piece_data.get("quantity_needed", 1)
                )
                simplified_pieces.append(piece)
                
            except Exception as e:
                logger.warning(f"Invalid piece data: {e}")
                continue
        
        if not simplified_pieces:
            raise HTTPException(
                status_code=400,
                detail="No valid pattern pieces found in request"
            )
        
        # Get estimation
        optimization_service = OptimizationService()
        estimation = await optimization_service.estimate_optimization_time(
            simplified_pieces,
            fabric_width,
            algorithm_type
        )
        
        logger.info(
            f"Generated estimation for {algorithm_type.value}",
            extra={
                "algorithm": algorithm_type.value,
                "pieces_count": estimation["pieces_count"],
                "estimated_time": estimation["estimated_time_seconds"]
            }
        )
        
        return estimation
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to estimate optimization time: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to generate time estimation"
        )


@router.get("/algorithms/{algorithm_type}/parameters")
async def get_algorithm_parameters(
    algorithm_type: AlgorithmType,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Get detailed parameter information for a specific algorithm
    
    Args:
        algorithm_type: The algorithm type to get parameters for
        
    Returns:
        Detailed parameter specifications including defaults, ranges, and descriptions
    """
    try:
        config = ALGORITHM_CONFIGS.get(algorithm_type.value)
        if not config:
            raise HTTPException(
                status_code=404,
                detail=f"Algorithm '{algorithm_type.value}' not found"
            )
        
        parameters = config.get("parameters", {})
        
        # Add additional parameter metadata
        enhanced_parameters = {}
        for param_name, param_config in parameters.items():
            enhanced_parameters[param_name] = {
                **param_config,
                "name": param_name,
                "type": _infer_parameter_type(param_config),
                "description": _get_parameter_description(algorithm_type, param_name)
            }
        
        return {
            "algorithm_type": algorithm_type.value,
            "parameters": enhanced_parameters,
            "parameter_count": len(enhanced_parameters)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to retrieve parameters for {algorithm_type.value}: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to retrieve algorithm parameters"
        )


def _infer_parameter_type(param_config: Dict[str, Any]) -> str:
    """Infer parameter type from configuration"""
    if "options" in param_config:
        return "enum"
    elif "min" in param_config and "max" in param_config:
        if isinstance(param_config.get("default"), int):
            return "integer"
        else:
            return "float"
    elif isinstance(param_config.get("default"), bool):
        return "boolean"
    elif isinstance(param_config.get("default"), str):
        return "string"
    else:
        return "unknown"


def _get_parameter_description(algorithm_type: AlgorithmType, param_name: str) -> str:
    """Get human-readable description for algorithm parameters"""
    descriptions = {
        "genetic": {
            "population_size": "Number of individuals in each generation",
            "generations": "Number of generations to evolve",
            "mutation_rate": "Probability of mutation for each individual",
            "crossover_rate": "Probability of crossover between parents"
        },
        "bottom_left_fill": {
            "sort_strategy": "Strategy for sorting pieces before placement",
            "rotation_step": "Angle step for rotation attempts (degrees)"
        },
        "no_fit_polygon": {
            "precision": "Geometric precision for polygon operations",
            "simplification_tolerance": "Tolerance for polygon simplification"
        }
    }
    
    algo_descriptions = descriptions.get(algorithm_type.value, {})
    return algo_descriptions.get(param_name, f"Parameter for {algorithm_type.value} algorithm")
