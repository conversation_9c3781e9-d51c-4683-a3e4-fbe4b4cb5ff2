"""
Health check and system status API endpoints
"""

import logging
import time
import psutil
from datetime import datetime
from typing import Dict, Any

from fastapi import APIRouter, HTTPException
from fastapi.responses import JSONResponse

from app.core.config import get_settings

router = APIRouter()
settings = get_settings()
logger = logging.getLogger(__name__)

# Store startup time
startup_time = datetime.utcnow()


@router.get("/health")
async def health_check():
    """
    Basic health check endpoint
    
    Returns:
        Simple health status for load balancers and monitoring
    """
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "service": "AI Marker Optimization Tool",
        "version": "1.0.0"
    }


@router.get("/health/detailed")
async def detailed_health_check():
    """
    Detailed health check with system metrics
    
    Returns:
        Comprehensive health information including system resources,
        dependencies, and performance metrics
    """
    try:
        health_data = {
            "status": "healthy",
            "timestamp": datetime.utcnow().isoformat(),
            "service": {
                "name": "AI Marker Optimization Tool",
                "version": "1.0.0",
                "uptime_seconds": (datetime.utcnow() - startup_time).total_seconds()
            },
            "system": await _get_system_metrics(),
            "dependencies": await _check_dependencies(),
            "algorithms": await _check_algorithms(),
            "configuration": _get_configuration_status()
        }
        
        # Determine overall health status
        overall_status = "healthy"
        
        # Check system resources
        if health_data["system"]["memory_usage_percent"] > 90:
            overall_status = "degraded"
        
        if health_data["system"]["cpu_usage_percent"] > 95:
            overall_status = "degraded"
        
        # Check dependencies
        for dep_name, dep_status in health_data["dependencies"].items():
            if not dep_status.get("available", False):
                overall_status = "unhealthy"
                break
        
        health_data["status"] = overall_status
        
        logger.info(f"Health check completed with status: {overall_status}")
        
        if overall_status == "unhealthy":
            return JSONResponse(
                status_code=503,
                content=health_data
            )
        
        return health_data
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return JSONResponse(
            status_code=503,
            content={
                "status": "unhealthy",
                "timestamp": datetime.utcnow().isoformat(),
                "error": str(e)
            }
        )


@router.get("/health/ready")
async def readiness_check():
    """
    Kubernetes readiness probe endpoint
    
    Returns:
        Ready status indicating if the service can accept traffic
    """
    try:
        # Check critical dependencies
        dependencies_ok = True
        
        # Check if algorithms are available
        from app.services.optimization_service import OptimizationService
        optimization_service = OptimizationService()
        available_algorithms = optimization_service.get_available_algorithms()
        
        if not available_algorithms:
            dependencies_ok = False
        
        # Check system resources
        memory_usage = psutil.virtual_memory().percent
        if memory_usage > 95:
            dependencies_ok = False
        
        if dependencies_ok:
            return {"status": "ready", "timestamp": datetime.utcnow().isoformat()}
        else:
            return JSONResponse(
                status_code=503,
                content={
                    "status": "not_ready",
                    "timestamp": datetime.utcnow().isoformat()
                }
            )
            
    except Exception as e:
        logger.error(f"Readiness check failed: {e}")
        return JSONResponse(
            status_code=503,
            content={
                "status": "not_ready",
                "timestamp": datetime.utcnow().isoformat(),
                "error": str(e)
            }
        )


@router.get("/health/live")
async def liveness_check():
    """
    Kubernetes liveness probe endpoint
    
    Returns:
        Live status indicating if the service is running
    """
    return {
        "status": "alive",
        "timestamp": datetime.utcnow().isoformat(),
        "uptime_seconds": (datetime.utcnow() - startup_time).total_seconds()
    }


async def _get_system_metrics() -> Dict[str, Any]:
    """Get system resource metrics"""
    try:
        # CPU usage
        cpu_percent = psutil.cpu_percent(interval=1)
        
        # Memory usage
        memory = psutil.virtual_memory()
        
        # Disk usage
        disk = psutil.disk_usage('/')
        
        return {
            "cpu_usage_percent": cpu_percent,
            "memory_usage_percent": memory.percent,
            "memory_available_gb": round(memory.available / (1024**3), 2),
            "memory_total_gb": round(memory.total / (1024**3), 2),
            "disk_usage_percent": disk.percent,
            "disk_free_gb": round(disk.free / (1024**3), 2),
            "disk_total_gb": round(disk.total / (1024**3), 2)
        }
        
    except Exception as e:
        logger.warning(f"Failed to get system metrics: {e}")
        return {
            "error": "Failed to retrieve system metrics",
            "cpu_usage_percent": 0,
            "memory_usage_percent": 0
        }


async def _check_dependencies() -> Dict[str, Dict[str, Any]]:
    """Check status of external dependencies"""
    dependencies = {}
    
    # Check database connection
    try:
        # In a real implementation, this would test the database connection
        dependencies["database"] = {
            "available": True,
            "response_time_ms": 5,
            "status": "connected"
        }
    except Exception as e:
        dependencies["database"] = {
            "available": False,
            "error": str(e),
            "status": "disconnected"
        }
    
    # Check Redis connection
    try:
        # In a real implementation, this would test Redis connection
        dependencies["redis"] = {
            "available": True,
            "response_time_ms": 2,
            "status": "connected"
        }
    except Exception as e:
        dependencies["redis"] = {
            "available": False,
            "error": str(e),
            "status": "disconnected"
        }
    
    # Check Laravel API connection
    try:
        # In a real implementation, this would test Laravel API
        dependencies["laravel_api"] = {
            "available": True,
            "response_time_ms": 50,
            "status": "reachable"
        }
    except Exception as e:
        dependencies["laravel_api"] = {
            "available": False,
            "error": str(e),
            "status": "unreachable"
        }
    
    return dependencies


async def _check_algorithms() -> Dict[str, Any]:
    """Check availability of optimization algorithms"""
    try:
        from app.services.optimization_service import OptimizationService
        
        optimization_service = OptimizationService()
        available_algorithms = optimization_service.get_available_algorithms()
        
        return {
            "total_algorithms": len(available_algorithms),
            "available_algorithms": [algo["algorithm_type"] for algo in available_algorithms],
            "status": "operational" if available_algorithms else "no_algorithms"
        }
        
    except Exception as e:
        return {
            "total_algorithms": 0,
            "available_algorithms": [],
            "status": "error",
            "error": str(e)
        }


def _get_configuration_status() -> Dict[str, Any]:
    """Get configuration status and validation"""
    config_status = {
        "environment": "development" if settings.DEBUG else "production",
        "debug_mode": settings.DEBUG,
        "max_pattern_pieces": settings.MAX_PATTERN_PIECES,
        "max_fabric_width": settings.MAX_FABRIC_WIDTH,
        "default_algorithm": settings.DEFAULT_ALGORITHM,
        "cors_enabled": bool(settings.CORS_ORIGINS),
        "metrics_enabled": settings.ENABLE_METRICS
    }
    
    # Check for required environment variables
    required_vars = ["SECRET_KEY", "LARAVEL_API_URL"]
    missing_vars = []
    
    for var in required_vars:
        if not getattr(settings, var, None):
            missing_vars.append(var)
    
    config_status["missing_required_vars"] = missing_vars
    config_status["configuration_valid"] = len(missing_vars) == 0
    
    return config_status
