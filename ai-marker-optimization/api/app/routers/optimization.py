"""
Optimization API endpoints
"""

import logging
import time
from typing import Dict, Any
from uuid import uuid4

from fastapi import APIRouter, HTTPException, BackgroundTasks, Depends
from fastapi.responses import JSONResponse

from app.core.config import get_settings
from app.models.optimization import (
    MarkerOptimizationRequest,
    OptimizationResponse,
    AlgorithmType,
    ProductionData
)
from app.services.optimization_service import OptimizationService
from app.services.validation_service import ValidationService
from app.middleware.auth import get_current_user

router = APIRouter()
settings = get_settings()
logger = logging.getLogger(__name__)


@router.post("/optimize-marker", response_model=OptimizationResponse)
async def optimize_marker(
    request: MarkerOptimizationRequest,
    background_tasks: BackgroundTasks,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Main marker optimization endpoint
    
    Optimizes garment pattern layout on fabric using AI algorithms
    to minimize waste and maximize efficiency.
    """
    start_time = time.time()
    optimization_id = uuid4()
    
    logger.info(
        f"Starting optimization {optimization_id}",
        extra={
            "optimization_id": str(optimization_id),
            "user_id": current_user.get("id"),
            "company_id": request.company_id,
            "algorithm": request.optimization_settings.algorithm_type,
            "pieces_count": len(request.pattern_pieces),
            "fabric_width": request.fabric_width
        }
    )
    
    try:
        # Validate request
        validation_service = ValidationService()
        validation_result = await validation_service.validate_optimization_request(request)
        
        if not validation_result.is_valid:
            logger.warning(
                f"Validation failed for optimization {optimization_id}",
                extra={"errors": [error.message for error in validation_result.errors]}
            )
            raise HTTPException(
                status_code=400,
                detail={
                    "message": "Validation failed",
                    "errors": [error.dict() for error in validation_result.errors]
                }
            )
        
        # Check resource limits
        if len(request.pattern_pieces) > settings.MAX_PATTERN_PIECES:
            raise HTTPException(
                status_code=400,
                detail=f"Too many pattern pieces. Maximum allowed: {settings.MAX_PATTERN_PIECES}"
            )
        
        if request.fabric_width > settings.MAX_FABRIC_WIDTH:
            raise HTTPException(
                status_code=400,
                detail=f"Fabric width too large. Maximum allowed: {settings.MAX_FABRIC_WIDTH} inches"
            )
        
        # Perform optimization
        optimization_service = OptimizationService()
        
        marker_layout, efficiency_metrics = await optimization_service.optimize(
            request.pattern_pieces,
            request.fabric_width,
            request.fabric_length,
            request.optimization_settings
        )
        
        # Calculate production data
        production_data = _calculate_production_data(
            request, 
            efficiency_metrics, 
            marker_layout
        )
        
        execution_time_ms = int((time.time() - start_time) * 1000)
        
        # Create response
        response = OptimizationResponse(
            optimization_id=optimization_id,
            status="success",
            execution_time_ms=execution_time_ms,
            algorithm_used=request.optimization_settings.algorithm_type,
            marker_layout=marker_layout,
            efficiency_metrics=efficiency_metrics,
            production_data=production_data,
            request_parameters={
                "fabric_width": request.fabric_width,
                "fabric_length": request.fabric_length,
                "algorithm": request.optimization_settings.algorithm_type.value,
                "pieces_count": len(request.pattern_pieces)
            }
        )
        
        # Save optimization result in background
        background_tasks.add_task(
            _save_optimization_result,
            response,
            request,
            current_user
        )
        
        logger.info(
            f"Optimization {optimization_id} completed successfully",
            extra={
                "optimization_id": str(optimization_id),
                "execution_time_ms": execution_time_ms,
                "yield_percentage": efficiency_metrics.yield_percentage,
                "pieces_placed": efficiency_metrics.pieces_placed,
                "pieces_failed": efficiency_metrics.pieces_failed
            }
        )
        
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Optimization {optimization_id} failed",
            extra={
                "optimization_id": str(optimization_id),
                "error": str(e)
            },
            exc_info=True
        )
        raise HTTPException(
            status_code=500,
            detail={
                "message": "Optimization failed",
                "error": str(e),
                "optimization_id": str(optimization_id)
            }
        )


@router.get("/optimization-history")
async def get_optimization_history(
    limit: int = 50,
    offset: int = 0,
    company_id: str = None,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Get optimization history for the current user/company
    """
    try:
        # In a real implementation, this would query the database
        # For now, return a mock response
        return {
            "total": 0,
            "limit": limit,
            "offset": offset,
            "optimizations": []
        }
        
    except Exception as e:
        logger.error(f"Failed to retrieve optimization history: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to retrieve optimization history"
        )


@router.get("/optimization/{optimization_id}")
async def get_optimization(
    optimization_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Get a specific optimization result by ID
    """
    try:
        # In a real implementation, this would query the database
        # For now, return a 404
        raise HTTPException(
            status_code=404,
            detail="Optimization not found"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to retrieve optimization {optimization_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to retrieve optimization"
        )


def _calculate_production_data(
    request: MarkerOptimizationRequest,
    efficiency_metrics: Any,
    marker_layout: Any
) -> ProductionData:
    """Calculate production planning data"""
    
    # Calculate total pieces needed
    total_pieces_needed = sum(
        piece.quantity_needed for piece in request.pattern_pieces
    )
    
    # Calculate how many pieces fit per lay
    pieces_per_lay = efficiency_metrics.pieces_placed
    
    # Calculate number of lays needed
    estimated_lays = max(1, (total_pieces_needed + pieces_per_lay - 1) // pieces_per_lay)
    
    # Calculate fabric requirements
    fabric_length_yards = marker_layout.fabric_dimensions["length"] / 36  # Convert inches to yards
    total_fabric_yards = fabric_length_yards * estimated_lays
    
    # Estimate cutting time (simplified calculation)
    cutting_time_per_piece = 2.0  # 2 minutes per piece (example)
    total_cutting_time = total_pieces_needed * cutting_time_per_piece
    
    return ProductionData(
        estimated_lays_needed=estimated_lays,
        fabric_utilization_per_lay=efficiency_metrics.yield_percentage,
        total_fabric_required_yards=round(total_fabric_yards, 2),
        cutting_time_estimate=round(total_cutting_time, 1),
        material_cost_estimate=None  # Would be calculated based on fabric cost
    )


async def _save_optimization_result(
    response: OptimizationResponse,
    request: MarkerOptimizationRequest,
    user: Dict[str, Any]
):
    """Save optimization result to database (background task)"""
    try:
        # In a real implementation, this would save to database
        logger.info(
            f"Saving optimization result {response.optimization_id}",
            extra={
                "optimization_id": str(response.optimization_id),
                "user_id": user.get("id"),
                "company_id": request.company_id
            }
        )
        
        # TODO: Implement database saving
        # await database.save_optimization_result(response, request, user)
        
    except Exception as e:
        logger.error(
            f"Failed to save optimization result {response.optimization_id}: {e}",
            exc_info=True
        )
