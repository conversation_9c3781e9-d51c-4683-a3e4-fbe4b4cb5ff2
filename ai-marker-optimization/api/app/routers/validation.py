"""
Pattern validation API endpoints
"""

import logging
from typing import List, Dict, Any

from fastapi import APIRouter, HTTPException, Depends

from app.models.optimization import (
    PatternPiece,
    PatternValidationResponse,
    ValidationError
)
from app.services.validation_service import ValidationService
from app.middleware.auth import get_current_user

router = APIRouter()
logger = logging.getLogger(__name__)


@router.post("/validate-patterns", response_model=PatternValidationResponse)
async def validate_patterns(
    pattern_pieces: List[PatternPiece],
    fabric_width: float = None,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Validate pattern pieces before optimization
    
    Performs comprehensive validation of pattern piece data including:
    - Geometric validity
    - Size consistency
    - Dimension constraints
    - Fabric compatibility
    
    Args:
        pattern_pieces: List of pattern pieces to validate
        fabric_width: Optional fabric width for compatibility checking
        
    Returns:
        Validation result with errors, warnings, and recommendations
    """
    try:
        if not pattern_pieces:
            raise HTTPException(
                status_code=400,
                detail="Pattern pieces list cannot be empty"
            )
        
        validation_service = ValidationService()
        
        # Perform validation
        validation_result = await validation_service.validate_pattern_pieces(
            pattern_pieces,
            fabric_width
        )
        
        logger.info(
            f"Validated {len(pattern_pieces)} pattern pieces",
            extra={
                "pieces_count": len(pattern_pieces),
                "is_valid": validation_result.is_valid,
                "errors_count": len(validation_result.errors),
                "warnings_count": len(validation_result.warnings)
            }
        )
        
        return validation_result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Pattern validation failed: {e}")
        raise HTTPException(
            status_code=500,
            detail="Pattern validation failed"
        )


@router.post("/validate-geometry")
async def validate_geometry(
    geometry_data: Dict[str, Any],
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Validate individual pattern piece geometry
    
    Args:
        geometry_data: Geometry data to validate (coordinates or SVG path)
        
    Returns:
        Validation result for the geometry
    """
    try:
        validation_service = ValidationService()
        
        # Validate geometry structure
        if "type" not in geometry_data:
            raise HTTPException(
                status_code=400,
                detail="Geometry data must include 'type' field"
            )
        
        geometry_type = geometry_data["type"]
        
        if geometry_type == "polygon":
            if "coordinates" not in geometry_data:
                raise HTTPException(
                    status_code=400,
                    detail="Polygon geometry must include 'coordinates' field"
                )
            
            result = await validation_service.validate_polygon_coordinates(
                geometry_data["coordinates"]
            )
            
        elif geometry_type == "svg":
            if "svg_path" not in geometry_data:
                raise HTTPException(
                    status_code=400,
                    detail="SVG geometry must include 'svg_path' field"
                )
            
            result = await validation_service.validate_svg_path(
                geometry_data["svg_path"]
            )
            
        else:
            raise HTTPException(
                status_code=400,
                detail=f"Unsupported geometry type: {geometry_type}"
            )
        
        logger.info(f"Validated {geometry_type} geometry")
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Geometry validation failed: {e}")
        raise HTTPException(
            status_code=500,
            detail="Geometry validation failed"
        )


@router.post("/validate-fabric-compatibility")
async def validate_fabric_compatibility(
    request: Dict[str, Any],
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Validate compatibility between pattern pieces and fabric specifications
    
    Args:
        request: Contains pattern_pieces, fabric_width, fabric_length, and other specs
        
    Returns:
        Compatibility analysis and recommendations
    """
    try:
        # Validate request structure
        required_fields = ["pattern_pieces", "fabric_width"]
        for field in required_fields:
            if field not in request:
                raise HTTPException(
                    status_code=400,
                    detail=f"Missing required field: {field}"
                )
        
        pattern_pieces = request["pattern_pieces"]
        fabric_width = request["fabric_width"]
        fabric_length = request.get("fabric_length")
        
        validation_service = ValidationService()
        
        # Convert pattern pieces data to PatternPiece objects
        validated_pieces = []
        for piece_data in pattern_pieces:
            try:
                from app.models.optimization import PatternPiece, PatternPieceGeometry
                
                geometry = PatternPieceGeometry(**piece_data["geometry"])
                piece = PatternPiece(
                    piece_id=piece_data["piece_id"],
                    name=piece_data["name"],
                    size=piece_data["size"],
                    geometry=geometry,
                    quantity_needed=piece_data["quantity_needed"],
                    rotation_allowed=piece_data.get("rotation_allowed", True)
                )
                validated_pieces.append(piece)
                
            except Exception as e:
                logger.warning(f"Invalid piece data: {e}")
                continue
        
        if not validated_pieces:
            raise HTTPException(
                status_code=400,
                detail="No valid pattern pieces found"
            )
        
        # Perform compatibility analysis
        compatibility_result = await validation_service.validate_fabric_compatibility(
            validated_pieces,
            fabric_width,
            fabric_length
        )
        
        logger.info(
            f"Validated fabric compatibility for {len(validated_pieces)} pieces",
            extra={
                "fabric_width": fabric_width,
                "fabric_length": fabric_length,
                "pieces_count": len(validated_pieces)
            }
        )
        
        return compatibility_result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Fabric compatibility validation failed: {e}")
        raise HTTPException(
            status_code=500,
            detail="Fabric compatibility validation failed"
        )


@router.get("/validation-rules")
async def get_validation_rules(
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """
    Get current validation rules and constraints
    
    Returns:
        Dictionary of validation rules, limits, and requirements
    """
    try:
        from app.core.config import get_settings
        
        settings = get_settings()
        
        validation_rules = {
            "pattern_pieces": {
                "max_count": settings.MAX_PATTERN_PIECES,
                "min_dimensions": {
                    "width": 0.1,  # inches
                    "height": 0.1   # inches
                },
                "max_dimensions": {
                    "width": settings.MAX_FABRIC_WIDTH,
                    "height": settings.MAX_FABRIC_LENGTH
                },
                "required_fields": [
                    "piece_id", "name", "size", "geometry", "quantity_needed"
                ]
            },
            "fabric": {
                "max_width": settings.MAX_FABRIC_WIDTH,
                "max_length": settings.MAX_FABRIC_LENGTH,
                "standard_widths": [36, 45, 54, 60, 72, 108],  # inches
                "min_width": 12,  # inches
                "min_length": 12   # inches
            },
            "geometry": {
                "supported_types": ["polygon", "svg"],
                "min_polygon_points": 3,
                "max_polygon_points": 1000,
                "coordinate_precision": 0.001,  # inches
                "max_svg_path_length": 10000    # characters
            },
            "sizes": {
                "valid_sizes": ["XXS", "XS", "S", "M", "L", "XL", "XXL", "XXXL"],
                "case_sensitive": False
            },
            "optimization": {
                "min_spacing": 0.0,     # inches
                "max_spacing": 2.0,     # inches
                "default_spacing": 0.125, # inches
                "max_rotation_angles": 36,  # maximum number of rotation angles
                "max_iterations": settings.DEFAULT_MAX_ITERATIONS
            }
        }
        
        logger.info("Retrieved validation rules")
        return validation_rules
        
    except Exception as e:
        logger.error(f"Failed to retrieve validation rules: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to retrieve validation rules"
        )
