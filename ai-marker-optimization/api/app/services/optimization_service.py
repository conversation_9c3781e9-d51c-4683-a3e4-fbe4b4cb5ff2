"""
Core optimization service
"""

import logging
import asyncio
from typing import List, Tuple, Optional, Dict, Any

from app.models.optimization import (
    Pat<PERSON><PERSON><PERSON>ce,
    MarkerLayout,
    EfficiencyMetrics,
    OptimizationSettings,
    AlgorithmType
)
from app.algorithms.bottom_left_fill import BottomLeftFillAlgorithm, ImprovedBottomLeftFillAlgorithm
from app.core.config import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


class OptimizationService:
    """
    Main optimization service that coordinates different algorithms
    """
    
    def __init__(self):
        self.algorithms = {
            AlgorithmType.BOTTOM_LEFT_FILL: BottomLeftFillAlgorithm,
            # AlgorithmType.GENETIC: GeneticAlgorithm,  # To be implemented
            # AlgorithmType.NO_FIT_POLYGON: NoFitPolygonAlgorithm,  # To be implemented
        }
    
    async def optimize(
        self,
        pattern_pieces: List[PatternPiece],
        fabric_width: float,
        fabric_length: Optional[float] = None,
        settings: OptimizationSettings = None
    ) -> <PERSON><PERSON>[MarkerLayout, EfficiencyMetrics]:
        """
        Perform marker optimization using the specified algorithm
        
        Args:
            pattern_pieces: List of pattern pieces to optimize
            fabric_width: Fabric width in inches
            fabric_length: Fabric length in inches (optional)
            settings: Optimization settings
            
        Returns:
            Tuple of (MarkerLayout, EfficiencyMetrics)
        """
        if settings is None:
            settings = OptimizationSettings()
        
        logger.info(
            f"Starting optimization with {settings.algorithm_type.value} algorithm",
            extra={
                "algorithm": settings.algorithm_type.value,
                "pieces_count": len(pattern_pieces),
                "fabric_width": fabric_width,
                "fabric_length": fabric_length
            }
        )
        
        # Validate inputs
        self._validate_inputs(pattern_pieces, fabric_width, fabric_length, settings)
        
        # Get algorithm class
        algorithm_class = self.algorithms.get(settings.algorithm_type)
        if not algorithm_class:
            raise ValueError(f"Algorithm {settings.algorithm_type.value} not implemented")
        
        # Create algorithm instance
        algorithm = algorithm_class(fabric_width, fabric_length)
        
        # Run optimization with timeout
        try:
            marker_layout, efficiency_metrics = await asyncio.wait_for(
                self._run_optimization(algorithm, pattern_pieces, settings),
                timeout=settings.max_iterations  # Use max_iterations as timeout in seconds
            )
            
            logger.info(
                f"Optimization completed successfully",
                extra={
                    "algorithm": settings.algorithm_type.value,
                    "yield_percentage": efficiency_metrics.yield_percentage,
                    "pieces_placed": efficiency_metrics.pieces_placed,
                    "execution_time": algorithm.execution_time
                }
            )
            
            return marker_layout, efficiency_metrics
            
        except asyncio.TimeoutError:
            logger.error(f"Optimization timed out after {settings.max_iterations} seconds")
            raise ValueError(f"Optimization timed out after {settings.max_iterations} seconds")
    
    async def _run_optimization(
        self,
        algorithm,
        pattern_pieces: List[PatternPiece],
        settings: OptimizationSettings
    ) -> Tuple[MarkerLayout, EfficiencyMetrics]:
        """Run the optimization in an async context"""
        
        # Run the optimization in a thread pool to avoid blocking
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            None,
            algorithm.optimize,
            pattern_pieces,
            settings
        )
    
    def _validate_inputs(
        self,
        pattern_pieces: List[PatternPiece],
        fabric_width: float,
        fabric_length: Optional[float],
        settings: OptimizationSettings
    ):
        """Validate optimization inputs"""
        
        if not pattern_pieces:
            raise ValueError("Pattern pieces list cannot be empty")
        
        if fabric_width <= 0:
            raise ValueError("Fabric width must be positive")
        
        if fabric_length is not None and fabric_length <= 0:
            raise ValueError("Fabric length must be positive")
        
        if fabric_width > settings.MAX_FABRIC_WIDTH:
            raise ValueError(f"Fabric width exceeds maximum allowed: {settings.MAX_FABRIC_WIDTH}")
        
        if len(pattern_pieces) > settings.MAX_PATTERN_PIECES:
            raise ValueError(f"Too many pattern pieces. Maximum: {settings.MAX_PATTERN_PIECES}")
        
        # Validate individual pieces
        for piece in pattern_pieces:
            if piece.geometry.width <= 0 or piece.geometry.height <= 0:
                raise ValueError(f"Invalid dimensions for piece {piece.piece_id}")
            
            if piece.quantity_needed <= 0:
                raise ValueError(f"Invalid quantity for piece {piece.piece_id}")
    
    def get_algorithm_info(self, algorithm_type: AlgorithmType) -> Dict[str, Any]:
        """Get information about a specific algorithm"""
        from app.core.config import ALGORITHM_CONFIGS
        
        config = ALGORITHM_CONFIGS.get(algorithm_type.value)
        if not config:
            raise ValueError(f"Unknown algorithm: {algorithm_type.value}")
        
        return {
            "algorithm_type": algorithm_type.value,
            "available": algorithm_type in self.algorithms,
            **config
        }
    
    def get_available_algorithms(self) -> List[Dict[str, Any]]:
        """Get list of all available algorithms"""
        algorithms = []
        
        for algorithm_type in AlgorithmType:
            try:
                info = self.get_algorithm_info(algorithm_type)
                algorithms.append(info)
            except ValueError:
                continue
        
        return algorithms
    
    async def estimate_optimization_time(
        self,
        pattern_pieces: List[PatternPiece],
        fabric_width: float,
        algorithm_type: AlgorithmType
    ) -> Dict[str, Any]:
        """
        Estimate optimization time and resource requirements
        
        Args:
            pattern_pieces: List of pattern pieces
            fabric_width: Fabric width
            algorithm_type: Algorithm to use
            
        Returns:
            Dictionary with time estimates and resource requirements
        """
        pieces_count = sum(piece.quantity_needed for piece in pattern_pieces)
        total_area = sum(
            (piece.geometry.area or piece.geometry.width * piece.geometry.height) * piece.quantity_needed
            for piece in pattern_pieces
        )
        
        # Simple estimation based on algorithm complexity
        if algorithm_type == AlgorithmType.BOTTOM_LEFT_FILL:
            estimated_time = min(5, pieces_count * 0.1)  # Fast algorithm
            complexity = "low"
        elif algorithm_type == AlgorithmType.GENETIC:
            estimated_time = min(300, pieces_count * 2)  # Slower but better results
            complexity = "high"
        elif algorithm_type == AlgorithmType.NO_FIT_POLYGON:
            estimated_time = min(60, pieces_count * 0.5)  # Medium complexity
            complexity = "medium"
        else:
            estimated_time = 30
            complexity = "unknown"
        
        return {
            "estimated_time_seconds": estimated_time,
            "complexity": complexity,
            "pieces_count": pieces_count,
            "total_area": total_area,
            "fabric_width": fabric_width,
            "memory_estimate_mb": max(50, pieces_count * 0.5),  # Rough memory estimate
            "recommended": pieces_count < 100 if algorithm_type == AlgorithmType.BOTTOM_LEFT_FILL else pieces_count < 500
        }


class OptimizationCache:
    """
    Simple in-memory cache for optimization results
    In production, this would use Redis or similar
    """
    
    def __init__(self):
        self._cache: Dict[str, Tuple[MarkerLayout, EfficiencyMetrics]] = {}
        self._max_size = 100
    
    def get_cache_key(
        self,
        pattern_pieces: List[PatternPiece],
        fabric_width: float,
        fabric_length: Optional[float],
        settings: OptimizationSettings
    ) -> str:
        """Generate cache key for optimization parameters"""
        import hashlib
        import json
        
        # Create a deterministic hash of the optimization parameters
        cache_data = {
            "fabric_width": fabric_width,
            "fabric_length": fabric_length,
            "algorithm": settings.algorithm_type.value,
            "pieces": [
                {
                    "id": piece.piece_id,
                    "size": piece.size,
                    "width": piece.geometry.width,
                    "height": piece.geometry.height,
                    "quantity": piece.quantity_needed,
                    "rotation_allowed": piece.rotation_allowed
                }
                for piece in sorted(pattern_pieces, key=lambda p: p.piece_id)
            ],
            "settings": {
                "rotation_angles": settings.rotation_angles,
                "minimum_spacing": settings.minimum_spacing,
                "seam_allowance": settings.seam_allowance
            }
        }
        
        cache_string = json.dumps(cache_data, sort_keys=True)
        return hashlib.md5(cache_string.encode()).hexdigest()
    
    def get(self, cache_key: str) -> Optional[Tuple[MarkerLayout, EfficiencyMetrics]]:
        """Get cached optimization result"""
        return self._cache.get(cache_key)
    
    def set(
        self,
        cache_key: str,
        marker_layout: MarkerLayout,
        efficiency_metrics: EfficiencyMetrics
    ):
        """Cache optimization result"""
        if len(self._cache) >= self._max_size:
            # Remove oldest entry (simple LRU)
            oldest_key = next(iter(self._cache))
            del self._cache[oldest_key]
        
        self._cache[cache_key] = (marker_layout, efficiency_metrics)
    
    def clear(self):
        """Clear all cached results"""
        self._cache.clear()
