"""
Pattern validation service
"""

import logging
import re
from typing import List, Dict, Any, Optional

from shapely.geometry import Polygon
from shapely.validation import explain_validity

from app.models.optimization import (
    <PERSON><PERSON>P<PERSON>ce,
    PatternValidationResponse,
    ValidationError,
    MarkerOptimizationRequest
)
from app.core.config import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


class ValidationService:
    """
    Service for validating pattern pieces and optimization requests
    """
    
    def __init__(self):
        self.valid_sizes = ["XXS", "XS", "S", "M", "L", "XL", "XXL", "XXXL"]
        self.min_dimension = 0.1  # inches
        self.max_dimension = 200.0  # inches
    
    async def validate_optimization_request(
        self, 
        request: MarkerOptimizationRequest
    ) -> PatternValidationResponse:
        """
        Validate complete optimization request
        
        Args:
            request: Optimization request to validate
            
        Returns:
            Validation response with errors and warnings
        """
        errors = []
        warnings = []
        
        # Validate fabric dimensions
        fabric_errors = self._validate_fabric_dimensions(
            request.fabric_width, 
            request.fabric_length
        )
        errors.extend(fabric_errors)
        
        # Validate pattern pieces
        pattern_validation = await self.validate_pattern_pieces(
            request.pattern_pieces,
            request.fabric_width
        )
        errors.extend(pattern_validation.errors)
        warnings.extend(pattern_validation.warnings)
        
        # Validate size ratios
        size_errors = self._validate_size_ratios(
            request.size_ratios,
            request.pattern_pieces
        )
        errors.extend(size_errors)
        
        # Validate optimization settings
        settings_errors = self._validate_optimization_settings(
            request.optimization_settings
        )
        errors.extend(settings_errors)
        
        # Calculate totals
        total_pieces = sum(piece.quantity_needed for piece in request.pattern_pieces)
        total_area = sum(
            (piece.geometry.area or piece.geometry.width * piece.geometry.height) * piece.quantity_needed
            for piece in request.pattern_pieces
        )
        
        # Estimate fabric needed
        fabric_area = request.fabric_width * (request.fabric_length or 100)
        estimated_fabric_needed = total_area / 0.85  # Assume 85% efficiency
        
        if estimated_fabric_needed > fabric_area:
            warnings.append(ValidationError(
                piece_id="fabric",
                error_type="capacity_warning",
                message=f"Pattern pieces may not fit in specified fabric dimensions. "
                       f"Estimated area needed: {estimated_fabric_needed:.1f} sq in, "
                       f"Available: {fabric_area:.1f} sq in",
                severity="warning"
            ))
        
        return PatternValidationResponse(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings,
            total_pieces=total_pieces,
            total_area=total_area,
            estimated_fabric_needed=estimated_fabric_needed
        )
    
    async def validate_pattern_pieces(
        self,
        pattern_pieces: List[PatternPiece],
        fabric_width: Optional[float] = None
    ) -> PatternValidationResponse:
        """
        Validate list of pattern pieces
        
        Args:
            pattern_pieces: List of pattern pieces to validate
            fabric_width: Optional fabric width for compatibility checking
            
        Returns:
            Validation response
        """
        errors = []
        warnings = []
        total_area = 0
        
        # Check for duplicate piece IDs
        piece_ids = [piece.piece_id for piece in pattern_pieces]
        duplicate_ids = set([pid for pid in piece_ids if piece_ids.count(pid) > 1])
        
        for duplicate_id in duplicate_ids:
            errors.append(ValidationError(
                piece_id=duplicate_id,
                error_type="duplicate_id",
                message=f"Duplicate piece ID: {duplicate_id}"
            ))
        
        # Validate individual pieces
        for piece in pattern_pieces:
            piece_errors, piece_warnings = await self._validate_single_piece(
                piece, fabric_width
            )
            errors.extend(piece_errors)
            warnings.extend(piece_warnings)
            
            # Calculate area
            piece_area = piece.geometry.area or (piece.geometry.width * piece.geometry.height)
            total_area += piece_area * piece.quantity_needed
        
        return PatternValidationResponse(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings,
            total_pieces=sum(piece.quantity_needed for piece in pattern_pieces),
            total_area=total_area
        )
    
    async def _validate_single_piece(
        self,
        piece: PatternPiece,
        fabric_width: Optional[float] = None
    ) -> tuple[List[ValidationError], List[ValidationError]]:
        """Validate a single pattern piece"""
        errors = []
        warnings = []
        
        # Validate piece ID
        if not piece.piece_id or not piece.piece_id.strip():
            errors.append(ValidationError(
                piece_id=piece.piece_id or "unknown",
                error_type="invalid_id",
                message="Piece ID cannot be empty"
            ))
        
        # Validate name
        if not piece.name or not piece.name.strip():
            warnings.append(ValidationError(
                piece_id=piece.piece_id,
                error_type="missing_name",
                message="Piece name is empty",
                severity="warning"
            ))
        
        # Validate size
        if piece.size.upper() not in self.valid_sizes:
            errors.append(ValidationError(
                piece_id=piece.piece_id,
                error_type="invalid_size",
                message=f"Invalid size '{piece.size}'. Valid sizes: {', '.join(self.valid_sizes)}"
            ))
        
        # Validate quantity
        if piece.quantity_needed <= 0:
            errors.append(ValidationError(
                piece_id=piece.piece_id,
                error_type="invalid_quantity",
                message="Quantity needed must be positive"
            ))
        
        if piece.quantity_needed > 1000:
            warnings.append(ValidationError(
                piece_id=piece.piece_id,
                error_type="high_quantity",
                message=f"High quantity ({piece.quantity_needed}) may impact performance",
                severity="warning"
            ))
        
        # Validate geometry
        geometry_errors = await self._validate_geometry(piece)
        errors.extend(geometry_errors)
        
        # Validate fabric compatibility
        if fabric_width:
            if piece.geometry.width > fabric_width:
                if piece.rotation_allowed and piece.geometry.height <= fabric_width:
                    warnings.append(ValidationError(
                        piece_id=piece.piece_id,
                        error_type="requires_rotation",
                        message=f"Piece width ({piece.geometry.width:.1f}\") exceeds fabric width ({fabric_width:.1f}\") but can fit with rotation",
                        severity="warning"
                    ))
                else:
                    errors.append(ValidationError(
                        piece_id=piece.piece_id,
                        error_type="too_wide",
                        message=f"Piece width ({piece.geometry.width:.1f}\") exceeds fabric width ({fabric_width:.1f}\")"
                    ))
        
        return errors, warnings
    
    async def _validate_geometry(self, piece: PatternPiece) -> List[ValidationError]:
        """Validate pattern piece geometry"""
        errors = []
        geometry = piece.geometry
        
        # Validate dimensions
        if geometry.width <= self.min_dimension:
            errors.append(ValidationError(
                piece_id=piece.piece_id,
                error_type="invalid_width",
                message=f"Width ({geometry.width:.3f}\") is too small. Minimum: {self.min_dimension}\""
            ))
        
        if geometry.height <= self.min_dimension:
            errors.append(ValidationError(
                piece_id=piece.piece_id,
                error_type="invalid_height",
                message=f"Height ({geometry.height:.3f}\") is too small. Minimum: {self.min_dimension}\""
            ))
        
        if geometry.width > self.max_dimension:
            errors.append(ValidationError(
                piece_id=piece.piece_id,
                error_type="invalid_width",
                message=f"Width ({geometry.width:.1f}\") exceeds maximum: {self.max_dimension}\""
            ))
        
        if geometry.height > self.max_dimension:
            errors.append(ValidationError(
                piece_id=piece.piece_id,
                error_type="invalid_height",
                message=f"Height ({geometry.height:.1f}\") exceeds maximum: {self.max_dimension}\""
            ))
        
        # Validate geometry data
        if geometry.coordinates:
            coord_errors = await self.validate_polygon_coordinates(geometry.coordinates)
            if coord_errors.get("errors"):
                for error in coord_errors["errors"]:
                    errors.append(ValidationError(
                        piece_id=piece.piece_id,
                        error_type="invalid_coordinates",
                        message=error
                    ))
        
        if geometry.svg_path:
            svg_errors = await self.validate_svg_path(geometry.svg_path)
            if svg_errors.get("errors"):
                for error in svg_errors["errors"]:
                    errors.append(ValidationError(
                        piece_id=piece.piece_id,
                        error_type="invalid_svg",
                        message=error
                    ))
        
        return errors
    
    async def validate_polygon_coordinates(self, coordinates: List[List[float]]) -> Dict[str, Any]:
        """Validate polygon coordinates"""
        errors = []
        warnings = []
        
        try:
            if len(coordinates) < 3:
                errors.append("Polygon must have at least 3 points")
                return {"valid": False, "errors": errors, "warnings": warnings}
            
            # Check for valid coordinate format
            for i, coord in enumerate(coordinates):
                if not isinstance(coord, (list, tuple)) or len(coord) != 2:
                    errors.append(f"Invalid coordinate format at index {i}")
                    continue
                
                if not all(isinstance(c, (int, float)) for c in coord):
                    errors.append(f"Non-numeric coordinate at index {i}")
            
            if errors:
                return {"valid": False, "errors": errors, "warnings": warnings}
            
            # Create Shapely polygon for validation
            polygon = Polygon(coordinates)
            
            if not polygon.is_valid:
                validity_reason = explain_validity(polygon)
                errors.append(f"Invalid polygon geometry: {validity_reason}")
            
            # Check for self-intersection
            if polygon.is_valid and not polygon.is_simple:
                warnings.append("Polygon has self-intersections")
            
            # Check area
            if polygon.is_valid and polygon.area < 0.01:
                warnings.append("Polygon has very small area")
            
        except Exception as e:
            errors.append(f"Error validating polygon: {str(e)}")
        
        return {
            "valid": len(errors) == 0,
            "errors": errors,
            "warnings": warnings
        }
    
    async def validate_svg_path(self, svg_path: str) -> Dict[str, Any]:
        """Validate SVG path data"""
        errors = []
        warnings = []
        
        try:
            if not svg_path or not svg_path.strip():
                errors.append("SVG path cannot be empty")
                return {"valid": False, "errors": errors, "warnings": warnings}
            
            if len(svg_path) > 10000:
                warnings.append("SVG path is very long and may impact performance")
            
            # Basic SVG path validation
            svg_commands = re.findall(r'[MmLlHhVvCcSsQqTtAaZz]', svg_path)
            if not svg_commands:
                errors.append("No valid SVG commands found in path")
            
            # Check for required move command
            if svg_commands and svg_commands[0].upper() != 'M':
                errors.append("SVG path must start with a move command (M or m)")
            
        except Exception as e:
            errors.append(f"Error validating SVG path: {str(e)}")
        
        return {
            "valid": len(errors) == 0,
            "errors": errors,
            "warnings": warnings
        }
    
    def _validate_fabric_dimensions(
        self, 
        fabric_width: float, 
        fabric_length: Optional[float]
    ) -> List[ValidationError]:
        """Validate fabric dimensions"""
        errors = []
        
        if fabric_width <= 0:
            errors.append(ValidationError(
                piece_id="fabric",
                error_type="invalid_width",
                message="Fabric width must be positive"
            ))
        
        if fabric_width > settings.MAX_FABRIC_WIDTH:
            errors.append(ValidationError(
                piece_id="fabric",
                error_type="width_too_large",
                message=f"Fabric width ({fabric_width:.1f}\") exceeds maximum: {settings.MAX_FABRIC_WIDTH}\""
            ))
        
        if fabric_length is not None:
            if fabric_length <= 0:
                errors.append(ValidationError(
                    piece_id="fabric",
                    error_type="invalid_length",
                    message="Fabric length must be positive"
                ))
            
            if fabric_length > settings.MAX_FABRIC_LENGTH:
                errors.append(ValidationError(
                    piece_id="fabric",
                    error_type="length_too_large",
                    message=f"Fabric length ({fabric_length:.1f}\") exceeds maximum: {settings.MAX_FABRIC_LENGTH}\""
                ))
        
        return errors
    
    def _validate_size_ratios(
        self,
        size_ratios: Dict[str, int],
        pattern_pieces: List[PatternPiece]
    ) -> List[ValidationError]:
        """Validate size ratios"""
        errors = []
        
        if not size_ratios:
            errors.append(ValidationError(
                piece_id="size_ratios",
                error_type="empty_ratios",
                message="Size ratios cannot be empty"
            ))
            return errors
        
        # Check for valid sizes in ratios
        for size in size_ratios.keys():
            if size.upper() not in self.valid_sizes:
                errors.append(ValidationError(
                    piece_id="size_ratios",
                    error_type="invalid_size",
                    message=f"Invalid size in ratios: {size}"
                ))
        
        # Check for positive quantities
        for size, quantity in size_ratios.items():
            if quantity <= 0:
                errors.append(ValidationError(
                    piece_id="size_ratios",
                    error_type="invalid_quantity",
                    message=f"Size ratio quantity must be positive for size {size}"
                ))
        
        return errors
    
    def _validate_optimization_settings(self, settings) -> List[ValidationError]:
        """Validate optimization settings"""
        errors = []
        
        if settings.max_iterations <= 0:
            errors.append(ValidationError(
                piece_id="settings",
                error_type="invalid_iterations",
                message="Max iterations must be positive"
            ))
        
        if settings.minimum_spacing < 0:
            errors.append(ValidationError(
                piece_id="settings",
                error_type="invalid_spacing",
                message="Minimum spacing cannot be negative"
            ))
        
        if settings.seam_allowance < 0:
            errors.append(ValidationError(
                piece_id="settings",
                error_type="invalid_seam_allowance",
                message="Seam allowance cannot be negative"
            ))
        
        return errors
