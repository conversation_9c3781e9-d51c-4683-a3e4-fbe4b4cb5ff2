"""
Test suite for marker optimization functionality
"""

import pytest
import asyncio
from fastapi.testclient import TestClient
from unittest.mock import Mock, patch

from main import app
from app.models.optimization import (
    MarkerOptimizationRequest,
    PatternPiece,
    PatternPieceGeometry,
    OptimizationSettings,
    AlgorithmType
)
from app.services.optimization_service import OptimizationService


class TestOptimizationAPI:
    """Test cases for optimization API endpoints"""
    
    def setup_method(self):
        """Set up test client and mock data"""
        self.client = TestClient(app)
        self.mock_user = {
            "id": 1,
            "name": "Test User",
            "email": "<EMAIL>",
            "company_id": 1
        }
        
        # Mock authentication
        with patch('app.middleware.auth.AuthMiddleware.dispatch') as mock_auth:
            mock_auth.return_value = None
    
    def test_optimize_marker_success(self):
        """Test successful marker optimization"""
        request_data = {
            "fabric_width": 60.0,
            "fabric_length": 120.0,
            "size_ratios": {"S": 50, "M": 100, "L": 75, "XL": 25},
            "pattern_pieces": [
                {
                    "piece_id": "front_panel_001",
                    "name": "Front Panel",
                    "size": "M",
                    "geometry": {
                        "type": "polygon",
                        "coordinates": [[0, 0], [12.5, 0], [12.5, 18], [0, 18], [0, 0]],
                        "width": 12.5,
                        "height": 18.0
                    },
                    "quantity_needed": 1,
                    "rotation_allowed": True
                }
            ],
            "optimization_settings": {
                "algorithm_type": "bottom_left_fill",
                "max_iterations": 1000,
                "rotation_angles": [0, 90, 180, 270],
                "minimum_spacing": 0.125
            }
        }
        
        with patch('app.services.optimization_service.OptimizationService.optimize') as mock_optimize:
            # Mock successful optimization result
            mock_optimize.return_value = (
                {
                    "fabric_dimensions": {"width": 60, "length": 120},
                    "pattern_pieces": [
                        {
                            "piece_id": "front_panel_001",
                            "size": "M",
                            "position": {"x": 0, "y": 0},
                            "rotation": 0,
                            "bounds": {"width": 12.5, "height": 18.0},
                            "area": 225.0
                        }
                    ]
                },
                {
                    "total_fabric_area": 7200,
                    "used_area": 225,
                    "waste_area": 6975,
                    "yield_percentage": 3.125,
                    "waste_percentage": 96.875,
                    "pieces_placed": 1,
                    "pieces_failed": 0
                }
            )
            
            response = self.client.post("/api/v1/optimize-marker", json=request_data)
            
            assert response.status_code == 200
            result = response.json()
            
            assert "optimization_id" in result
            assert result["status"] == "success"
            assert "marker_layout" in result
            assert "efficiency_metrics" in result
            assert result["efficiency_metrics"]["pieces_placed"] == 1
    
    def test_optimize_marker_validation_error(self):
        """Test optimization with invalid data"""
        request_data = {
            "fabric_width": -10,  # Invalid negative width
            "pattern_pieces": []   # Empty pattern pieces
        }
        
        response = self.client.post("/api/v1/optimize-marker", json=request_data)
        
        assert response.status_code == 422
        result = response.json()
        assert "detail" in result
    
    def test_validate_patterns_success(self):
        """Test successful pattern validation"""
        request_data = {
            "pattern_pieces": [
                {
                    "piece_id": "test_piece",
                    "name": "Test Piece",
                    "size": "M",
                    "geometry": {
                        "type": "polygon",
                        "coordinates": [[0, 0], [10, 0], [10, 10], [0, 10], [0, 0]],
                        "width": 10,
                        "height": 10
                    },
                    "quantity_needed": 1
                }
            ],
            "fabric_width": 60
        }
        
        with patch('app.services.validation_service.ValidationService.validate_pattern_pieces') as mock_validate:
            mock_validate.return_value = {
                "is_valid": True,
                "errors": [],
                "warnings": [],
                "total_pieces": 1,
                "total_area": 100
            }
            
            response = self.client.post("/api/v1/validate-patterns", json=request_data)
            
            assert response.status_code == 200
            result = response.json()
            assert result["is_valid"] is True
    
    def test_get_algorithms(self):
        """Test getting available algorithms"""
        with patch('app.services.optimization_service.OptimizationService.get_available_algorithms') as mock_algorithms:
            mock_algorithms.return_value = [
                {
                    "name": "Bottom-Left Fill",
                    "algorithm_type": "bottom_left_fill",
                    "description": "Fast greedy algorithm",
                    "complexity": "low",
                    "typical_time": "1-5 seconds",
                    "best_for": "Quick optimization",
                    "parameters": {}
                }
            ]
            
            response = self.client.get("/api/v1/algorithms")
            
            assert response.status_code == 200
            result = response.json()
            assert len(result) == 1
            assert result[0]["algorithm_type"] == "bottom_left_fill"
    
    def test_health_check(self):
        """Test health check endpoint"""
        response = self.client.get("/api/v1/health")
        
        assert response.status_code == 200
        result = response.json()
        assert result["status"] == "healthy"
        assert "timestamp" in result


class TestOptimizationService:
    """Test cases for optimization service"""
    
    def setup_method(self):
        """Set up test data"""
        self.service = OptimizationService()
        
        self.test_pattern_piece = PatternPiece(
            piece_id="test_001",
            name="Test Piece",
            size="M",
            geometry=PatternPieceGeometry(
                type="polygon",
                coordinates=[[0, 0], [10, 0], [10, 10], [0, 10], [0, 0]],
                width=10,
                height=10
            ),
            quantity_needed=1,
            rotation_allowed=True
        )
    
    @pytest.mark.asyncio
    async def test_optimize_bottom_left_fill(self):
        """Test bottom-left fill optimization"""
        pattern_pieces = [self.test_pattern_piece]
        fabric_width = 60
        fabric_length = 120
        settings = OptimizationSettings(algorithm_type=AlgorithmType.BOTTOM_LEFT_FILL)
        
        marker_layout, efficiency_metrics = await self.service.optimize(
            pattern_pieces, fabric_width, fabric_length, settings
        )
        
        assert marker_layout is not None
        assert efficiency_metrics is not None
        assert efficiency_metrics.pieces_placed >= 0
        assert 0 <= efficiency_metrics.yield_percentage <= 100
    
    def test_validate_inputs_success(self):
        """Test input validation with valid data"""
        pattern_pieces = [self.test_pattern_piece]
        fabric_width = 60
        fabric_length = 120
        settings = OptimizationSettings()
        
        # Should not raise any exception
        self.service._validate_inputs(pattern_pieces, fabric_width, fabric_length, settings)
    
    def test_validate_inputs_empty_pieces(self):
        """Test input validation with empty pattern pieces"""
        pattern_pieces = []
        fabric_width = 60
        fabric_length = 120
        settings = OptimizationSettings()
        
        with pytest.raises(ValueError, match="Pattern pieces list cannot be empty"):
            self.service._validate_inputs(pattern_pieces, fabric_width, fabric_length, settings)
    
    def test_validate_inputs_negative_width(self):
        """Test input validation with negative fabric width"""
        pattern_pieces = [self.test_pattern_piece]
        fabric_width = -10
        fabric_length = 120
        settings = OptimizationSettings()
        
        with pytest.raises(ValueError, match="Fabric width must be positive"):
            self.service._validate_inputs(pattern_pieces, fabric_width, fabric_length, settings)
    
    def test_get_algorithm_info(self):
        """Test getting algorithm information"""
        info = self.service.get_algorithm_info(AlgorithmType.BOTTOM_LEFT_FILL)
        
        assert info["algorithm_type"] == "bottom_left_fill"
        assert "name" in info
        assert "description" in info
        assert "complexity" in info
    
    @pytest.mark.asyncio
    async def test_estimate_optimization_time(self):
        """Test optimization time estimation"""
        pattern_pieces = [self.test_pattern_piece]
        fabric_width = 60
        algorithm_type = AlgorithmType.BOTTOM_LEFT_FILL
        
        estimation = await self.service.estimate_optimization_time(
            pattern_pieces, fabric_width, algorithm_type
        )
        
        assert "estimated_time_seconds" in estimation
        assert "complexity" in estimation
        assert "pieces_count" in estimation
        assert estimation["pieces_count"] == 1


class TestBottomLeftFillAlgorithm:
    """Test cases for Bottom-Left Fill algorithm"""
    
    def setup_method(self):
        """Set up algorithm instance"""
        from app.algorithms.bottom_left_fill import BottomLeftFillAlgorithm
        self.algorithm = BottomLeftFillAlgorithm(fabric_width=60, fabric_length=120)
        
        self.test_piece = PatternPiece(
            piece_id="test_001",
            name="Test Piece",
            size="M",
            geometry=PatternPieceGeometry(
                type="polygon",
                coordinates=[[0, 0], [10, 0], [10, 10], [0, 10], [0, 0]],
                width=10,
                height=10
            ),
            quantity_needed=1,
            rotation_allowed=True
        )
    
    def test_create_polygon_from_piece(self):
        """Test polygon creation from pattern piece"""
        polygon = self.algorithm.create_polygon_from_piece(self.test_piece)
        
        assert polygon is not None
        assert polygon.is_valid
        assert polygon.area == 100  # 10x10 square
    
    def test_can_place_piece_valid_position(self):
        """Test piece placement validation at valid position"""
        polygon = self.algorithm.create_polygon_from_piece(self.test_piece)
        
        can_place = self.algorithm.can_place_piece(polygon, 0, 0, 0, 0.125)
        
        assert can_place is True
    
    def test_can_place_piece_out_of_bounds(self):
        """Test piece placement validation at invalid position"""
        polygon = self.algorithm.create_polygon_from_piece(self.test_piece)
        
        # Try to place piece outside fabric bounds
        can_place = self.algorithm.can_place_piece(polygon, 100, 100, 0, 0.125)
        
        assert can_place is False
    
    def test_optimize_single_piece(self):
        """Test optimization with single pattern piece"""
        pattern_pieces = [self.test_piece]
        settings = OptimizationSettings(algorithm_type=AlgorithmType.BOTTOM_LEFT_FILL)
        
        marker_layout, efficiency_metrics = self.algorithm.optimize(pattern_pieces, settings)
        
        assert marker_layout is not None
        assert len(marker_layout.pattern_pieces) == 1
        assert efficiency_metrics.pieces_placed == 1
        assert efficiency_metrics.pieces_failed == 0
    
    def test_optimize_multiple_pieces(self):
        """Test optimization with multiple pattern pieces"""
        # Create multiple pieces
        pieces = []
        for i in range(5):
            piece = PatternPiece(
                piece_id=f"test_{i:03d}",
                name=f"Test Piece {i}",
                size="M",
                geometry=PatternPieceGeometry(
                    type="polygon",
                    coordinates=[[0, 0], [8, 0], [8, 8], [0, 8], [0, 0]],
                    width=8,
                    height=8
                ),
                quantity_needed=1,
                rotation_allowed=True
            )
            pieces.append(piece)
        
        settings = OptimizationSettings(algorithm_type=AlgorithmType.BOTTOM_LEFT_FILL)
        
        marker_layout, efficiency_metrics = self.algorithm.optimize(pieces, settings)
        
        assert marker_layout is not None
        assert efficiency_metrics.pieces_placed >= 1
        assert efficiency_metrics.yield_percentage > 0


class TestPerformance:
    """Performance test cases"""
    
    @pytest.mark.performance
    def test_optimization_performance_small(self):
        """Test optimization performance with small dataset"""
        from app.algorithms.bottom_left_fill import BottomLeftFillAlgorithm
        import time
        
        algorithm = BottomLeftFillAlgorithm(fabric_width=60, fabric_length=120)
        
        # Create 10 small pieces
        pieces = []
        for i in range(10):
            piece = PatternPiece(
                piece_id=f"perf_test_{i:03d}",
                name=f"Performance Test Piece {i}",
                size="M",
                geometry=PatternPieceGeometry(
                    type="polygon",
                    coordinates=[[0, 0], [5, 0], [5, 5], [0, 5], [0, 0]],
                    width=5,
                    height=5
                ),
                quantity_needed=1,
                rotation_allowed=True
            )
            pieces.append(piece)
        
        settings = OptimizationSettings(algorithm_type=AlgorithmType.BOTTOM_LEFT_FILL)
        
        start_time = time.time()
        marker_layout, efficiency_metrics = algorithm.optimize(pieces, settings)
        execution_time = time.time() - start_time
        
        # Should complete within 5 seconds for small dataset
        assert execution_time < 5.0
        assert efficiency_metrics.pieces_placed > 0
    
    @pytest.mark.performance
    @pytest.mark.slow
    def test_optimization_performance_large(self):
        """Test optimization performance with larger dataset"""
        from app.algorithms.bottom_left_fill import BottomLeftFillAlgorithm
        import time
        
        algorithm = BottomLeftFillAlgorithm(fabric_width=72, fabric_length=200)
        
        # Create 100 pieces of varying sizes
        pieces = []
        sizes = ["S", "M", "L", "XL"]
        dimensions = [(6, 8), (8, 10), (10, 12), (12, 14)]
        
        for i in range(100):
            size_idx = i % len(sizes)
            width, height = dimensions[size_idx]
            
            piece = PatternPiece(
                piece_id=f"large_test_{i:03d}",
                name=f"Large Test Piece {i}",
                size=sizes[size_idx],
                geometry=PatternPieceGeometry(
                    type="polygon",
                    coordinates=[[0, 0], [width, 0], [width, height], [0, height], [0, 0]],
                    width=width,
                    height=height
                ),
                quantity_needed=1,
                rotation_allowed=True
            )
            pieces.append(piece)
        
        settings = OptimizationSettings(algorithm_type=AlgorithmType.BOTTOM_LEFT_FILL)
        
        start_time = time.time()
        marker_layout, efficiency_metrics = algorithm.optimize(pieces, settings)
        execution_time = time.time() - start_time
        
        # Should complete within 30 seconds for larger dataset
        assert execution_time < 30.0
        assert efficiency_metrics.pieces_placed > 50  # Should place at least half


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
