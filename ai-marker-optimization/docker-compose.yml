# AI Marker Optimization Tool - Docker Compose Configuration
version: '3.8'

services:
  # Python FastAPI Backend
  api:
    build:
      context: ./api
      dockerfile: Dockerfile
    container_name: marker-optimization-api
    ports:
      - "8001:8001"
    environment:
      - DEBUG=true
      - HOST=0.0.0.0
      - PORT=8001
      - DATABASE_URL=********************************************/marker_optimization
      - REDIS_URL=redis://redis:6379/0
      - LARAVEL_API_URL=http://host.docker.internal:8000
      - SECRET_KEY=your-secret-key-change-in-production
      - CORS_ORIGINS=http://localhost:3000,http://localhost:8000
    volumes:
      - ./api:/app
      - marker_optimization_logs:/app/logs
      - marker_optimization_uploads:/app/uploads
      - marker_optimization_exports:/app/exports
    depends_on:
      - postgres
      - redis
    networks:
      - marker_optimization_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Vue.js Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: marker-optimization-frontend
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - VITE_API_BASE_URL=http://localhost:8001
      - VITE_LARAVEL_API_URL=http://localhost:8000
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - api
    networks:
      - marker_optimization_network
    restart: unless-stopped

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: marker-optimization-postgres
    environment:
      - POSTGRES_DB=marker_optimization
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - marker_optimization_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: marker-optimization-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - marker_optimization_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: marker-optimization-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - api
      - frontend
    networks:
      - marker_optimization_network
    restart: unless-stopped

  # Monitoring - Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: marker-optimization-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    networks:
      - marker_optimization_network
    restart: unless-stopped

  # Monitoring - Grafana
  grafana:
    image: grafana/grafana:latest
    container_name: marker-optimization-grafana
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - prometheus
    networks:
      - marker_optimization_network
    restart: unless-stopped

  # Log Management - ELK Stack (Optional)
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    container_name: marker-optimization-elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    networks:
      - marker_optimization_network
    restart: unless-stopped
    profiles:
      - logging

  logstash:
    image: docker.elastic.co/logstash/logstash:8.8.0
    container_name: marker-optimization-logstash
    volumes:
      - ./logging/logstash.conf:/usr/share/logstash/pipeline/logstash.conf
    depends_on:
      - elasticsearch
    networks:
      - marker_optimization_network
    restart: unless-stopped
    profiles:
      - logging

  kibana:
    image: docker.elastic.co/kibana/kibana:8.8.0
    container_name: marker-optimization-kibana
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    depends_on:
      - elasticsearch
    networks:
      - marker_optimization_network
    restart: unless-stopped
    profiles:
      - logging

# Named Volumes
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  marker_optimization_logs:
    driver: local
  marker_optimization_uploads:
    driver: local
  marker_optimization_exports:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  elasticsearch_data:
    driver: local

# Networks
networks:
  marker_optimization_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
