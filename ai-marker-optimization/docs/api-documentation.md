# AI Marker Optimization Tool - API Documentation

## Overview

The AI Marker Optimization Tool provides a comprehensive API for optimizing garment pattern layouts on fabric rolls using advanced algorithms. This documentation covers all available endpoints, request/response formats, and integration guidelines.

## Base URL

```
Production: https://api.redrooksapparel.com/marker-optimization
Development: http://localhost:8001
```

## Authentication

All API requests require authentication using Laravel Sanctum tokens:

```http
Authorization: Bearer YOUR_API_TOKEN
```

## Core Endpoints

### 1. Optimize Marker Layout

**POST** `/api/v1/optimize-marker`

Optimizes garment pattern layout using AI algorithms.

#### Request Body

```json
{
  "fabric_width": 60.0,
  "fabric_length": 120.0,
  "size_ratios": {
    "S": 100,
    "M": 200,
    "L": 150,
    "XL": 75
  },
  "pattern_pieces": [
    {
      "piece_id": "front_panel_001",
      "name": "Front Panel",
      "size": "M",
      "geometry": {
        "type": "polygon",
        "coordinates": [[0,0], [12.5,0], [12.5,18], [0,18], [0,0]],
        "width": 12.5,
        "height": 18.0
      },
      "quantity_needed": 1,
      "rotation_allowed": true
    }
  ],
  "optimization_settings": {
    "algorithm_type": "bottom_left_fill",
    "max_iterations": 1000,
    "rotation_angles": [0, 90, 180, 270],
    "minimum_spacing": 0.125,
    "seam_allowance": 0.625
  }
}
```

#### Response

```json
{
  "optimization_id": "550e8400-e29b-41d4-a716-446655440000",
  "status": "success",
  "execution_time_ms": 1250,
  "algorithm_used": "bottom_left_fill",
  "marker_layout": {
    "fabric_dimensions": {"width": 60, "length": 120},
    "pattern_pieces": [
      {
        "piece_id": "front_panel_001",
        "size": "M",
        "position": {"x": 10.5, "y": 15.2},
        "rotation": 0,
        "bounds": {"width": 12.5, "height": 18.0},
        "area": 225.0
      }
    ]
  },
  "efficiency_metrics": {
    "total_fabric_area": 7200,
    "used_area": 6000,
    "waste_area": 1200,
    "yield_percentage": 83.33,
    "waste_percentage": 16.67,
    "pieces_placed": 45,
    "pieces_failed": 0
  },
  "production_data": {
    "estimated_lays_needed": 3,
    "fabric_utilization_per_lay": 83.33,
    "total_fabric_required_yards": 10.0,
    "cutting_time_estimate": 90.0
  }
}
```

### 2. Validate Pattern Pieces

**POST** `/api/v1/validate-patterns`

Validates pattern piece data before optimization.

#### Request Body

```json
{
  "pattern_pieces": [
    {
      "piece_id": "front_panel_001",
      "name": "Front Panel",
      "size": "M",
      "geometry": {
        "type": "polygon",
        "coordinates": [[0,0], [12.5,0], [12.5,18], [0,18]],
        "width": 12.5,
        "height": 18.0
      },
      "quantity_needed": 1
    }
  ],
  "fabric_width": 60.0
}
```

#### Response

```json
{
  "is_valid": true,
  "errors": [],
  "warnings": [
    {
      "piece_id": "front_panel_001",
      "error_type": "geometry_warning",
      "message": "Polygon is not closed, auto-closing",
      "severity": "warning"
    }
  ],
  "total_pieces": 1,
  "total_area": 225.0,
  "estimated_fabric_needed": 264.7
}
```

### 3. Get Available Algorithms

**GET** `/api/v1/algorithms`

Returns list of available optimization algorithms.

#### Response

```json
[
  {
    "name": "Bottom-Left Fill",
    "algorithm_type": "bottom_left_fill",
    "description": "Fast greedy algorithm for quick optimization",
    "complexity": "low",
    "typical_time": "1-5 seconds",
    "best_for": "Real-time optimization and simple layouts",
    "parameters": {
      "sort_strategy": {
        "default": "area_desc",
        "options": ["area_desc", "area_asc", "width_desc", "height_desc"]
      }
    }
  }
]
```

### 4. Health Check

**GET** `/api/v1/health`

Returns service health status.

#### Response

```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T12:00:00Z",
  "service": "AI Marker Optimization Tool",
  "version": "1.0.0"
}
```

## Algorithm Types

### Bottom-Left Fill (BLF)
- **Type**: `bottom_left_fill`
- **Complexity**: Low
- **Speed**: 1-5 seconds
- **Best for**: Real-time optimization, simple rectangular pieces
- **Parameters**:
  - `sort_strategy`: Piece sorting strategy
  - `rotation_step`: Rotation angle step

### Genetic Algorithm
- **Type**: `genetic`
- **Complexity**: High
- **Speed**: 30-300 seconds
- **Best for**: Complex layouts with many constraints
- **Parameters**:
  - `population_size`: Number of individuals per generation
  - `generations`: Number of evolution cycles
  - `mutation_rate`: Probability of mutation
  - `crossover_rate`: Probability of crossover

### No-Fit Polygon (NFP)
- **Type**: `no_fit_polygon`
- **Complexity**: Medium
- **Speed**: 10-60 seconds
- **Best for**: Irregular pattern pieces
- **Parameters**:
  - `precision`: Geometric precision
  - `simplification_tolerance`: Polygon simplification tolerance

## Error Handling

### HTTP Status Codes

- `200 OK`: Request successful
- `400 Bad Request`: Invalid request data
- `401 Unauthorized`: Authentication required
- `422 Unprocessable Entity`: Validation errors
- `429 Too Many Requests`: Rate limit exceeded
- `500 Internal Server Error`: Server error

### Error Response Format

```json
{
  "error": "Validation failed",
  "message": "Pattern pieces validation failed",
  "details": {
    "errors": [
      {
        "piece_id": "front_panel_001",
        "field": "geometry.width",
        "message": "Width must be positive"
      }
    ]
  }
}
```

## Rate Limiting

- **Default**: 100 requests per hour per user
- **Optimization endpoint**: 10 requests per hour per user
- **Validation endpoint**: 50 requests per hour per user

Rate limit headers are included in responses:
- `X-RateLimit-Limit`: Request limit
- `X-RateLimit-Remaining`: Remaining requests
- `X-RateLimit-Reset`: Reset timestamp

## Data Formats

### Pattern Piece Geometry

#### Polygon Format
```json
{
  "type": "polygon",
  "coordinates": [[x1,y1], [x2,y2], [x3,y3], ...],
  "width": 12.5,
  "height": 18.0
}
```

#### SVG Format
```json
{
  "type": "svg",
  "svg_path": "M 0 0 L 12.5 0 L 12.5 18 L 0 18 Z",
  "width": 12.5,
  "height": 18.0
}
```

### Size Specifications

Valid garment sizes: `XXS`, `XS`, `S`, `M`, `L`, `XL`, `XXL`, `XXXL`

### Measurement Units

- All dimensions in inches
- Areas in square inches
- Angles in degrees
- Time in milliseconds

## Integration Examples

### JavaScript/Node.js

```javascript
const axios = require('axios');

const optimizeMarker = async (optimizationData) => {
  try {
    const response = await axios.post(
      'http://localhost:8001/api/v1/optimize-marker',
      optimizationData,
      {
        headers: {
          'Authorization': 'Bearer YOUR_API_TOKEN',
          'Content-Type': 'application/json'
        }
      }
    );
    
    return response.data;
  } catch (error) {
    console.error('Optimization failed:', error.response.data);
    throw error;
  }
};
```

### Python

```python
import requests

def optimize_marker(optimization_data, api_token):
    headers = {
        'Authorization': f'Bearer {api_token}',
        'Content-Type': 'application/json'
    }
    
    response = requests.post(
        'http://localhost:8001/api/v1/optimize-marker',
        json=optimization_data,
        headers=headers
    )
    
    if response.status_code == 200:
        return response.json()
    else:
        raise Exception(f'Optimization failed: {response.text}')
```

### PHP/Laravel

```php
use Illuminate\Support\Facades\Http;

$response = Http::withToken($apiToken)
    ->post('http://localhost:8001/api/v1/optimize-marker', $optimizationData);

if ($response->successful()) {
    $result = $response->json();
    // Process result
} else {
    throw new Exception('Optimization failed: ' . $response->body());
}
```

## Performance Guidelines

### Optimization Tips

1. **Pattern Piece Count**: Keep under 500 pieces for best performance
2. **Algorithm Selection**: Use BLF for quick results, Genetic for best efficiency
3. **Fabric Dimensions**: Larger fabrics increase processing time exponentially
4. **Rotation Angles**: Limit rotation angles to improve speed

### Caching

- Algorithm information cached for 1 hour
- Validation results cached for 15 minutes
- Use `Cache-Control` headers to manage caching

## Support

For technical support or questions:
- Email: <EMAIL>
- Documentation: https://docs.redrooksapparel.com/marker-optimization
- Status Page: https://status.redrooksapparel.com
