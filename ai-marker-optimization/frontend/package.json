{"name": "ai-marker-optimization-frontend", "version": "1.0.0", "description": "Frontend for AI Marker Optimization Tool - Red Rooks Apparel", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/", "type-check": "vue-tsc --noEmit"}, "dependencies": {"vue": "^3.3.8", "vue-router": "^4.2.5", "pinia": "^2.1.7", "fabric": "^5.3.0", "axios": "^1.6.2", "jspdf": "^2.5.1", "html2canvas": "^1.4.1", "file-saver": "^2.0.5", "@vueuse/core": "^10.5.0", "@headlessui/vue": "^1.7.16", "@heroicons/vue": "^2.0.18", "chart.js": "^4.4.0", "vue-chartjs": "^5.2.0", "date-fns": "^2.30.0", "lodash-es": "^4.17.21", "uuid": "^9.0.1"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.0", "@vue/test-utils": "^2.4.2", "@vue/tsconfig": "^0.4.0", "vite": "^5.0.0", "vitest": "^0.34.6", "@vitest/ui": "^0.34.6", "@vitest/coverage-v8": "^0.34.6", "typescript": "~5.2.0", "vue-tsc": "^1.8.22", "eslint": "^8.53.0", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "eslint-plugin-vue": "^9.18.1", "prettier": "^3.1.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "@types/fabric": "^5.3.0", "@types/file-saver": "^2.0.7", "@types/lodash-es": "^4.17.12", "@types/uuid": "^9.0.7", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "tailwindcss": "^3.3.6"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}