<template>
  <div id="app" class="min-h-screen bg-gray-50">
    <!-- Navigation Header -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex items-center">
            <!-- Logo -->
            <div class="flex-shrink-0 flex items-center">
              <div class="w-10 h-10 bg-gradient-to-br from-red-600 to-red-700 rounded-lg flex items-center justify-center">
                <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                </svg>
              </div>
              <div class="ml-3">
                <h1 class="text-xl font-bold text-gray-900">Red Rooks Apparel</h1>
                <p class="text-sm text-gray-500">AI Marker Optimization</p>
              </div>
            </div>
          </div>
          
          <!-- User Menu -->
          <div class="flex items-center space-x-4">
            <span class="text-sm text-gray-700">{{ userStore.user?.name || 'Guest User' }}</span>
            <button
              @click="logout"
              class="text-sm text-red-600 hover:text-red-700 font-medium"
            >
              Logout
            </button>
          </div>
        </div>
      </div>
    </nav>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
      <router-view />
    </main>

    <!-- Loading Overlay -->
    <div
      v-if="isLoading"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
    >
      <div class="bg-white rounded-lg p-6 flex items-center space-x-3">
        <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-red-600"></div>
        <span class="text-gray-700">{{ loadingMessage }}</span>
      </div>
    </div>

    <!-- Error Toast -->
    <div
      v-if="errorMessage"
      class="fixed top-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded z-50"
      role="alert"
    >
      <div class="flex">
        <div class="py-1">
          <svg class="fill-current h-6 w-6 text-red-500 mr-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
            <path d="M2.93 17.07A10 10 0 1 1 17.07 2.93 10 10 0 0 1 2.93 17.07zm12.73-1.41A8 8 0 1 0 4.34 4.34a8 8 0 0 0 11.32 11.32zM9 11V9h2v6H9v-4zm0-6h2v2H9V5z"/>
          </svg>
        </div>
        <div>
          <p class="font-bold">Error</p>
          <p class="text-sm">{{ errorMessage }}</p>
        </div>
        <button
          @click="clearError"
          class="ml-4 text-red-500 hover:text-red-700"
        >
          <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
          </svg>
        </button>
      </div>
    </div>

    <!-- Success Toast -->
    <div
      v-if="successMessage"
      class="fixed top-4 right-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded z-50"
      role="alert"
    >
      <div class="flex">
        <div class="py-1">
          <svg class="fill-current h-6 w-6 text-green-500 mr-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
            <path d="M2.93 17.07A10 10 0 1 1 17.07 2.93 10 10 0 0 1 2.93 17.07zm12.73-1.41A8 8 0 1 0 4.34 4.34a8 8 0 0 0 11.32 11.32zM6.7 9.29L9 11.6l4.3-4.3 1.4 1.42L9 14.4l-3.7-3.7 1.4-1.41z"/>
          </svg>
        </div>
        <div>
          <p class="font-bold">Success</p>
          <p class="text-sm">{{ successMessage }}</p>
        </div>
        <button
          @click="clearSuccess"
          class="ml-4 text-green-500 hover:text-green-700"
        >
          <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
          </svg>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from './stores/user'
import { useNotificationStore } from './stores/notifications'

const router = useRouter()
const userStore = useUserStore()
const notificationStore = useNotificationStore()

// Reactive state
const isLoading = computed(() => notificationStore.isLoading)
const loadingMessage = computed(() => notificationStore.loadingMessage)
const errorMessage = computed(() => notificationStore.errorMessage)
const successMessage = computed(() => notificationStore.successMessage)

// Methods
const logout = async () => {
  try {
    await userStore.logout()
    router.push('/login')
  } catch (error) {
    console.error('Logout failed:', error)
  }
}

const clearError = () => {
  notificationStore.clearError()
}

const clearSuccess = () => {
  notificationStore.clearSuccess()
}

// Auto-clear notifications after 5 seconds
let errorTimeout: NodeJS.Timeout | null = null
let successTimeout: NodeJS.Timeout | null = null

// Watch for error messages
const unwatchError = notificationStore.$subscribe((mutation, state) => {
  if (state.errorMessage && !errorTimeout) {
    errorTimeout = setTimeout(() => {
      notificationStore.clearError()
      errorTimeout = null
    }, 5000)
  } else if (!state.errorMessage && errorTimeout) {
    clearTimeout(errorTimeout)
    errorTimeout = null
  }
})

// Watch for success messages
const unwatchSuccess = notificationStore.$subscribe((mutation, state) => {
  if (state.successMessage && !successTimeout) {
    successTimeout = setTimeout(() => {
      notificationStore.clearSuccess()
      successTimeout = null
    }, 5000)
  } else if (!state.successMessage && successTimeout) {
    clearTimeout(successTimeout)
    successTimeout = null
  }
})

// Initialize app
onMounted(async () => {
  try {
    // Check if user is authenticated
    await userStore.checkAuth()
  } catch (error) {
    console.error('Auth check failed:', error)
    // Redirect to login if not authenticated
    if (router.currentRoute.value.path !== '/login') {
      router.push('/login')
    }
  }
})
</script>

<style>
/* Global styles */
#app {
  font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #dc2626;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #991b1b;
}

/* Fabric.js canvas container */
.canvas-container {
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* Loading animation */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Transitions */
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from, .fade-leave-to {
  opacity: 0;
}

/* Red Rooks Apparel brand colors */
.text-brand-red {
  color: #dc2626;
}

.bg-brand-red {
  background-color: #dc2626;
}

.border-brand-red {
  border-color: #dc2626;
}

.hover\:bg-brand-red-dark:hover {
  background-color: #991b1b;
}
</style>
