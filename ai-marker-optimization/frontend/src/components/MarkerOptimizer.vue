<template>
  <div class="marker-optimizer">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">AI Marker Optimization</h1>
          <p class="text-gray-600 mt-1">Optimize garment pattern layouts for maximum fabric efficiency</p>
        </div>
        <div class="flex items-center space-x-3">
          <button
            @click="showHistory = !showHistory"
            class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
          >
            <svg class="w-4 h-4 mr-2 inline" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
            </svg>
            History
          </button>
          <button
            @click="resetOptimization"
            class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
          >
            <svg class="w-4 h-4 mr-2 inline" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd"></path>
            </svg>
            Reset
          </button>
        </div>
      </div>
    </div>

    <!-- Main Content Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- Left Panel - Controls -->
      <div class="lg:col-span-1 space-y-6">
        <!-- Optimization Controls -->
        <OptimizationControls
          v-model:fabric-width="optimizationParams.fabricWidth"
          v-model:fabric-length="optimizationParams.fabricLength"
          v-model:algorithm="optimizationParams.algorithm"
          v-model:pattern-pieces="optimizationParams.patternPieces"
          v-model:size-ratios="optimizationParams.sizeRatios"
          v-model:settings="optimizationParams.settings"
          :is-optimizing="isOptimizing"
          @optimize="runOptimization"
          @validate="validatePatterns"
        />

        <!-- Efficiency Report -->
        <EfficiencyReport
          v-if="optimizationResult"
          :efficiency-metrics="optimizationResult.efficiency_metrics"
          :production-data="optimizationResult.production_data"
          :execution-time="optimizationResult.execution_time_ms"
        />
      </div>

      <!-- Right Panel - Canvas and Export -->
      <div class="lg:col-span-2 space-y-6">
        <!-- Pattern Canvas -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center justify-between mb-4">
            <h2 class="text-lg font-semibold text-gray-900">Marker Layout</h2>
            <div class="flex items-center space-x-2">
              <!-- Zoom Controls -->
              <button
                @click="zoomOut"
                :disabled="!canvasReady"
                class="p-2 text-gray-600 hover:text-gray-900 disabled:opacity-50"
              >
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd"></path>
                </svg>
              </button>
              <span class="text-sm text-gray-600">{{ Math.round(canvasZoom * 100) }}%</span>
              <button
                @click="zoomIn"
                :disabled="!canvasReady"
                class="p-2 text-gray-600 hover:text-gray-900 disabled:opacity-50"
              >
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd"></path>
                </svg>
              </button>
              <button
                @click="resetZoom"
                :disabled="!canvasReady"
                class="px-3 py-1 text-sm text-gray-600 hover:text-gray-900 disabled:opacity-50"
              >
                Fit
              </button>
            </div>
          </div>

          <!-- Canvas Container -->
          <PatternCanvas
            ref="patternCanvas"
            :fabric-width="optimizationParams.fabricWidth"
            :fabric-length="optimizationParams.fabricLength"
            :marker-layout="optimizationResult?.marker_layout"
            :is-loading="isOptimizing"
            @canvas-ready="onCanvasReady"
            @zoom-changed="onZoomChanged"
            @piece-selected="onPieceSelected"
          />
        </div>

        <!-- Export Tools -->
        <ExportTools
          v-if="optimizationResult"
          :optimization-result="optimizationResult"
          :canvas-ref="patternCanvas"
          @export-pdf="exportToPDF"
          @export-svg="exportToSVG"
          @export-png="exportToPNG"
        />
      </div>
    </div>

    <!-- History Panel -->
    <div
      v-if="showHistory"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
      @click="showHistory = false"
    >
      <div
        class="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[80vh] overflow-hidden"
        @click.stop
      >
        <div class="p-6 border-b border-gray-200">
          <div class="flex items-center justify-between">
            <h2 class="text-xl font-semibold text-gray-900">Optimization History</h2>
            <button
              @click="showHistory = false"
              class="text-gray-400 hover:text-gray-600"
            >
              <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
              </svg>
            </button>
          </div>
        </div>
        <div class="p-6 overflow-y-auto max-h-[60vh]">
          <!-- History content would go here -->
          <p class="text-gray-500 text-center py-8">No optimization history available</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import OptimizationControls from './OptimizationControls.vue'
import PatternCanvas from './PatternCanvas.vue'
import EfficiencyReport from './EfficiencyReport.vue'
import ExportTools from './ExportTools.vue'
import { useOptimizationStore } from '../stores/optimization'
import { useNotificationStore } from '../stores/notifications'
import type { OptimizationRequest, OptimizationResult } from '../types/optimization'

const router = useRouter()
const optimizationStore = useOptimizationStore()
const notificationStore = useNotificationStore()

// Component refs
const patternCanvas = ref<InstanceType<typeof PatternCanvas> | null>(null)

// Reactive state
const isOptimizing = ref(false)
const canvasReady = ref(false)
const canvasZoom = ref(1)
const showHistory = ref(false)

// Optimization parameters
const optimizationParams = reactive<OptimizationRequest>({
  fabricWidth: 60,
  fabricLength: null,
  algorithm: 'bottom_left_fill',
  patternPieces: [],
  sizeRatios: { S: 50, M: 100, L: 75, XL: 25 },
  settings: {
    maxIterations: 1000,
    rotationAngles: [0, 90, 180, 270],
    allowRotation: true,
    minimumSpacing: 0.125,
    seamAllowance: 0.625
  }
})

// Computed properties
const optimizationResult = computed(() => optimizationStore.currentResult)

// Methods
const runOptimization = async () => {
  if (optimizationParams.patternPieces.length === 0) {
    notificationStore.showError('Please add pattern pieces before optimizing')
    return
  }

  try {
    isOptimizing.value = true
    notificationStore.showLoading('Optimizing marker layout...')

    const result = await optimizationStore.optimize(optimizationParams)
    
    notificationStore.showSuccess(
      `Optimization completed! Efficiency: ${result.efficiency_metrics.yield_percentage.toFixed(1)}%`
    )
  } catch (error) {
    console.error('Optimization failed:', error)
    notificationStore.showError('Optimization failed. Please try again.')
  } finally {
    isOptimizing.value = false
    notificationStore.hideLoading()
  }
}

const validatePatterns = async () => {
  try {
    notificationStore.showLoading('Validating patterns...')
    
    const validation = await optimizationStore.validatePatterns(
      optimizationParams.patternPieces,
      optimizationParams.fabricWidth
    )
    
    if (validation.is_valid) {
      notificationStore.showSuccess('All patterns are valid')
    } else {
      const errorCount = validation.errors.length
      const warningCount = validation.warnings.length
      notificationStore.showError(
        `Validation failed: ${errorCount} errors, ${warningCount} warnings`
      )
    }
  } catch (error) {
    console.error('Validation failed:', error)
    notificationStore.showError('Pattern validation failed')
  } finally {
    notificationStore.hideLoading()
  }
}

const resetOptimization = () => {
  optimizationStore.clearResult()
  optimizationParams.patternPieces = []
  if (patternCanvas.value) {
    patternCanvas.value.clearCanvas()
  }
  notificationStore.showSuccess('Optimization reset')
}

// Canvas methods
const onCanvasReady = () => {
  canvasReady.value = true
}

const onZoomChanged = (zoom: number) => {
  canvasZoom.value = zoom
}

const onPieceSelected = (pieceId: string) => {
  console.log('Piece selected:', pieceId)
  // Handle piece selection
}

const zoomIn = () => {
  if (patternCanvas.value) {
    patternCanvas.value.zoomIn()
  }
}

const zoomOut = () => {
  if (patternCanvas.value) {
    patternCanvas.value.zoomOut()
  }
}

const resetZoom = () => {
  if (patternCanvas.value) {
    patternCanvas.value.fitToScreen()
  }
}

// Export methods
const exportToPDF = async () => {
  try {
    notificationStore.showLoading('Generating PDF...')
    // Implementation would go here
    notificationStore.showSuccess('PDF exported successfully')
  } catch (error) {
    notificationStore.showError('PDF export failed')
  } finally {
    notificationStore.hideLoading()
  }
}

const exportToSVG = async () => {
  try {
    notificationStore.showLoading('Generating SVG...')
    // Implementation would go here
    notificationStore.showSuccess('SVG exported successfully')
  } catch (error) {
    notificationStore.showError('SVG export failed')
  } finally {
    notificationStore.hideLoading()
  }
}

const exportToPNG = async () => {
  try {
    notificationStore.showLoading('Generating PNG...')
    // Implementation would go here
    notificationStore.showSuccess('PNG exported successfully')
  } catch (error) {
    notificationStore.showError('PNG export failed')
  } finally {
    notificationStore.hideLoading()
  }
}

// Lifecycle
onMounted(() => {
  // Load any saved optimization parameters
  // Initialize canvas
})
</script>

<style scoped>
.marker-optimizer {
  @apply min-h-screen;
}

/* Custom scrollbar for history panel */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #dc2626;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #991b1b;
}
</style>
