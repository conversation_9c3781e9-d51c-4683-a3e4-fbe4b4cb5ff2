/**
 * AI Marker Optimization Tool - Frontend Entry Point
 * Red Rooks Apparel
 */

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import router from './router'
import App from './App.vue'

// Styles
import './assets/css/main.css'

// Create Vue app
const app = createApp(App)

// Install plugins
app.use(createPinia())
app.use(router)

// Global error handler
app.config.errorHandler = (err, vm, info) => {
  console.error('Global error:', err, info)
  // In production, send to error tracking service
}

// Mount app
app.mount('#app')
