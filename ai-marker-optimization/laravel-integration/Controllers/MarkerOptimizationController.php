<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use App\Models\MarkerOptimization;
use App\Services\MarkerOptimizationService;
use App\Http\Requests\OptimizeMarkerRequest;

/**
 * Marker Optimization Controller
 * 
 * Handles marker optimization requests and integrates with the Python FastAPI backend
 * for Red Rooks Apparel's AI Marker Optimization Tool
 */
class MarkerOptimizationController extends Controller
{
    protected MarkerOptimizationService $optimizationService;

    public function __construct(MarkerOptimizationService $optimizationService)
    {
        $this->optimizationService = $optimizationService;
    }

    /**
     * Display the marker optimization interface
     */
    public function index()
    {
        return view('marker-optimization.index', [
            'title' => 'AI Marker Optimization',
            'breadcrumbs' => [
                ['name' => 'Dashboard', 'url' => route('dashboard')],
                ['name' => 'Marker Optimization', 'url' => null]
            ]
        ]);
    }

    /**
     * Optimize marker layout
     */
    public function optimize(OptimizeMarkerRequest $request): JsonResponse
    {
        try {
            $user = auth()->user();
            $company = $user->company;

            // Prepare optimization request
            $optimizationData = [
                'fabric_width' => $request->fabric_width,
                'fabric_length' => $request->fabric_length,
                'size_ratios' => $request->size_ratios,
                'pattern_pieces' => $request->pattern_pieces,
                'optimization_settings' => $request->optimization_settings ?? [
                    'algorithm_type' => 'bottom_left_fill',
                    'max_iterations' => 1000,
                    'rotation_angles' => [0, 90, 180, 270],
                    'allow_rotation' => true,
                    'minimum_spacing' => 0.125,
                    'seam_allowance' => 0.625
                ],
                'company_id' => $company->id,
                'user_id' => $user->id,
                'order_id' => $request->order_id
            ];

            // Call optimization service
            $result = $this->optimizationService->optimize($optimizationData);

            // Save optimization record
            $optimization = MarkerOptimization::create([
                'company_id' => $company->id,
                'user_id' => $user->id,
                'order_id' => $request->order_id,
                'optimization_id' => $result['optimization_id'],
                'algorithm_used' => $result['algorithm_used'],
                'fabric_dimensions' => $result['marker_layout']['fabric_dimensions'],
                'efficiency_metrics' => $result['efficiency_metrics'],
                'execution_time_ms' => $result['execution_time_ms'],
                'optimization_data' => $result,
                'status' => 'completed'
            ]);

            Log::info('Marker optimization completed', [
                'optimization_id' => $result['optimization_id'],
                'user_id' => $user->id,
                'company_id' => $company->id,
                'yield_percentage' => $result['efficiency_metrics']['yield_percentage']
            ]);

            return response()->json([
                'success' => true,
                'data' => $result,
                'optimization_record_id' => $optimization->id
            ]);

        } catch (\Exception $e) {
            Log::error('Marker optimization failed', [
                'error' => $e->getMessage(),
                'user_id' => auth()->id(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Optimization failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Validate pattern pieces
     */
    public function validatePatterns(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'pattern_pieces' => 'required|array|min:1',
                'pattern_pieces.*.piece_id' => 'required|string',
                'pattern_pieces.*.name' => 'required|string',
                'pattern_pieces.*.size' => 'required|string',
                'pattern_pieces.*.geometry' => 'required|array',
                'pattern_pieces.*.quantity_needed' => 'required|integer|min:1',
                'fabric_width' => 'nullable|numeric|min:1'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ], 422);
            }

            $validationResult = $this->optimizationService->validatePatterns(
                $request->pattern_pieces,
                $request->fabric_width
            );

            return response()->json([
                'success' => true,
                'data' => $validationResult
            ]);

        } catch (\Exception $e) {
            Log::error('Pattern validation failed', [
                'error' => $e->getMessage(),
                'user_id' => auth()->id()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Validation failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get optimization history
     */
    public function getHistory(Request $request): JsonResponse
    {
        try {
            $user = auth()->user();
            $company = $user->company;

            $query = MarkerOptimization::where('company_id', $company->id)
                ->with(['user', 'order'])
                ->orderBy('created_at', 'desc');

            // Apply filters
            if ($request->has('order_id')) {
                $query->where('order_id', $request->order_id);
            }

            if ($request->has('algorithm')) {
                $query->where('algorithm_used', $request->algorithm);
            }

            if ($request->has('date_from')) {
                $query->whereDate('created_at', '>=', $request->date_from);
            }

            if ($request->has('date_to')) {
                $query->whereDate('created_at', '<=', $request->date_to);
            }

            // Pagination
            $perPage = min($request->get('per_page', 20), 100);
            $optimizations = $query->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $optimizations
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to retrieve optimization history', [
                'error' => $e->getMessage(),
                'user_id' => auth()->id()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve history'
            ], 500);
        }
    }

    /**
     * Get specific optimization result
     */
    public function getOptimization(string $optimizationId): JsonResponse
    {
        try {
            $user = auth()->user();
            $company = $user->company;

            $optimization = MarkerOptimization::where('company_id', $company->id)
                ->where('optimization_id', $optimizationId)
                ->with(['user', 'order'])
                ->firstOrFail();

            return response()->json([
                'success' => true,
                'data' => $optimization
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Optimization not found'
            ], 404);
        }
    }

    /**
     * Export marker layout
     */
    public function exportMarker(Request $request, string $optimizationId): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'format' => 'required|in:pdf,svg,png,dxf',
                'options' => 'nullable|array'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ], 422);
            }

            $user = auth()->user();
            $company = $user->company;

            $optimization = MarkerOptimization::where('company_id', $company->id)
                ->where('optimization_id', $optimizationId)
                ->firstOrFail();

            $exportResult = $this->optimizationService->exportMarker(
                $optimization->optimization_data,
                $request->format,
                $request->options ?? []
            );

            Log::info('Marker exported', [
                'optimization_id' => $optimizationId,
                'format' => $request->format,
                'user_id' => $user->id
            ]);

            return response()->json([
                'success' => true,
                'data' => $exportResult
            ]);

        } catch (\Exception $e) {
            Log::error('Marker export failed', [
                'error' => $e->getMessage(),
                'optimization_id' => $optimizationId,
                'user_id' => auth()->id()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Export failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get available algorithms
     */
    public function getAlgorithms(): JsonResponse
    {
        try {
            $algorithms = $this->optimizationService->getAvailableAlgorithms();

            return response()->json([
                'success' => true,
                'data' => $algorithms
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to retrieve algorithms', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve algorithms'
            ], 500);
        }
    }

    /**
     * Get optimization statistics for dashboard
     */
    public function getStatistics(Request $request): JsonResponse
    {
        try {
            $user = auth()->user();
            $company = $user->company;

            $dateFrom = $request->get('date_from', now()->subDays(30)->toDateString());
            $dateTo = $request->get('date_to', now()->toDateString());

            $stats = MarkerOptimization::where('company_id', $company->id)
                ->whereDate('created_at', '>=', $dateFrom)
                ->whereDate('created_at', '<=', $dateTo)
                ->selectRaw('
                    COUNT(*) as total_optimizations,
                    AVG(JSON_EXTRACT(efficiency_metrics, "$.yield_percentage")) as avg_efficiency,
                    SUM(JSON_EXTRACT(efficiency_metrics, "$.pieces_placed")) as total_pieces_placed,
                    AVG(execution_time_ms) as avg_execution_time
                ')
                ->first();

            // Algorithm usage statistics
            $algorithmStats = MarkerOptimization::where('company_id', $company->id)
                ->whereDate('created_at', '>=', $dateFrom)
                ->whereDate('created_at', '<=', $dateTo)
                ->selectRaw('algorithm_used, COUNT(*) as usage_count')
                ->groupBy('algorithm_used')
                ->get();

            // Daily optimization counts
            $dailyStats = MarkerOptimization::where('company_id', $company->id)
                ->whereDate('created_at', '>=', $dateFrom)
                ->whereDate('created_at', '<=', $dateTo)
                ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
                ->groupBy('date')
                ->orderBy('date')
                ->get();

            return response()->json([
                'success' => true,
                'data' => [
                    'summary' => $stats,
                    'algorithm_usage' => $algorithmStats,
                    'daily_optimizations' => $dailyStats,
                    'period' => [
                        'from' => $dateFrom,
                        'to' => $dateTo
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to retrieve optimization statistics', [
                'error' => $e->getMessage(),
                'user_id' => auth()->id()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve statistics'
            ], 500);
        }
    }
}
