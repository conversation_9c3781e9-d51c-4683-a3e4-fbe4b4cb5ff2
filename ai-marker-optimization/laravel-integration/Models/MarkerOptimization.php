<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Marker Optimization Model
 * 
 * Stores marker optimization results and metadata for Red Rooks Apparel
 * 
 * @property int $id
 * @property int $company_id
 * @property int $user_id
 * @property int|null $order_id
 * @property string $optimization_id
 * @property string $algorithm_used
 * @property array $fabric_dimensions
 * @property array $efficiency_metrics
 * @property int $execution_time_ms
 * @property array $optimization_data
 * @property string $status
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property \Carbon\Carbon|null $deleted_at
 */
class MarkerOptimization extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'company_id',
        'user_id',
        'order_id',
        'optimization_id',
        'algorithm_used',
        'fabric_dimensions',
        'efficiency_metrics',
        'execution_time_ms',
        'optimization_data',
        'status'
    ];

    protected $casts = [
        'fabric_dimensions' => 'array',
        'efficiency_metrics' => 'array',
        'optimization_data' => 'array',
        'execution_time_ms' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime'
    ];

    protected $hidden = [
        'deleted_at'
    ];

    /**
     * Get the company that owns the optimization
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    /**
     * Get the user who created the optimization
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the associated order (if any)
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * Scope for filtering by company
     */
    public function scopeForCompany($query, $companyId)
    {
        return $query->where('company_id', $companyId);
    }

    /**
     * Scope for filtering by algorithm
     */
    public function scopeByAlgorithm($query, $algorithm)
    {
        return $query->where('algorithm_used', $algorithm);
    }

    /**
     * Scope for filtering by date range
     */
    public function scopeInDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * Scope for successful optimizations only
     */
    public function scopeSuccessful($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Get formatted execution time
     */
    public function getFormattedExecutionTimeAttribute(): string
    {
        $seconds = $this->execution_time_ms / 1000;
        
        if ($seconds < 1) {
            return $this->execution_time_ms . 'ms';
        } elseif ($seconds < 60) {
            return number_format($seconds, 1) . 's';
        } else {
            $minutes = floor($seconds / 60);
            $remainingSeconds = $seconds % 60;
            return $minutes . 'm ' . number_format($remainingSeconds, 0) . 's';
        }
    }

    /**
     * Get yield percentage with formatting
     */
    public function getFormattedYieldPercentageAttribute(): string
    {
        $yield = $this->efficiency_metrics['yield_percentage'] ?? 0;
        return number_format($yield, 1) . '%';
    }

    /**
     * Get waste percentage with formatting
     */
    public function getFormattedWastePercentageAttribute(): string
    {
        $waste = $this->efficiency_metrics['waste_percentage'] ?? 0;
        return number_format($waste, 1) . '%';
    }

    /**
     * Get fabric utilization summary
     */
    public function getFabricUtilizationSummaryAttribute(): array
    {
        $metrics = $this->efficiency_metrics;
        
        return [
            'total_area' => $metrics['total_fabric_area'] ?? 0,
            'used_area' => $metrics['used_area'] ?? 0,
            'waste_area' => $metrics['waste_area'] ?? 0,
            'yield_percentage' => $metrics['yield_percentage'] ?? 0,
            'pieces_placed' => $metrics['pieces_placed'] ?? 0,
            'pieces_failed' => $metrics['pieces_failed'] ?? 0
        ];
    }

    /**
     * Get pattern pieces count
     */
    public function getPatternPiecesCountAttribute(): int
    {
        $markerLayout = $this->optimization_data['marker_layout'] ?? [];
        return count($markerLayout['pattern_pieces'] ?? []);
    }

    /**
     * Get fabric dimensions summary
     */
    public function getFabricDimensionsSummaryAttribute(): array
    {
        return [
            'width' => $this->fabric_dimensions['width'] ?? 0,
            'length' => $this->fabric_dimensions['length'] ?? 0,
            'area' => ($this->fabric_dimensions['width'] ?? 0) * ($this->fabric_dimensions['length'] ?? 0)
        ];
    }

    /**
     * Check if optimization was successful
     */
    public function isSuccessful(): bool
    {
        return $this->status === 'completed' && 
               isset($this->efficiency_metrics['pieces_placed']) &&
               $this->efficiency_metrics['pieces_placed'] > 0;
    }

    /**
     * Get efficiency rating based on yield percentage
     */
    public function getEfficiencyRating(): string
    {
        $yield = $this->efficiency_metrics['yield_percentage'] ?? 0;
        
        if ($yield >= 90) {
            return 'excellent';
        } elseif ($yield >= 80) {
            return 'good';
        } elseif ($yield >= 70) {
            return 'fair';
        } else {
            return 'poor';
        }
    }

    /**
     * Get efficiency rating color for UI
     */
    public function getEfficiencyRatingColor(): string
    {
        $rating = $this->getEfficiencyRating();
        
        return match($rating) {
            'excellent' => 'green',
            'good' => 'blue',
            'fair' => 'yellow',
            'poor' => 'red',
            default => 'gray'
        };
    }

    /**
     * Get cost savings estimate (if available)
     */
    public function getCostSavingsEstimate(): ?float
    {
        $productionData = $this->optimization_data['production_data'] ?? [];
        return $productionData['material_cost_estimate'] ?? null;
    }

    /**
     * Get fabric requirement in yards
     */
    public function getFabricRequirementYards(): float
    {
        $productionData = $this->optimization_data['production_data'] ?? [];
        return $productionData['total_fabric_required_yards'] ?? 0;
    }

    /**
     * Export optimization data for API
     */
    public function toApiArray(): array
    {
        return [
            'id' => $this->id,
            'optimization_id' => $this->optimization_id,
            'algorithm_used' => $this->algorithm_used,
            'fabric_dimensions' => $this->fabric_dimensions,
            'efficiency_metrics' => $this->efficiency_metrics,
            'execution_time_ms' => $this->execution_time_ms,
            'execution_time_formatted' => $this->formatted_execution_time,
            'yield_percentage_formatted' => $this->formatted_yield_percentage,
            'efficiency_rating' => $this->getEfficiencyRating(),
            'pattern_pieces_count' => $this->pattern_pieces_count,
            'fabric_requirement_yards' => $this->getFabricRequirementYards(),
            'cost_savings_estimate' => $this->getCostSavingsEstimate(),
            'status' => $this->status,
            'created_at' => $this->created_at->toISOString(),
            'user' => [
                'id' => $this->user->id,
                'name' => $this->user->name
            ],
            'order' => $this->order ? [
                'id' => $this->order->id,
                'order_number' => $this->order->order_number
            ] : null
        ];
    }
}
