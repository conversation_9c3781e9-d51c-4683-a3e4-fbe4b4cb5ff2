<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Http\Client\RequestException;

/**
 * Marker Optimization Service
 * 
 * Service class for communicating with the Python FastAPI backend
 * for marker optimization operations
 */
class MarkerOptimizationService
{
    protected string $apiBaseUrl;
    protected string $apiToken;
    protected int $timeout;

    public function __construct()
    {
        $this->apiBaseUrl = config('services.marker_optimization.api_url');
        $this->apiToken = config('services.marker_optimization.api_token');
        $this->timeout = config('services.marker_optimization.timeout', 300);
    }

    /**
     * Optimize marker layout
     */
    public function optimize(array $optimizationData): array
    {
        try {
            Log::info('Starting marker optimization', [
                'fabric_width' => $optimizationData['fabric_width'],
                'pieces_count' => count($optimizationData['pattern_pieces']),
                'algorithm' => $optimizationData['optimization_settings']['algorithm_type'] ?? 'bottom_left_fill'
            ]);

            $response = Http::withToken($this->apiToken)
                ->timeout($this->timeout)
                ->post($this->apiBaseUrl . '/api/v1/optimize-marker', $optimizationData);

            if ($response->successful()) {
                $result = $response->json();
                
                Log::info('Marker optimization completed', [
                    'optimization_id' => $result['optimization_id'],
                    'execution_time_ms' => $result['execution_time_ms'],
                    'yield_percentage' => $result['efficiency_metrics']['yield_percentage']
                ]);

                return $result;
            } else {
                $error = $response->json()['detail'] ?? 'Unknown error';
                throw new \Exception("Optimization API error: {$error}");
            }

        } catch (RequestException $e) {
            Log::error('Marker optimization API request failed', [
                'error' => $e->getMessage(),
                'status_code' => $e->response?->status()
            ]);
            
            throw new \Exception('Optimization service unavailable');
        }
    }

    /**
     * Validate pattern pieces
     */
    public function validatePatterns(array $patternPieces, ?float $fabricWidth = null): array
    {
        try {
            $requestData = [
                'pattern_pieces' => $patternPieces
            ];

            if ($fabricWidth) {
                $requestData['fabric_width'] = $fabricWidth;
            }

            $response = Http::withToken($this->apiToken)
                ->timeout(30)
                ->post($this->apiBaseUrl . '/api/v1/validate-patterns', $requestData);

            if ($response->successful()) {
                return $response->json();
            } else {
                $error = $response->json()['detail'] ?? 'Validation failed';
                throw new \Exception("Validation API error: {$error}");
            }

        } catch (RequestException $e) {
            Log::error('Pattern validation API request failed', [
                'error' => $e->getMessage(),
                'status_code' => $e->response?->status()
            ]);
            
            throw new \Exception('Validation service unavailable');
        }
    }

    /**
     * Get available algorithms
     */
    public function getAvailableAlgorithms(): array
    {
        $cacheKey = 'marker_optimization_algorithms';
        
        return Cache::remember($cacheKey, 3600, function () {
            try {
                $response = Http::withToken($this->apiToken)
                    ->timeout(10)
                    ->get($this->apiBaseUrl . '/api/v1/algorithms');

                if ($response->successful()) {
                    return $response->json();
                } else {
                    Log::warning('Failed to fetch algorithms from API');
                    return $this->getDefaultAlgorithms();
                }

            } catch (RequestException $e) {
                Log::error('Algorithms API request failed', [
                    'error' => $e->getMessage()
                ]);
                
                return $this->getDefaultAlgorithms();
            }
        });
    }

    /**
     * Export marker layout
     */
    public function exportMarker(array $optimizationData, string $format, array $options = []): array
    {
        try {
            $requestData = [
                'optimization_data' => $optimizationData,
                'format' => $format,
                'options' => $options
            ];

            $response = Http::withToken($this->apiToken)
                ->timeout(60)
                ->post($this->apiBaseUrl . '/api/v1/export-marker', $requestData);

            if ($response->successful()) {
                return $response->json();
            } else {
                $error = $response->json()['detail'] ?? 'Export failed';
                throw new \Exception("Export API error: {$error}");
            }

        } catch (RequestException $e) {
            Log::error('Marker export API request failed', [
                'error' => $e->getMessage(),
                'format' => $format
            ]);
            
            throw new \Exception('Export service unavailable');
        }
    }

    /**
     * Get algorithm information
     */
    public function getAlgorithmInfo(string $algorithmType): array
    {
        $cacheKey = "marker_optimization_algorithm_{$algorithmType}";
        
        return Cache::remember($cacheKey, 3600, function () use ($algorithmType) {
            try {
                $response = Http::withToken($this->apiToken)
                    ->timeout(10)
                    ->get($this->apiBaseUrl . "/api/v1/algorithms/{$algorithmType}");

                if ($response->successful()) {
                    return $response->json();
                } else {
                    throw new \Exception('Algorithm not found');
                }

            } catch (RequestException $e) {
                Log::error('Algorithm info API request failed', [
                    'error' => $e->getMessage(),
                    'algorithm' => $algorithmType
                ]);
                
                throw new \Exception('Algorithm information unavailable');
            }
        });
    }

    /**
     * Estimate optimization time
     */
    public function estimateOptimizationTime(array $patternPieces, float $fabricWidth, string $algorithmType): array
    {
        try {
            $requestData = [
                'pattern_pieces' => $patternPieces,
                'fabric_width' => $fabricWidth
            ];

            $response = Http::withToken($this->apiToken)
                ->timeout(10)
                ->post($this->apiBaseUrl . "/api/v1/algorithms/{$algorithmType}/estimate", $requestData);

            if ($response->successful()) {
                return $response->json();
            } else {
                $error = $response->json()['detail'] ?? 'Estimation failed';
                throw new \Exception("Estimation API error: {$error}");
            }

        } catch (RequestException $e) {
            Log::error('Time estimation API request failed', [
                'error' => $e->getMessage(),
                'algorithm' => $algorithmType
            ]);
            
            // Return default estimation
            return [
                'estimated_time_seconds' => 30,
                'complexity' => 'unknown',
                'pieces_count' => count($patternPieces),
                'recommended' => true
            ];
        }
    }

    /**
     * Check API health
     */
    public function checkApiHealth(): array
    {
        try {
            $response = Http::timeout(5)
                ->get($this->apiBaseUrl . '/api/v1/health');

            if ($response->successful()) {
                return [
                    'status' => 'healthy',
                    'response_time_ms' => $response->transferStats?->getTransferTime() * 1000,
                    'data' => $response->json()
                ];
            } else {
                return [
                    'status' => 'unhealthy',
                    'error' => 'API returned error status'
                ];
            }

        } catch (RequestException $e) {
            return [
                'status' => 'unreachable',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get default algorithms when API is unavailable
     */
    protected function getDefaultAlgorithms(): array
    {
        return [
            [
                'name' => 'Bottom-Left Fill',
                'algorithm_type' => 'bottom_left_fill',
                'description' => 'Fast greedy algorithm for quick optimization',
                'complexity' => 'low',
                'typical_time' => '1-5 seconds',
                'best_for' => 'Real-time optimization and simple layouts',
                'parameters' => [
                    'sort_strategy' => [
                        'default' => 'area_desc',
                        'options' => ['area_desc', 'area_asc', 'width_desc', 'height_desc']
                    ]
                ]
            ],
            [
                'name' => 'Genetic Algorithm',
                'algorithm_type' => 'genetic',
                'description' => 'Advanced optimization using evolutionary algorithms',
                'complexity' => 'high',
                'typical_time' => '30-300 seconds',
                'best_for' => 'Complex layouts with many constraints',
                'parameters' => [
                    'population_size' => ['default' => 50, 'min' => 10, 'max' => 200],
                    'generations' => ['default' => 100, 'min' => 10, 'max' => 1000]
                ]
            ]
        ];
    }

    /**
     * Format optimization result for frontend
     */
    public function formatOptimizationResult(array $result): array
    {
        return [
            'optimization_id' => $result['optimization_id'],
            'status' => $result['status'],
            'algorithm_used' => $result['algorithm_used'],
            'execution_time' => [
                'ms' => $result['execution_time_ms'],
                'formatted' => $this->formatExecutionTime($result['execution_time_ms'])
            ],
            'efficiency' => [
                'yield_percentage' => round($result['efficiency_metrics']['yield_percentage'], 1),
                'waste_percentage' => round($result['efficiency_metrics']['waste_percentage'], 1),
                'pieces_placed' => $result['efficiency_metrics']['pieces_placed'],
                'pieces_failed' => $result['efficiency_metrics']['pieces_failed'],
                'rating' => $this->getEfficiencyRating($result['efficiency_metrics']['yield_percentage'])
            ],
            'fabric' => [
                'dimensions' => $result['marker_layout']['fabric_dimensions'],
                'utilization' => $result['efficiency_metrics']['used_area'],
                'waste_area' => $result['efficiency_metrics']['waste_area']
            ],
            'production' => $result['production_data'] ?? [],
            'marker_layout' => $result['marker_layout']
        ];
    }

    /**
     * Format execution time for display
     */
    protected function formatExecutionTime(int $milliseconds): string
    {
        $seconds = $milliseconds / 1000;
        
        if ($seconds < 1) {
            return $milliseconds . 'ms';
        } elseif ($seconds < 60) {
            return number_format($seconds, 1) . 's';
        } else {
            $minutes = floor($seconds / 60);
            $remainingSeconds = $seconds % 60;
            return $minutes . 'm ' . number_format($remainingSeconds, 0) . 's';
        }
    }

    /**
     * Get efficiency rating based on yield percentage
     */
    protected function getEfficiencyRating(float $yieldPercentage): string
    {
        if ($yieldPercentage >= 90) {
            return 'excellent';
        } elseif ($yieldPercentage >= 80) {
            return 'good';
        } elseif ($yieldPercentage >= 70) {
            return 'fair';
        } else {
            return 'poor';
        }
    }
}
