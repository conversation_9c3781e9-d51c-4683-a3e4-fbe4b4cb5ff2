@extends('layouts.app')

@section('title', 'AI Marker Optimization')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-1 text-gray-800">
                        <i class="fas fa-tshirt text-red-600 me-2"></i>
                        AI Marker Optimization
                    </h1>
                    <p class="text-muted mb-0">Optimize garment pattern layouts for maximum fabric efficiency</p>
                </div>
                <div class="d-flex gap-2">
                    <button type="button" class="btn btn-outline-secondary" id="viewHistoryBtn">
                        <i class="fas fa-history me-1"></i>
                        History
                    </button>
                    <button type="button" class="btn btn-outline-secondary" id="helpBtn">
                        <i class="fas fa-question-circle me-1"></i>
                        Help
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Alert Container -->
    <div id="alertContainer"></div>

    <!-- Main Content -->
    <div class="row">
        <!-- Left Panel - Controls -->
        <div class="col-lg-4 col-xl-3 mb-4">
            <div class="card shadow-sm border-0">
                <div class="card-header bg-white border-bottom">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-cogs text-red-600 me-2"></i>
                        Optimization Settings
                    </h5>
                </div>
                <div class="card-body">
                    <form id="optimizationForm">
                        @csrf
                        
                        <!-- Fabric Specifications -->
                        <div class="mb-4">
                            <h6 class="fw-bold text-gray-700 mb-3">
                                <i class="fas fa-ruler-horizontal me-1"></i>
                                Fabric Specifications
                            </h6>
                            
                            <div class="mb-3">
                                <label for="fabricWidth" class="form-label">Fabric Width (inches) *</label>
                                <select class="form-select" id="fabricWidth" name="fabric_width" required>
                                    <option value="">Select Width</option>
                                    <option value="36">36"</option>
                                    <option value="45">45"</option>
                                    <option value="54">54"</option>
                                    <option value="60" selected>60"</option>
                                    <option value="72">72"</option>
                                    <option value="108">108"</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="fabricLength" class="form-label">Fabric Length (inches)</label>
                                <input type="number" class="form-control" id="fabricLength" name="fabric_length" 
                                       placeholder="Leave empty for continuous roll" min="12" max="1000" step="0.1">
                                <div class="form-text">Optional - leave empty for continuous roll</div>
                            </div>
                        </div>

                        <!-- Algorithm Selection -->
                        <div class="mb-4">
                            <h6 class="fw-bold text-gray-700 mb-3">
                                <i class="fas fa-brain me-1"></i>
                                Algorithm
                            </h6>
                            
                            <div class="mb-3">
                                <label for="algorithm" class="form-label">Optimization Algorithm *</label>
                                <select class="form-select" id="algorithm" name="algorithm" required>
                                    <option value="bottom_left_fill" selected>Bottom-Left Fill (Fast)</option>
                                    <option value="genetic">Genetic Algorithm (Advanced)</option>
                                    <option value="no_fit_polygon">No-Fit Polygon (Precise)</option>
                                </select>
                                <div class="form-text" id="algorithmDescription">
                                    Fast greedy algorithm for quick optimization
                                </div>
                            </div>
                        </div>

                        <!-- Pattern Pieces -->
                        <div class="mb-4">
                            <h6 class="fw-bold text-gray-700 mb-3">
                                <i class="fas fa-puzzle-piece me-1"></i>
                                Pattern Pieces
                            </h6>
                            
                            <div class="d-flex gap-2 mb-3">
                                <button type="button" class="btn btn-sm btn-outline-primary flex-fill" id="addPatternBtn">
                                    <i class="fas fa-plus me-1"></i>
                                    Add Pattern
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-secondary" id="importPatternBtn">
                                    <i class="fas fa-upload me-1"></i>
                                    Import
                                </button>
                            </div>
                            
                            <div id="patternPiecesList" class="border rounded p-3 bg-light">
                                <div class="text-center text-muted py-3">
                                    <i class="fas fa-puzzle-piece fa-2x mb-2 opacity-50"></i>
                                    <p class="mb-0">No pattern pieces added yet</p>
                                    <small>Click "Add Pattern" to get started</small>
                                </div>
                            </div>
                        </div>

                        <!-- Size Ratios -->
                        <div class="mb-4">
                            <h6 class="fw-bold text-gray-700 mb-3">
                                <i class="fas fa-chart-pie me-1"></i>
                                Size Distribution
                            </h6>
                            
                            <div class="row g-2">
                                <div class="col-6">
                                    <label class="form-label">S</label>
                                    <input type="number" class="form-control form-control-sm" name="size_ratios[S]" value="50" min="0">
                                </div>
                                <div class="col-6">
                                    <label class="form-label">M</label>
                                    <input type="number" class="form-control form-control-sm" name="size_ratios[M]" value="100" min="0">
                                </div>
                                <div class="col-6">
                                    <label class="form-label">L</label>
                                    <input type="number" class="form-control form-control-sm" name="size_ratios[L]" value="75" min="0">
                                </div>
                                <div class="col-6">
                                    <label class="form-label">XL</label>
                                    <input type="number" class="form-control form-control-sm" name="size_ratios[XL]" value="25" min="0">
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-grid gap-2">
                            <button type="button" class="btn btn-outline-secondary" id="validateBtn">
                                <i class="fas fa-check-circle me-1"></i>
                                Validate Patterns
                            </button>
                            <button type="submit" class="btn btn-red" id="optimizeBtn">
                                <i class="fas fa-magic me-1"></i>
                                Optimize Layout
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Efficiency Report Card -->
            <div class="card shadow-sm border-0 mt-4" id="efficiencyCard" style="display: none;">
                <div class="card-header bg-white border-bottom">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-line text-green-600 me-2"></i>
                        Efficiency Report
                    </h5>
                </div>
                <div class="card-body" id="efficiencyContent">
                    <!-- Efficiency metrics will be populated here -->
                </div>
            </div>
        </div>

        <!-- Right Panel - Canvas and Results -->
        <div class="col-lg-8 col-xl-9">
            <!-- Canvas Card -->
            <div class="card shadow-sm border-0 mb-4">
                <div class="card-header bg-white border-bottom">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-drafting-compass text-blue-600 me-2"></i>
                            Marker Layout
                        </h5>
                        <div class="d-flex align-items-center gap-2">
                            <!-- Zoom Controls -->
                            <div class="btn-group btn-group-sm" role="group">
                                <button type="button" class="btn btn-outline-secondary" id="zoomOutBtn" disabled>
                                    <i class="fas fa-search-minus"></i>
                                </button>
                                <button type="button" class="btn btn-outline-secondary" id="zoomResetBtn" disabled>
                                    <span id="zoomLevel">100%</span>
                                </button>
                                <button type="button" class="btn btn-outline-secondary" id="zoomInBtn" disabled>
                                    <i class="fas fa-search-plus"></i>
                                </button>
                            </div>
                            
                            <!-- Export Dropdown -->
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button" 
                                        id="exportDropdown" data-bs-toggle="dropdown" disabled>
                                    <i class="fas fa-download me-1"></i>
                                    Export
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#" data-export="pdf">
                                        <i class="fas fa-file-pdf me-2"></i>PDF
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" data-export="svg">
                                        <i class="fas fa-file-code me-2"></i>SVG
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" data-export="png">
                                        <i class="fas fa-file-image me-2"></i>PNG
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="#" data-export="dxf">
                                        <i class="fas fa-file-alt me-2"></i>DXF
                                    </a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <!-- Canvas Container -->
                    <div id="canvasContainer" class="position-relative bg-light" style="min-height: 500px;">
                        <div id="canvasPlaceholder" class="d-flex align-items-center justify-content-center h-100 text-muted">
                            <div class="text-center py-5">
                                <i class="fas fa-drafting-compass fa-3x mb-3 opacity-50"></i>
                                <h5>Marker Canvas</h5>
                                <p class="mb-0">Add pattern pieces and click "Optimize Layout" to see the result</p>
                            </div>
                        </div>
                        <canvas id="markerCanvas" style="display: none;"></canvas>
                        
                        <!-- Loading Overlay -->
                        <div id="canvasLoading" class="position-absolute top-0 start-0 w-100 h-100 d-none" 
                             style="background: rgba(255,255,255,0.9); z-index: 10;">
                            <div class="d-flex align-items-center justify-content-center h-100">
                                <div class="text-center">
                                    <div class="spinner-border text-red-600 mb-3" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                    <h6>Optimizing Layout...</h6>
                                    <p class="text-muted mb-0">This may take a few moments</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Pattern Piece Modal -->
<div class="modal fade" id="patternPieceModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add Pattern Piece</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="patternPieceForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Piece Name *</label>
                                <input type="text" class="form-control" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Size *</label>
                                <select class="form-select" name="size" required>
                                    <option value="">Select Size</option>
                                    <option value="XS">XS</option>
                                    <option value="S">S</option>
                                    <option value="M">M</option>
                                    <option value="L">L</option>
                                    <option value="XL">XL</option>
                                    <option value="XXL">XXL</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Width (inches) *</label>
                                <input type="number" class="form-control" name="width" step="0.1" min="0.1" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Height (inches) *</label>
                                <input type="number" class="form-control" name="height" step="0.1" min="0.1" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Quantity *</label>
                                <input type="number" class="form-control" name="quantity" min="1" value="1" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="rotation_allowed" checked>
                            <label class="form-check-label">Allow rotation</label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-red" id="addPatternPieceBtn">Add Piece</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.btn-red {
    background-color: #dc2626;
    border-color: #dc2626;
    color: white;
}

.btn-red:hover {
    background-color: #991b1b;
    border-color: #991b1b;
    color: white;
}

.text-red-600 {
    color: #dc2626 !important;
}

.text-green-600 {
    color: #059669 !important;
}

.text-blue-600 {
    color: #2563eb !important;
}

#canvasContainer {
    border: 2px solid #e5e7eb;
    border-radius: 8px;
}

.pattern-piece-item {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 8px 12px;
    margin-bottom: 8px;
}

.pattern-piece-item:last-child {
    margin-bottom: 0;
}

.size-badge {
    font-size: 0.75rem;
    padding: 2px 6px;
    border-radius: 4px;
}

.efficiency-metric {
    text-align: center;
    padding: 1rem;
    border-radius: 8px;
    background: #f8f9fa;
    margin-bottom: 1rem;
}

.efficiency-metric h4 {
    margin-bottom: 0.5rem;
    font-weight: 700;
}

.efficiency-metric small {
    color: #6b7280;
    text-transform: uppercase;
    font-weight: 500;
    letter-spacing: 0.5px;
}
</style>
@endpush

@push('scripts')
<script src="{{ asset('js/marker-optimization.js') }}"></script>
@endpush
