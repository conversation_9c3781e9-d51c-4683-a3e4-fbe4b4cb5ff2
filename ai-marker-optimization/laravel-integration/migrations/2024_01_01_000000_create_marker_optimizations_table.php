<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * Migration for marker optimizations table
 * 
 * Stores marker optimization results and metadata for Red Rooks Apparel
 */
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('marker_optimizations', function (Blueprint $table) {
            $table->id();
            
            // Foreign keys
            $table->foreignId('company_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('order_id')->nullable()->constrained()->onDelete('set null');
            
            // Optimization metadata
            $table->uuid('optimization_id')->unique();
            $table->string('algorithm_used', 50);
            $table->string('status', 20)->default('pending');
            
            // Fabric specifications
            $table->json('fabric_dimensions'); // {width: float, length: float}
            
            // Efficiency metrics
            $table->json('efficiency_metrics'); // {yield_percentage, waste_percentage, pieces_placed, etc.}
            
            // Performance data
            $table->integer('execution_time_ms');
            
            // Complete optimization data (for export and analysis)
            $table->longText('optimization_data'); // JSON data
            
            // Additional metadata
            $table->text('notes')->nullable();
            $table->json('tags')->nullable(); // For categorization
            
            $table->timestamps();
            $table->softDeletes();
            
            // Indexes for performance
            $table->index(['company_id', 'created_at']);
            $table->index(['user_id', 'created_at']);
            $table->index(['order_id']);
            $table->index(['algorithm_used']);
            $table->index(['status']);
            $table->index(['optimization_id']);
            
            // Composite indexes
            $table->index(['company_id', 'algorithm_used', 'created_at']);
            $table->index(['company_id', 'status', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('marker_optimizations');
    }
};
