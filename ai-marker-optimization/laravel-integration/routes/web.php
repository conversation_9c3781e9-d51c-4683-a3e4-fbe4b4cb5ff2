<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\MarkerOptimizationController;

/*
|--------------------------------------------------------------------------
| Marker Optimization Routes
|--------------------------------------------------------------------------
|
| Routes for the AI Marker Optimization Tool integration with Red Rooks Apparel ERP
|
*/

Route::middleware(['auth', 'verified'])->group(function () {
    
    // Main marker optimization interface
    Route::get('/marker-optimization', [MarkerOptimizationController::class, 'index'])
        ->name('marker-optimization.index');
    
    // API routes for marker optimization
    Route::prefix('api/marker-optimization')->name('api.marker-optimization.')->group(function () {
        
        // Core optimization endpoints
        Route::post('/optimize', [MarkerOptimizationController::class, 'optimize'])
            ->name('optimize');
        
        Route::post('/validate-patterns', [MarkerOptimizationController::class, 'validatePatterns'])
            ->name('validate-patterns');
        
        // History and retrieval
        Route::get('/history', [MarkerOptimizationController::class, 'getHistory'])
            ->name('history');
        
        Route::get('/optimization/{optimizationId}', [MarkerOptimizationController::class, 'getOptimization'])
            ->name('get-optimization');
        
        // Export functionality
        Route::post('/export/{optimizationId}', [MarkerOptimizationController::class, 'exportMarker'])
            ->name('export');
        
        // Algorithm information
        Route::get('/algorithms', [MarkerOptimizationController::class, 'getAlgorithms'])
            ->name('algorithms');
        
        // Statistics and analytics
        Route::get('/statistics', [MarkerOptimizationController::class, 'getStatistics'])
            ->name('statistics');
    });
});

// Public API routes (for external integrations)
Route::prefix('api/public/marker-optimization')->middleware(['api', 'throttle:60,1'])->group(function () {
    
    // Health check
    Route::get('/health', function () {
        return response()->json([
            'status' => 'healthy',
            'service' => 'Red Rooks Apparel - Marker Optimization',
            'timestamp' => now()->toISOString()
        ]);
    })->name('api.marker-optimization.health');
    
    // Algorithm information (public)
    Route::get('/algorithms', [MarkerOptimizationController::class, 'getAlgorithms'])
        ->name('api.marker-optimization.public.algorithms');
});



// Integration routes for other modules
Route::middleware(['auth', 'verified'])->prefix('integration/marker-optimization')->group(function () {
    
    // Order integration
    Route::post('/from-order/{orderId}', function ($orderId) {
        // Implementation for creating optimization from order
        return redirect()->route('marker-optimization.index', ['order_id' => $orderId]);
    })->name('integration.marker-optimization.from-order');
    
    // Garment orders integration
    Route::post('/from-garment-order/{garmentOrderId}', function ($garmentOrderId) {
        // Implementation for creating optimization from garment order
        return redirect()->route('marker-optimization.index', ['garment_order_id' => $garmentOrderId]);
    })->name('integration.marker-optimization.from-garment-order');
});

// Webhook routes (for external notifications)
Route::prefix('webhooks/marker-optimization')->middleware(['api'])->group(function () {
    
    // Optimization completion webhook
    Route::post('/optimization-complete', function (Illuminate\Http\Request $request) {
        // Handle optimization completion notifications
        \Illuminate\Support\Facades\Log::info('Optimization completion webhook received', $request->all());
        
        return response()->json(['status' => 'received']);
    })->name('webhooks.marker-optimization.complete');
    
    // System status webhook
    Route::post('/system-status', function (Illuminate\Http\Request $request) {
        // Handle system status notifications
        \Illuminate\Support\Facades\Log::info('System status webhook received', $request->all());
        
        return response()->json(['status' => 'received']);
    })->name('webhooks.marker-optimization.status');
});
