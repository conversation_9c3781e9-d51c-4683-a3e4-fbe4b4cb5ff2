<?php

namespace App\Console\Commands;

use App\Services\ZKTecoService;
use App\Models\Employee;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Notification;
use Carbon\Carbon;

class AutoSyncAttendance extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'attendance:auto-sync
                            {--date= : Specific date to sync (default: yesterday)}
                            {--machines= : Comma-separated machine IPs (default: all configured)}
                            {--notify-failures : Send notifications on failures}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Automatically sync attendance data from all biometric devices';

    protected $zktecoService;
    protected $logger;
    protected $config;

    public function __construct(ZKTecoService $zktecoService)
    {
        parent::__construct();
        $this->zktecoService = $zktecoService;
        $this->logger = Log::channel('zkteco');
        $this->config = config('zkteco');
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $startTime = now();
        $this->logger->info('Starting automated attendance sync', ['started_at' => $startTime]);

        // Get sync parameters
        $date = $this->option('date') ?: Carbon::yesterday()->format('Y-m-d');
        $machineIps = $this->getMachineIps();
        $notifyFailures = $this->option('notify-failures') || $this->config['notifications']['enabled'];

        $this->info("🔄 Starting automated attendance sync for {$date}");
        $this->info("🖥️  Syncing from " . count($machineIps) . " machine(s)");
        $this->newLine();

        $results = [];
        $totalProcessed = 0;
        $totalErrors = 0;

        // Sync from each machine
        foreach ($machineIps as $machineKey => $machineIp) {
            $this->info("🔌 Syncing from machine: {$machineKey} ({$machineIp})");

            try {
                $result = $this->syncFromMachine($machineIp, $date);
                $results[$machineKey] = $result;

                if ($result['success']) {
                    $totalProcessed += $result['data']['processed'];
                    $this->info("✅ {$machineKey}: {$result['data']['processed']} processed, {$result['data']['skipped']} skipped");
                } else {
                    $totalErrors++;
                    $this->error("❌ {$machineKey}: {$result['message']}");
                }

            } catch (\Exception $e) {
                $totalErrors++;
                $error = "Sync failed for {$machineKey}: " . $e->getMessage();
                $this->error("❌ " . $error);

                $results[$machineKey] = [
                    'success' => false,
                    'message' => $error,
                    'machine_ip' => $machineIp
                ];

                $this->logger->error('Machine sync failed', [
                    'machine' => $machineKey,
                    'ip' => $machineIp,
                    'error' => $e->getMessage()
                ]);
            }

            $this->newLine();
        }

        // Generate summary
        $endTime = now();
        $duration = $endTime->diffInSeconds($startTime);

        $summary = [
            'date' => $date,
            'machines_synced' => count($machineIps),
            'total_processed' => $totalProcessed,
            'total_errors' => $totalErrors,
            'duration_seconds' => $duration,
            'started_at' => $startTime,
            'completed_at' => $endTime,
            'results' => $results
        ];

        $this->displaySummary($summary);
        $this->logSummary($summary);

        // Send notifications if there were failures
        if ($totalErrors > 0 && $notifyFailures) {
            $this->sendFailureNotifications($summary);
        }

        // Return appropriate exit code
        return $totalErrors > 0 ? 1 : 0;
    }

    /**
     * Get machine IPs to sync from
     */
    protected function getMachineIps(): array
    {
        $machinesOption = $this->option('machines');

        if ($machinesOption) {
            // Parse comma-separated IPs
            $ips = array_map('trim', explode(',', $machinesOption));
            $machines = [];
            foreach ($ips as $index => $ip) {
                $machines["machine_" . ($index + 1)] = $ip;
            }
            return $machines;
        }

        // Use all configured machines
        $configuredMachines = $this->config['machines'];
        $machines = [];

        foreach ($configuredMachines as $key => $machine) {
            $machines[$key] = $machine['ip'];
        }

        return $machines;
    }

    /**
     * Sync attendance from a single machine
     */
    protected function syncFromMachine(string $machineIp, string $date): array
    {
        try {
            // Fetch attendance logs
            $logsResult = $this->zktecoService->fetchAttendanceLogs($date, $machineIp);

            if (!$logsResult['success']) {
                return [
                    'success' => false,
                    'message' => 'Failed to fetch logs: ' . $logsResult['message'],
                    'machine_ip' => $machineIp
                ];
            }

            // Process logs
            $processResult = $this->zktecoService->processAttendanceLogs(
                $logsResult['data'],
                $date,
                false // Not a dry run
            );

            return [
                'success' => $processResult['success'],
                'message' => $processResult['success'] ? 'Sync completed successfully' : $processResult['message'],
                'data' => $processResult,
                'machine_ip' => $machineIp
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Sync exception: ' . $e->getMessage(),
                'machine_ip' => $machineIp
            ];
        }
    }

    /**
     * Display sync summary
     */
    protected function displaySummary(array $summary): void
    {
        $this->newLine();
        $this->info('📊 Sync Summary:');

        $this->table(['Metric', 'Value'], [
            ['Date', $summary['date']],
            ['Machines Synced', $summary['machines_synced']],
            ['Total Processed', $summary['total_processed']],
            ['Total Errors', $summary['total_errors']],
            ['Duration', $summary['duration_seconds'] . ' seconds'],
            ['Status', $summary['total_errors'] > 0 ? 'Completed with errors' : 'Completed successfully'],
        ]);

        // Show per-machine results
        if (!empty($summary['results'])) {
            $this->newLine();
            $this->info('🖥️  Per-Machine Results:');

            $machineResults = [];
            foreach ($summary['results'] as $machine => $result) {
                $machineResults[] = [
                    $machine,
                    $result['machine_ip'],
                    $result['success'] ? 'Success' : 'Failed',
                    $result['success'] ? ($result['data']['processed'] ?? 0) : 0,
                    $result['success'] ? ($result['data']['skipped'] ?? 0) : 0,
                    $result['success'] ? '' : $result['message']
                ];
            }

            $this->table([
                'Machine', 'IP', 'Status', 'Processed', 'Skipped', 'Error'
            ], $machineResults);
        }
    }

    /**
     * Log sync summary
     */
    protected function logSummary(array $summary): void
    {
        if ($summary['total_errors'] > 0) {
            $this->logger->warning('Automated sync completed with errors', $summary);
        } else {
            $this->logger->info('Automated sync completed successfully', $summary);
        }
    }

    /**
     * Send failure notifications
     */
    protected function sendFailureNotifications(array $summary): void
    {
        try {
            $this->info('📧 Sending failure notifications...');

            $recipients = $this->config['notifications']['email_recipients'] ?? [];

            if (empty($recipients)) {
                $this->warn('⚠️  No email recipients configured for notifications');
                return;
            }

            // Prepare notification data
            $notificationData = [
                'subject' => 'ZKTeco Attendance Sync Failures - ' . $summary['date'],
                'summary' => $summary,
                'failed_machines' => array_filter($summary['results'], function($result) {
                    return !$result['success'];
                })
            ];

            // Send email notifications
            foreach ($recipients as $email) {
                // Here you would send actual email notification
                // For now, just log it
                $this->logger->warning('Sync failure notification sent', [
                    'recipient' => $email,
                    'failures' => count($notificationData['failed_machines'])
                ]);
            }

            $this->info("✅ Notifications sent to " . count($recipients) . " recipients");

        } catch (\Exception $e) {
            $this->error("❌ Failed to send notifications: " . $e->getMessage());
            $this->logger->error('Notification sending failed', ['error' => $e->getMessage()]);
        }
    }
}
