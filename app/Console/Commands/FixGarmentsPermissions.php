<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use App\Models\User;

class FixGarmentsPermissions extends Command
{
    protected $signature = 'fix:garments-permissions';
    protected $description = 'Fix all Garments Orders permissions for superadmin user';

    public function handle()
    {
        $this->info('🔧 Fixing Garments Orders Permissions...');
        $this->newLine();

        try {
            // Step 1: Create all required permissions
            $this->info('📝 Creating all required permissions...');
            
            $permissions = [
                // Core Garment Orders
                'garment_orders.view',
                'garment_orders.create',
                'garment_orders.edit',
                'garment_orders.delete',
                'garment_orders.duplicate',
                'garment_orders.export',
                
                // Size Ratios
                'order_size_ratios.view',
                'order_size_ratios.create',
                'order_size_ratios.edit',
                'order_size_ratios.delete',
                'order_size_ratios.recalculate',
                'order_size_ratios.manual_override',
                
                // Tech Packs
                'order_tech_packs.view',
                'order_tech_packs.create',
                'order_tech_packs.edit',
                'order_tech_packs.delete',
                'order_tech_packs.download',
                'order_tech_packs.approve_samples',
                'order_tech_packs.reject_samples',
                'order_tech_packs.create_version',
                
                // BOM Items
                'order_bom_items.view',
                'order_bom_items.create',
                'order_bom_items.edit',
                'order_bom_items.delete',
                'order_bom_items.create_version',
                'order_bom_items.update_quantities',
                'order_bom_items.export',
                
                // Costing
                'order_costing.view',
                'order_costing.create',
                'order_costing.edit',
                'order_costing.delete',
                'order_costing.approve',
                'order_costing.reject',
                'order_costing.create_version',
                'order_costing.recalculate',
                'order_costing.compare_versions',
                
                // Delivery Batches
                'order_delivery_batches.view',
                'order_delivery_batches.create',
                'order_delivery_batches.edit',
                'order_delivery_batches.delete',
                'order_delivery_batches.update_status',
                'order_delivery_batches.approve_quality',
                'order_delivery_batches.manage_delays',
                
                // SOP Compliance
                'order_sop_compliance.view',
                'order_sop_compliance.create',
                'order_sop_compliance.edit',
                'order_sop_compliance.delete',
                'order_sop_compliance.verify',
                'order_sop_compliance.buyer_approve',
                'order_sop_compliance.mark_non_compliant',
                'order_sop_compliance.manage_alerts',
                
                // Production Checklist
                'order_production_checklist.view',
                'order_production_checklist.edit',
                'order_production_checklist.approve_production',
                'order_production_checklist.lock_production',
                'order_production_checklist.refresh_status',
                
                // Reports
                'garment_orders_reports.view',
                'garment_orders_reports.export',
                'garment_orders_reports.dashboard',
                'garment_orders_reports.analytics',
                
                // Basic permissions
                'dashboard.view',
                'buyer_profiles.view',
                'buyer_profiles.create',
                'buyer_profiles.edit',
                'buyer_profiles.delete',
                'buyer_pipeline.view',
                'buyer_pipeline.edit',
                'buyer_analysis.view',
                'buyer_scoring.view',
                'buyer_meetings.view',
                'buyer_meetings.create',
                'buyer_meetings.edit',
                'buyer_meetings.delete',
                'buyer_follow_ups.view',
                'buyer_follow_ups.create',
                'buyer_follow_ups.edit',
                'buyer_follow_ups.delete',
                'buyer_documents.view',
                'buyer_documents.create',
                'buyer_documents.edit',
                'buyer_documents.delete',
                'marketing_campaigns.view',
                'marketing_campaigns.create',
                'marketing_campaigns.edit',
                'marketing_campaigns.delete',
                'buyer_drop_reasons.view',
                'buyer_drop_reasons.create',
                'buyer_drop_reasons.edit',
                'buyer_drop_reasons.delete',
                'reports.view',
                'reports.export',
                'roles-read',
                'permissions-read',
                'users-read',
                'users-create',
                'users-edit',
                'users-delete',
            ];

            $createdCount = 0;
            $existingCount = 0;

            foreach ($permissions as $permissionName) {
                $permission = Permission::firstOrCreate([
                    'name' => $permissionName,
                    'guard_name' => 'web'
                ]);

                if ($permission->wasRecentlyCreated) {
                    $this->line("  ✅ Created: {$permissionName}");
                    $createdCount++;
                } else {
                    $this->line("  ℹ️  Exists: {$permissionName}");
                    $existingCount++;
                }
            }

            $this->newLine();
            $this->info("📊 Permission Summary:");
            $this->line("  - Created: {$createdCount}");
            $this->line("  - Already existed: {$existingCount}");
            $this->line("  - Total: " . count($permissions));
            $this->newLine();

            // Step 2: Create or get Super Admin role
            $this->info('👑 Setting up Super Admin role...');
            $superAdminRole = Role::firstOrCreate([
                'name' => 'Super Admin',
                'guard_name' => 'web'
            ]);

            if ($superAdminRole->wasRecentlyCreated) {
                $this->line("  ✅ Created Super Admin role");
            } else {
                $this->line("  ℹ️  Super Admin role already exists");
            }

            // Step 3: Assign ALL permissions to Super Admin role
            $this->newLine();
            $this->info('🔗 Assigning ALL permissions to Super Admin role...');
            $allPermissions = Permission::all();
            $superAdminRole->syncPermissions($allPermissions);
            $this->line("  ✅ Assigned all " . $allPermissions->count() . " permissions to Super Admin role");

            // Step 4: Create or get superadmin user
            $this->newLine();
            $this->info('👤 Setting up superadmin user...');
            $superadminUser = User::firstOrCreate([
                'email' => '<EMAIL>'
            ], [
                'name' => 'Super Admin',
                'password' => bcrypt('password'),
                'email_verified_at' => now(),
            ]);

            if ($superadminUser->wasRecentlyCreated) {
                $this->line("  ✅ Created superadmin user");
            } else {
                $this->line("  ℹ️  Superadmin user already exists");
            }

            // Step 5: Assign Super Admin role to superadmin user
            $this->newLine();
            $this->info('🎭 Assigning role to user...');
            
            // Remove all existing roles first
            $superadminUser->roles()->detach();
            
            // Assign Super Admin role
            $superadminUser->assignRole('Super Admin');
            $this->line("  ✅ Assigned Super Admin <NAME_EMAIL>");

            // Step 6: Verification
            $this->newLine();
            $this->info('🔍 Verification...');
            
            $userPermissions = $superadminUser->getAllPermissions();
            $garmentOrdersPermissions = $userPermissions->filter(function ($permission) {
                return str_contains($permission->name, 'garment_orders') || 
                       str_contains($permission->name, 'order_');
            });

            $this->line("  - Total user permissions: " . $userPermissions->count());
            $this->line("  - Garments Orders permissions: " . $garmentOrdersPermissions->count());
            $this->line("  - User has Super Admin role: " . ($superadminUser->hasRole('Super Admin') ? 'YES' : 'NO'));

            // Clear permission cache
            $this->newLine();
            $this->info('🧹 Clearing permission cache...');
            \Artisan::call('permission:cache-reset');
            $this->line("  ✅ Permission cache cleared");

            $this->newLine();
            $this->info('🎉 All permissions fixed successfully!');
            $this->newLine();
            
            $this->info('📋 Next steps:');
            $this->line('1. Clear application cache: php artisan cache:clear');
            $this->line('2. Clear view cache: php artisan view:clear');
            $this->line('3. Login with: <EMAIL> / password');
            $this->line('4. Test: http://localhost:8000/garment-orders/');
            
            return 0;

        } catch (\Exception $e) {
            $this->error("❌ Error: " . $e->getMessage());
            $this->error("📍 File: " . $e->getFile() . " Line: " . $e->getLine());
            return 1;
        }
    }
}
