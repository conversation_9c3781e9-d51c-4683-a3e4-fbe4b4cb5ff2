<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use <PERSON>tie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\Models\User;

class GrantAdminPermissions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'admin:grant-permissions';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Grant all new attendance and employee management permissions to admin users';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Granting admin permissions for attendance and employee management...');

        // Define all the new permissions that need to be created/assigned
        $newPermissions = [
            // Attendance Management
            'attendances-read',
            'attendances-create', 
            'attendances-update',
            'attendances-delete',
            'attendances-list',
            
            // Attendance Reports
            'attendance-reports-read',
            'attendance-reports-create',
            'attendance-reports-update', 
            'attendance-reports-delete',
            'attendance-reports-list',
            
            // Attendance Import
            'attendance-import-read',
            'attendance-import-create',
            'attendance-import-update',
            'attendance-import-delete', 
            'attendance-import-list',
            
            // Monthly Attendance Sheet
            'attendance-monthly-sheet-read',
            'attendance-monthly-sheet-create',
            'attendance-monthly-sheet-update',
            'attendance-monthly-sheet-delete',
            'attendance-monthly-sheet-list',
            
            // HR Dashboard
            'hr-dashboard-read',
        ];

        // Create permissions if they don't exist
        foreach ($newPermissions as $permissionName) {
            $permission = Permission::firstOrCreate(['name' => $permissionName]);
            $this->line("✓ Permission '{$permissionName}' ensured");
        }

        // Get admin roles
        $adminRoles = ['Super Admin', 'Admin'];
        
        foreach ($adminRoles as $roleName) {
            $role = Role::where('name', $roleName)->first();
            
            if ($role) {
                $this->info("Granting permissions to {$roleName} role...");
                
                // Grant all new permissions to the role
                foreach ($newPermissions as $permissionName) {
                    $permission = Permission::where('name', $permissionName)->first();
                    if ($permission && !$role->hasPermissionTo($permission)) {
                        $role->givePermissionTo($permission);
                        $this->line("  ✓ Granted '{$permissionName}' to {$roleName}");
                    } else {
                        $this->line("  - '{$permissionName}' already granted to {$roleName}");
                    }
                }
            } else {
                $this->warn("Role '{$roleName}' not found!");
            }
        }

        // Also grant permissions directly to admin users (as backup)
        $adminUsers = User::whereIn('role', ['admin', 'superadmin'])->get();
        
        if ($adminUsers->count() > 0) {
            $this->info("Granting permissions directly to admin users...");
            
            foreach ($adminUsers as $user) {
                foreach ($newPermissions as $permissionName) {
                    $permission = Permission::where('name', $permissionName)->first();
                    if ($permission && !$user->hasPermissionTo($permission)) {
                        $user->givePermissionTo($permission);
                        $this->line("  ✓ Granted '{$permissionName}' to user {$user->name}");
                    }
                }
            }
        }

        $this->info('✅ All admin permissions have been granted successfully!');
        $this->info('Admin users now have access to:');
        $this->line('  • Employee Management (view, create, edit, delete)');
        $this->line('  • Attendance Management (daily marking, bulk operations)');
        $this->line('  • Attendance Reports (monthly, yearly analysis)');
        $this->line('  • Monthly Attendance Sheets (printable format)');
        $this->line('  • Attendance Import (biometric data import)');
        $this->line('  • HR Dashboard (cost tracking, statistics)');
        $this->line('  • Salary Management (all operations)');
        
        return 0;
    }
}
