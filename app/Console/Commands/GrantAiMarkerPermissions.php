<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use App\Models\User;
use App\Models\Company;
use App\Models\AiMarker\UserPermission;
use App\Services\AiMarker\PermissionService;
use Spatie\Permission\Models\Role;

/**
 * Grant AI Marker permissions to a specific user
 */
class GrantAiMarkerPermissions extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'ai-marker:grant-permissions 
                            {email : Email address of the user}
                            {--create : Create user if it doesn\'t exist}
                            {--company-id= : Company ID for the user}';

    /**
     * The console command description.
     */
    protected $description = 'Grant all AI Marker permissions to a specific user';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email');
        $createUser = $this->option('create');
        $companyId = $this->option('company-id');

        $this->info("🚀 Granting AI Marker permissions to: {$email}");
        $this->newLine();

        // Step 1: Find or create user
        $user = $this->findOrCreateUser($email, $createUser, $companyId);
        if (!$user) {
            return 1;
        }

        // Step 2: Ensure user has superadmin role
        $this->ensureSuperadminRole($user);

        // Step 3: Grant all AI Marker permissions
        $this->grantAllPermissions($user);

        // Step 4: Verify permissions
        $this->verifyPermissions($user);

        $this->newLine();
        $this->info('✅ All AI Marker permissions granted successfully!');
        $this->newLine();
        
        $this->info('🎉 User can now access:');
        $this->line('   • AI Marker Optimization menu in sidebar');
        $this->line('   • Dashboard and analytics');
        $this->line('   • Pattern library and templates');
        $this->line('   • Optimization tools');
        $this->line('   • Administrative functions');
        $this->line('   • All reports and exports');

        return 0;
    }

    /**
     * Find or create user
     */
    protected function findOrCreateUser(string $email, bool $createUser, ?string $companyId): ?User
    {
        $user = User::where('email', $email)->first();

        if ($user) {
            $this->info("👤 Found existing user: {$user->name} ({$user->email})");
            return $user;
        }

        if (!$createUser) {
            $this->error("❌ User not found: {$email}");
            $this->line('   Use --create flag to create the user automatically');
            return null;
        }

        // Create new user
        $this->info("👤 Creating new user: {$email}");

        // Get or create company
        $company = $this->getOrCreateCompany($companyId);

        $user = User::create([
            'name' => 'AI Marker Superadmin',
            'email' => $email,
            'password' => Hash::make('password123'), // Default password
            'email_verified_at' => now(),
            'company_id' => $company->id,
            'status' => 'active'
        ]);

        $this->info("✅ User created successfully");
        $this->warn("⚠️  Default password: password123 (please change after first login)");

        return $user;
    }

    /**
     * Get or create company
     */
    protected function getOrCreateCompany(?string $companyId): Company
    {
        if ($companyId) {
            $company = Company::find($companyId);
            if ($company) {
                $this->info("🏢 Using existing company: {$company->name}");
                return $company;
            }
        }

        // Get first company or create one
        $company = Company::first();
        
        if (!$company) {
            $company = Company::create([
                'name' => 'Red Rooks Apparel',
                'email' => '<EMAIL>',
                'status' => 'active',
                'subscription_status' => 'active'
            ]);
            $this->info("🏢 Created new company: {$company->name}");
        } else {
            $this->info("🏢 Using company: {$company->name}");
        }

        return $company;
    }

    /**
     * Ensure user has superadmin role
     */
    protected function ensureSuperadminRole(User $user): void
    {
        $this->info("🔐 Checking superadmin role...");

        // Create superadmin role if it doesn't exist
        $superadminRole = Role::firstOrCreate(['name' => 'superadmin']);

        if (!$user->hasRole('superadmin')) {
            $user->assignRole('superadmin');
            $this->info("✅ Assigned superadmin role");
        } else {
            $this->info("✅ User already has superadmin role");
        }
    }

    /**
     * Grant all AI Marker permissions
     */
    protected function grantAllPermissions(User $user): void
    {
        $this->info("🎯 Granting AI Marker permissions...");

        try {
            $permissionService = app(PermissionService::class);
            $availablePermissions = $permissionService->getAvailablePermissions();

            $grantedCount = 0;
            $totalPermissions = count($availablePermissions);

            foreach ($availablePermissions as $code => $permission) {
                try {
                    UserPermission::updateOrCreate(
                        [
                            'user_id' => $user->id,
                            'company_id' => $user->company_id,
                            'permission_code' => $code
                        ],
                        [
                            'permission_category' => $permission['category'],
                            'permission_level' => $permission['level'],
                            'permission_name' => $permission['name'],
                            'permission_description' => $permission['description'],
                            'granted_by' => $user->id,
                            'is_active' => true,
                            'scope' => 'global',
                            'grant_reason' => 'Superadmin setup via command',
                            'approval_status' => 'approved',
                            'approved_by' => $user->id,
                            'approved_at' => now()
                        ]
                    );
                    $grantedCount++;
                } catch (\Exception $e) {
                    $this->warn("   ⚠️  Failed to grant {$code}: " . $e->getMessage());
                }
            }

            $this->info("✅ Granted {$grantedCount}/{$totalPermissions} permissions");

            // Clear permission cache
            \Cache::forget("ai_marker_permissions_{$user->id}");
            \Cache::forget("ai_marker_permissions_detailed_{$user->id}");

        } catch (\Exception $e) {
            $this->error("❌ Error granting permissions: " . $e->getMessage());
        }
    }

    /**
     * Verify permissions were granted
     */
    protected function verifyPermissions(User $user): void
    {
        $this->info("🔍 Verifying permissions...");

        try {
            $permissions = UserPermission::where('user_id', $user->id)
                ->where('is_active', true)
                ->get();

            $this->info("📊 Permission Summary:");
            
            $categories = $permissions->groupBy('permission_category');
            foreach ($categories as $category => $categoryPermissions) {
                $this->line("   • {$category}: {$categoryPermissions->count()} permissions");
            }

            // Test specific key permissions
            $keyPermissions = [
                'ai-marker.access' => 'Basic Access',
                'ai-marker.optimize.create' => 'Create Optimizations',
                'ai-marker.admin.users' => 'User Management',
                'ai-marker.admin.system' => 'System Administration'
            ];

            $this->newLine();
            $this->info("🔑 Key Permissions Check:");
            
            foreach ($keyPermissions as $code => $name) {
                $hasPermission = $permissions->where('permission_code', $code)->isNotEmpty();
                $status = $hasPermission ? '✅' : '❌';
                $this->line("   {$status} {$name}");
            }

        } catch (\Exception $e) {
            $this->error("❌ Error verifying permissions: " . $e->getMessage());
        }
    }
}
