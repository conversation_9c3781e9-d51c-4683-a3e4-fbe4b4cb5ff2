<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;

class HrCleanCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'hr:clean {--force : Force deletion without confirmation}';

    /**
     * The console command description.
     */
    protected $description = 'Clean old HR module files (Controllers, Models, Views, Migrations)';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🧹 HR Module Cleanup Tool');
        $this->info('This will remove old HR-related files from your application.');
        
        if (!$this->option('force')) {
            if (!$this->confirm('Are you sure you want to proceed? This action cannot be undone.')) {
                $this->info('Cleanup cancelled.');
                return 0;
            }
        }

        $deletedFiles = [];
        $deletedFolders = [];

        // Define files and folders to clean
        $controllersToDelete = [
            'app/Http/Controllers/EmployeeController.php',
            'app/Http/Controllers/AttendanceController.php',
            'app/Http/Controllers/LeaveController.php',
            'app/Http/Controllers/SalaryController.php',
        ];

        $modelsToDelete = [
            'app/Models/Employee.php',
            'app/Models/Attendance.php',
            'app/Models/Leave.php',
            'app/Models/Salary.php',
        ];

        $viewFoldersToDelete = [
            'resources/views/employees',
            'resources/views/attendances',
            'resources/views/leaves',
            'resources/views/salaries',
            'resources/views/pages/employees',
            'resources/views/pages/attendances',
            'resources/views/pages/leaves',
            'resources/views/pages/salaries',
            'resources/views/pages/attendance',
        ];

        // Clean Controllers
        $this->info('🎯 Cleaning Controllers...');
        foreach ($controllersToDelete as $controller) {
            if (File::exists(base_path($controller))) {
                File::delete(base_path($controller));
                $deletedFiles[] = $controller;
                $this->line("   ✅ Deleted: {$controller}");
            }
        }

        // Clean Models
        $this->info('🎯 Cleaning Models...');
        foreach ($modelsToDelete as $model) {
            if (File::exists(base_path($model))) {
                File::delete(base_path($model));
                $deletedFiles[] = $model;
                $this->line("   ✅ Deleted: {$model}");
            }
        }

        // Clean View Folders
        $this->info('🎯 Cleaning View Folders...');
        foreach ($viewFoldersToDelete as $folder) {
            if (File::exists(base_path($folder))) {
                File::deleteDirectory(base_path($folder));
                $deletedFolders[] = $folder;
                $this->line("   ✅ Deleted folder: {$folder}");
            }
        }

        // Clean Migration Files
        $this->info('🎯 Cleaning Migration Files...');
        $migrationPath = database_path('migrations');
        $migrationPatterns = [
            '*_create_employees_table.php',
            '*_create_attendances_table.php',
            '*_create_leaves_table.php',
            '*_create_salaries_table.php',
            '*_add_*_to_employees_table.php',
            '*_add_*_to_attendances_table.php',
            '*_add_*_to_leaves_table.php',
            '*_add_*_to_salaries_table.php',
        ];

        foreach ($migrationPatterns as $pattern) {
            $files = glob($migrationPath . '/' . $pattern);
            foreach ($files as $file) {
                File::delete($file);
                $deletedFiles[] = str_replace(base_path() . '/', '', $file);
                $this->line("   ✅ Deleted migration: " . basename($file));
            }
        }

        // Log the cleanup
        $logData = [
            'deleted_files' => $deletedFiles,
            'deleted_folders' => $deletedFolders,
            'timestamp' => now()->toDateTimeString(),
        ];
        
        Log::info('HR Module Cleanup Completed', $logData);

        // Summary
        $this->info('');
        $this->info('📊 Cleanup Summary:');
        $this->line("   Files deleted: " . count($deletedFiles));
        $this->line("   Folders deleted: " . count($deletedFolders));
        $this->info('');
        $this->info('✅ HR Module cleanup completed successfully!');
        $this->info('📝 Details logged to Laravel log file.');

        return 0;
    }
}
