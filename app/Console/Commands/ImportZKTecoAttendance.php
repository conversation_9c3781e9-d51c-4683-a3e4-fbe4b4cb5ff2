<?php

namespace App\Console\Commands;

use App\Services\ZKTecoService;
use Illuminate\Console\Command;
use Carbon\Carbon;

class ImportZKTecoAttendance extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'attendance:import-zkt
                            {--date= : Date to import (YYYY-MM-DD format, default: yesterday)}
                            {--machine-ip= : Machine IP address (default: from config)}
                            {--dry-run : Preview import without saving data}
                            {--force : Force import even if data already exists}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import attendance data from ZKTeco biometric devices';

    protected $zktecoService;

    public function __construct(ZKTecoService $zktecoService)
    {
        parent::__construct();
        $this->zktecoService = $zktecoService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔄 Starting ZKTeco Attendance Import...');
        $this->newLine();

        // Get command options
        $date = $this->option('date') ?: Carbon::yesterday()->format('Y-m-d');
        $machineIp = $this->option('machine-ip') ?: config('zkteco.default_machine.ip');
        $dryRun = $this->option('dry-run');
        $force = $this->option('force');

        // Validate date format
        try {
            $carbonDate = Carbon::createFromFormat('Y-m-d', $date);
        } catch (\Exception $e) {
            $this->error('❌ Invalid date format. Please use YYYY-MM-DD format.');
            return 1;
        }

        // Display import parameters
        $this->table(['Parameter', 'Value'], [
            ['Date', $date],
            ['Machine IP', $machineIp],
            ['Dry Run', $dryRun ? 'Yes' : 'No'],
            ['Force Import', $force ? 'Yes' : 'No'],
        ]);

        if ($dryRun) {
            $this->warn('🔍 DRY RUN MODE: No data will be saved to database');
        }

        $this->newLine();

        // Test machine connection
        $this->info('🔌 Testing connection to ZKTeco device...');
        $connectionTest = $this->zktecoService->testConnection($machineIp);

        if (!$connectionTest['success']) {
            $this->error('❌ Connection failed: ' . $connectionTest['message']);
            return 1;
        }

        $this->info('✅ Connection successful');
        $this->newLine();

        // Fetch attendance logs
        $this->info('📥 Fetching attendance logs...');
        $logsResult = $this->zktecoService->fetchAttendanceLogs($date, $machineIp);

        if (!$logsResult['success']) {
            $this->error('❌ Failed to fetch logs: ' . $logsResult['message']);
            return 1;
        }

        $logs = $logsResult['data'];
        $summary = $logsResult['summary'];

        $this->info("✅ Fetched {$summary['total_records']} records for {$summary['unique_users']} users");
        $this->newLine();

        if (empty($logs)) {
            $this->warn('⚠️  No attendance logs found for the specified date.');
            return 0;
        }

        // Process attendance logs
        $this->info('⚙️  Processing attendance data...');

        $progressBar = $this->output->createProgressBar($summary['unique_users']);
        $progressBar->start();

        $processResult = $this->zktecoService->processAttendanceLogs($logs, $date, $dryRun);

        $progressBar->finish();
        $this->newLine(2);

        // Display results
        if ($processResult['success']) {
            $this->displayResults($processResult, $dryRun);

            if (!$dryRun) {
                $this->info('✅ Attendance import completed successfully!');
            } else {
                $this->info('🔍 Dry run completed. Use --dry-run=false to actually import data.');
            }

            return 0;
        } else {
            $this->error('❌ Import failed: ' . $processResult['message']);
            return 1;
        }
    }

    /**
     * Display import results in a formatted table
     */
    protected function displayResults(array $result, bool $dryRun): void
    {
        $this->newLine();
        $this->info('📊 Import Summary:');

        $summaryData = [
            ['Metric', 'Count'],
            ['Total Records', $result['total']],
            ['Successfully Processed', $result['processed']],
            ['Skipped', $result['skipped']],
            ['Errors', $result['errors']],
        ];

        $this->table($summaryData[0], array_slice($summaryData, 1));

        // Show detailed results if requested
        if ($this->option('verbose') && !empty($result['details'])) {
            $this->newLine();
            $this->info('📋 Detailed Results:');

            $detailsTable = [];
            foreach ($result['details'] as $detail) {
                $detailsTable[] = [
                    $detail['machine_user_id'] ?? 'N/A',
                    $detail['employee_name'] ?? 'Unknown',
                    $detail['status'] ?? 'Unknown',
                    $detail['in_time'] ?? 'N/A',
                    $detail['out_time'] ?? 'N/A',
                    $detail['working_hours'] ?? 'N/A',
                    $detail['message'] ?? 'N/A',
                ];
            }

            $this->table([
                'Machine ID', 'Employee', 'Status', 'In Time', 'Out Time', 'Hours', 'Message'
            ], $detailsTable);
        }

        // Show warnings for skipped records
        if ($result['skipped'] > 0) {
            $this->newLine();
            $this->warn("⚠️  {$result['skipped']} records were skipped. Common reasons:");
            $this->line('   • Employee not found for machine user ID');
            $this->line('   • Invalid or incomplete punch data');
            $this->line('   • Duplicate records (use --force to override)');
        }

        // Show errors
        if ($result['errors'] > 0) {
            $this->newLine();
            $this->error("❌ {$result['errors']} records had errors during processing.");
            $this->line('   Check the logs for detailed error information.');
        }
    }
}
