<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Artisan;

/**
 * Install AI Marker Optimization Tool
 */
class InstallAiMarker extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'ai-marker:install {--force : Force installation even if tables exist}';

    /**
     * The console command description.
     */
    protected $description = 'Install the AI Marker Optimization Tool database tables and permissions';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🚀 Installing AI Marker Optimization Tool...');
        $this->newLine();

        // Check if tables already exist
        if (!$this->option('force') && $this->tablesExist()) {
            $this->warn('AI Marker tables already exist!');
            if (!$this->confirm('Do you want to continue anyway?')) {
                $this->info('Installation cancelled.');
                return 0;
            }
        }

        // Step 1: Run migrations
        $this->info('📊 Running database migrations...');
        $this->runMigrations();

        // Step 2: Seed permissions
        $this->info('👥 Setting up permissions...');
        $this->seedPermissions();

        // Step 3: Clear cache
        $this->info('🧹 Clearing application cache...');
        $this->clearCache();

        // Step 4: Verify installation
        $this->info('🔍 Verifying installation...');
        $this->verifyInstallation();

        $this->newLine();
        $this->info('✅ AI Marker Optimization Tool installed successfully!');
        $this->newLine();
        
        $this->info('🎉 Next steps:');
        $this->line('   1. Visit your application dashboard');
        $this->line('   2. Look for "AI Marker Optimization" in the sidebar');
        $this->line('   3. Click on "Dashboard" to start using the tool');
        
        return 0;
    }

    /**
     * Run AI Marker migrations
     */
    protected function runMigrations(): void
    {
        $migrations = [
            '2024_01_15_000001_create_ai_marker_pattern_pieces_table.php',
            '2024_01_15_000002_create_ai_marker_fabric_specifications_table.php',
            '2024_01_15_000003_create_ai_marker_optimization_templates_table.php',
            '2024_01_15_000004_create_ai_marker_exports_table.php',
            '2024_01_15_000005_create_ai_marker_user_permissions_table.php'
        ];

        foreach ($migrations as $migration) {
            $migrationPath = database_path("migrations/{$migration}");
            
            if (!file_exists($migrationPath)) {
                $this->warn("   ⚠️  Migration not found: {$migration}");
                continue;
            }

            try {
                $this->line("   Running: {$migration}");
                Artisan::call('migrate', [
                    '--path' => "database/migrations/{$migration}",
                    '--force' => true
                ]);
                $this->line("   ✅ Success");
            } catch (\Exception $e) {
                $this->error("   ❌ Failed: " . $e->getMessage());
            }
        }
    }

    /**
     * Seed AI Marker permissions
     */
    protected function seedPermissions(): void
    {
        try {
            if (class_exists('Database\Seeders\AiMarkerPermissionsSeeder')) {
                Artisan::call('db:seed', [
                    '--class' => 'AiMarkerPermissionsSeeder',
                    '--force' => true
                ]);
                $this->line('   ✅ Permissions seeded successfully');
            } else {
                $this->warn('   ⚠️  Permissions seeder not found');
            }
        } catch (\Exception $e) {
            $this->error('   ❌ Failed to seed permissions: ' . $e->getMessage());
        }
    }

    /**
     * Clear application cache
     */
    protected function clearCache(): void
    {
        $commands = [
            'cache:clear' => 'Application cache',
            'config:clear' => 'Configuration cache',
            'route:clear' => 'Route cache',
            'view:clear' => 'View cache'
        ];

        foreach ($commands as $command => $description) {
            try {
                Artisan::call($command);
                $this->line("   ✅ {$description} cleared");
            } catch (\Exception $e) {
                $this->warn("   ⚠️  Failed to clear {$description}");
            }
        }
    }

    /**
     * Verify installation
     */
    protected function verifyInstallation(): void
    {
        $tables = [
            'ai_marker_pattern_pieces',
            'ai_marker_fabric_specifications',
            'ai_marker_optimization_templates',
            'ai_marker_exports',
            'ai_marker_user_permissions'
        ];

        $created = 0;
        $total = count($tables);

        foreach ($tables as $table) {
            if (Schema::hasTable($table)) {
                $this->line("   ✅ {$table}");
                $created++;
            } else {
                $this->error("   ❌ {$table}");
            }
        }

        $this->newLine();
        $this->info("📊 Installation Status: {$created}/{$total} tables created");

        if ($created === $total) {
            $this->info('🎉 All tables created successfully!');
        } else {
            $this->warn('⚠️  Some tables were not created. You may need to run migrations manually.');
        }
    }

    /**
     * Check if AI Marker tables exist
     */
    protected function tablesExist(): bool
    {
        try {
            return Schema::hasTable('ai_marker_user_permissions') ||
                   Schema::hasTable('ai_marker_pattern_pieces');
        } catch (\Exception $e) {
            return false;
        }
    }
}
