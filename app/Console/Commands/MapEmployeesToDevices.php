<?php

namespace App\Console\Commands;

use App\Models\Employee;
use App\Services\ZKTecoService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class MapEmployeesToDevices extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'biometric:map-employees
                            {--machine-ip= : Machine IP address to fetch users from}
                            {--auto-map : Automatically map employees by name matching}
                            {--dry-run : Preview mappings without saving}
                            {--force : Overwrite existing mappings}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Map employees to biometric device user IDs';

    protected $zktecoService;

    public function __construct(ZKTecoService $zktecoService)
    {
        parent::__construct();
        $this->zktecoService = $zktecoService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔗 Starting Employee-Device Mapping Process...');
        $this->newLine();

        $machineIp = $this->option('machine-ip') ?: config('zkteco.default_machine.ip');
        $autoMap = $this->option('auto-map');
        $dryRun = $this->option('dry-run');
        $force = $this->option('force');

        // Display options
        $this->table(['Option', 'Value'], [
            ['Machine IP', $machineIp],
            ['Auto Map', $autoMap ? 'Yes' : 'No'],
            ['Dry Run', $dryRun ? 'Yes' : 'No'],
            ['Force Overwrite', $force ? 'Yes' : 'No'],
        ]);

        if ($dryRun) {
            $this->warn('🔍 DRY RUN MODE: No changes will be saved');
        }

        $this->newLine();

        // Test connection and fetch device users
        $this->info('🔌 Connecting to biometric device...');
        $connectionTest = $this->zktecoService->testConnection($machineIp);

        if (!$connectionTest['success']) {
            $this->error('❌ Connection failed: ' . $connectionTest['message']);
            return 1;
        }

        $this->info('✅ Connected successfully');

        // Fetch users from device
        $this->info('👥 Fetching users from device...');

        // For now, we'll simulate this since we don't have real device
        $deviceUsers = $this->fetchDeviceUsers($machineIp);

        if (empty($deviceUsers)) {
            $this->warn('⚠️  No users found on device');
            return 0;
        }

        $this->info("✅ Found {" . count($deviceUsers) . "} users on device");
        $this->newLine();

        // Get employees from database
        $employees = Employee::where('status', 1)->get();
        $this->info("📋 Found {$employees->count()} active employees in database");
        $this->newLine();

        // Process mappings
        if ($autoMap) {
            $mappings = $this->autoMapEmployees($employees, $deviceUsers, $force);
        } else {
            $mappings = $this->interactiveMapEmployees($employees, $deviceUsers, $force);
        }

        // Apply mappings
        if (!empty($mappings)) {
            $this->applyMappings($mappings, $dryRun);
        } else {
            $this->warn('⚠️  No mappings to apply');
        }

        return 0;
    }

    /**
     * Fetch users from biometric device
     */
    protected function fetchDeviceUsers(string $machineIp): array
    {
        try {
            // This would use the actual ZKTeco adapter
            // For now, return mock data if in development mode
            if (config('zkteco.development.mock_data_enabled')) {
                return $this->generateMockDeviceUsers();
            }

            // In production, this would fetch real users from device
            return [];

        } catch (\Exception $e) {
            $this->error("Failed to fetch device users: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Generate mock device users for testing
     */
    protected function generateMockDeviceUsers(): array
    {
        return [
            ['user_id' => '001', 'name' => 'John Doe', 'card_number' => '12345'],
            ['user_id' => '002', 'name' => 'Jane Smith', 'card_number' => '12346'],
            ['user_id' => '003', 'name' => 'Mike Johnson', 'card_number' => '12347'],
            ['user_id' => '004', 'name' => 'Sarah Wilson', 'card_number' => '12348'],
            ['user_id' => '005', 'name' => 'David Brown', 'card_number' => '12349'],
            ['user_id' => '006', 'name' => 'Lisa Davis', 'card_number' => '12350'],
            ['user_id' => '007', 'name' => 'Tom Miller', 'card_number' => '12351'],
            ['user_id' => '008', 'name' => 'Amy Taylor', 'card_number' => '12352'],
        ];
    }

    /**
     * Auto-map employees by name matching
     */
    protected function autoMapEmployees($employees, $deviceUsers, $force): array
    {
        $this->info('🤖 Auto-mapping employees by name similarity...');

        $mappings = [];
        $mapped = 0;
        $skipped = 0;

        foreach ($employees as $employee) {
            // Skip if already mapped and not forcing
            if ($employee->machine_user_id && !$force) {
                $skipped++;
                continue;
            }

            // Find best match by name
            $bestMatch = $this->findBestNameMatch($employee->name, $deviceUsers);

            if ($bestMatch) {
                $mappings[] = [
                    'employee' => $employee,
                    'device_user' => $bestMatch,
                    'confidence' => $this->calculateNameSimilarity($employee->name, $bestMatch['name'])
                ];
                $mapped++;
            }
        }

        $this->info("✅ Auto-mapped {$mapped} employees, skipped {$skipped}");

        // Show mappings table
        if (!empty($mappings)) {
            $this->newLine();
            $this->info('📋 Proposed Mappings:');

            $tableData = [];
            foreach ($mappings as $mapping) {
                $tableData[] = [
                    $mapping['employee']->name,
                    $mapping['employee']->employee_id,
                    $mapping['device_user']['user_id'],
                    $mapping['device_user']['name'],
                    number_format($mapping['confidence'] * 100, 1) . '%'
                ];
            }

            $this->table([
                'Employee Name', 'Employee ID', 'Device User ID', 'Device Name', 'Confidence'
            ], $tableData);
        }

        return $mappings;
    }

    /**
     * Interactive mapping process
     */
    protected function interactiveMapEmployees($employees, $deviceUsers, $force): array
    {
        $this->info('👤 Starting interactive mapping process...');
        $this->line('You will be prompted to map each employee to a device user.');
        $this->newLine();

        $mappings = [];

        foreach ($employees as $employee) {
            // Skip if already mapped and not forcing
            if ($employee->machine_user_id && !$force) {
                $this->line("⏭️  Skipping {$employee->name} (already mapped to {$employee->machine_user_id})");
                continue;
            }

            $this->info("👤 Mapping employee: {$employee->name} ({$employee->employee_id})");

            // Show available device users
            $this->line('Available device users:');
            foreach ($deviceUsers as $index => $user) {
                $this->line("  {$index}: {$user['user_id']} - {$user['name']}");
            }
            $this->line("  s: Skip this employee");
            $this->line("  q: Quit mapping process");

            $choice = $this->ask('Select device user (number, s to skip, q to quit)');

            if ($choice === 'q') {
                break;
            }

            if ($choice === 's') {
                continue;
            }

            if (is_numeric($choice) && isset($deviceUsers[$choice])) {
                $mappings[] = [
                    'employee' => $employee,
                    'device_user' => $deviceUsers[$choice],
                    'confidence' => 1.0 // Manual mapping has 100% confidence
                ];
                $this->info("✅ Mapped {$employee->name} to {$deviceUsers[$choice]['user_id']}");
            } else {
                $this->error("❌ Invalid choice. Skipping {$employee->name}");
            }

            $this->newLine();
        }

        return $mappings;
    }

    /**
     * Apply mappings to database
     */
    protected function applyMappings(array $mappings, bool $dryRun): void
    {
        $this->info('💾 Applying mappings...');

        if ($dryRun) {
            $this->warn('🔍 DRY RUN: Showing what would be updated');
        }

        $updated = 0;

        DB::transaction(function() use ($mappings, $dryRun, &$updated) {
            foreach ($mappings as $mapping) {
                $employee = $mapping['employee'];
                $deviceUser = $mapping['device_user'];

                if (!$dryRun) {
                    $employee->update([
                        'machine_user_id' => $deviceUser['user_id']
                    ]);
                }

                $this->line("✅ {$employee->name} → Device User ID: {$deviceUser['user_id']}");
                $updated++;
            }
        });

        $this->newLine();

        if ($dryRun) {
            $this->info("🔍 Would update {$updated} employee records");
            $this->line("Run without --dry-run to apply changes");
        } else {
            $this->info("✅ Successfully updated {$updated} employee records");
        }
    }

    /**
     * Find best name match using similarity
     */
    protected function findBestNameMatch(string $employeeName, array $deviceUsers): ?array
    {
        $bestMatch = null;
        $bestSimilarity = 0;

        foreach ($deviceUsers as $user) {
            $similarity = $this->calculateNameSimilarity($employeeName, $user['name']);

            if ($similarity > $bestSimilarity && $similarity > 0.7) { // 70% threshold
                $bestSimilarity = $similarity;
                $bestMatch = $user;
            }
        }

        return $bestMatch;
    }

    /**
     * Calculate name similarity using Levenshtein distance
     */
    protected function calculateNameSimilarity(string $name1, string $name2): float
    {
        $name1 = strtolower(trim($name1));
        $name2 = strtolower(trim($name2));

        $maxLength = max(strlen($name1), strlen($name2));

        if ($maxLength === 0) {
            return 1.0;
        }

        $distance = levenshtein($name1, $name2);
        return 1 - ($distance / $maxLength);
    }
}
