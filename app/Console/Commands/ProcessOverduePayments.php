<?php

namespace App\Console\Commands;

use App\Services\ManualPaymentService;
use Illuminate\Console\Command;

class ProcessOverduePayments extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'subscription:process-overdue-payments {--dry-run : Show what would be processed without making changes}';

    /**
     * The console command description.
     */
    protected $description = 'Process overdue payments and send reminders';

    protected $manualPaymentService;

    /**
     * Create a new command instance.
     */
    public function __construct(ManualPaymentService $manualPaymentService)
    {
        parent::__construct();
        $this->manualPaymentService = $manualPaymentService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $isDryRun = $this->option('dry-run');
        
        if ($isDryRun) {
            $this->info("🔍 DRY RUN MODE - No changes will be made");
        }

        $this->info("Processing overdue payments...");

        if ($isDryRun) {
            // In dry run, just count what would be processed
            $overduePayments = \App\Models\SubscriptionManagement\SubscriptionPayment::where('status', 'pending')
                                                                                      ->where('created_at', '<', now()->subDays(7))
                                                                                      ->count();
            
            $this->info("Would process {$overduePayments} overdue payments");
            return 0;
        }

        $result = $this->manualPaymentService->processOverduePayments();

        $this->info("✅ Processing completed!");
        $this->info("   • Reminders sent: {$result['processed']}");
        $this->info("   • Total overdue: {$result['total_overdue']}");

        return 0;
    }
}
