<?php

namespace App\Console\Commands;

use App\Models\Employee;
use Illuminate\Console\Command;

class SetupBiometricTestData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'biometric:setup-test-data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Setup test data for biometric attendance system';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔧 Setting up biometric test data...');

        // Get first 5 active employees
        $employees = Employee::where('status', 1)->take(5)->get();

        if ($employees->isEmpty()) {
            $this->error('❌ No active employees found. Please create some employees first.');
            return 1;
        }

        $updated = 0;

        foreach ($employees as $index => $employee) {
            // Assign machine user ID if not already set
            if (!$employee->machine_user_id) {
                $machineUserId = str_pad($index + 1, 3, '0', STR_PAD_LEFT);

                $employee->update([
                    'machine_user_id' => $machineUserId,
                    'wage_type' => $index % 2 == 0 ? 'Monthly' : 'Daily', // Mix of wage types
                    'basic_salary' => $employee->salary ?? rand(15000, 50000),
                ]);

                $updated++;

                $this->line("✅ Updated {$employee->name} - Machine ID: {$machineUserId}, Wage: {$employee->wage_type}");
            } else {
                $this->line("⏭️  Skipped {$employee->name} - Already has Machine ID: {$employee->machine_user_id}");
            }
        }

        $this->newLine();
        $this->info("🎉 Setup completed! Updated {$updated} employees with biometric data.");
        $this->line("💡 You can now test the biometric sync with: php artisan attendance:import-zkt --dry-run");

        return 0;
    }
}
