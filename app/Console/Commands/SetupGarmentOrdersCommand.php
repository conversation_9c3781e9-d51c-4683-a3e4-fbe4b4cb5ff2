<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class SetupGarmentOrdersCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'garment-orders:setup {--force : Force setup even if already configured}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Set up Garments Order Management module with permissions and roles';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🚀 Setting up Garments Order Management Module...');
        $this->newLine();

        try {
            // Step 1: Create permissions
            $this->info('📋 Creating permissions...');
            $this->createPermissions();
            $this->info('✅ Permissions created successfully!');
            $this->newLine();

            // Step 2: Create roles
            $this->info('👥 Creating roles...');
            $this->createRoles();
            $this->info('✅ Roles created successfully!');
            $this->newLine();

            // Step 3: Create or update superadmin user
            $this->info('👤 Setting up superadmin user...');
            $this->setupSuperadminUser();
            $this->info('✅ Superadmin user configured successfully!');
            $this->newLine();

            // Step 4: Clear caches
            $this->info('🧹 Clearing caches...');
            $this->call('cache:clear');
            $this->call('config:clear');
            $this->call('route:clear');
            $this->info('✅ Caches cleared successfully!');
            $this->newLine();

            // Step 5: Verify setup
            $this->info('🔍 Verifying setup...');
            $this->verifySetup();
            $this->newLine();

            $this->info('🎉 Garments Order Management Module setup completed successfully!');
            $this->newLine();

            $this->table(['Setting', 'Value'], [
                ['Login Email', '<EMAIL>'],
                ['Login Password', 'password'],
                ['Module URL', config('app.url') . '/buyer-crm/garment-orders'],
            ]);

        } catch (\Exception $e) {
            $this->error('❌ Setup failed: ' . $e->getMessage());
            $this->error('File: ' . $e->getFile() . ' Line: ' . $e->getLine());
            return 1;
        }

        return 0;
    }

    /**
     * Create all required permissions.
     */
    private function createPermissions()
    {
        $permissions = [
            // Garment Orders
            'garment_orders.view',
            'garment_orders.create',
            'garment_orders.edit',
            'garment_orders.delete',
            'garment_orders.duplicate',
            'garment_orders.export',

            // Order Size Ratios
            'order_size_ratios.view',
            'order_size_ratios.create',
            'order_size_ratios.edit',
            'order_size_ratios.delete',
            'order_size_ratios.recalculate',
            'order_size_ratios.manual_override',

            // Order Tech Packs
            'order_tech_packs.view',
            'order_tech_packs.create',
            'order_tech_packs.edit',
            'order_tech_packs.delete',
            'order_tech_packs.download',
            'order_tech_packs.approve_samples',
            'order_tech_packs.reject_samples',
            'order_tech_packs.create_version',

            // Order BOM Items
            'order_bom_items.view',
            'order_bom_items.create',
            'order_bom_items.edit',
            'order_bom_items.delete',
            'order_bom_items.create_version',
            'order_bom_items.update_quantities',
            'order_bom_items.export',

            // Order Costing
            'order_costing.view',
            'order_costing.create',
            'order_costing.edit',
            'order_costing.delete',
            'order_costing.approve',
            'order_costing.reject',
            'order_costing.create_version',
            'order_costing.recalculate',
            'order_costing.compare_versions',

            // Order Delivery Batches
            'order_delivery_batches.view',
            'order_delivery_batches.create',
            'order_delivery_batches.edit',
            'order_delivery_batches.delete',
            'order_delivery_batches.update_status',
            'order_delivery_batches.approve_quality',
            'order_delivery_batches.manage_delays',

            // Order SOP Compliance
            'order_sop_compliance.view',
            'order_sop_compliance.create',
            'order_sop_compliance.edit',
            'order_sop_compliance.delete',
            'order_sop_compliance.verify',
            'order_sop_compliance.buyer_approve',
            'order_sop_compliance.mark_non_compliant',
            'order_sop_compliance.manage_alerts',

            // Order Production Checklist
            'order_production_checklist.view',
            'order_production_checklist.edit',
            'order_production_checklist.approve_production',
            'order_production_checklist.lock_production',
            'order_production_checklist.refresh_status',

            // Garment Orders Reports
            'garment_orders_reports.view',
            'garment_orders_reports.export',
            'garment_orders_reports.dashboard',
            'garment_orders_reports.analytics',
        ];

        $created = 0;
        $existing = 0;

        foreach ($permissions as $permission) {
            $perm = Permission::firstOrCreate(['name' => $permission]);
            if ($perm->wasRecentlyCreated) {
                $created++;
            } else {
                $existing++;
            }
        }

        $this->line("   Created: {$created} permissions");
        $this->line("   Existing: {$existing} permissions");
    }

    /**
     * Create roles and assign permissions.
     */
    private function createRoles()
    {
        // Create Super Admin role with all permissions
        $superAdminRole = Role::firstOrCreate(['name' => 'Super Admin']);
        $allPermissions = Permission::all();
        $superAdminRole->syncPermissions($allPermissions);
        $this->line("   ✅ Super Admin role configured with " . $allPermissions->count() . " permissions");

        // Create other roles with specific permissions
        $this->createGarmentOrderManagerRole();
        $this->createProductionManagerRole();
        $this->createMerchandiserRole();
        $this->createQualityManagerRole();
    }

    /**
     * Create Garment Order Manager role.
     */
    private function createGarmentOrderManagerRole()
    {
        $role = Role::firstOrCreate(['name' => 'Garment Order Manager']);
        
        $permissions = [
            'garment_orders.view', 'garment_orders.create', 'garment_orders.edit', 'garment_orders.duplicate', 'garment_orders.export',
            'order_size_ratios.view', 'order_size_ratios.create', 'order_size_ratios.edit', 'order_size_ratios.recalculate', 'order_size_ratios.manual_override',
            'order_tech_packs.view', 'order_tech_packs.create', 'order_tech_packs.edit', 'order_tech_packs.download', 'order_tech_packs.create_version',
            'order_bom_items.view', 'order_bom_items.create', 'order_bom_items.edit', 'order_bom_items.create_version', 'order_bom_items.update_quantities', 'order_bom_items.export',
            'order_costing.view', 'order_costing.create', 'order_costing.edit', 'order_costing.create_version', 'order_costing.recalculate', 'order_costing.compare_versions',
            'order_delivery_batches.view', 'order_delivery_batches.create', 'order_delivery_batches.edit', 'order_delivery_batches.update_status', 'order_delivery_batches.manage_delays',
            'order_sop_compliance.view', 'order_sop_compliance.create', 'order_sop_compliance.edit', 'order_sop_compliance.manage_alerts',
            'order_production_checklist.view', 'order_production_checklist.edit', 'order_production_checklist.refresh_status',
            'garment_orders_reports.view', 'garment_orders_reports.export', 'garment_orders_reports.dashboard',
        ];
        
        $role->syncPermissions($permissions);
        $this->line("   ✅ Garment Order Manager role configured");
    }

    /**
     * Create Production Manager role.
     */
    private function createProductionManagerRole()
    {
        $role = Role::firstOrCreate(['name' => 'Production Manager']);
        
        $permissions = [
            'garment_orders.view',
            'order_tech_packs.view', 'order_tech_packs.approve_samples', 'order_tech_packs.reject_samples',
            'order_bom_items.view', 'order_bom_items.update_quantities',
            'order_costing.view',
            'order_delivery_batches.view', 'order_delivery_batches.update_status', 'order_delivery_batches.approve_quality',
            'order_sop_compliance.view', 'order_sop_compliance.verify',
            'order_production_checklist.view', 'order_production_checklist.edit', 'order_production_checklist.approve_production', 'order_production_checklist.lock_production',
            'garment_orders_reports.view',
        ];
        
        $role->syncPermissions($permissions);
        $this->line("   ✅ Production Manager role configured");
    }

    /**
     * Create Merchandiser role.
     */
    private function createMerchandiserRole()
    {
        $role = Role::firstOrCreate(['name' => 'Merchandiser']);
        
        $permissions = [
            'garment_orders.view', 'garment_orders.create', 'garment_orders.edit',
            'order_size_ratios.view', 'order_size_ratios.create', 'order_size_ratios.edit',
            'order_tech_packs.view', 'order_tech_packs.create', 'order_tech_packs.download',
            'order_bom_items.view', 'order_bom_items.create', 'order_bom_items.edit',
            'order_costing.view', 'order_costing.create', 'order_costing.edit',
            'order_delivery_batches.view',
            'order_sop_compliance.view', 'order_sop_compliance.create', 'order_sop_compliance.edit',
            'order_production_checklist.view',
            'garment_orders_reports.view',
        ];
        
        $role->syncPermissions($permissions);
        $this->line("   ✅ Merchandiser role configured");
    }

    /**
     * Create Quality Manager role.
     */
    private function createQualityManagerRole()
    {
        $role = Role::firstOrCreate(['name' => 'Quality Manager']);
        
        $permissions = [
            'garment_orders.view',
            'order_tech_packs.view', 'order_tech_packs.approve_samples', 'order_tech_packs.reject_samples',
            'order_bom_items.view',
            'order_costing.view', 'order_costing.approve', 'order_costing.reject',
            'order_delivery_batches.view', 'order_delivery_batches.approve_quality',
            'order_sop_compliance.view', 'order_sop_compliance.verify', 'order_sop_compliance.buyer_approve', 'order_sop_compliance.mark_non_compliant',
            'order_production_checklist.view',
            'garment_orders_reports.view',
        ];
        
        $role->syncPermissions($permissions);
        $this->line("   ✅ Quality Manager role configured");
    }

    /**
     * Setup superadmin user.
     */
    private function setupSuperadminUser()
    {
        $user = User::where('email', '<EMAIL>')->first();
        
        if (!$user) {
            $user = User::create([
                'name' => 'Super Admin',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'email_verified_at' => now(),
            ]);
            $this->line("   ✅ Created superadmin user");
        } else {
            $this->line("   ✅ Superadmin user already exists");
        }

        // Assign Super Admin role
        $superAdminRole = Role::where('name', 'Super Admin')->first();
        if ($superAdminRole && !$user->hasRole('Super Admin')) {
            $user->assignRole($superAdminRole);
            $this->line("   ✅ Assigned Super Admin role to user");
        } else {
            $this->line("   ✅ User already has Super Admin role");
        }
    }

    /**
     * Verify the setup.
     */
    private function verifySetup()
    {
        $user = User::where('email', '<EMAIL>')->first();
        
        if (!$user) {
            $this->error("   ❌ Superadmin user not found");
            return;
        }

        $testPermissions = [
            'garment_orders.view',
            'order_tech_packs.view',
            'order_costing.view',
            'garment_orders_reports.view'
        ];

        $hasAllPermissions = true;
        foreach ($testPermissions as $permission) {
            if (!$user->can($permission)) {
                $this->error("   ❌ Missing permission: {$permission}");
                $hasAllPermissions = false;
            }
        }

        if ($hasAllPermissions) {
            $this->line("   ✅ All test permissions verified");
        }

        $roles = $user->getRoleNames();
        $this->line("   ✅ User roles: " . $roles->implode(', '));
    }
}
