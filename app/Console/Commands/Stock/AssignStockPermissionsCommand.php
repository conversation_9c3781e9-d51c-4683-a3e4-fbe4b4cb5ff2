<?php

namespace App\Console\Commands\Stock;

use Illuminate\Console\Command;
use App\Models\User;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class AssignStockPermissionsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'stock:assign-permissions 
                            {--role=* : Specific roles to assign permissions to}
                            {--user=* : Specific users to assign permissions to}
                            {--all-admins : Assign to all Super Admin and Admin users}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Assign stock management permissions to users and roles';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('Assigning Stock Management System permissions...');

        try {
            // Create permissions if they don't exist
            $this->createStockPermissions();

            // Assign to specific roles
            if ($this->option('role')) {
                $this->assignToRoles($this->option('role'));
            }

            // Assign to specific users
            if ($this->option('user')) {
                $this->assignToUsers($this->option('user'));
            }

            // Assign to all admin users
            if ($this->option('all-admins')) {
                $this->assignToAllAdmins();
            }

            // If no specific options, assign to Super Admin and Admin roles
            if (!$this->option('role') && !$this->option('user') && !$this->option('all-admins')) {
                $this->assignToDefaultRoles();
            }

            $this->info('Stock management permissions assigned successfully!');
            return Command::SUCCESS;

        } catch (\Exception $e) {
            $this->error('Failed to assign permissions: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }

    /**
     * Create stock management permissions
     */
    private function createStockPermissions(): void
    {
        $this->info('Creating stock management permissions...');

        $permissions = [
            // Main stock permissions
            'stock.view' => 'View stock items',
            'stock.create' => 'Create stock items',
            'stock.update' => 'Update stock items',
            'stock.delete' => 'Delete stock items',
            'stock.list' => 'List stock items',

            // Stock transactions
            'stock.transactions.view' => 'View stock transactions',
            'stock.transactions.create' => 'Create stock transactions',
            'stock.transactions.update' => 'Update stock transactions',
            'stock.transactions.delete' => 'Delete stock transactions',
            'stock.transactions.approve' => 'Approve stock transactions',
            'stock.transactions.list' => 'List stock transactions',

            // Stock transfers
            'stock.transfers.view' => 'View stock transfers',
            'stock.transfers.create' => 'Create stock transfers',
            'stock.transfers.update' => 'Update stock transfers',
            'stock.transfers.delete' => 'Delete stock transfers',
            'stock.transfers.approve' => 'Approve stock transfers',
            'stock.transfers.receive' => 'Receive stock transfers',
            'stock.transfers.list' => 'List stock transfers',

            // Physical counts
            'stock.counts.view' => 'View physical counts',
            'stock.counts.create' => 'Create physical counts',
            'stock.counts.update' => 'Update physical counts',
            'stock.counts.delete' => 'Delete physical counts',
            'stock.counts.approve' => 'Approve physical counts',
            'stock.counts.list' => 'List physical counts',

            // Stock alerts
            'stock.alerts.view' => 'View stock alerts',
            'stock.alerts.create' => 'Create stock alerts',
            'stock.alerts.update' => 'Update stock alerts',
            'stock.alerts.delete' => 'Delete stock alerts',
            'stock.alerts.manage' => 'Manage stock alerts',
            'stock.alerts.list' => 'List stock alerts',

            // Stock reports
            'stock.reports.view' => 'View stock reports',
            'stock.reports.create' => 'Create stock reports',
            'stock.reports.update' => 'Update stock reports',
            'stock.reports.delete' => 'Delete stock reports',
            'stock.reports.list' => 'List stock reports',

            // Stock categories
            'stock.categories.view' => 'View stock categories',
            'stock.categories.create' => 'Create stock categories',
            'stock.categories.update' => 'Update stock categories',
            'stock.categories.delete' => 'Delete stock categories',
            'stock.categories.list' => 'List stock categories',

            // Stock locations
            'stock.locations.view' => 'View stock locations',
            'stock.locations.create' => 'Create stock locations',
            'stock.locations.update' => 'Update stock locations',
            'stock.locations.delete' => 'Delete stock locations',
            'stock.locations.list' => 'List stock locations',

            // Stock items (alias for main stock permissions)
            'stock.items.view' => 'View stock items',
            'stock.items.create' => 'Create stock items',
            'stock.items.update' => 'Update stock items',
            'stock.items.delete' => 'Delete stock items',
            'stock.items.list' => 'List stock items',
        ];

        foreach ($permissions as $name => $description) {
            Permission::firstOrCreate(
                ['name' => $name],
                ['guard_name' => 'web']
            );
        }

        $this->info('Created ' . count($permissions) . ' stock management permissions.');
    }

    /**
     * Assign permissions to specific roles
     */
    private function assignToRoles(array $roleNames): void
    {
        foreach ($roleNames as $roleName) {
            $role = Role::where('name', $roleName)->first();
            
            if (!$role) {
                $this->warn("Role '{$roleName}' not found. Skipping...");
                continue;
            }

            $this->assignPermissionsToRole($role);
            $this->info("Assigned stock permissions to role: {$roleName}");
        }
    }

    /**
     * Assign permissions to specific users
     */
    private function assignToUsers(array $userIds): void
    {
        foreach ($userIds as $userId) {
            $user = User::find($userId);
            
            if (!$user) {
                $this->warn("User with ID '{$userId}' not found. Skipping...");
                continue;
            }

            $this->assignPermissionsToUser($user);
            $this->info("Assigned stock permissions to user: {$user->name} ({$user->email})");
        }
    }

    /**
     * Assign to all admin users
     */
    private function assignToAllAdmins(): void
    {
        $adminUsers = User::role(['Super Admin', 'Admin'])->get();
        
        foreach ($adminUsers as $user) {
            $this->assignPermissionsToUser($user);
        }

        $this->info("Assigned stock permissions to {$adminUsers->count()} admin users.");
    }

    /**
     * Assign to default roles (Super Admin and Admin)
     */
    private function assignToDefaultRoles(): void
    {
        $defaultRoles = ['Super Admin', 'Admin'];
        
        foreach ($defaultRoles as $roleName) {
            $role = Role::where('name', $roleName)->first();
            
            if ($role) {
                $this->assignPermissionsToRole($role);
                $this->info("Assigned stock permissions to role: {$roleName}");
            } else {
                $this->warn("Role '{$roleName}' not found. Skipping...");
            }
        }
    }

    /**
     * Assign all stock permissions to a role
     */
    private function assignPermissionsToRole(Role $role): void
    {
        $stockPermissions = Permission::where('name', 'like', 'stock.%')->get();
        
        foreach ($stockPermissions as $permission) {
            if (!$role->hasPermissionTo($permission)) {
                $role->givePermissionTo($permission);
            }
        }
    }

    /**
     * Assign all stock permissions to a user
     */
    private function assignPermissionsToUser(User $user): void
    {
        $stockPermissions = Permission::where('name', 'like', 'stock.%')->get();
        
        foreach ($stockPermissions as $permission) {
            if (!$user->hasPermissionTo($permission)) {
                $user->givePermissionTo($permission);
            }
        }
    }

    /**
     * Display current stock permissions
     */
    private function displayCurrentPermissions(): void
    {
        $this->info('Current Stock Management Permissions:');
        
        $stockPermissions = Permission::where('name', 'like', 'stock.%')
            ->orderBy('name')
            ->get();

        foreach ($stockPermissions as $permission) {
            $this->line("  - {$permission->name}");
        }

        $this->info('');
        $this->info('Roles with Stock Permissions:');
        
        $rolesWithStockPermissions = Role::whereHas('permissions', function ($query) {
            $query->where('name', 'like', 'stock.%');
        })->with('permissions')->get();

        foreach ($rolesWithStockPermissions as $role) {
            $stockPerms = $role->permissions->where('name', 'like', 'stock.%');
            $this->line("  - {$role->name}: {$stockPerms->count()} permissions");
        }
    }
}
