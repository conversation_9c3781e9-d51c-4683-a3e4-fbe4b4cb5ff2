<?php

namespace App\Console\Commands\Stock;

use Illuminate\Console\Command;
use App\Services\Stock\StockAlertService;

class CheckStockAlertsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'stock:check-alerts 
                            {--send-notifications : Send email notifications for critical alerts}
                            {--resolve-obsolete : Resolve alerts that are no longer relevant}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check stock levels and generate alerts for low stock, out of stock, and other conditions';

    /**
     * The stock alert service instance.
     */
    protected StockAlertService $stockAlertService;

    /**
     * Create a new command instance.
     */
    public function __construct(StockAlertService $stockAlertService)
    {
        parent::__construct();
        $this->stockAlertService = $stockAlertService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('Starting stock alert check...');

        try {
            // Check stock levels and create alerts
            $this->info('Checking stock levels...');
            $stockResults = $this->stockAlertService->checkStockLevels();
            $this->displayStockResults($stockResults);

            // Check expiry alerts if enabled
            $this->info('Checking expiry alerts...');
            $expiryResults = $this->stockAlertService->checkExpiryAlerts();
            $this->displayExpiryResults($expiryResults);

            // Send notifications if requested
            if ($this->option('send-notifications')) {
                $this->info('Sending alert notifications...');
                $notificationResults = $this->stockAlertService->sendAlertNotifications();
                $this->displayNotificationResults($notificationResults);
            }

            // Resolve obsolete alerts if requested
            if ($this->option('resolve-obsolete')) {
                $this->info('Resolving obsolete alerts...');
                $resolveResults = $this->stockAlertService->resolveObsoleteAlerts();
                $this->displayResolveResults($resolveResults);
            }

            // Display summary
            $this->displaySummary();

            $this->info('Stock alert check completed successfully.');
            return Command::SUCCESS;

        } catch (\Exception $e) {
            $this->error('Error during stock alert check: ' . $e->getMessage());
            $this->error('Stack trace: ' . $e->getTraceAsString());
            return Command::FAILURE;
        }
    }

    /**
     * Display stock level check results
     */
    private function displayStockResults(array $results): void
    {
        $this->line('Stock Level Check Results:');
        $this->line("  Low Stock Alerts: {$results['low_stock_alerts']}");
        $this->line("  Out of Stock Alerts: {$results['out_of_stock_alerts']}");
        $this->line("  Overstock Alerts: {$results['overstock_alerts']}");
        $this->line("  Negative Stock Alerts: {$results['negative_stock_alerts']}");

        if (!empty($results['errors'])) {
            $this->warn('Errors encountered:');
            foreach ($results['errors'] as $error) {
                $this->warn("  - {$error}");
            }
        }
    }

    /**
     * Display expiry check results
     */
    private function displayExpiryResults(array $results): void
    {
        $this->line('Expiry Check Results:');
        $this->line("  Expiry Warning Alerts: {$results['expiry_warning_alerts']}");
        $this->line("  Expiry Critical Alerts: {$results['expiry_critical_alerts']}");
        $this->line("  Expired Alerts: {$results['expired_alerts']}");

        if (!empty($results['errors'])) {
            $this->warn('Expiry check errors:');
            foreach ($results['errors'] as $error) {
                $this->warn("  - {$error}");
            }
        }
    }

    /**
     * Display notification results
     */
    private function displayNotificationResults(array $results): void
    {
        $this->line('Notification Results:');
        $this->line("  Emails Sent: {$results['emails_sent']}");
        $this->line("  Total Notifications: {$results['notifications_sent']}");

        if (!empty($results['errors'])) {
            $this->warn('Notification errors:');
            foreach ($results['errors'] as $error) {
                $this->warn("  - {$error}");
            }
        }
    }

    /**
     * Display resolve results
     */
    private function displayResolveResults(array $results): void
    {
        $this->line('Obsolete Alert Resolution Results:');
        $this->line("  Resolved Alerts: {$results['resolved_alerts']}");

        if (!empty($results['errors'])) {
            $this->warn('Resolution errors:');
            foreach ($results['errors'] as $error) {
                $this->warn("  - {$error}");
            }
        }
    }

    /**
     * Display overall summary
     */
    private function displaySummary(): void
    {
        $summary = $this->stockAlertService->getAlertSummary();

        $this->line('');
        $this->line('Current Alert Summary:');
        $this->line("  Total Active Alerts: {$summary['total_active']}");
        $this->line("  Critical: {$summary['critical']}");
        $this->line("  High Priority: {$summary['high']}");
        $this->line("  Medium Priority: {$summary['medium']}");
        $this->line("  Low Priority: {$summary['low']}");

        $this->line('');
        $this->line('Alerts by Type:');
        foreach ($summary['by_type'] as $type => $count) {
            $typeName = ucwords(str_replace('_', ' ', $type));
            $this->line("  {$typeName}: {$count}");
        }

        $this->line('');
        $this->line("Recent Alerts (last 7 days): {$summary['recent']}");
    }
}
