<?php

namespace App\Console\Commands\Stock;

use Illuminate\Console\Command;
use App\Models\User;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class DiagnoseStockMenuCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'stock:diagnose-menu 
                            {--user= : User ID or email to check}
                            {--fix : Automatically fix permission issues}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Diagnose why Stock Management System menu is not visible';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🔍 Stock Management System Menu Diagnostics');
        $this->info('============================================');
        $this->line('');

        // Get user to check
        $user = $this->getUserToCheck();
        if (!$user) {
            return Command::FAILURE;
        }

        $this->info("Checking user: {$user->name} ({$user->email})");
        $this->line('');

        // Run diagnostics
        $issues = [];
        
        // Check 1: Stock permissions exist
        $this->info('1. Checking if stock permissions exist...');
        $stockPermissions = Permission::where('name', 'like', 'stock.%')->get();
        if ($stockPermissions->isEmpty()) {
            $issues[] = 'No stock permissions found in database';
            $this->error('   ❌ No stock permissions found');
        } else {
            $this->info("   ✅ Found {$stockPermissions->count()} stock permissions");
        }

        // Check 2: User has stock permissions
        $this->info('2. Checking user stock permissions...');
        $userStockPermissions = $user->getAllPermissions()->filter(function ($permission) {
            return str_starts_with($permission->name, 'stock.');
        });

        if ($userStockPermissions->isEmpty()) {
            $issues[] = 'User has no stock permissions';
            $this->error('   ❌ User has no stock permissions');
        } else {
            $this->info("   ✅ User has {$userStockPermissions->count()} stock permissions");
            foreach ($userStockPermissions as $permission) {
                $this->line("      - {$permission->name}");
            }
        }

        // Check 3: User roles
        $this->info('3. Checking user roles...');
        $userRoles = $user->roles;
        if ($userRoles->isEmpty()) {
            $issues[] = 'User has no roles assigned';
            $this->error('   ❌ User has no roles assigned');
        } else {
            $this->info("   ✅ User has {$userRoles->count()} roles:");
            foreach ($userRoles as $role) {
                $roleStockPermissions = $role->permissions->filter(function ($permission) {
                    return str_starts_with($permission->name, 'stock.');
                });
                $this->line("      - {$role->name} ({$roleStockPermissions->count()} stock permissions)");
            }
        }

        // Check 4: Routes exist
        $this->info('4. Checking if stock routes are registered...');
        try {
            $stockRoutes = collect(\Route::getRoutes())->filter(function ($route) {
                return str_starts_with($route->getName() ?? '', 'stock.');
            });

            if ($stockRoutes->isEmpty()) {
                $issues[] = 'Stock routes not registered';
                $this->error('   ❌ No stock routes found');
            } else {
                $this->info("   ✅ Found {$stockRoutes->count()} stock routes");
            }
        } catch (\Exception $e) {
            $issues[] = 'Error checking routes: ' . $e->getMessage();
            $this->error('   ❌ Error checking routes');
        }

        // Check 5: Menu permissions
        $this->info('5. Checking menu visibility permissions...');
        $menuPermissions = ['stock.view', 'stock.transactions.view', 'stock.transfers.view', 'stock.counts.view', 'stock.reports.view', 'stock.alerts.view'];
        $hasMenuPermission = false;

        foreach ($menuPermissions as $permission) {
            if ($user->can($permission)) {
                $hasMenuPermission = true;
                $this->info("   ✅ User can: {$permission}");
            } else {
                $this->line("   ❌ User cannot: {$permission}");
            }
        }

        if (!$hasMenuPermission) {
            $issues[] = 'User has no menu visibility permissions';
            $this->error('   ❌ User has no permissions to see the menu');
        }

        // Check 6: Cache issues
        $this->info('6. Checking for cache issues...');
        try {
            // Try to clear permission cache
            app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();
            $this->info('   ✅ Permission cache cleared');
        } catch (\Exception $e) {
            $issues[] = 'Cache clearing failed: ' . $e->getMessage();
            $this->error('   ❌ Failed to clear permission cache');
        }

        // Summary
        $this->line('');
        $this->info('📋 Diagnosis Summary');
        $this->info('===================');

        if (empty($issues)) {
            $this->info('✅ No issues found! The menu should be visible.');
            $this->line('');
            $this->info('If you still don\'t see the menu:');
            $this->line('1. Clear your browser cache');
            $this->line('2. Log out and log back in');
            $this->line('3. Check browser console for JavaScript errors');
        } else {
            $this->error('❌ Issues found:');
            foreach ($issues as $issue) {
                $this->line("   - {$issue}");
            }

            if ($this->option('fix')) {
                $this->line('');
                $this->info('🔧 Attempting to fix issues...');
                $this->fixIssues($user, $issues);
            } else {
                $this->line('');
                $this->info('💡 To automatically fix these issues, run:');
                $this->line("   php artisan stock:diagnose-menu --user={$user->id} --fix");
            }
        }

        return empty($issues) ? Command::SUCCESS : Command::FAILURE;
    }

    /**
     * Get user to check
     */
    private function getUserToCheck(): ?User
    {
        $userInput = $this->option('user');

        if ($userInput) {
            // Try to find by ID first
            if (is_numeric($userInput)) {
                $user = User::find($userInput);
                if ($user) {
                    return $user;
                }
            }

            // Try to find by email
            $user = User::where('email', $userInput)->first();
            if ($user) {
                return $user;
            }

            $this->error("User not found: {$userInput}");
            return null;
        }

        // Show available users
        $users = User::all();
        if ($users->isEmpty()) {
            $this->error('No users found in the system');
            return null;
        }

        $this->info('Available users:');
        foreach ($users as $user) {
            $roles = $user->roles->pluck('name')->join(', ');
            $this->line("  {$user->id}: {$user->name} ({$user->email}) - Roles: {$roles}");
        }

        $userId = $this->ask('Enter user ID to check');
        return User::find($userId);
    }

    /**
     * Fix identified issues
     */
    private function fixIssues(User $user, array $issues): void
    {
        foreach ($issues as $issue) {
            if (str_contains($issue, 'No stock permissions found')) {
                $this->info('Creating stock permissions...');
                $this->call('stock:assign-permissions', ['--user' => [$user->id]]);
            }

            if (str_contains($issue, 'User has no stock permissions') || str_contains($issue, 'User has no roles assigned')) {
                $this->info('Assigning stock permissions to user...');
                
                // Check if user is admin
                if ($user->hasRole(['Super Admin', 'Admin'])) {
                    $this->call('stock:assign-permissions', ['--user' => [$user->id]]);
                } else {
                    // Assign basic permissions
                    $basicPermissions = [
                        'stock.view',
                        'stock.list',
                        'stock.transactions.view',
                        'stock.alerts.view',
                        'stock.reports.view'
                    ];

                    foreach ($basicPermissions as $permissionName) {
                        $permission = Permission::where('name', $permissionName)->first();
                        if ($permission && !$user->hasPermissionTo($permission)) {
                            $user->givePermissionTo($permission);
                            $this->info("   ✅ Granted: {$permissionName}");
                        }
                    }
                }
            }
        }

        $this->info('🎉 Fix attempts completed! Please check the menu again.');
    }
}
