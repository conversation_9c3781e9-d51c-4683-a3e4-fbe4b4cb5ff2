<?php

namespace App\Console\Commands\Stock;

use Illuminate\Console\Command;
use App\Services\Stock\StockReportService;
use Illuminate\Support\Facades\Storage;
use League\Csv\Writer;

class GenerateStockReportsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'stock:generate-reports 
                            {--type=all : Type of report to generate (summary, movement, valuation, abc, variance, all)}
                            {--format=csv : Output format (csv, json)}
                            {--output= : Output directory (default: storage/reports)}
                            {--date-from= : Start date for reports (YYYY-MM-DD)}
                            {--date-to= : End date for reports (YYYY-MM-DD)}
                            {--category= : Filter by category ID}
                            {--location= : Filter by location ID}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate stock management reports';

    /**
     * The stock report service instance.
     */
    protected StockReportService $stockReportService;

    /**
     * Create a new command instance.
     */
    public function __construct(StockReportService $stockReportService)
    {
        parent::__construct();
        $this->stockReportService = $stockReportService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $type = $this->option('type');
        $format = $this->option('format');
        $outputDir = $this->option('output') ?: 'reports/stock';
        
        // Prepare filters
        $filters = $this->prepareFilters();

        $this->info("Generating {$type} stock reports in {$format} format...");

        try {
            // Ensure output directory exists
            Storage::makeDirectory($outputDir);

            $reportTypes = $type === 'all' 
                ? ['summary', 'movement', 'valuation', 'abc', 'variance']
                : [$type];

            foreach ($reportTypes as $reportType) {
                $this->generateReport($reportType, $format, $outputDir, $filters);
            }

            $this->info('All reports generated successfully.');
            return Command::SUCCESS;

        } catch (\Exception $e) {
            $this->error('Report generation failed: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }

    /**
     * Prepare filters from command options
     */
    private function prepareFilters(): array
    {
        $filters = [];

        if ($this->option('date-from')) {
            $filters['date_from'] = $this->option('date-from');
        }

        if ($this->option('date-to')) {
            $filters['date_to'] = $this->option('date-to');
        }

        if ($this->option('category')) {
            $filters['category_id'] = $this->option('category');
        }

        if ($this->option('location')) {
            $filters['location_id'] = $this->option('location');
        }

        return $filters;
    }

    /**
     * Generate a specific report
     */
    private function generateReport(string $type, string $format, string $outputDir, array $filters): void
    {
        $this->info("Generating {$type} report...");

        $data = $this->getReportData($type, $filters);
        $filename = $this->generateFilename($type, $format);
        $filepath = "{$outputDir}/{$filename}";

        switch ($format) {
            case 'csv':
                $this->generateCsvReport($data, $filepath, $type);
                break;
            case 'json':
                $this->generateJsonReport($data, $filepath);
                break;
            default:
                throw new \InvalidArgumentException("Unsupported format: {$format}");
        }

        $this->info("Report saved: {$filepath}");
    }

    /**
     * Get report data based on type
     */
    private function getReportData(string $type, array $filters): array
    {
        switch ($type) {
            case 'summary':
                return $this->stockReportService->generateStockSummary($filters);
            case 'movement':
                return $this->stockReportService->generateStockMovement($filters);
            case 'valuation':
                return $this->stockReportService->generateStockValuation($filters);
            case 'abc':
                return $this->stockReportService->generateAbcAnalysis($filters);
            case 'variance':
                return $this->stockReportService->generateVarianceAnalysis($filters);
            default:
                throw new \InvalidArgumentException("Unsupported report type: {$type}");
        }
    }

    /**
     * Generate filename with timestamp
     */
    private function generateFilename(string $type, string $format): string
    {
        $timestamp = now()->format('Y-m-d_H-i-s');
        return "stock_{$type}_report_{$timestamp}.{$format}";
    }

    /**
     * Generate CSV report
     */
    private function generateCsvReport(array $data, string $filepath, string $type): void
    {
        $csv = Writer::createFromString('');
        
        switch ($type) {
            case 'summary':
                $this->generateSummaryCsv($csv, $data);
                break;
            case 'movement':
                $this->generateMovementCsv($csv, $data);
                break;
            case 'valuation':
                $this->generateValuationCsv($csv, $data);
                break;
            case 'abc':
                $this->generateAbcCsv($csv, $data);
                break;
            case 'variance':
                $this->generateVarianceCsv($csv, $data);
                break;
        }

        Storage::put($filepath, $csv->toString());
    }

    /**
     * Generate JSON report
     */
    private function generateJsonReport(array $data, string $filepath): void
    {
        $json = json_encode($data, JSON_PRETTY_PRINT);
        Storage::put($filepath, $json);
    }

    /**
     * Generate stock summary CSV
     */
    private function generateSummaryCsv(Writer $csv, array $data): void
    {
        // Header
        $csv->insertOne([
            'Item SKU',
            'Item Name',
            'Category',
            'Location',
            'Quantity',
            'Unit Cost',
            'Total Value',
            'Status'
        ]);

        // Data rows
        foreach ($data['data'] as $item) {
            $csv->insertOne([
                $item->item->sku,
                $item->item->name,
                $item->item->category->name ?? 'N/A',
                $item->location->name,
                number_format($item->quantity, 2),
                number_format($item->average_cost, 2),
                number_format($item->total_value, 2),
                ucwords(str_replace('_', ' ', $item->stock_status))
            ]);
        }
    }

    /**
     * Generate stock movement CSV
     */
    private function generateMovementCsv(Writer $csv, array $data): void
    {
        // Header
        $csv->insertOne([
            'Date',
            'Item SKU',
            'Item Name',
            'Location',
            'Transaction Type',
            'Quantity',
            'Unit Cost',
            'Total Cost',
            'Reference',
            'Created By'
        ]);

        // Data rows
        foreach ($data['data'] as $transaction) {
            $csv->insertOne([
                $transaction->transaction_date->format('Y-m-d'),
                $transaction->item->sku,
                $transaction->item->name,
                $transaction->location->name,
                $transaction->transaction_type_name,
                number_format($transaction->quantity, 2),
                number_format($transaction->unit_cost, 2),
                number_format($transaction->total_cost, 2),
                $transaction->reference_number ?? 'N/A',
                $transaction->creator->name ?? 'N/A'
            ]);
        }
    }

    /**
     * Generate stock valuation CSV
     */
    private function generateValuationCsv(Writer $csv, array $data): void
    {
        // Header
        $csv->insertOne([
            'Item SKU',
            'Item Name',
            'Category',
            'Location',
            'Quantity',
            'Average Cost',
            'Total Value',
            'Percentage of Total'
        ]);

        $totalValue = $data['summary']['total_value'];

        // Data rows
        foreach ($data['data'] as $item) {
            $percentage = $totalValue > 0 ? ($item->total_value / $totalValue) * 100 : 0;
            
            $csv->insertOne([
                $item->item->sku,
                $item->item->name,
                $item->item->category->name ?? 'N/A',
                $item->location->name,
                number_format($item->quantity, 2),
                number_format($item->average_cost, 2),
                number_format($item->total_value, 2),
                number_format($percentage, 2) . '%'
            ]);
        }
    }

    /**
     * Generate ABC analysis CSV
     */
    private function generateAbcCsv(Writer $csv, array $data): void
    {
        // Header
        $csv->insertOne([
            'Item SKU',
            'Item Name',
            'Category',
            'Total Consumption',
            'Consumption Value',
            'Percentage of Total',
            'Cumulative Percentage',
            'ABC Category',
            'Transaction Count'
        ]);

        // Data rows
        foreach ($data['data'] as $item) {
            $csv->insertOne([
                $item['item']->sku,
                $item['item']->name,
                $item['item']->category->name ?? 'N/A',
                number_format($item['total_consumption'], 2),
                number_format($item['total_consumption_value'], 2),
                number_format($item['percentage_of_total'], 2) . '%',
                number_format($item['cumulative_percentage'], 2) . '%',
                $item['abc_category'],
                $item['transaction_count']
            ]);
        }
    }

    /**
     * Generate variance analysis CSV
     */
    private function generateVarianceCsv(Writer $csv, array $data): void
    {
        // Header
        $csv->insertOne([
            'Count Date',
            'Item SKU',
            'Item Name',
            'Location',
            'System Quantity',
            'Physical Quantity',
            'Variance',
            'Variance Percentage',
            'Variance Value',
            'Counted By'
        ]);

        // Data rows
        foreach ($data['data'] as $count) {
            $csv->insertOne([
                $count->count_date->format('Y-m-d'),
                $count->item->sku,
                $count->item->name,
                $count->location->name,
                number_format($count->system_quantity, 2),
                number_format($count->physical_quantity, 2),
                number_format($count->variance, 2),
                number_format($count->variance_percentage, 2) . '%',
                number_format($count->variance_value, 2),
                $count->counter->name ?? 'N/A'
            ]);
        }
    }
}
