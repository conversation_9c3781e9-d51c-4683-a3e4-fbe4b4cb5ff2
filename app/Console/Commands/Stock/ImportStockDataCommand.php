<?php

namespace App\Console\Commands\Stock;

use Illuminate\Console\Command;
use App\Models\Stock\StockItem;
use App\Models\Stock\StockItemCategory;
use App\Models\Stock\StockLocation;
use App\Models\Stock\StockLocationItem;
use App\Models\Stock\StockTransaction;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use League\Csv\Reader;

class ImportStockDataCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'stock:import 
                            {file : Path to the CSV file to import}
                            {--type=items : Type of data to import (items, transactions, levels)}
                            {--dry-run : Preview the import without making changes}
                            {--batch-size=100 : Number of records to process in each batch}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import stock data from CSV files';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $filePath = $this->argument('file');
        $type = $this->option('type');
        $dryRun = $this->option('dry-run');
        $batchSize = (int) $this->option('batch-size');

        // Validate file exists
        if (!file_exists($filePath)) {
            $this->error("File not found: {$filePath}");
            return Command::FAILURE;
        }

        // Validate import type
        if (!in_array($type, ['items', 'transactions', 'levels'])) {
            $this->error("Invalid import type: {$type}. Must be one of: items, transactions, levels");
            return Command::FAILURE;
        }

        $this->info("Starting {$type} import from: {$filePath}");
        if ($dryRun) {
            $this->warn("DRY RUN MODE - No changes will be made");
        }

        try {
            $csv = Reader::createFromPath($filePath, 'r');
            $csv->setHeaderOffset(0);
            $records = $csv->getRecords();

            $totalRecords = iterator_count($csv->getRecords());
            $this->info("Found {$totalRecords} records to process");

            // Reset iterator
            $records = $csv->getRecords();

            switch ($type) {
                case 'items':
                    return $this->importItems($records, $dryRun, $batchSize);
                case 'transactions':
                    return $this->importTransactions($records, $dryRun, $batchSize);
                case 'levels':
                    return $this->importStockLevels($records, $dryRun, $batchSize);
                default:
                    $this->error("Unsupported import type: {$type}");
                    return Command::FAILURE;
            }

        } catch (\Exception $e) {
            $this->error("Import failed: " . $e->getMessage());
            return Command::FAILURE;
        }
    }

    /**
     * Import stock items
     */
    private function importItems($records, bool $dryRun, int $batchSize): int
    {
        $this->info("Importing stock items...");
        
        $successCount = 0;
        $errorCount = 0;
        $batch = [];
        $lineNumber = 1;

        foreach ($records as $record) {
            $lineNumber++;
            
            // Validate required fields
            $validator = Validator::make($record, [
                'name' => 'required|string|max:200',
                'sku' => 'nullable|string|max:50',
                'category_name' => 'required|string',
                'item_type' => 'required|in:fabric,accessory,wip,finished_goods',
                'unit_of_measure' => 'required|string|max:20',
                'current_quantity' => 'required|numeric|min:0',
                'reorder_level' => 'required|numeric|min:0',
                'cost_price' => 'required|numeric|min:0',
                'costing_method' => 'required|in:fifo,lifo,average',
            ]);

            if ($validator->fails()) {
                $this->warn("Line {$lineNumber}: Validation failed - " . implode(', ', $validator->errors()->all()));
                $errorCount++;
                continue;
            }

            try {
                // Find or create category
                $category = StockItemCategory::firstOrCreate(
                    ['name' => $record['category_name']],
                    [
                        'code' => StockItemCategory::generateCode($record['item_type']),
                        'type' => $record['item_type'],
                        'is_active' => true,
                    ]
                );

                $itemData = [
                    'category_id' => $category->id,
                    'name' => $record['name'],
                    'sku' => $record['sku'] ?: StockItem::generateSku($record['item_type'], $category->id),
                    'item_type' => $record['item_type'],
                    'description' => $record['description'] ?? null,
                    'color' => $record['color'] ?? null,
                    'size' => $record['size'] ?? null,
                    'style_number' => $record['style_number'] ?? null,
                    'season' => $record['season'] ?? null,
                    'quality_grade' => $record['quality_grade'] ?? null,
                    'unit_of_measure' => $record['unit_of_measure'],
                    'current_quantity' => $record['current_quantity'],
                    'reorder_level' => $record['reorder_level'],
                    'max_level' => $record['max_level'] ?? null,
                    'cost_price' => $record['cost_price'],
                    'average_cost' => $record['cost_price'],
                    'costing_method' => $record['costing_method'],
                    'track_batches' => isset($record['track_batches']) ? (bool) $record['track_batches'] : false,
                    'track_expiry' => isset($record['track_expiry']) ? (bool) $record['track_expiry'] : false,
                    'is_active' => true,
                    'created_by' => 1, // System user
                ];

                $batch[] = $itemData;

                if (count($batch) >= $batchSize) {
                    if (!$dryRun) {
                        $this->processBatch($batch, 'items');
                    }
                    $successCount += count($batch);
                    $this->info("Processed {$successCount} items...");
                    $batch = [];
                }

            } catch (\Exception $e) {
                $this->warn("Line {$lineNumber}: Error - " . $e->getMessage());
                $errorCount++;
            }
        }

        // Process remaining batch
        if (!empty($batch)) {
            if (!$dryRun) {
                $this->processBatch($batch, 'items');
            }
            $successCount += count($batch);
        }

        $this->info("Import completed:");
        $this->info("  Successful: {$successCount}");
        $this->info("  Errors: {$errorCount}");

        return $errorCount > 0 ? Command::FAILURE : Command::SUCCESS;
    }

    /**
     * Import stock transactions
     */
    private function importTransactions($records, bool $dryRun, int $batchSize): int
    {
        $this->info("Importing stock transactions...");
        
        $successCount = 0;
        $errorCount = 0;
        $lineNumber = 1;

        foreach ($records as $record) {
            $lineNumber++;
            
            // Validate required fields
            $validator = Validator::make($record, [
                'item_sku' => 'required|string',
                'location_name' => 'required|string',
                'transaction_type' => 'required|in:in,out,adjustment,production_in,production_out,return,damage,loss',
                'quantity' => 'required|numeric|min:0.0001',
                'unit_cost' => 'required|numeric|min:0',
                'transaction_date' => 'required|date',
            ]);

            if ($validator->fails()) {
                $this->warn("Line {$lineNumber}: Validation failed - " . implode(', ', $validator->errors()->all()));
                $errorCount++;
                continue;
            }

            try {
                // Find item and location
                $item = StockItem::where('sku', $record['item_sku'])->first();
                $location = StockLocation::where('name', $record['location_name'])->first();

                if (!$item) {
                    $this->warn("Line {$lineNumber}: Item not found with SKU: {$record['item_sku']}");
                    $errorCount++;
                    continue;
                }

                if (!$location) {
                    $this->warn("Line {$lineNumber}: Location not found: {$record['location_name']}");
                    $errorCount++;
                    continue;
                }

                if (!$dryRun) {
                    StockTransaction::create([
                        'item_id' => $item->id,
                        'location_id' => $location->id,
                        'transaction_type' => $record['transaction_type'],
                        'quantity' => $record['quantity'],
                        'unit_cost' => $record['unit_cost'],
                        'total_cost' => $record['quantity'] * $record['unit_cost'],
                        'batch_number' => $record['batch_number'] ?? null,
                        'lot_number' => $record['lot_number'] ?? null,
                        'reference_type' => $record['reference_type'] ?? 'import',
                        'reference_number' => $record['reference_number'] ?? null,
                        'transaction_date' => $record['transaction_date'],
                        'notes' => $record['notes'] ?? 'Imported from CSV',
                        'status' => 'posted',
                        'created_by' => 1, // System user
                        'approved_by' => 1,
                        'approved_at' => now(),
                    ]);
                }

                $successCount++;

                if ($successCount % $batchSize === 0) {
                    $this->info("Processed {$successCount} transactions...");
                }

            } catch (\Exception $e) {
                $this->warn("Line {$lineNumber}: Error - " . $e->getMessage());
                $errorCount++;
            }
        }

        $this->info("Import completed:");
        $this->info("  Successful: {$successCount}");
        $this->info("  Errors: {$errorCount}");

        return $errorCount > 0 ? Command::FAILURE : Command::SUCCESS;
    }

    /**
     * Import stock levels
     */
    private function importStockLevels($records, bool $dryRun, int $batchSize): int
    {
        $this->info("Importing stock levels...");
        
        $successCount = 0;
        $errorCount = 0;
        $lineNumber = 1;

        foreach ($records as $record) {
            $lineNumber++;
            
            // Validate required fields
            $validator = Validator::make($record, [
                'item_sku' => 'required|string',
                'location_name' => 'required|string',
                'quantity' => 'required|numeric|min:0',
                'average_cost' => 'required|numeric|min:0',
            ]);

            if ($validator->fails()) {
                $this->warn("Line {$lineNumber}: Validation failed - " . implode(', ', $validator->errors()->all()));
                $errorCount++;
                continue;
            }

            try {
                // Find item and location
                $item = StockItem::where('sku', $record['item_sku'])->first();
                $location = StockLocation::where('name', $record['location_name'])->first();

                if (!$item) {
                    $this->warn("Line {$lineNumber}: Item not found with SKU: {$record['item_sku']}");
                    $errorCount++;
                    continue;
                }

                if (!$location) {
                    $this->warn("Line {$lineNumber}: Location not found: {$record['location_name']}");
                    $errorCount++;
                    continue;
                }

                if (!$dryRun) {
                    StockLocationItem::updateOrCreate(
                        [
                            'item_id' => $item->id,
                            'location_id' => $location->id,
                        ],
                        [
                            'quantity' => $record['quantity'],
                            'reserved_quantity' => $record['reserved_quantity'] ?? 0,
                            'available_quantity' => $record['quantity'] - ($record['reserved_quantity'] ?? 0),
                            'damaged_quantity' => $record['damaged_quantity'] ?? 0,
                            'min_quantity' => $record['min_quantity'] ?? 0,
                            'max_quantity' => $record['max_quantity'] ?? 0,
                            'reorder_point' => $record['reorder_point'] ?? 0,
                            'average_cost' => $record['average_cost'],
                            'total_value' => $record['quantity'] * $record['average_cost'],
                        ]
                    );
                }

                $successCount++;

                if ($successCount % $batchSize === 0) {
                    $this->info("Processed {$successCount} stock levels...");
                }

            } catch (\Exception $e) {
                $this->warn("Line {$lineNumber}: Error - " . $e->getMessage());
                $errorCount++;
            }
        }

        $this->info("Import completed:");
        $this->info("  Successful: {$successCount}");
        $this->info("  Errors: {$errorCount}");

        return $errorCount > 0 ? Command::FAILURE : Command::SUCCESS;
    }

    /**
     * Process a batch of records
     */
    private function processBatch(array $batch, string $type): void
    {
        DB::transaction(function () use ($batch, $type) {
            switch ($type) {
                case 'items':
                    foreach ($batch as $itemData) {
                        StockItem::create($itemData);
                    }
                    break;
            }
        });
    }
}
