<?php

namespace App\Console\Commands\Stock;

use Illuminate\Console\Command;
use App\Models\Stock\StockItem;
use App\Models\Stock\StockLocationItem;
use App\Models\Stock\StockTransaction;
use App\Models\Stock\StockAlert;
use Illuminate\Support\Facades\DB;

class StockMaintenanceCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'stock:maintenance 
                            {--recalculate-levels : Recalculate stock levels from transactions}
                            {--update-costs : Update average costs based on recent transactions}
                            {--cleanup-alerts : Remove old resolved alerts}
                            {--cleanup-transactions : Archive old transactions}
                            {--all : Run all maintenance tasks}
                            {--days=90 : Number of days for cleanup operations}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Perform stock management maintenance tasks';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('Starting stock maintenance tasks...');

        $runAll = $this->option('all');
        $days = (int) $this->option('days');

        try {
            if ($runAll || $this->option('recalculate-levels')) {
                $this->recalculateStockLevels();
            }

            if ($runAll || $this->option('update-costs')) {
                $this->updateAverageCosts();
            }

            if ($runAll || $this->option('cleanup-alerts')) {
                $this->cleanupOldAlerts($days);
            }

            if ($runAll || $this->option('cleanup-transactions')) {
                $this->cleanupOldTransactions($days);
            }

            $this->info('Stock maintenance completed successfully.');
            return Command::SUCCESS;

        } catch (\Exception $e) {
            $this->error('Maintenance failed: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }

    /**
     * Recalculate stock levels from transactions
     */
    private function recalculateStockLevels(): void
    {
        $this->info('Recalculating stock levels...');

        $items = StockItem::active()->get();
        $progressBar = $this->output->createProgressBar($items->count());
        $progressBar->start();

        foreach ($items as $item) {
            try {
                DB::transaction(function () use ($item) {
                    // Get all posted transactions for this item
                    $transactions = StockTransaction::where('item_id', $item->id)
                        ->where('status', 'posted')
                        ->orderBy('transaction_date')
                        ->get();

                    // Reset all location items for this item
                    StockLocationItem::where('item_id', $item->id)->delete();

                    // Recalculate from transactions
                    foreach ($transactions as $transaction) {
                        $locationItem = StockLocationItem::firstOrCreate(
                            [
                                'item_id' => $item->id,
                                'location_id' => $transaction->location_id,
                            ],
                            [
                                'quantity' => 0,
                                'reserved_quantity' => 0,
                                'available_quantity' => 0,
                                'damaged_quantity' => 0,
                                'average_cost' => 0,
                                'total_value' => 0,
                            ]
                        );

                        // Apply transaction
                        if ($transaction->is_stock_in) {
                            $locationItem->addQuantity($transaction->quantity);
                            $locationItem->updateAverageCost($transaction->quantity, $transaction->unit_cost);
                        } else {
                            $locationItem->subtractQuantity($transaction->quantity);
                        }
                    }

                    // Update item totals
                    $totalQuantity = $item->locationItems()->sum('quantity');
                    $totalReserved = $item->locationItems()->sum('reserved_quantity');
                    $totalAvailable = $totalQuantity - $totalReserved;

                    $item->update([
                        'current_quantity' => $totalQuantity,
                        'reserved_quantity' => $totalReserved,
                        'available_quantity' => $totalAvailable,
                    ]);
                });

                $progressBar->advance();

            } catch (\Exception $e) {
                $this->warn("Error recalculating item {$item->sku}: " . $e->getMessage());
            }
        }

        $progressBar->finish();
        $this->line('');
        $this->info("Recalculated stock levels for {$items->count()} items.");
    }

    /**
     * Update average costs based on recent transactions
     */
    private function updateAverageCosts(): void
    {
        $this->info('Updating average costs...');

        $locationItems = StockLocationItem::where('quantity', '>', 0)->get();
        $progressBar = $this->output->createProgressBar($locationItems->count());
        $progressBar->start();

        foreach ($locationItems as $locationItem) {
            try {
                // Get recent stock-in transactions for this item/location
                $recentTransactions = StockTransaction::where('item_id', $locationItem->item_id)
                    ->where('location_id', $locationItem->location_id)
                    ->where('status', 'posted')
                    ->whereIn('transaction_type', ['in', 'transfer_in', 'production_in'])
                    ->where('transaction_date', '>=', now()->subDays(90))
                    ->orderBy('transaction_date', 'desc')
                    ->get();

                if ($recentTransactions->isNotEmpty()) {
                    $totalCost = $recentTransactions->sum('total_cost');
                    $totalQuantity = $recentTransactions->sum('quantity');

                    if ($totalQuantity > 0) {
                        $newAverageCost = $totalCost / $totalQuantity;
                        
                        $locationItem->update([
                            'average_cost' => $newAverageCost,
                            'total_value' => $locationItem->quantity * $newAverageCost,
                        ]);
                    }
                }

                $progressBar->advance();

            } catch (\Exception $e) {
                $this->warn("Error updating costs for location item {$locationItem->id}: " . $e->getMessage());
            }
        }

        $progressBar->finish();
        $this->line('');
        $this->info("Updated average costs for {$locationItems->count()} location items.");
    }

    /**
     * Clean up old resolved alerts
     */
    private function cleanupOldAlerts(int $days): void
    {
        $this->info("Cleaning up alerts older than {$days} days...");

        $cutoffDate = now()->subDays($days);

        $deletedCount = StockAlert::where('status', 'resolved')
            ->where('resolved_at', '<', $cutoffDate)
            ->delete();

        $this->info("Deleted {$deletedCount} old resolved alerts.");
    }

    /**
     * Clean up old transactions (archive or delete)
     */
    private function cleanupOldTransactions(int $days): void
    {
        $this->info("Cleaning up transactions older than {$days} days...");

        $cutoffDate = now()->subDays($days);

        // For now, we'll just count them. In a real implementation,
        // you might want to archive them to a separate table
        $oldTransactionCount = StockTransaction::where('transaction_date', '<', $cutoffDate)
            ->where('status', 'posted')
            ->count();

        $this->info("Found {$oldTransactionCount} old transactions that could be archived.");
        
        // Uncomment the following lines if you want to actually delete old transactions
        // WARNING: This will permanently delete transaction history
        /*
        $deletedCount = StockTransaction::where('transaction_date', '<', $cutoffDate)
            ->where('status', 'posted')
            ->delete();
        
        $this->info("Deleted {$deletedCount} old transactions.");
        */
    }

    /**
     * Validate stock data integrity
     */
    private function validateStockIntegrity(): void
    {
        $this->info('Validating stock data integrity...');

        $issues = [];

        // Check for items with negative stock
        $negativeStockItems = StockItem::where('current_quantity', '<', 0)->count();
        if ($negativeStockItems > 0) {
            $issues[] = "{$negativeStockItems} items have negative stock";
        }

        // Check for location items with negative stock
        $negativeLocationItems = StockLocationItem::where('quantity', '<', 0)->count();
        if ($negativeLocationItems > 0) {
            $issues[] = "{$negativeLocationItems} location items have negative stock";
        }

        // Check for mismatched totals
        $mismatchedItems = StockItem::whereRaw('current_quantity != (
            SELECT COALESCE(SUM(quantity), 0) 
            FROM stock_location_items 
            WHERE item_id = stock_items.id
        )')->count();
        
        if ($mismatchedItems > 0) {
            $issues[] = "{$mismatchedItems} items have mismatched total quantities";
        }

        // Check for orphaned location items
        $orphanedLocationItems = StockLocationItem::whereNotExists(function ($query) {
            $query->select(DB::raw(1))
                  ->from('stock_items')
                  ->whereRaw('stock_items.id = stock_location_items.item_id');
        })->count();

        if ($orphanedLocationItems > 0) {
            $issues[] = "{$orphanedLocationItems} orphaned location items found";
        }

        if (empty($issues)) {
            $this->info('Stock data integrity check passed.');
        } else {
            $this->warn('Stock data integrity issues found:');
            foreach ($issues as $issue) {
                $this->warn("  - {$issue}");
            }
        }
    }

    /**
     * Generate maintenance report
     */
    private function generateMaintenanceReport(): void
    {
        $this->info('Generating maintenance report...');

        $report = [
            'total_items' => StockItem::count(),
            'active_items' => StockItem::active()->count(),
            'total_locations' => StockLocationItem::distinct('location_id')->count(),
            'total_transactions' => StockTransaction::count(),
            'pending_transactions' => StockTransaction::where('status', 'pending')->count(),
            'active_alerts' => StockAlert::active()->count(),
            'critical_alerts' => StockAlert::active()->where('priority', 'critical')->count(),
            'low_stock_items' => StockItem::active()->lowStock()->count(),
            'out_of_stock_items' => StockItem::active()->outOfStock()->count(),
            'total_stock_value' => StockLocationItem::sum('total_value'),
        ];

        $this->line('');
        $this->line('=== STOCK MAINTENANCE REPORT ===');
        $this->line("Generated: " . now()->format('Y-m-d H:i:s'));
        $this->line('');
        $this->line('Inventory Summary:');
        $this->line("  Total Items: {$report['total_items']}");
        $this->line("  Active Items: {$report['active_items']}");
        $this->line("  Storage Locations: {$report['total_locations']}");
        $this->line("  Total Stock Value: $" . number_format($report['total_stock_value'], 2));
        $this->line('');
        $this->line('Transaction Summary:');
        $this->line("  Total Transactions: {$report['total_transactions']}");
        $this->line("  Pending Transactions: {$report['pending_transactions']}");
        $this->line('');
        $this->line('Alert Summary:');
        $this->line("  Active Alerts: {$report['active_alerts']}");
        $this->line("  Critical Alerts: {$report['critical_alerts']}");
        $this->line('');
        $this->line('Stock Status:');
        $this->line("  Low Stock Items: {$report['low_stock_items']}");
        $this->line("  Out of Stock Items: {$report['out_of_stock_items']}");
        $this->line('');
    }
}
