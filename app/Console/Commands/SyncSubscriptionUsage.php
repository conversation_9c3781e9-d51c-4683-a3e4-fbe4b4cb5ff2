<?php

namespace App\Console\Commands;

use App\Services\UsageTrackingService;
use Illuminate\Console\Command;

class SyncSubscriptionUsage extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'subscription:sync-usage {--subscription-id= : Sync usage for a specific subscription}';

    /**
     * The console command description.
     */
    protected $description = 'Sync subscription usage statistics with actual data';

    protected $usageTrackingService;

    /**
     * Create a new command instance.
     */
    public function __construct(UsageTrackingService $usageTrackingService)
    {
        parent::__construct();
        $this->usageTrackingService = $usageTrackingService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $subscriptionId = $this->option('subscription-id');

        if ($subscriptionId) {
            return $this->syncSpecificSubscription($subscriptionId);
        }

        return $this->syncAllSubscriptions();
    }

    /**
     * Sync usage for a specific subscription.
     */
    protected function syncSpecificSubscription(int $subscriptionId): int
    {
        $subscription = \App\Models\SubscriptionManagement\UserSubscription::find($subscriptionId);

        if (!$subscription) {
            $this->error("Subscription with ID {$subscriptionId} not found.");
            return 1;
        }

        $this->info("Syncing usage for subscription ID: {$subscriptionId}");
        
        try {
            $this->usageTrackingService->syncUsageForSubscription($subscription);
            $this->info("✅ Usage synced successfully for subscription {$subscriptionId}");
            return 0;
        } catch (\Exception $e) {
            $this->error("❌ Failed to sync usage for subscription {$subscriptionId}: " . $e->getMessage());
            return 1;
        }
    }

    /**
     * Sync usage for all active subscriptions.
     */
    protected function syncAllSubscriptions(): int
    {
        $this->info("Syncing usage for all active subscriptions...");
        
        $totalSubscriptions = \App\Models\SubscriptionManagement\UserSubscription::active()->count();
        
        if ($totalSubscriptions === 0) {
            $this->info("No active subscriptions found.");
            return 0;
        }

        $this->info("Found {$totalSubscriptions} active subscriptions to sync.");
        
        $progressBar = $this->output->createProgressBar($totalSubscriptions);
        $progressBar->start();

        $synced = 0;
        $failed = 0;

        try {
            \App\Models\SubscriptionManagement\UserSubscription::active()->chunk(50, function ($subscriptions) use ($progressBar, &$synced, &$failed) {
                foreach ($subscriptions as $subscription) {
                    try {
                        $this->usageTrackingService->syncUsageForSubscription($subscription);
                        $synced++;
                    } catch (\Exception $e) {
                        $failed++;
                        $this->newLine();
                        $this->error("Failed to sync subscription {$subscription->id}: " . $e->getMessage());
                    }
                    
                    $progressBar->advance();
                }
            });

            $progressBar->finish();
            $this->newLine();
            
            $this->info("✅ Sync completed!");
            $this->info("   • Successfully synced: {$synced}");
            
            if ($failed > 0) {
                $this->warn("   • Failed: {$failed}");
            }

            return $failed > 0 ? 1 : 0;

        } catch (\Exception $e) {
            $progressBar->finish();
            $this->newLine();
            $this->error("❌ Sync failed: " . $e->getMessage());
            return 1;
        }
    }
}
