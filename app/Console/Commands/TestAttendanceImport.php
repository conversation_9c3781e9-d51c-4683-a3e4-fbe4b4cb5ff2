<?php

namespace App\Console\Commands;

use App\Imports\AttendanceImport;
use App\Models\Employee;
use Illuminate\Console\Command;
use Maatwebsite\Excel\Facades\Excel;

class TestAttendanceImport extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'attendance:test-import {file?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test attendance import functionality';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $filePath = $this->argument('file') ?? storage_path('app/sample_attendance.csv');

        if (!file_exists($filePath)) {
            $this->error("File not found: {$filePath}");
            return 1;
        }

        $this->info("Testing attendance import with file: {$filePath}");

        // Check if we have employees with machine_user_id
        $employeesWithMachineId = Employee::whereNotNull('machine_user_id')->count();
        $this->info("Employees with machine_user_id: {$employeesWithMachineId}");

        if ($employeesWithMachineId === 0) {
            $this->warn("No employees found with machine_user_id. Creating test employee...");
            $employee = Employee::first();
            if ($employee) {
                $employee->update(['machine_user_id' => '001']);
                $this->info("Updated employee '{$employee->name}' with machine_user_id: 001");
            }
        }

        try {
            $import = new AttendanceImport();
            Excel::import($import, $filePath);

            $results = $import->getResults();

            $this->info("Import completed successfully!");
            $this->table(
                ['Metric', 'Value'],
                [
                    ['Total Rows', $results['total_rows']],
                    ['Processed Employees', $results['processed_employees']],
                    ['Inserted Records', $results['inserted_records']],
                    ['Updated Records', $results['updated_records']],
                    ['Errors', count($results['errors'])],
                ]
            );

            if (!empty($results['errors'])) {
                $this->error("Errors encountered:");
                foreach ($results['errors'] as $error) {
                    $this->line("  - {$error}");
                }
            }

            if (!empty($results['attendance_data'])) {
                $this->info("Attendance Records:");
                $this->table(
                    ['Employee', 'Date', 'In Time', 'Out Time', 'Hours', 'Status', 'Action'],
                    collect($results['attendance_data'])->map(function ($record) {
                        return [
                            $record['employee_name'],
                            $record['date'],
                            $record['in_time'] ?? '-',
                            $record['out_time'] ?? '-',
                            $record['working_hours'] ?? '-',
                            $record['status'],
                            $record['action']
                        ];
                    })->toArray()
                );
            }

            return 0;

        } catch (\Exception $e) {
            $this->error("Import failed: " . $e->getMessage());
            return 1;
        }
    }
}
