<?php

namespace App\Console\Commands;

use App\Services\ZKTecoService;
use App\Models\Employee;
use Illuminate\Console\Command;

class TestEmployeeMapping extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:employee-mapping';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test employee mapping functionality';

    protected $zktecoService;

    public function __construct(ZKTecoService $zktecoService)
    {
        parent::__construct();
        $this->zktecoService = $zktecoService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🧪 Testing Employee Mapping Functionality...');
        $this->newLine();

        // Test 1: Check employee counts
        $totalEmployees = Employee::where('status', 1)->count();
        $mappedEmployees = Employee::whereNotNull('machine_user_id')->where('status', 1)->count();
        $unmappedEmployees = $totalEmployees - $mappedEmployees;

        $this->info("📊 Employee Statistics:");
        $this->line("   Total Active: {$totalEmployees}");
        $this->line("   Mapped: {$mappedEmployees}");
        $this->line("   Unmapped: {$unmappedEmployees}");
        $this->newLine();

        // Test 2: Test device connection
        $this->info('🔌 Testing Device Connection...');
        $machineIp = config('zkteco.default_machine.ip');
        $connectionTest = $this->zktecoService->testConnection($machineIp);

        if ($connectionTest['success']) {
            $this->info("✅ Connection successful to {$machineIp}");
        } else {
            $this->error("❌ Connection failed: " . $connectionTest['message']);
        }
        $this->newLine();

        // Test 3: Fetch device users
        $this->info('👥 Fetching Device Users...');
        try {
            $adapter = $this->zktecoService->getAdapter();
            $connected = $adapter->connect($machineIp);

            if ($connected) {
                $deviceUsers = $adapter->getUsers();
                $adapter->disconnect();

                $this->info("✅ Found {" . count($deviceUsers) . "} users on device:");
                foreach ($deviceUsers as $user) {
                    $this->line("   {$user['user_id']} - {$user['name']}");
                }
            } else {
                $this->error("❌ Failed to connect to device");
            }
        } catch (\Exception $e) {
            $this->error("❌ Error fetching users: " . $e->getMessage());
        }
        $this->newLine();

        // Test 4: Show current mappings
        $this->info('🔗 Current Employee Mappings:');
        $mappedEmployees = Employee::whereNotNull('machine_user_id')
            ->where('status', 1)
            ->get(['name', 'machine_user_id']);

        if ($mappedEmployees->count() > 0) {
            foreach ($mappedEmployees as $employee) {
                $this->line("   {$employee->name} → {$employee->machine_user_id}");
            }
        } else {
            $this->warn("   No employees currently mapped");
        }
        $this->newLine();

        // Test 5: Show unmapped employees (first 10)
        $this->info('⚠️  Unmapped Employees (first 10):');
        $unmappedEmployees = Employee::whereNull('machine_user_id')
            ->where('status', 1)
            ->take(10)
            ->get(['name', 'employee_id']);

        foreach ($unmappedEmployees as $employee) {
            $this->line("   {$employee->name} ({$employee->employee_id})");
        }

        if ($unmappedEmployees->count() === 10) {
            $remaining = Employee::whereNull('machine_user_id')->where('status', 1)->count() - 10;
            if ($remaining > 0) {
                $this->line("   ... and {$remaining} more");
            }
        }
        $this->newLine();

        $this->info('✅ Employee mapping test completed!');
        $this->line('💡 Use the web interface at /hr/settings/zkteco/employee-mapping to map employees');

        return 0;
    }
}
