<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class TestNetworkConnectivity extends Command
{
    protected $signature = 'network:test-zkteco {--ip=*************} {--port=4370}';
    protected $description = 'Test network connectivity to ZKTeco device';

    public function handle()
    {
        $ip = $this->option('ip');
        $port = $this->option('port');

        $this->info("🌐 Testing Network Connectivity to ZKTeco Device");
        $this->info("==============================================");
        $this->newLine();

        $this->info("Target: $ip:$port");
        $this->newLine();

        // Get server information
        $this->info("📡 Server Network Information:");
        $serverIP = $this->getServerIP();
        if ($serverIP) {
            $this->line("Server IP: $serverIP");
            
            // Check if same network
            $serverNetwork = substr($serverIP, 0, strrpos($serverIP, '.'));
            $deviceNetwork = substr($ip, 0, strrpos($ip, '.'));
            
            $this->line("Server Network: $serverNetwork.x");
            $this->line("Device Network: $deviceNetwork.x");
            
            $sameNetwork = ($serverNetwork === $deviceNetwork);
            $this->line("Same Network: " . ($sameNetwork ? "✅ YES" : "❌ NO"));
            
            if (!$sameNetwork) {
                $this->warn("⚠️  Server and device are on different networks!");
                $this->line("   This requires routing configuration by hosting provider.");
            }
        }
        $this->newLine();

        // Test 1: Ping
        $this->info("🏓 Ping Test:");
        $pingResult = $this->testPing($ip);
        $this->line("Result: " . ($pingResult ? "✅ SUCCESS" : "❌ FAILED"));
        
        if (!$pingResult) {
            $this->error("❌ Device is not reachable from server");
            $this->line("   This indicates a network routing issue.");
        }
        $this->newLine();

        // Test 2: TCP Port
        $this->info("🔌 TCP Port Test:");
        $tcpResult = $this->testTCPPort($ip, $port);
        $this->line("Result: " . ($tcpResult['success'] ? "✅ OPEN" : "❌ CLOSED"));
        
        if (!$tcpResult['success']) {
            $this->error("❌ Port $port is not accessible");
            $this->line("   Error: " . $tcpResult['error']);
        }
        $this->newLine();

        // Test 3: UDP Port
        $this->info("📡 UDP Port Test:");
        $udpResult = $this->testUDPPort($ip, $port);
        $this->line("Result: " . ($udpResult['success'] ? "✅ ACCESSIBLE" : "❌ BLOCKED"));
        
        if (!$udpResult['success']) {
            $this->error("❌ UDP communication blocked");
            $this->line("   Error: " . $udpResult['error']);
        }
        $this->newLine();

        // Test 4: ZKTeco Protocol
        if ($udpResult['success']) {
            $this->info("🎯 ZKTeco Protocol Test:");
            $zkResult = $this->testZKTecoProtocol($ip, $port);
            $this->line("Result: " . ($zkResult['success'] ? "✅ RESPONDING" : "❌ NO RESPONSE"));
            
            if ($zkResult['success']) {
                $this->line("Response: " . $zkResult['response']);
            } else {
                $this->line("Error: " . $zkResult['error']);
            }
        }
        $this->newLine();

        // Summary and recommendations
        $this->info("📋 Summary and Recommendations:");
        
        if (!$pingResult) {
            $this->error("🚨 CRITICAL: No network connectivity to device");
            $this->line("   Action: Contact hosting provider for network routing");
            $this->line("   Request: Enable routing to 192.168.0.x subnet");
        } elseif (!$tcpResult['success'] && !$udpResult['success']) {
            $this->error("🚨 CRITICAL: Port 4370 is blocked");
            $this->line("   Action: Contact hosting provider for firewall configuration");
            $this->line("   Request: Allow outbound TCP/UDP port 4370");
        } elseif (!$udpResult['success']) {
            $this->warn("⚠️  WARNING: UDP communication blocked");
            $this->line("   ZKTeco devices primarily use UDP protocol");
            $this->line("   Action: Request UDP port 4370 to be unblocked");
        } else {
            $this->info("✅ Network connectivity appears functional");
            $this->line("   If ZKTeco still fails, check application configuration");
        }

        $this->newLine();
        $this->info("📞 Next Steps:");
        $this->line("1. Save this output for hosting provider");
        $this->line("2. Use hosting provider request template");
        $this->line("3. Request specific network configuration changes");

        return 0;
    }

    private function getServerIP()
    {
        if (function_exists('socket_create')) {
            $socket = @socket_create(AF_INET, SOCK_DGRAM, SOL_UDP);
            if ($socket && @socket_connect($socket, "*******", 53)) {
                socket_getsockname($socket, $localIP);
                socket_close($socket);
                return $localIP;
            }
        }
        return null;
    }

    private function testPing($ip)
    {
        $pingCommand = PHP_OS_FAMILY === 'Windows' ? "ping -n 1 $ip" : "ping -c 1 -W 3 $ip";
        $output = shell_exec("$pingCommand 2>/dev/null");
        
        return $output && (strpos($output, '1 received') !== false || 
                          strpos($output, 'TTL=') !== false ||
                          strpos($output, '0% packet loss') !== false);
    }

    private function testTCPPort($ip, $port)
    {
        if (function_exists('socket_create')) {
            $socket = @socket_create(AF_INET, SOCK_STREAM, SOL_TCP);
            if (!$socket) {
                return ['success' => false, 'error' => 'Failed to create socket'];
            }
            
            socket_set_option($socket, SOL_SOCKET, SO_RCVTIMEO, array('sec' => 5, 'usec' => 0));
            socket_set_option($socket, SOL_SOCKET, SO_SNDTIMEO, array('sec' => 5, 'usec' => 0));
            
            $result = @socket_connect($socket, $ip, $port);
            
            if ($result) {
                socket_close($socket);
                return ['success' => true];
            } else {
                $error = socket_strerror(socket_last_error($socket));
                socket_close($socket);
                return ['success' => false, 'error' => $error];
            }
        } else {
            $socket = @fsockopen($ip, $port, $errno, $errstr, 5);
            if ($socket) {
                fclose($socket);
                return ['success' => true];
            } else {
                return ['success' => false, 'error' => "$errstr ($errno)"];
            }
        }
    }

    private function testUDPPort($ip, $port)
    {
        if (!function_exists('socket_create')) {
            return ['success' => false, 'error' => 'Socket functions not available'];
        }
        
        $socket = @socket_create(AF_INET, SOCK_DGRAM, SOL_UDP);
        if (!$socket) {
            return ['success' => false, 'error' => 'Failed to create UDP socket'];
        }
        
        socket_set_option($socket, SOL_SOCKET, SO_RCVTIMEO, array('sec' => 3, 'usec' => 0));
        socket_set_option($socket, SOL_SOCKET, SO_SNDTIMEO, array('sec' => 3, 'usec' => 0));
        
        $testData = "TEST";
        $result = @socket_sendto($socket, $testData, strlen($testData), 0, $ip, $port);
        
        socket_close($socket);
        
        if ($result === false) {
            return ['success' => false, 'error' => 'UDP send failed'];
        }
        
        return ['success' => true];
    }

    private function testZKTecoProtocol($ip, $port)
    {
        if (!function_exists('socket_create')) {
            return ['success' => false, 'error' => 'Socket functions not available'];
        }
        
        $socket = @socket_create(AF_INET, SOCK_DGRAM, SOL_UDP);
        if (!$socket) {
            return ['success' => false, 'error' => 'Failed to create socket'];
        }
        
        socket_set_option($socket, SOL_SOCKET, SO_RCVTIMEO, array('sec' => 5, 'usec' => 0));
        socket_set_option($socket, SOL_SOCKET, SO_SNDTIMEO, array('sec' => 5, 'usec' => 0));
        
        // ZKTeco connect command
        $command = pack('VVVV', 1000, 0, 0, 65534);
        $result = @socket_sendto($socket, $command, strlen($command), 0, $ip, $port);
        
        if ($result === false) {
            $error = socket_strerror(socket_last_error($socket));
            socket_close($socket);
            return ['success' => false, 'error' => "Send failed: $error"];
        }
        
        // Try to receive response
        $response = '';
        $from = '';
        $port_from = 0;
        $recv_result = @socket_recvfrom($socket, $response, 1024, 0, $from, $port_from);
        
        socket_close($socket);
        
        if ($recv_result === false) {
            return ['success' => false, 'error' => 'No response from device'];
        }
        
        return [
            'success' => true, 
            'response' => "Received $recv_result bytes from $from:$port_from"
        ];
    }
}
