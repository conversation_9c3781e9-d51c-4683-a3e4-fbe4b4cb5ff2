<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\ZKTecoService;
use Exception;

class TestZKTecoProduction extends Command
{
    protected $signature = 'zkteco:test-production {--ip=*************} {--port=4370} {--verbose} {--debug}';
    protected $description = 'Test ZKTeco connection in production environment';

    public function handle()
    {
        $ip = $this->option('ip');
        $port = $this->option('port');
        $verbose = $this->option('verbose');
        $debug = $this->option('debug');

        $this->info("🧪 Testing ZKTeco Production Connection");
        $this->info("=====================================");
        $this->newLine();

        // Environment Information
        $this->info("🖥️  Environment Information:");
        $this->line("PHP Version: " . PHP_VERSION);
        $this->line("OS: " . PHP_OS);
        $this->line("User: " . get_current_user());
        
        if (function_exists('posix_getuid')) {
            $this->line("UID: " . posix_getuid());
            $this->line("GID: " . posix_getgid());
        }
        $this->newLine();

        // Check Extensions
        $this->info("🔌 PHP Extensions:");
        $extensions = ['sockets', 'curl', 'openssl'];
        foreach ($extensions as $ext) {
            $status = extension_loaded($ext) ? '✅ ENABLED' : '❌ DISABLED';
            $this->line("$ext: $status");
        }
        $this->newLine();

        // Test Socket Functions
        $this->info("🔧 Socket Functions:");
        $functions = ['socket_create', 'socket_sendto', 'socket_recvfrom'];
        foreach ($functions as $func) {
            $status = function_exists($func) ? '✅ AVAILABLE' : '❌ MISSING';
            $this->line("$func: $status");
        }
        $this->newLine();

        if (!extension_loaded('sockets')) {
            $this->error("❌ Sockets extension not available!");
            $this->line("This explains why you're getting socket errors.");
            return 1;
        }

        // Test Basic Connectivity
        $this->info("🌐 Testing Basic Connectivity:");
        $this->testPing($ip);
        $this->newLine();

        // Test Socket Operations
        $this->info("🔌 Testing Socket Operations:");
        $socketResult = $this->testSocketOperations($ip, $port, $verbose);
        $this->newLine();

        // Test ZKTeco Service
        $this->info("🎯 Testing ZKTeco Service:");
        $serviceResult = $this->testZKTecoService($ip, $verbose);
        $this->newLine();

        // Debug mode - additional tests
        if ($debug) {
            $this->info("🐛 Debug Mode - Additional Tests:");
            $this->testNetworkConfiguration($ip);
            $this->testAlternativeConnections($ip);
            $this->testZKTecoConfiguration();
            $this->newLine();
        }

        // Summary
        $this->info("📋 Test Summary:");
        $this->line("Socket Operations: " . ($socketResult ? '✅ SUCCESS' : '❌ FAILED'));
        $this->line("ZKTeco Service: " . ($serviceResult ? '✅ SUCCESS' : '❌ FAILED'));
        
        if (!$socketResult) {
            $this->newLine();
            $this->error("❌ Socket operations are blocked on this server!");
            $this->showSolutions();
        }

        return $socketResult && $serviceResult ? 0 : 1;
    }

    private function testPing($ip)
    {
        $this->line("Testing ping to $ip...");
        
        $pingCommand = PHP_OS_FAMILY === 'Windows' ? "ping -n 1 $ip" : "ping -c 1 -W 1 $ip";
        $output = shell_exec("$pingCommand 2>/dev/null");
        
        if ($output && (strpos($output, '1 received') !== false || strpos($output, 'TTL=') !== false)) {
            $this->line("✅ Ping successful - device is reachable");
            return true;
        } else {
            $this->line("❌ Ping failed - device unreachable or network issue");
            return false;
        }
    }

    private function testSocketOperations($ip, $port, $verbose)
    {
        $this->line("Testing socket operations to $ip:$port...");

        try {
            // Create socket
            $socket = socket_create(AF_INET, SOCK_DGRAM, SOL_UDP);
            if (!$socket) {
                $error = socket_strerror(socket_last_error());
                $this->line("❌ Failed to create socket: $error");
                return false;
            }

            if ($verbose) {
                $this->line("✅ Socket created successfully");
            }

            // Set socket options
            socket_set_option($socket, SOL_SOCKET, SO_RCVTIMEO, array('sec' => 5, 'usec' => 0));
            socket_set_option($socket, SOL_SOCKET, SO_SNDTIMEO, array('sec' => 5, 'usec' => 0));

            // Create test packet (similar to ZKTeco command)
            $testData = pack('VVVV', 1000, 0, 0, 65534);
            
            // Test socket_sendto (the failing operation)
            $result = @socket_sendto($socket, $testData, strlen($testData), 0, $ip, $port);
            
            if ($result === false) {
                $error = socket_strerror(socket_last_error($socket));
                $errno = socket_last_error($socket);
                
                $this->line("❌ socket_sendto() failed: $error (Error: $errno)");
                
                if ($errno === 1) {
                    $this->line("🎯 This is the exact error you're experiencing!");
                    $this->line("   Error Code 1 = Operation not permitted (EPERM)");
                }
                
                socket_close($socket);
                return false;
            }

            $this->line("✅ socket_sendto() successful: sent $result bytes");
            
            // Try to receive response
            if ($verbose) {
                $response = '';
                $from = '';
                $port_from = 0;
                $recv_result = @socket_recvfrom($socket, $response, 1024, 0, $from, $port_from);
                
                if ($recv_result !== false) {
                    $this->line("✅ Received response: $recv_result bytes from $from:$port_from");
                } else {
                    $this->line("⚠️  No response received (timeout or device not responding)");
                }
            }

            socket_close($socket);
            return true;

        } catch (Exception $e) {
            $this->line("❌ Exception during socket test: " . $e->getMessage());
            return false;
        }
    }

    private function testZKTecoService($ip, $verbose)
    {
        $this->line("Testing ZKTeco service...");

        try {
            $zktecoService = app(ZKTecoService::class);
            
            if ($verbose) {
                $this->line("✅ ZKTeco service created");
            }

            // Test connection
            $connected = $zktecoService->testConnection($ip);
            
            if ($connected) {
                $this->line("✅ ZKTeco service connection successful");
                
                // Try to get users
                if ($verbose) {
                    $users = $zktecoService->getUsers($ip);
                    $this->line("✅ Retrieved " . count($users) . " users from device");
                }
                
                return true;
            } else {
                $this->line("❌ ZKTeco service connection failed");
                return false;
            }

        } catch (Exception $e) {
            $this->line("❌ ZKTeco service error: " . $e->getMessage());
            
            if ($verbose) {
                $this->line("Stack trace: " . $e->getTraceAsString());
            }
            
            return false;
        }
    }

    private function showSolutions()
    {
        $this->info("🔧 Possible Solutions:");
        $this->line("1. SELinux (CentOS/RHEL): sudo setsebool -P httpd_can_network_connect 1");
        $this->line("2. Firewall: sudo ufw allow out 4370/udp");
        $this->line("3. User permissions: sudo usermod -a -G dialout www-data");
        $this->line("4. Container: Add NET_ADMIN capability");
        $this->line("5. Hosting provider: Request socket operation permissions");
        $this->newLine();
        $this->line("📞 If none work, contact your hosting provider or system administrator.");
    }

    private function testNetworkConfiguration($ip)
    {
        $this->line("Testing network configuration...");

        // Get server IP and check network
        $serverIP = $this->getServerIP();
        if ($serverIP) {
            $this->line("Server IP: $serverIP");

            // Check if same network
            $sameNetwork = $this->isSameNetwork($serverIP, $ip, '*************');
            $this->line("Same network as device: " . ($sameNetwork ? "✅ YES" : "❌ NO"));

            if (!$sameNetwork) {
                $this->line("⚠️  Server and device are on different networks");
                $this->line("   This may require routing configuration");
            }
        }

        // Test gateway connectivity
        $gateway = substr($ip, 0, strrpos($ip, '.')) . '.1';
        $gatewayTest = $this->testPing($gateway);
        $this->line("Gateway ($gateway) reachable: " . ($gatewayTest ? "✅ YES" : "❌ NO"));
    }

    private function testAlternativeConnections($ip)
    {
        $this->line("Testing alternative connection methods...");

        // Test different ports
        $alternativePorts = [80, 8080, 8000, 443, 23];
        foreach ($alternativePorts as $port) {
            $result = $this->testTCPConnection($ip, $port, 3);
            if ($result) {
                $this->line("✅ Port $port is open - device might use this port");
            }
        }

        // Test HTTP interface
        $httpUrls = [
            "http://$ip/",
            "http://$ip/cgi-bin/AccessControl.cgi",
            "http://$ip:8080/"
        ];

        foreach ($httpUrls as $url) {
            if ($this->testHTTP($url)) {
                $this->line("✅ HTTP interface found at: $url");
                break;
            }
        }
    }

    private function testZKTecoConfiguration()
    {
        $this->line("Testing ZKTeco service configuration...");

        try {
            // Check current adapter
            $zktecoService = app(\App\Services\ZKTecoService::class);
            $reflection = new \ReflectionClass($zktecoService);
            $adapterProperty = $reflection->getProperty('adapter');
            $adapterProperty->setAccessible(true);
            $adapter = $adapterProperty->getValue($zktecoService);

            $adapterClass = get_class($adapter);
            $this->line("Current adapter: " . $adapterClass);

            if (strpos($adapterClass, 'Mock') !== false) {
                $this->line("⚠️  Using mock adapter - real device connection not working");
            } elseif (strpos($adapterClass, 'Fallback') !== false) {
                $this->line("⚠️  Using fallback adapter - socket operations blocked");
            } else {
                $this->line("✅ Using real adapter - should support full functionality");
            }

        } catch (\Exception $e) {
            $this->line("❌ Error checking ZKTeco configuration: " . $e->getMessage());
        }
    }

    private function getServerIP()
    {
        if (function_exists('socket_create')) {
            $socket = @socket_create(AF_INET, SOCK_DGRAM, SOL_UDP);
            if ($socket && @socket_connect($socket, "*******", 53)) {
                socket_getsockname($socket, $localIP);
                socket_close($socket);
                return $localIP;
            }
        }
        return null;
    }

    private function isSameNetwork($ip1, $ip2, $netmask)
    {
        $ip1_long = ip2long($ip1);
        $ip2_long = ip2long($ip2);
        $netmask_long = ip2long($netmask);

        return ($ip1_long & $netmask_long) === ($ip2_long & $netmask_long);
    }

    private function testTCPConnection($ip, $port, $timeout)
    {
        if (function_exists('socket_create')) {
            $socket = @socket_create(AF_INET, SOCK_STREAM, SOL_TCP);
            if (!$socket) return false;

            socket_set_option($socket, SOL_SOCKET, SO_RCVTIMEO, array('sec' => $timeout, 'usec' => 0));
            $result = @socket_connect($socket, $ip, $port);
            socket_close($socket);

            return $result !== false;
        } else {
            $socket = @fsockopen($ip, $port, $errno, $errstr, $timeout);
            if ($socket) {
                fclose($socket);
                return true;
            }
            return false;
        }
    }

    private function testHTTP($url)
    {
        $context = stream_context_create([
            'http' => [
                'timeout' => 5,
                'ignore_errors' => true
            ]
        ]);

        $response = @file_get_contents($url, false, $context);
        return $response !== false;
    }
}
