<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        $schedule->command('backup:database')->twiceDaily(12, 0); // Runs every 12 hours
        $schedule->command('backup:database')->monthlyOn(1, '12:00'); // every month
        $schedule->command('demo:restore-public-images')->hourly();

        // ZKTeco Attendance Sync Schedules
        if (config('zkteco.sync.auto_sync_enabled', false)) {
            // Daily sync at 6:00 AM (sync previous day's attendance)
            $schedule->command('attendance:auto-sync --notify-failures')
                     ->dailyAt('06:00')
                     ->withoutOverlapping()
                     ->runInBackground()
                     ->appendOutputTo(storage_path('logs/attendance-sync.log'));

            // Hourly sync during working hours (8 AM to 6 PM)
            $schedule->command('attendance:auto-sync --notify-failures')
                     ->hourly()
                     ->between('08:00', '18:00')
                     ->withoutOverlapping()
                     ->runInBackground()
                     ->when(function () {
                         // Only run on weekdays
                         return now()->isWeekday();
                     });

            // Weekly cleanup and full sync on Sundays at 2:00 AM
            $schedule->command('attendance:auto-sync --date=' . now()->subWeek()->format('Y-m-d') . ' --notify-failures')
                     ->weeklyOn(0, '02:00') // Sunday at 2 AM
                     ->withoutOverlapping()
                     ->runInBackground();
        }
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
