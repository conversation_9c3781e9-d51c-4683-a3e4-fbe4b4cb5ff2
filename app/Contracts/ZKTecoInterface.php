<?php

namespace App\Contracts;

/**
 * ZKTeco Device Interface
 * 
 * This interface defines the contract for ZKTeco device communication.
 * Different implementations can be created for different ZKTeco libraries.
 */
interface ZKTecoInterface
{
    /**
     * Connect to the ZKTeco device
     *
     * @param string $ip
     * @param int $port
     * @param int $timeout
     * @return bool
     */
    public function connect(string $ip, int $port = 4370, int $timeout = 5): bool;

    /**
     * Disconnect from the ZKTeco device
     *
     * @return bool
     */
    public function disconnect(): bool;

    /**
     * Test connection to the device
     *
     * @return bool
     */
    public function testConnection(): bool;

    /**
     * Get device information
     *
     * @return array
     */
    public function getDeviceInfo(): array;

    /**
     * Get all attendance logs from the device
     *
     * @return array
     */
    public function getAttendanceLogs(): array;

    /**
     * Get attendance logs for a specific date
     *
     * @param string $date Y-m-d format
     * @return array
     */
    public function getAttendanceLogsByDate(string $date): array;

    /**
     * Get attendance logs for a date range
     *
     * @param string $startDate Y-m-d format
     * @param string $endDate Y-m-d format
     * @return array
     */
    public function getAttendanceLogsByDateRange(string $startDate, string $endDate): array;

    /**
     * Get all users from the device
     *
     * @return array
     */
    public function getUsers(): array;

    /**
     * Get user by ID
     *
     * @param string $userId
     * @return array|null
     */
    public function getUser(string $userId): ?array;

    /**
     * Clear attendance logs from device
     *
     * @return bool
     */
    public function clearAttendanceLogs(): bool;

    /**
     * Get device time
     *
     * @return string
     */
    public function getDeviceTime(): string;

    /**
     * Set device time
     *
     * @param string $datetime
     * @return bool
     */
    public function setDeviceTime(string $datetime): bool;

    /**
     * Get last error message
     *
     * @return string
     */
    public function getLastError(): string;

    /**
     * Check if device is connected
     *
     * @return bool
     */
    public function isConnected(): bool;
}
