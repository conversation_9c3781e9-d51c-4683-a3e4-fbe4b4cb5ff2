<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\SubscriptionManagement\UserSubscription;
use App\Models\SubscriptionManagement\SubscriptionAdminAction;
use App\Services\SubscriptionAdminService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;

class ExpiredSubscriptionsController extends Controller
{
    protected $subscriptionAdminService;

    public function __construct(SubscriptionAdminService $subscriptionAdminService)
    {
        $this->middleware(['auth', 'super.admin']);
        $this->subscriptionAdminService = $subscriptionAdminService;
    }

    /**
     * Display list of expired subscriptions.
     */
    public function index(Request $request)
    {
        $filters = $request->only(['status', 'plan', 'search']);
        $query = $this->subscriptionAdminService->getExpiredSubscriptions($filters);
        $expiredSubscriptions = $query->paginate(20);
        $stats = $this->subscriptionAdminService->getExpiredSubscriptionStats();

        return view('admin.subscriptions.expired', compact('expiredSubscriptions', 'stats'));
    }

    /**
     * Show form to update billing date for a specific subscription.
     */
    public function show(UserSubscription $subscription)
    {
        $subscription->load(['user', 'tenant', 'subscriptionPlan']);
        
        // Get recent admin actions for this subscription
        $recentActions = SubscriptionAdminAction::forSubscription($subscription->id)
                                               ->with('adminUser')
                                               ->latest()
                                               ->limit(10)
                                               ->get();

        return view('admin.subscriptions.expired-detail', compact('subscription', 'recentActions'));
    }

    /**
     * Update the next billing date for an expired subscription.
     */
    public function updateBillingDate(Request $request, UserSubscription $subscription)
    {
        $validator = Validator::make($request->all(), [
            'next_billing_date' => 'required|date|after_or_equal:today',
            'reactivate' => 'boolean',
            'reason' => 'required|string|max:500',
            'notes' => 'nullable|string|max:1000',
            'send_notification' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $result = $this->subscriptionAdminService->updateBillingDate(
            $subscription,
            Carbon::parse($request->next_billing_date),
            $request->boolean('reactivate'),
            $request->reason,
            $request->notes ?? '',
            $request->boolean('send_notification', true)
        );

        return response()->json($result, $result['success'] ? 200 : 500);
    }



    /**
     * Get admin actions history for a subscription.
     */
    public function getActionHistory(UserSubscription $subscription)
    {
        $actions = SubscriptionAdminAction::forSubscription($subscription->id)
                                         ->with('adminUser')
                                         ->latest()
                                         ->paginate(10);

        return response()->json([
            'success' => true,
            'actions' => $actions
        ]);
    }
}
