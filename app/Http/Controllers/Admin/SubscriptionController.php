<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Tenant;
use App\Models\User;
use App\Models\SubscriptionManagement\SubscriptionPlan;
use App\Models\SubscriptionManagement\UserSubscription;
use App\Models\SubscriptionManagement\SubscriptionPayment;
use App\Models\SubscriptionManagement\SubscriptionCode;
use App\Models\SubscriptionManagement\SubscriptionUsage;
use App\Services\SubscriptionService;
use App\Services\ManualPaymentService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class SubscriptionController extends Controller
{
    protected $subscriptionService;
    protected $manualPaymentService;

    public function __construct(SubscriptionService $subscriptionService, ManualPaymentService $manualPaymentService)
    {
        $this->middleware(['auth', 'super.admin']);
        $this->subscriptionService = $subscriptionService;
        $this->manualPaymentService = $manualPaymentService;
    }

    /**
     * Display the subscription management dashboard.
     */
    public function index()
    {
        $stats = $this->subscriptionService->getSubscriptionStats();
        
        // Recent subscriptions
        $recentSubscriptions = UserSubscription::with(['user', 'tenant', 'subscriptionPlan'])
                                              ->latest()
                                              ->limit(10)
                                              ->get();

        // Revenue statistics
        $revenueStats = $this->getRevenueStats();
        
        // Usage statistics
        $usageStats = $this->getUsageStats();

        return view('admin.subscriptions.index', compact(
            'stats',
            'recentSubscriptions',
            'revenueStats',
            'usageStats'
        ));
    }

    /**
     * Display all tenants with subscription details.
     */
    public function tenants(Request $request)
    {
        $query = Tenant::with(['users', 'userSubscriptions.subscriptionPlan']);

        // Filter by status
        if ($request->filled('status')) {
            switch ($request->status) {
                case 'active':
                    $query->where('is_active', true)->where('is_suspended', false);
                    break;
                case 'suspended':
                    $query->where('is_suspended', true);
                    break;
                case 'inactive':
                    $query->where('is_active', false);
                    break;
            }
        }

        // Filter by plan
        if ($request->filled('plan')) {
            $query->where('plan', $request->plan);
        }

        // Search
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('company_name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('name', 'like', "%{$search}%");
            });
        }

        $tenants = $query->paginate(20);

        return view('admin.subscriptions.tenants', compact('tenants'));
    }

    /**
     * Display subscription details for a specific tenant.
     */
    public function tenantDetails(Tenant $tenant)
    {
        $tenant->load([
            'users',
            'userSubscriptions.subscriptionPlan',
            'subscriptionPayments' => function ($q) {
                $q->latest()->limit(10);
            },
            'subscriptionUsage' => function ($q) {
                $q->currentPeriod();
            }
        ]);

        return view('admin.subscriptions.tenant-details', compact('tenant'));
    }

    /**
     * Display subscription codes management.
     */
    public function codes(Request $request)
    {
        $query = SubscriptionCode::with(['subscriptionPlan', 'generatedBy', 'usedBy']);

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by type
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        // Search
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('code', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        $codes = $query->latest()->paginate(20);
        $plans = SubscriptionPlan::active()->get();

        return view('admin.subscriptions.codes', compact('codes', 'plans'));
    }

    /**
     * Generate new subscription codes.
     */
    public function generateCodes(Request $request)
    {
        $request->validate([
            'subscription_plan_id' => 'required|exists:subscription_plans,id',
            'count' => 'required|integer|min:1|max:100',
            'type' => 'required|in:upgrade,extension,trial',
            'duration_months' => 'required|integer|min:1|max:60',
            'expires_at' => 'nullable|date|after:today',
            'description' => 'nullable|string|max:255',
            'email_restriction' => 'nullable|email',
            'domain_restriction' => 'nullable|string',
        ]);

        $codes = SubscriptionCode::generateBulk($request->count, [
            'subscription_plan_id' => $request->subscription_plan_id,
            'type' => $request->type,
            'duration_months' => $request->duration_months,
            'expires_at' => $request->expires_at,
            'description' => $request->description,
            'email_restriction' => $request->email_restriction,
            'domain_restriction' => $request->domain_restriction,
        ]);

        return response()->json([
            'success' => true,
            'message' => "Generated {$request->count} subscription codes successfully.",
            'codes' => $codes->pluck('code')
        ]);
    }

    /**
     * Display payment history.
     */
    public function payments(Request $request)
    {
        $query = SubscriptionPayment::with(['user', 'tenant', 'userSubscription.subscriptionPlan']);

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by payment method
        if ($request->filled('payment_method')) {
            $query->where('payment_method', $request->payment_method);
        }

        // Date range filter
        if ($request->filled('date_from')) {
            $query->whereDate('payment_date', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('payment_date', '<=', $request->date_to);
        }

        $payments = $query->latest('payment_date')->paginate(20);

        return view('admin.subscriptions.payments', compact('payments'));
    }

    /**
     * Display pending payment approvals.
     */
    public function pendingPayments()
    {
        $pendingPayments = $this->manualPaymentService->getPendingPayments();
        $paymentStats = $this->manualPaymentService->getPaymentStats();

        return view('admin.subscriptions.pending-payments', compact('pendingPayments', 'paymentStats'));
    }

    /**
     * Approve a payment.
     */
    public function approvePayment(Request $request, SubscriptionPayment $payment)
    {
        $request->validate([
            'notes' => 'nullable|string|max:500'
        ]);

        $success = $this->manualPaymentService->approvePayment(
            $payment,
            auth()->user(),
            $request->notes
        );

        if ($success) {
            return response()->json([
                'success' => true,
                'message' => 'Payment approved successfully.'
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Payment could not be approved.'
        ], 422);
    }

    /**
     * Reject a payment.
     */
    public function rejectPayment(Request $request, SubscriptionPayment $payment)
    {
        $request->validate([
            'reason' => 'required|string|max:500'
        ]);

        $success = $this->manualPaymentService->rejectPayment(
            $payment,
            auth()->user(),
            $request->reason
        );

        if ($success) {
            return response()->json([
                'success' => true,
                'message' => 'Payment rejected successfully.'
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Payment could not be rejected.'
        ], 422);
    }

    /**
     * Suspend a tenant.
     */
    public function suspendTenant(Request $request, Tenant $tenant)
    {
        $request->validate([
            'reason' => 'required|string|max:255'
        ]);

        $tenant->suspend($request->reason);

        return response()->json([
            'success' => true,
            'message' => 'Tenant suspended successfully.'
        ]);
    }

    /**
     * Reactivate a tenant.
     */
    public function reactivateTenant(Tenant $tenant)
    {
        $tenant->reactivate();

        return response()->json([
            'success' => true,
            'message' => 'Tenant reactivated successfully.'
        ]);
    }

    /**
     * Upgrade tenant to premium.
     */
    public function upgradeTenant(Tenant $tenant)
    {
        try {
            // Check if tenant is already premium
            if ($tenant->plan === 'premium') {
                return response()->json([
                    'success' => false,
                    'message' => 'Tenant is already on premium plan.'
                ], 400);
            }

            // Start database transaction
            DB::beginTransaction();

            // Upgrade tenant
            $tenant->upgradeToPremium();

            // Update user subscription if exists
            $activeSubscription = $tenant->getActiveSubscription();
            if ($activeSubscription) {
                $premiumPlan = SubscriptionPlan::where('slug', 'premium')->first();
                if (!$premiumPlan) {
                    throw new \Exception('Premium subscription plan not found.');
                }

                $this->subscriptionService->upgradeSubscription($activeSubscription, $premiumPlan, [
                    'billing_cycle' => 'yearly'
                ]);
            } else {
                // Create new subscription for tenant if none exists
                $premiumPlan = SubscriptionPlan::where('slug', 'premium')->first();
                if (!$premiumPlan) {
                    throw new \Exception('Premium subscription plan not found.');
                }

                // Find the primary user for this tenant
                $primaryUser = $tenant->users()->first();
                if ($primaryUser) {
                    $this->subscriptionService->createSubscription($primaryUser, $premiumPlan, [
                        'tenant_id' => $tenant->id,
                        'billing_cycle' => 'yearly'
                    ]);
                }
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Tenant upgraded to premium successfully.'
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            \Log::error('Tenant upgrade failed', [
                'tenant_id' => $tenant->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to upgrade tenant: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get revenue statistics.
     */
    protected function getRevenueStats(): array
    {
        $currentMonth = now()->startOfMonth();
        $lastMonth = now()->subMonth()->startOfMonth();

        return [
            'current_month' => SubscriptionPayment::where('status', 'completed')
                                                 ->whereDate('payment_date', '>=', $currentMonth)
                                                 ->sum('amount'),
            'last_month' => SubscriptionPayment::where('status', 'completed')
                                              ->whereDate('payment_date', '>=', $lastMonth)
                                              ->whereDate('payment_date', '<', $currentMonth)
                                              ->sum('amount'),
            'total_revenue' => SubscriptionPayment::where('status', 'completed')->sum('amount'),
            'failed_payments' => SubscriptionPayment::where('status', 'failed')
                                                   ->whereDate('created_at', '>=', $currentMonth)
                                                   ->count(),
        ];
    }

    /**
     * Get usage statistics.
     */
    protected function getUsageStats(): array
    {
        try {
            // Check if status column exists before using it
            $activeUsersCount = 0;
            if (Schema::hasColumn('users', 'status')) {
                $activeUsersCount = User::where('status', true)->count();
            } else {
                $activeUsersCount = User::count(); // Fallback if status column doesn't exist
            }

            return [
                'total_users' => User::count(),
                'active_users' => $activeUsersCount,
                'total_orders' => $this->getTableCount('orders'),
                'total_buyers' => $this->getTableCount('buyer_profiles'),
                'total_garment_orders' => $this->getTableCount('garment_orders'),
                'total_stock_items' => $this->getTableCount('stock_items'),
            ];
        } catch (\Exception $e) {
            // Return default values if there's any database error
            return [
                'total_users' => 0,
                'active_users' => 0,
                'total_orders' => 0,
                'total_buyers' => 0,
                'total_garment_orders' => 0,
                'total_stock_items' => 0,
            ];
        }
    }

    /**
     * Safely get table count.
     */
    protected function getTableCount(string $tableName): int
    {
        try {
            if (Schema::hasTable($tableName)) {
                return DB::table($tableName)->count();
            }
            return 0;
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Export subscription codes.
     */
    public function exportCodes(Request $request)
    {
        $query = SubscriptionCode::with(['subscriptionPlan', 'generatedBy']);

        // Apply same filters as codes method
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        $codes = $query->get();

        $csvData = "Code,Plan,Type,Status,Generated By,Generated At,Expires At,Used By,Used At\n";

        foreach ($codes as $code) {
            $csvData .= sprintf(
                "%s,%s,%s,%s,%s,%s,%s,%s,%s\n",
                $code->code,
                $code->subscriptionPlan->name,
                $code->getTypeLabel(),
                $code->getStatusLabel(),
                $code->generatedBy->name,
                $code->created_at->format('Y-m-d H:i:s'),
                $code->expires_at ? $code->expires_at->format('Y-m-d H:i:s') : 'Never',
                $code->usedBy ? $code->usedBy->name : '',
                $code->used_at ? $code->used_at->format('Y-m-d H:i:s') : ''
            );
        }

        return response($csvData)
            ->header('Content-Type', 'text/csv')
            ->header('Content-Disposition', 'attachment; filename="subscription_codes_' . date('Y-m-d') . '.csv"');
    }

    /**
     * Get payment details for a specific payment.
     */
    public function getPaymentDetails(SubscriptionPayment $payment)
    {
        $payment->load(['user', 'tenant', 'userSubscription.subscriptionPlan']);

        return response()->json([
            'success' => true,
            'payment' => $payment
        ]);
    }
}
