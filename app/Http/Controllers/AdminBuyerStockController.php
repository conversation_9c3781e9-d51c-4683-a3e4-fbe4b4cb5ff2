<?php

namespace App\Http\Controllers;

use App\Models\Party;
use App\Models\BuyerUse;
use Illuminate\Http\Request;
use App\Services\BuyerStockService;

class AdminBuyerStockController extends Controller
{
    protected $stockService;

    public function __construct(BuyerStockService $stockService)
    {
        $this->stockService = $stockService;
        $this->middleware('permission:parties-read')->only('index', 'show');
        $this->middleware('permission:parties-update')->only('edit', 'update', 'destroy');
    }

    /**
     * Display all buyers' stock overview.
     */
    public function index(Request $request)
    {
        $buyersStock = $this->stockService->getAllBuyersStockSummary();

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->search;
            $buyersStock = $buyersStock->filter(function ($item) use ($search) {
                return stripos($item['buyer']->name, $search) !== false ||
                       stripos($item['buyer']->user->email, $search) !== false;
            });
        }

        return view('admin.buyer-stock.index', compact('buyersStock'));
    }

    /**
     * Show detailed stock for a specific buyer.
     */
    public function show($partyId)
    {
        $party = Party::where('type', 'buyer')->findOrFail($partyId);
        $stockData = $this->stockService->calculateBuyerStock($partyId);
        $summary = $this->stockService->getBuyerStockSummary($partyId);
        $recentUsage = $this->stockService->getRecentUsageHistory($partyId, 10);

        return view('admin.buyer-stock.show', compact('party', 'stockData', 'summary', 'recentUsage'));
    }

    /**
     * Show all usage records for a buyer.
     */
    public function usageHistory($partyId, Request $request)
    {
        $party = Party::where('type', 'buyer')->findOrFail($partyId);

        $query = BuyerUse::with('user')->where('party_id', $partyId);

        // Apply filters
        if ($request->filled('start_date') && $request->filled('end_date')) {
            $query->betweenDates($request->start_date, $request->end_date);
        }

        if ($request->filled('style')) {
            $query->where('style', 'like', '%' . $request->style . '%');
        }

        // Calculate total quantity used for all filtered records
        $totalQuantityUsed = $query->sum('qty_used');

        $usageHistory = $query->orderBy('usage_date', 'desc')
                             ->orderBy('created_at', 'desc')
                             ->paginate(20);

        return view('admin.buyer-stock.usage-history', compact('party', 'usageHistory', 'totalQuantityUsed'));
    }

    /**
     * Delete a usage record.
     */
    public function destroyUsage($usageId)
    {
        $usage = BuyerUse::findOrFail($usageId);
        $usage->delete();

        return response()->json([
            'message' => __('Usage record deleted successfully')
        ]);
    }

    /**
     * Export stock data to CSV.
     */
    public function exportStock($partyId)
    {
        $party = Party::where('type', 'buyer')->findOrFail($partyId);
        $stockData = $this->stockService->calculateBuyerStock($partyId);

        $filename = "buyer_stock_{$party->name}_" . date('Y-m-d') . ".csv";

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"$filename\"",
        ];

        $callback = function() use ($stockData) {
            $file = fopen('php://output', 'w');
            
            // CSV headers
            fputcsv($file, [
                'Style',
                'Color',
                'Item',
                'Size',
                'Total Shipped',
                'Total Used',
                'Available Stock'
            ]);

            // CSV data
            foreach ($stockData as $item) {
                fputcsv($file, [
                    $item['style'],
                    $item['color'],
                    $item['item'],
                    $item['size'],
                    $item['total_shipped'],
                    $item['total_used'],
                    $item['available_stock']
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Get stock data for AJAX requests.
     */
    public function getStockData(Request $request)
    {
        if (!$request->filled('party_id')) {
            return response()->json(['error' => 'Party ID is required'], 400);
        }

        $stockData = $this->stockService->calculateBuyerStock($request->party_id);
        $summary = $this->stockService->getBuyerStockSummary($request->party_id);

        return response()->json([
            'stock_data' => $stockData,
            'summary' => $summary
        ]);
    }

    /**
     * Filter stock data.
     */
    public function filter(Request $request)
    {
        $buyersStock = $this->stockService->getAllBuyersStockSummary();

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->search;
            $buyersStock = $buyersStock->filter(function ($item) use ($search) {
                return stripos($item['buyer']->name, $search) !== false ||
                       stripos($item['buyer']->user->email, $search) !== false;
            });
        }

        if ($request->filled('low_stock_only')) {
            $buyersStock = $buyersStock->filter(function ($item) {
                return $item['summary']['low_stock_items'] > 0;
            });
        }

        if ($request->filled('out_of_stock_only')) {
            $buyersStock = $buyersStock->filter(function ($item) {
                return $item['summary']['out_of_stock_items'] > 0;
            });
        }

        if ($request->ajax()) {
            return response()->json([
                'data' => view('admin.buyer-stock.data', compact('buyersStock'))->render(),
            ]);
        }

        return view('admin.buyer-stock.index', compact('buyersStock'));
    }
}
