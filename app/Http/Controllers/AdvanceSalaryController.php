<?php

namespace App\Http\Controllers;

use App\Models\Bank;
use App\Models\Employee;
use App\Models\Salary;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class AdvanceSalaryController extends Controller
{
    public function __construct()
    {
        $this->middleware('permission:salaries-create')->only(['create', 'store']);
        $this->middleware('permission:salaries-read')->only('index');
        $this->middleware('permission:salaries-update')->only(['edit', 'update']);
        $this->middleware('permission:salaries-delete')->only('destroy');
    }

    /**
     * Display advance salary records
     */
    public function index(Request $request)
    {
        $query = Salary::with(['employee', 'user', 'bank'])
                      ->where('payment_status', 'advance');

        // Apply filters
        if ($request->filled('employee_id')) {
            $query->where('employee_id', $request->employee_id);
        }

        if ($request->filled('year')) {
            $query->where('year', $request->year);
        }

        if ($request->filled('month')) {
            $query->where('month', $request->month);
        }

        $advances = $query->orderBy('created_at', 'desc')->paginate(20);
        $employees = Employee::select('id', 'employee_id', 'name')->orderBy('name')->get();

        return view('pages.salaries.advances.index', compact('advances', 'employees'));
    }

    /**
     * Show advance salary form
     */
    public function create()
    {
        $employees = Employee::select('id', 'employee_id', 'name', 'salary')
                            ->where('status', 1)
                            ->orderBy('name')
                            ->get();
        
        $banks = Bank::select('id', 'bank_name')->orderBy('bank_name')->get();
        
        return view('pages.salaries.advances.create', compact('employees', 'banks'));
    }

    /**
     * Store advance salary
     */
    public function store(Request $request)
    {
        $request->validate([
            'employee_id' => 'required|exists:employees,id',
            'year' => 'required|integer|min:2020|max:' . (date('Y') + 1),
            'month' => 'required|integer|min:1|max:12',
            'advance_amount' => 'required|numeric|min:1',
            'bank_id' => 'nullable|exists:banks,id',
            'payment_date' => 'required|date|before_or_equal:today',
            'notes' => 'nullable|string|max:1000',
            'remarks' => 'nullable|string|max:1000',
        ]);

        $employee = Employee::findOrFail($request->employee_id);

        // Check if advance amount exceeds base salary
        if ($request->advance_amount > $employee->salary) {
            return response()->json([
                'message' => __('Advance amount cannot exceed base salary.')
            ], 400);
        }

        // Check if salary already exists for this period
        $existingSalary = Salary::where('employee_id', $request->employee_id)
                               ->where('year', $request->year)
                               ->where('month', $request->month)
                               ->first();

        if ($existingSalary) {
            return response()->json([
                'message' => __('Salary record for this period already exists.')
            ], 400);
        }

        DB::beginTransaction();
        try {
            $salary = Salary::create([
                'employee_id' => $request->employee_id,
                'user_id' => Auth::id(),
                'bank_id' => $request->bank_id,
                'year' => $request->year,
                'month' => $request->month,
                'base_salary' => $employee->salary,
                'attendance_days' => 0, // Will be updated later
                'overtime_hours' => 0, // Will be updated later
                'calculated_salary' => 0, // Will be calculated later
                'advance_amount' => $request->advance_amount,
                'payment_status' => 'advance',
                'payment_date' => $request->payment_date,
                'notes' => $request->notes,
                'remarks' => $request->remarks,
                'meta' => [
                    'payment_method' => $request->bank_id ? 'bank' : 'cash',
                    'is_advance' => true,
                ]
            ]);

            DB::commit();

            return response()->json([
                'message' => __('Advance salary recorded successfully.'),
                'redirect' => route('advance-salaries.index')
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'message' => __('Error recording advance salary: ') . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Show advance salary details
     */
    public function show(Salary $advanceSalary)
    {
        if ($advanceSalary->payment_status !== 'advance') {
            abort(404);
        }

        $advanceSalary->load(['employee', 'user', 'bank']);
        return view('pages.salaries.advances.show', compact('advanceSalary'));
    }

    /**
     * Show edit form for advance salary
     */
    public function edit(Salary $advanceSalary)
    {
        if ($advanceSalary->payment_status !== 'advance') {
            abort(404);
        }

        $employees = Employee::select('id', 'employee_id', 'name', 'salary')->orderBy('name')->get();
        $banks = Bank::select('id', 'bank_name')->orderBy('bank_name')->get();
        
        return view('pages.salaries.advances.edit', compact('advanceSalary', 'employees', 'banks'));
    }

    /**
     * Update advance salary
     */
    public function update(Request $request, Salary $advanceSalary)
    {
        if ($advanceSalary->payment_status !== 'advance') {
            return response()->json([
                'message' => __('Can only update advance salary records.')
            ], 400);
        }

        $request->validate([
            'advance_amount' => 'required|numeric|min:1',
            'bank_id' => 'nullable|exists:banks,id',
            'payment_date' => 'required|date|before_or_equal:today',
            'notes' => 'nullable|string|max:1000',
            'remarks' => 'nullable|string|max:1000',
        ]);

        $employee = $advanceSalary->employee;

        // Check if advance amount exceeds base salary
        if ($request->advance_amount > $employee->salary) {
            return response()->json([
                'message' => __('Advance amount cannot exceed base salary.')
            ], 400);
        }

        DB::beginTransaction();
        try {
            $advanceSalary->update([
                'advance_amount' => $request->advance_amount,
                'bank_id' => $request->bank_id,
                'payment_date' => $request->payment_date,
                'notes' => $request->notes,
                'remarks' => $request->remarks,
                'meta' => array_merge($advanceSalary->meta ?? [], [
                    'payment_method' => $request->bank_id ? 'bank' : 'cash',
                ])
            ]);

            DB::commit();

            return response()->json([
                'message' => __('Advance salary updated successfully.'),
                'redirect' => route('advance-salaries.index')
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'message' => __('Error updating advance salary: ') . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Convert advance to regular salary
     */
    public function convertToRegular(Request $request, Salary $advanceSalary)
    {
        if ($advanceSalary->payment_status !== 'advance') {
            return response()->json([
                'message' => __('Can only convert advance salary records.')
            ], 400);
        }

        $request->validate([
            'attendance_days' => 'required|integer|min:0|max:26',
            'overtime_hours' => 'required|numeric|min:0|max:500',
        ]);

        DB::beginTransaction();
        try {
            // Update the advance record to regular salary
            $advanceSalary->update([
                'attendance_days' => $request->attendance_days,
                'overtime_hours' => $request->overtime_hours,
                'payment_status' => 'paid', // Mark as paid since advance was already given
                'meta' => array_merge($advanceSalary->meta ?? [], [
                    'converted_from_advance' => true,
                    'conversion_date' => now(),
                ])
            ]);

            // The calculated_salary will be auto-calculated by the model

            DB::commit();

            return response()->json([
                'message' => __('Advance salary converted to regular salary successfully.'),
                'redirect' => route('salaries.index')
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'message' => __('Error converting advance salary: ') . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete advance salary record
     */
    public function destroy(Salary $advanceSalary)
    {
        if ($advanceSalary->payment_status !== 'advance') {
            return response()->json([
                'message' => __('Can only delete advance salary records.')
            ], 400);
        }

        $advanceSalary->delete();

        return response()->json([
            'message' => __('Advance salary record deleted successfully.'),
            'redirect' => route('advance-salaries.index')
        ]);
    }
}
