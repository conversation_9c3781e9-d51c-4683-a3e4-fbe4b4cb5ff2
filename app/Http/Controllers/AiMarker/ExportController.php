<?php

namespace App\Http\Controllers\AiMarker;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class ExportController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'verified']);
        $this->middleware('can:ai-marker.export.basic');
    }

    public function index()
    {
        return view('ai-marker.exports.index');
    }

    public function show($id)
    {
        return view('ai-marker.exports.show');
    }

    public function download($id)
    {
        return response()->json(['message' => 'Download feature coming soon']);
    }

    public function destroy($id)
    {
        return redirect()->route('ai-marker.exports.index')->with('success', 'Export deleted successfully');
    }
}
