<?php

namespace App\Http\Controllers\AiMarker;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\AiMarker\FabricSpecification;

class FabricSpecificationController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'verified']);
        // AI Marker access is handled by route middleware
    }

    public function index()
    {
        try {
            $fabrics = FabricSpecification::where('company_id', auth()->user()->company_id)
                ->with(['creator'])
                ->latest()
                ->get();
        } catch (\Exception $e) {
            // Handle case where table doesn't exist or other errors
            $fabrics = collect(); // Empty collection
        }

        return view('ai-marker.fabrics.index', compact('fabrics'));
    }

    public function create()
    {
        return view('ai-marker.fabrics.create');
    }

    public function store(Request $request)
    {
        return redirect()->route('ai-marker.fabrics.index')->with('success', 'Fabric specification created successfully');
    }

    public function show($id)
    {
        return view('ai-marker.fabrics.show');
    }

    public function edit($id)
    {
        return view('ai-marker.fabrics.edit');
    }

    public function update(Request $request, $id)
    {
        return redirect()->route('ai-marker.fabrics.index')->with('success', 'Fabric specification updated successfully');
    }

    public function destroy($id)
    {
        return redirect()->route('ai-marker.fabrics.index')->with('success', 'Fabric specification deleted successfully');
    }

    public function costCalculator()
    {
        return view('ai-marker.fabrics.cost-calculator');
    }
}
