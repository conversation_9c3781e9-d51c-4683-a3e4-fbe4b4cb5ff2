<?php

namespace App\Http\Controllers\AiMarker;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class IntegrationController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'verified']);
        $this->middleware('can:ai-marker.access');
    }

    public function orders()
    {
        return view('ai-marker.integration.orders');
    }

    public function garmentOrders()
    {
        return view('ai-marker.integration.garment-orders');
    }

    public function optimizeOrder($id)
    {
        return view('ai-marker.integration.optimize-order');
    }

    public function optimizeGarmentOrder($id)
    {
        return view('ai-marker.integration.optimize-garment-order');
    }
}
