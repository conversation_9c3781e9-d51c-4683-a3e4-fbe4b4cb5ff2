<?php

namespace App\Http\Controllers\AiMarker;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\AiMarker\MarkerOptimization;

class OptimizationController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'verified']);
        // AI Marker access is handled by route middleware
    }

    public function index()
    {
        try {
            $optimizations = MarkerOptimization::where('company_id', auth()->user()->company_id)
                ->with(['creator'])
                ->latest()
                ->get();
        } catch (\Exception $e) {
            // Handle case where table doesn't exist or other errors
            $optimizations = collect(); // Empty collection
        }

        return view('ai-marker.optimizations.index', compact('optimizations'));
    }

    public function show($id)
    {
        return view('ai-marker.optimizations.show');
    }

    public function destroy($id)
    {
        return redirect()->route('ai-marker.optimizations.index')->with('success', 'Optimization deleted successfully');
    }
}
