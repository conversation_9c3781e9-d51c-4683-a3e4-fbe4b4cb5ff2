<?php

namespace App\Http\Controllers\AiMarker;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\AiMarker\OptimizationTemplate;

class OptimizationTemplateController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'verified']);
        // AI Marker access is handled by route middleware
    }

    public function index()
    {
        try {
            $templates = OptimizationTemplate::where('company_id', auth()->user()->company_id)
                ->with(['creator'])
                ->latest()
                ->get();
        } catch (\Exception $e) {
            // Handle case where table doesn't exist or other errors
            $templates = collect(); // Empty collection
        }

        return view('ai-marker.templates.index', compact('templates'));
    }

    public function create()
    {
        return view('ai-marker.templates.create');
    }

    public function store(Request $request)
    {
        return redirect()->route('ai-marker.templates.index')->with('success', 'Template created successfully');
    }

    public function show($id)
    {
        return view('ai-marker.templates.show');
    }

    public function edit($id)
    {
        return view('ai-marker.templates.edit');
    }

    public function update(Request $request, $id)
    {
        return redirect()->route('ai-marker.templates.index')->with('success', 'Template updated successfully');
    }

    public function destroy($id)
    {
        return redirect()->route('ai-marker.templates.index')->with('success', 'Template deleted successfully');
    }
}
