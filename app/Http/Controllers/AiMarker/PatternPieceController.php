<?php

namespace App\Http\Controllers\AiMarker;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class PatternPieceController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'verified']);
        // AI Marker access is handled by route middleware
    }

    public function index()
    {
        return view('ai-marker.patterns.index');
    }

    public function create()
    {
        return view('ai-marker.patterns.create');
    }

    public function store(Request $request)
    {
        return redirect()->route('ai-marker.patterns.index')->with('success', 'Pattern piece created successfully');
    }

    public function show($id)
    {
        return view('ai-marker.patterns.show');
    }

    public function edit($id)
    {
        return view('ai-marker.patterns.edit');
    }

    public function update(Request $request, $id)
    {
        return redirect()->route('ai-marker.patterns.index')->with('success', 'Pattern piece updated successfully');
    }

    public function destroy($id)
    {
        return redirect()->route('ai-marker.patterns.index')->with('success', 'Pattern piece deleted successfully');
    }
}
