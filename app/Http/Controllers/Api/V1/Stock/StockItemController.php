<?php

namespace App\Http\Controllers\Api\V1\Stock;

use App\Http\Controllers\Controller;
use App\Http\Requests\Stock\StockItemRequest;
use App\Http\Resources\Stock\StockItemResource;
use App\Http\Resources\Stock\StockItemCollection;
use App\Services\Stock\Enhanced\StockItemService;
use App\Exceptions\Stock\StockItemException;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Log;

class StockItemController extends Controller
{
    protected $stockItemService;

    public function __construct(StockItemService $stockItemService)
    {
        $this->stockItemService = $stockItemService;
        
        // Apply permissions
        $this->middleware('permission:stock.items.view')->only(['index', 'show']);
        $this->middleware('permission:stock.items.create')->only(['store']);
        $this->middleware('permission:stock.items.update')->only(['update']);
        $this->middleware('permission:stock.items.delete')->only(['destroy']);
    }

    /**
     * Display a listing of stock items
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $filters = $request->only([
                'category_id', 'item_type', 'is_active', 'search', 
                'location_id', 'low_stock', 'out_of_stock', 'overstock'
            ]);

            $perPage = $request->get('per_page', 15);
            $with = $request->get('with', []);

            if (is_string($with)) {
                $with = explode(',', $with);
            }

            $items = $this->stockItemService->getPaginatedItems($filters, $perPage, $with);

            return response()->json([
                'success' => true,
                'data' => new StockItemCollection($items),
                'meta' => [
                    'current_page' => $items->currentPage(),
                    'last_page' => $items->lastPage(),
                    'per_page' => $items->perPage(),
                    'total' => $items->total(),
                    'from' => $items->firstItem(),
                    'to' => $items->lastItem(),
                ],
                'links' => [
                    'first' => $items->url(1),
                    'last' => $items->url($items->lastPage()),
                    'prev' => $items->previousPageUrl(),
                    'next' => $items->nextPageUrl(),
                ]
            ]);

        } catch (StockItemException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
                'error_code' => 'STOCK_ITEM_ERROR'
            ], 400);

        } catch (\Exception $e) {
            Log::error('Error in stock items index API', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while retrieving stock items',
                'error_code' => 'INTERNAL_ERROR'
            ], 500);
        }
    }

    /**
     * Store a newly created stock item
     */
    public function store(StockItemRequest $request): JsonResponse
    {
        try {
            $item = $this->stockItemService->createItem($request->validated());

            return response()->json([
                'success' => true,
                'message' => 'Stock item created successfully',
                'data' => new StockItemResource($item)
            ], 201);

        } catch (StockItemException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
                'error_code' => 'STOCK_ITEM_ERROR'
            ], 400);

        } catch (\Exception $e) {
            Log::error('Error creating stock item via API', [
                'data' => $request->validated(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while creating the stock item',
                'error_code' => 'INTERNAL_ERROR'
            ], 500);
        }
    }

    /**
     * Display the specified stock item
     */
    public function show(Request $request, int $id): JsonResponse
    {
        try {
            $with = $request->get('with', []);
            
            if (is_string($with)) {
                $with = explode(',', $with);
            }

            $item = $this->stockItemService->findItem($id, $with);

            if (!$item) {
                return response()->json([
                    'success' => false,
                    'message' => 'Stock item not found',
                    'error_code' => 'ITEM_NOT_FOUND'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => new StockItemResource($item)
            ]);

        } catch (StockItemException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
                'error_code' => 'STOCK_ITEM_ERROR'
            ], 400);

        } catch (\Exception $e) {
            Log::error('Error showing stock item via API', [
                'id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while retrieving the stock item',
                'error_code' => 'INTERNAL_ERROR'
            ], 500);
        }
    }

    /**
     * Update the specified stock item
     */
    public function update(StockItemRequest $request, int $id): JsonResponse
    {
        try {
            $updated = $this->stockItemService->updateItem($id, $request->validated());

            if (!$updated) {
                return response()->json([
                    'success' => false,
                    'message' => 'Stock item not found or could not be updated',
                    'error_code' => 'UPDATE_FAILED'
                ], 404);
            }

            $item = $this->stockItemService->findItem($id);

            return response()->json([
                'success' => true,
                'message' => 'Stock item updated successfully',
                'data' => new StockItemResource($item)
            ]);

        } catch (StockItemException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
                'error_code' => 'STOCK_ITEM_ERROR'
            ], 400);

        } catch (\Exception $e) {
            Log::error('Error updating stock item via API', [
                'id' => $id,
                'data' => $request->validated(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while updating the stock item',
                'error_code' => 'INTERNAL_ERROR'
            ], 500);
        }
    }

    /**
     * Remove the specified stock item
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $deleted = $this->stockItemService->deleteItem($id);

            if (!$deleted) {
                return response()->json([
                    'success' => false,
                    'message' => 'Stock item not found or could not be deleted',
                    'error_code' => 'DELETE_FAILED'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'Stock item deleted successfully'
            ]);

        } catch (StockItemException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
                'error_code' => 'STOCK_ITEM_ERROR'
            ], 400);

        } catch (\Exception $e) {
            Log::error('Error deleting stock item via API', [
                'id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while deleting the stock item',
                'error_code' => 'INTERNAL_ERROR'
            ], 500);
        }
    }

    /**
     * Search stock items
     */
    public function search(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'query' => 'required|string|min:2|max:100',
                'with' => 'nullable|string'
            ]);

            $query = $request->get('query');
            $with = $request->get('with', []);

            if (is_string($with)) {
                $with = explode(',', $with);
            }

            $items = $this->stockItemService->searchItems($query, $with);

            return response()->json([
                'success' => true,
                'data' => StockItemResource::collection($items),
                'meta' => [
                    'total' => $items->count(),
                    'query' => $query
                ]
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors(),
                'error_code' => 'VALIDATION_ERROR'
            ], 422);

        } catch (StockItemException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
                'error_code' => 'STOCK_ITEM_ERROR'
            ], 400);

        } catch (\Exception $e) {
            Log::error('Error searching stock items via API', [
                'query' => $request->get('query'),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while searching stock items',
                'error_code' => 'INTERNAL_ERROR'
            ], 500);
        }
    }

    /**
     * Get stock summary
     */
    public function summary(): JsonResponse
    {
        try {
            $summary = $this->stockItemService->getStockSummary();

            return response()->json([
                'success' => true,
                'data' => $summary
            ]);

        } catch (StockItemException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
                'error_code' => 'STOCK_ITEM_ERROR'
            ], 400);

        } catch (\Exception $e) {
            Log::error('Error getting stock summary via API', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while retrieving stock summary',
                'error_code' => 'INTERNAL_ERROR'
            ], 500);
        }
    }

    /**
     * Reserve stock quantity
     */
    public function reserve(Request $request, int $id): JsonResponse
    {
        try {
            $request->validate([
                'quantity' => 'required|numeric|min:0.0001',
                'reason' => 'nullable|string|max:255'
            ]);

            $reserved = $this->stockItemService->reserveStock(
                $id,
                $request->get('quantity'),
                $request->get('reason')
            );

            if (!$reserved) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to reserve stock',
                    'error_code' => 'RESERVATION_FAILED'
                ], 400);
            }

            return response()->json([
                'success' => true,
                'message' => 'Stock reserved successfully'
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors(),
                'error_code' => 'VALIDATION_ERROR'
            ], 422);

        } catch (StockItemException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
                'error_code' => 'STOCK_ITEM_ERROR'
            ], 400);

        } catch (\Exception $e) {
            Log::error('Error reserving stock via API', [
                'id' => $id,
                'quantity' => $request->get('quantity'),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while reserving stock',
                'error_code' => 'INTERNAL_ERROR'
            ], 500);
        }
    }

    /**
     * Release reserved stock
     */
    public function releaseReservation(Request $request, int $id): JsonResponse
    {
        try {
            $request->validate([
                'quantity' => 'required|numeric|min:0.0001'
            ]);

            $released = $this->stockItemService->releaseReservedStock(
                $id,
                $request->get('quantity')
            );

            if (!$released) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to release reserved stock',
                    'error_code' => 'RELEASE_FAILED'
                ], 400);
            }

            return response()->json([
                'success' => true,
                'message' => 'Reserved stock released successfully'
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors(),
                'error_code' => 'VALIDATION_ERROR'
            ], 422);

        } catch (StockItemException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
                'error_code' => 'STOCK_ITEM_ERROR'
            ], 400);

        } catch (\Exception $e) {
            Log::error('Error releasing reserved stock via API', [
                'id' => $id,
                'quantity' => $request->get('quantity'),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while releasing reserved stock',
                'error_code' => 'INTERNAL_ERROR'
            ], 500);
        }
    }
}
