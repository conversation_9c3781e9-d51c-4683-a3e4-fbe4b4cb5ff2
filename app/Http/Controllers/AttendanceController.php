<?php

namespace App\Http\Controllers;

use App\Models\Attendance;
use App\Models\Employee;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class AttendanceController extends Controller
{
    public function __construct()
    {
        $this->middleware('permission:attendances-read')->only('index', 'reports', 'monthlySheet', 'biometricReport', 'getAttendanceStats');
        $this->middleware('permission:attendances-create')->only('store', 'bulkMarkPresent');
        $this->middleware('permission:attendances-update')->only('edit', 'update', 'updateBulk');
        $this->middleware('permission:attendances-delete')->only('destroy');
        $this->middleware('permission:attendance-reports-read')->only('reports', 'monthlySheet', 'biometricReport');
        $this->middleware('permission:attendance-monthly-sheet-read')->only('monthlySheet');
        $this->middleware('permission:attendance-import-read')->only('import');
    }

    /**
     * Display daily attendance form
     */
    public function index(Request $request)
    {
        $date = $request->get('date', now()->format('Y-m-d'));
        $employees = Employee::with('designation')->where('status', 1)->get();

        // Get existing attendance for the date
        $existingAttendance = Attendance::forDate($date)
            ->with('employee')
            ->get()
            ->keyBy('employee_id');

        // Calculate total daily cost
        $totalDailyCost = $existingAttendance->sum('daily_cost');

        return view('pages.attendance.index', compact('employees', 'date', 'existingAttendance', 'totalDailyCost'));
    }

    /**
     * Store daily attendance
     */
    public function store(Request $request)
    {
        $request->validate([
            'date' => 'required|date|before_or_equal:today',
            'attendance' => 'required|array|min:1',
            'attendance.*.employee_id' => 'required|exists:employees,id',
            'attendance.*.status' => 'required|in:present,absent,half_day',
            'attendance.*.notes' => 'nullable|string|max:500',
        ], [
            'date.before_or_equal' => 'Cannot mark attendance for future dates.',
            'attendance.min' => 'At least one employee attendance must be provided.',
            'attendance.*.employee_id.exists' => 'Selected employee does not exist.',
            'attendance.*.status.in' => 'Invalid attendance status selected.',
            'attendance.*.notes.max' => 'Notes cannot exceed 500 characters.',
        ]);

        DB::beginTransaction();

        try {
            $date = $request->date;
            $totalCost = 0;
            $processedEmployees = [];

            foreach ($request->attendance as $attendanceData) {
                $employeeId = $attendanceData['employee_id'];

                // Prevent duplicate entries in the same request
                if (in_array($employeeId, $processedEmployees)) {
                    continue;
                }
                $processedEmployees[] = $employeeId;

                $employee = Employee::find($employeeId);

                if (!$employee || $employee->status != 1) {
                    continue; // Skip inactive employees
                }

                $attendance = Attendance::updateOrCreate(
                    [
                        'employee_id' => $employeeId,
                        'date' => $date,
                    ],
                    [
                        'status' => $attendanceData['status'],
                        'monthly_salary' => $employee->salary,
                        'notes' => $attendanceData['notes'] ?? null,
                    ]
                );

                $totalCost += $attendance->daily_cost;
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Attendance saved successfully!',
                'total_cost' => number_format($totalCost, 2),
            ]);

        } catch (\Exception $e) {
            DB::rollback();

            return response()->json([
                'success' => false,
                'message' => 'Error saving attendance: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Show past attendance records for editing
     */
    public function edit(Request $request)
    {
        $date = $request->get('date', now()->subDay()->format('Y-m-d'));
        $employees = Employee::with('designation')->where('status', 1)->get();

        // Get existing attendance for the date
        $existingAttendance = Attendance::forDate($date)
            ->with('employee')
            ->get()
            ->keyBy('employee_id');

        // Calculate total daily cost
        $totalDailyCost = $existingAttendance->sum('daily_cost');

        return view('pages.attendance.edit', compact('employees', 'date', 'existingAttendance', 'totalDailyCost'));
    }

    /**
     * Update past attendance records
     */
    public function update(Request $request)
    {
        $request->validate([
            'date' => 'required|date|before_or_equal:today',
            'attendance' => 'required|array',
            'attendance.*.employee_id' => 'required|exists:employees,id',
            'attendance.*.status' => 'required|in:present,absent,half_day',
        ]);

        DB::beginTransaction();

        try {
            $date = $request->date;
            $totalCost = 0;

            foreach ($request->attendance as $attendanceData) {
                $employee = Employee::find($attendanceData['employee_id']);

                $attendance = Attendance::updateOrCreate(
                    [
                        'employee_id' => $attendanceData['employee_id'],
                        'date' => $date,
                    ],
                    [
                        'status' => $attendanceData['status'],
                        'monthly_salary' => $employee->salary,
                        'notes' => $attendanceData['notes'] ?? null,
                    ]
                );

                $totalCost += $attendance->daily_cost;
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Attendance updated successfully!',
                'total_cost' => number_format($totalCost, 2),
            ]);

        } catch (\Exception $e) {
            DB::rollback();

            return response()->json([
                'success' => false,
                'message' => 'Error updating attendance: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * AJAX: Calculate real-time cost
     */
    public function calculateCost(Request $request)
    {
        $request->validate([
            'attendance' => 'required|array',
            'attendance.*.employee_id' => 'required|exists:employees,id',
            'attendance.*.status' => 'required|in:present,absent,half_day',
        ]);

        $totalCost = 0;
        $employeeCosts = [];

        foreach ($request->attendance as $attendanceData) {
            $employee = Employee::find($attendanceData['employee_id']);
            $dailyCost = Attendance::calculateDailyCost($employee->salary, $attendanceData['status']);

            $employeeCosts[] = [
                'employee_id' => $employee->id,
                'employee_name' => $employee->name,
                'monthly_salary' => number_format($employee->salary, 2),
                'status' => $attendanceData['status'],
                'daily_cost' => number_format($dailyCost, 2),
            ];

            $totalCost += $dailyCost;
        }

        return response()->json([
            'success' => true,
            'total_cost' => number_format($totalCost, 2),
            'employee_costs' => $employeeCosts,
        ]);
    }

    /**
     * AJAX: Get employee info
     */
    public function getEmployeeInfo($employeeId)
    {
        $employee = Employee::with('designation')->find($employeeId);

        if (!$employee) {
            return response()->json([
                'success' => false,
                'message' => 'Employee not found',
            ], 404);
        }

        $dailyRate = $employee->salary / 30;

        return response()->json([
            'success' => true,
            'employee' => [
                'id' => $employee->id,
                'name' => $employee->name,
                'designation' => $employee->designation->name ?? 'N/A',
                'monthly_salary' => number_format($employee->salary, 2),
                'daily_rate' => number_format($dailyRate, 2),
            ],
        ]);
    }

    /**
     * Show attendance reports
     */
    public function reports(Request $request)
    {
        try {
            $month = $request->get('month', now()->format('Y-m'));
            $year = $request->get('year', now()->year);
            $monthNumber = $request->get('month_number', now()->month);

            // Initialize with safe defaults
            $attendances = collect();
            $summary = [
                'total_cost' => 0,
                'present_count' => 0,
                'absent_count' => 0,
                'half_day_count' => 0,
            ];

            try {
                // Try to get database data if available
                if (class_exists('PDO') && \DB::connection()->getPdo()) {
                    $query = Attendance::with(['employee.designation'])
                        ->forMonth($year, $monthNumber);

                    // Apply employee filter if specified
                    if ($request->get('employee')) {
                        $query->where('employee_id', $request->get('employee'));
                    }

                    // Get all attendance records for the month
                    $allAttendances = $query->orderBy('date', 'asc')->get();

                    // Group by employee and create monthly summary for each
                    $employeeAttendances = $allAttendances->groupBy('employee_id')->map(function ($employeeRecords) use ($year, $monthNumber) {
                        $employee = $employeeRecords->first()->employee;
                        $daysInMonth = \Carbon\Carbon::create($year, $monthNumber)->daysInMonth;

                        // Create array for all days of the month
                        $monthlyData = [];
                        for ($day = 1; $day <= $daysInMonth; $day++) {
                            $date = \Carbon\Carbon::create($year, $monthNumber, $day);
                            $attendance = $employeeRecords->where('date', $date->format('Y-m-d'))->first();

                            $monthlyData[$day] = [
                                'date' => $date,
                                'status' => $attendance ? $attendance->status : null,
                                'daily_cost' => $attendance ? $attendance->daily_cost : 0,
                                'working_hours' => $attendance ? $attendance->working_hours : 0,
                                'notes' => $attendance ? $attendance->notes : null,
                            ];
                        }

                        // Calculate employee summary
                        $presentDays = $employeeRecords->where('status', 'present')->count();
                        $absentDays = $employeeRecords->where('status', 'absent')->count();
                        $halfDays = $employeeRecords->where('status', 'half_day')->count();
                        $totalCost = $employeeRecords->sum('daily_cost');
                        $totalHours = $employeeRecords->sum('working_hours');

                        return [
                            'employee' => $employee,
                            'monthly_data' => $monthlyData,
                            'summary' => [
                                'present_days' => $presentDays,
                                'absent_days' => $absentDays,
                                'half_days' => $halfDays,
                                'total_cost' => $totalCost,
                                'total_hours' => $totalHours,
                                'attendance_rate' => $daysInMonth > 0 ? round(($presentDays / $daysInMonth) * 100, 1) : 0,
                            ]
                        ];
                    });

                    // Calculate overall summary
                    $baseQuery = Attendance::forMonth($year, $monthNumber);
                    if ($request->get('employee')) {
                        $baseQuery->where('employee_id', $request->get('employee'));
                    }

                    $summary = [
                        'total_cost' => $baseQuery->sum('daily_cost'),
                        'present_count' => $baseQuery->present()->count(),
                        'absent_count' => $baseQuery->absent()->count(),
                        'half_day_count' => $baseQuery->halfDay()->count(),
                        'total_working_hours' => $baseQuery->sum('working_hours'),
                        'total_employees' => $employeeAttendances->count(),
                        'average_daily_cost' => 0,
                        'cost_per_employee' => 0,
                    ];

                    // Calculate averages
                    if ($summary['present_count'] > 0) {
                        $summary['average_daily_cost'] = $summary['total_cost'] / max(1, now()->day);
                        $summary['cost_per_employee'] = $summary['total_cost'] / $summary['present_count'];
                    }

                    // Convert to paginated collection for compatibility
                    $attendances = new \Illuminate\Pagination\LengthAwarePaginator(
                        $employeeAttendances->values(),
                        $employeeAttendances->count(),
                        50,
                        1,
                        ['path' => request()->url(), 'query' => request()->query()]
                    );
                }
            } catch (\Exception $e) {
                \Log::warning('Database error in attendance reports: ' . $e->getMessage());
            }

            // Try the main view first, fallback to standalone if needed
            try {
                return view('pages.attendance.reports', compact('attendances', 'summary', 'month', 'year', 'monthNumber'));
            } catch (\Exception $e) {
                \Log::warning('Layout issue in attendance reports, using standalone view: ' . $e->getMessage());
                return view('pages.attendance.reports-standalone', compact('attendances', 'summary', 'month', 'year', 'monthNumber'));
            }

        } catch (\Exception $e) {
            \Log::error('Complete failure in attendance reports: ' . $e->getMessage());
            return view('pages.attendance.reports-standalone', [
                'month' => $request->get('month', now()->format('Y-m')),
                'year' => $request->get('year', now()->year),
                'monthNumber' => $request->get('month_number', now()->month),
                'attendances' => collect(),
                'summary' => [
                    'total_cost' => 0,
                    'present_count' => 0,
                    'absent_count' => 0,
                    'half_day_count' => 0,
                ]
            ]);
        }
    }

    /**
     * Show biometric attendance reports
     */
    public function biometricReport(Request $request)
    {
        $startDate = $request->get('start_date', now()->subDays(7)->format('Y-m-d'));
        $endDate = $request->get('end_date', now()->format('Y-m-d'));
        $department = $request->get('department');
        $employee = $request->get('employee');
        $status = $request->get('status');

        // Build query
        $query = Attendance::with(['employee.designation'])
            ->whereBetween('date', [$startDate, $endDate])
            ->orderBy('date', 'desc')
            ->orderBy('in_time', 'asc');

        // Apply filters
        if ($department) {
            $query->whereHas('employee.designation', function($q) use ($department) {
                $q->where('id', $department);
            });
        }

        if ($employee) {
            $query->where('employee_id', $employee);
        }

        if ($status) {
            $query->where('status', $status);
        }

        $attendances = $query->paginate(50);

        // Calculate summary statistics
        $summaryQuery = Attendance::with(['employee.designation'])
            ->whereBetween('date', [$startDate, $endDate]);

        if ($department) {
            $summaryQuery->whereHas('employee.designation', function($q) use ($department) {
                $q->where('id', $department);
            });
        }

        if ($employee) {
            $summaryQuery->where('employee_id', $employee);
        }

        $summaryData = $summaryQuery->get();

        $summary = [
            'total_present' => $summaryData->where('status', 'present')->count(),
            'total_absent' => $summaryData->where('status', 'absent')->count(),
            'total_half_day' => $summaryData->where('status', 'half_day')->count(),
            'total_cost' => $summaryData->sum('daily_cost'),
            'avg_working_hours' => $summaryData->where('working_hours', '>', 0)->avg('working_hours'),
            'total_working_hours' => $summaryData->sum('working_hours'),
            'imported_records' => $summaryData->where('is_imported', true)->count(),
            'manual_records' => $summaryData->where('is_imported', false)->count(),
        ];

        // Get filter options
        $departments = \App\Models\Designation::orderBy('name')->get();
        $employees = \App\Models\Employee::active()->orderBy('name')->get();

        return view('pages.attendance.biometric-report', compact(
            'attendances',
            'summary',
            'startDate',
            'endDate',
            'department',
            'employee',
            'status',
            'departments',
            'employees'
        ));
    }

    /**
     * Bulk mark all employees as present
     */
    public function bulkMarkPresent(Request $request)
    {
        $request->validate([
            'date' => 'required|date|before_or_equal:today',
        ]);

        DB::beginTransaction();

        try {
            $date = $request->date;
            $employees = Employee::where('status', 1)->get();
            $totalCost = 0;

            foreach ($employees as $employee) {
                $attendance = Attendance::updateOrCreate(
                    [
                        'employee_id' => $employee->id,
                        'date' => $date,
                    ],
                    [
                        'status' => 'present',
                        'monthly_salary' => $employee->salary,
                        'notes' => 'Bulk marked as present',
                    ]
                );

                $totalCost += $attendance->daily_cost;
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'All employees marked as present successfully!',
                'total_cost' => number_format($totalCost, 2),
                'employees_count' => $employees->count(),
            ]);

        } catch (\Exception $e) {
            DB::rollback();

            return response()->json([
                'success' => false,
                'message' => 'Error marking bulk attendance: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get attendance statistics for dashboard
     */
    public function getStats(Request $request)
    {
        $date = $request->get('date', now()->format('Y-m-d'));

        $todayAttendance = Attendance::forDate($date)->get();
        $totalEmployees = Employee::where('status', 1)->count();

        $stats = [
            'total_employees' => $totalEmployees,
            'present_count' => $todayAttendance->where('status', 'present')->count(),
            'half_day_count' => $todayAttendance->where('status', 'half_day')->count(),
            'absent_count' => $todayAttendance->where('status', 'absent')->count(),
            'not_marked_count' => $totalEmployees - $todayAttendance->count(),
            'total_cost' => $todayAttendance->sum('daily_cost'),
            'attendance_percentage' => $totalEmployees > 0 ? round(($todayAttendance->count() / $totalEmployees) * 100, 1) : 0,
        ];

        return response()->json([
            'success' => true,
            'stats' => $stats,
            'date' => $date,
        ]);
    }

    /**
     * Show attendance import page
     */
    public function import()
    {
        return view('hr.attendances.import');
    }

    /**
     * Show monthly attendance sheet
     */
    public function monthlySheet(Request $request)
    {
        $month = $request->get('month', now()->month);
        $year = $request->get('year', now()->year);
        $department = $request->get('department');

        // Get month name and days in month
        $monthName = date('F', mktime(0, 0, 0, $month, 1));
        $daysInMonth = cal_days_in_month(CAL_GREGORIAN, $month, $year);

        // Get employees based on department filter
        $employeesQuery = Employee::with('designation')->where('status', 1);

        if ($department) {
            $employeesQuery->where('designation_id', $department);
        }

        $employees = $employeesQuery->orderBy('name')->get();

        // Get all attendance records for the month
        $attendances = Attendance::with('employee')
            ->whereYear('date', $year)
            ->whereMonth('date', $month)
            ->whereIn('employee_id', $employees->pluck('id'))
            ->get();

        // Organize attendance data by employee and date
        $attendanceData = [];
        foreach ($employees as $employee) {
            $attendanceData[$employee->id] = [];
        }

        foreach ($attendances as $attendance) {
            $date = $attendance->date->format('Y-m-d');
            $attendanceData[$attendance->employee_id][$date] = $attendance;
        }

        // Calculate summary statistics
        $summary = [
            'total_present' => $attendances->where('status', 'present')->count(),
            'total_absent' => $attendances->where('status', 'absent')->count(),
            'total_half_days' => $attendances->where('status', 'half_day')->count(),
            'attendance_percentage' => 0,
        ];

        $totalPossibleDays = $employees->count() * $daysInMonth;
        if ($totalPossibleDays > 0) {
            $totalAttendedDays = $summary['total_present'] + ($summary['total_half_days'] * 0.5);
            $summary['attendance_percentage'] = round(($totalAttendedDays / $totalPossibleDays) * 100, 1);
        }

        // Get departments for filter
        $departments = \App\Models\Designation::orderBy('name')->get();

        return view('pages.attendance.monthly-sheet', compact(
            'employees',
            'attendanceData',
            'month',
            'year',
            'monthName',
            'daysInMonth',
            'summary',
            'departments',
            'department'
        ));
    }
}
