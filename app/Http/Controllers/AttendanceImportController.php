<?php

namespace App\Http\Controllers;

use App\Imports\AttendanceImport;
use App\Models\Attendance;
use App\Models\Employee;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Carbon\Carbon;

class AttendanceImportController extends Controller
{
    public function __construct()
    {
        // Temporarily disable permission middleware for debugging
        // $this->middleware('permission:employees-read')->only('index', 'show');
        // $this->middleware('permission:employees-create')->only('store');
    }

    /**
     * Show the attendance import form
     */
    public function index()
    {
        // For debugging, let's start with a simple response
        try {
            // Get basic data with safe defaults
            $recentImports = [
                'total_imported_records' => 0,
                'recent_imports' => 0,
                'last_import_date' => null,
                'unique_employees' => 0
            ];

            $employeesWithMachineId = 0;
            $totalEmployees = 0;

            try {
                // Try to get database statistics, but don't fail if database is unavailable
                if (class_exists('PDO') && \DB::connection()->getPdo()) {
                    $recentImports['total_imported_records'] = Attendance::where('is_imported', true)->count();
                    $employeesWithMachineId = Employee::whereNotNull('machine_user_id')->count();
                    $totalEmployees = Employee::count();
                }
            } catch (\Exception $e) {
                // Log the error but continue with default values
                \Log::warning('Database connection issue in attendance import: ' . $e->getMessage());
            }

            // Try to render the standalone view first (it's more reliable)
            return view('pages.attendance.import-standalone', compact('recentImports', 'employeesWithMachineId', 'totalEmployees'));

        } catch (\Exception $e) {
            // If everything fails, return a simple HTML response
            \Log::error('Complete failure in attendance import controller: ' . $e->getMessage());
            return response('<html><body><h1>Attendance Import</h1><p>System is experiencing issues. Please contact administrator.</p><p>Error: ' . $e->getMessage() . '</p></body></html>');
        }
    }

    /**
     * Process the uploaded attendance file
     */
    public function store(Request $request)
    {
        $request->validate([
            'attendance_file' => 'required|file|mimes:csv,xlsx,xls|max:10240', // 10MB max
        ], [
            'attendance_file.required' => 'Please select a file to import.',
            'attendance_file.mimes' => 'File must be in CSV or Excel format (.csv, .xlsx, .xls).',
            'attendance_file.max' => 'File size must not exceed 10MB.',
        ]);

        try {
            DB::beginTransaction();

            $file = $request->file('attendance_file');
            $originalName = $file->getClientOriginalName();

            // Store file temporarily for audit
            $filePath = $file->store('imports/attendance', 'local');

            Log::info('Starting attendance import', [
                'file_name' => $originalName,
                'file_size' => $file->getSize(),
                'user_id' => auth()->id()
            ]);

            $import = new AttendanceImport();

            // Import the file
            Excel::import($import, $file);

            // Get the results from the import
            $results = $import->getResults();

            // Enhanced results with additional statistics
            $enhancedResults = $this->enhanceImportResults($results);

            // Log successful import
            Log::info('Attendance import completed', [
                'file_name' => $originalName,
                'total_rows' => $results['total_rows'],
                'inserted' => $results['inserted_records'],
                'updated' => $results['updated_records'],
                'errors' => count($results['errors'])
            ]);

            DB::commit();

            return redirect()->back()->with([
                'success' => 'Attendance data imported successfully!',
                'import_results' => $enhancedResults,
                'file_name' => $originalName
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Attendance import failed', [
                'error' => $e->getMessage(),
                'file_name' => $originalName ?? 'unknown',
                'user_id' => auth()->id()
            ]);

            return redirect()->back()->with('error', 'Import failed: ' . $e->getMessage());
        }
    }

    /**
     * Show detailed attendance report for imported data
     */
    public function show(Request $request)
    {
        try {
            $dateFrom = $request->get('date_from', Carbon::now()->startOfMonth()->format('Y-m-d'));
            $dateTo = $request->get('date_to', Carbon::now()->endOfMonth()->format('Y-m-d'));
            $employeeId = $request->get('employee_id');

            // Initialize with safe defaults
            $attendances = collect();
            $employees = collect();
            $statistics = [
                'overall' => [
                    'total_records' => 0,
                    'total_employees' => 0,
                    'total_present' => 0,
                    'total_half_day' => 0,
                    'total_absent' => 0,
                    'total_hours' => 0,
                    'average_hours_per_day' => 0
                ],
                'employee_stats' => collect()
            ];

            try {
                // Try to get database data if available
                if (class_exists('PDO') && \DB::connection()->getPdo()) {
                    $query = Attendance::with('employee')
                        ->where('is_imported', true)
                        ->whereBetween('date', [$dateFrom, $dateTo]);

                    if ($employeeId) {
                        $query->where('employee_id', $employeeId);
                    }

                    $attendances = $query->orderBy('date', 'desc')
                        ->orderBy('employee_id')
                        ->get();

                    $employees = Employee::whereNotNull('machine_user_id')->get();
                    $statistics = $this->calculateAttendanceStatistics($attendances);
                }
            } catch (\Exception $e) {
                \Log::warning('Database error in attendance report: ' . $e->getMessage());
            }

            return view('pages.attendance.import-report', compact(
                'attendances', 'employees', 'statistics', 'dateFrom', 'dateTo', 'employeeId'
            ));

        } catch (\Exception $e) {
            \Log::error('Complete failure in attendance report: ' . $e->getMessage());
            return view('pages.attendance.import-report-standalone', [
                'dateFrom' => $request->get('date_from', Carbon::now()->startOfMonth()->format('Y-m-d')),
                'dateTo' => $request->get('date_to', Carbon::now()->endOfMonth()->format('Y-m-d')),
                'employeeId' => $request->get('employee_id'),
                'attendances' => collect(),
                'employees' => collect(),
                'statistics' => [
                    'overall' => [
                        'total_records' => 0,
                        'total_employees' => 0,
                        'total_present' => 0,
                        'total_half_day' => 0,
                        'total_absent' => 0,
                        'total_hours' => 0,
                        'average_hours_per_day' => 0
                    ],
                    'employee_stats' => collect()
                ]
            ]);
        }
    }

    /**
     * Get recent import statistics
     */
    private function getRecentImportStats()
    {
        $lastWeek = Carbon::now()->subWeek();

        return [
            'total_imported_records' => Attendance::where('is_imported', true)->count(),
            'recent_imports' => Attendance::where('is_imported', true)
                ->where('created_at', '>=', $lastWeek)
                ->count(),
            'last_import_date' => Attendance::where('is_imported', true)
                ->latest('created_at')
                ->value('created_at'),
            'unique_employees' => Attendance::where('is_imported', true)
                ->distinct('employee_id')
                ->count('employee_id')
        ];
    }

    /**
     * Enhance import results with additional statistics
     */
    private function enhanceImportResults($results)
    {
        if (empty($results['attendance_data'])) {
            return $results;
        }

        // Group by employee for statistics
        $employeeGroups = collect($results['attendance_data'])->groupBy('employee_name');

        $results['employee_summary'] = $employeeGroups->map(function ($records, $employeeName) {
            $totalHours = $records->sum(function ($record) {
                return (float) ($record['working_hours'] ?? 0);
            });

            $presentDays = $records->where('status', 'present')->count();
            $halfDays = $records->where('status', 'half_day')->count();
            $absentDays = $records->where('status', 'absent')->count();

            return [
                'employee_name' => $employeeName,
                'total_days' => $records->count(),
                'present_days' => $presentDays,
                'half_days' => $halfDays,
                'absent_days' => $absentDays,
                'total_hours' => round($totalHours, 2),
                'average_hours' => $presentDays > 0 ? round($totalHours / $presentDays, 2) : 0,
                'date_range' => [
                    'from' => $records->min('date'),
                    'to' => $records->max('date')
                ]
            ];
        })->values();

        // Overall statistics
        $results['overall_stats'] = [
            'total_employees' => $employeeGroups->count(),
            'total_working_days' => collect($results['attendance_data'])->where('status', 'present')->count(),
            'total_working_hours' => collect($results['attendance_data'])->sum(function ($record) {
                return (float) ($record['working_hours'] ?? 0);
            }),
            'average_hours_per_day' => 0
        ];

        if ($results['overall_stats']['total_working_days'] > 0) {
            $results['overall_stats']['average_hours_per_day'] = round(
                $results['overall_stats']['total_working_hours'] / $results['overall_stats']['total_working_days'],
                2
            );
        }

        return $results;
    }

    /**
     * Calculate attendance statistics
     */
    private function calculateAttendanceStatistics($attendances)
    {
        // Handle empty attendances collection
        if (!$attendances || $attendances->isEmpty()) {
            return [
                'employee_stats' => collect(),
                'overall' => [
                    'total_records' => 0,
                    'total_employees' => 0,
                    'total_present' => 0,
                    'total_half_day' => 0,
                    'total_absent' => 0,
                    'total_hours' => 0,
                    'average_hours_per_day' => 0
                ]
            ];
        }

        try {
            $employeeStats = $attendances->groupBy('employee_id')->map(function ($records, $employeeId) {
                $employee = $records->first()->employee ?? (object)['name' => 'Unknown', 'machine_user_id' => 'N/A'];
                $totalHours = $records->sum('working_hours') ?? 0;
                $presentDays = $records->where('status', 'present')->count();
                $halfDays = $records->where('status', 'half_day')->count();
                $absentDays = $records->where('status', 'absent')->count();

                return [
                    'employee' => $employee,
                    'total_days' => $records->count(),
                    'present_days' => $presentDays,
                    'half_days' => $halfDays,
                    'absent_days' => $absentDays,
                    'total_hours' => round($totalHours, 2),
                    'average_hours' => $presentDays > 0 ? round($totalHours / $presentDays, 2) : 0,
                    'attendance_rate' => $records->count() > 0 ? round(($presentDays + $halfDays * 0.5) / $records->count() * 100, 1) : 0
                ];
            });

            return [
                'employee_stats' => $employeeStats,
                'overall' => [
                    'total_records' => $attendances->count(),
                    'total_employees' => $attendances->unique('employee_id')->count(),
                    'total_present' => $attendances->where('status', 'present')->count(),
                    'total_half_day' => $attendances->where('status', 'half_day')->count(),
                    'total_absent' => $attendances->where('status', 'absent')->count(),
                    'total_hours' => round($attendances->sum('working_hours'), 2),
                    'average_hours_per_day' => $attendances->where('status', 'present')->count() > 0
                        ? round($attendances->sum('working_hours') / $attendances->where('status', 'present')->count(), 2)
                        : 0
                ]
            ];
        } catch (\Exception $e) {
            \Log::warning('Error calculating attendance statistics: ' . $e->getMessage());
            return [
                'employee_stats' => collect(),
                'overall' => [
                    'total_records' => 0,
                    'total_employees' => 0,
                    'total_present' => 0,
                    'total_half_day' => 0,
                    'total_absent' => 0,
                    'total_hours' => 0,
                    'average_hours_per_day' => 0
                ]
            ];
        }
    }
}
