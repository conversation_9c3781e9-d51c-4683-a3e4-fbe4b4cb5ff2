<?php

namespace App\Http\Controllers\Auth;

use Illuminate\View\View;
use Illuminate\Http\Request;
use Spatie\Permission\Models\Role;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\RedirectResponse;
use App\Http\Requests\Auth\LoginRequest;

class AuthenticatedSessionController extends Controller
{
    /**
     * Display the login view.
     */
    public function create(): View
    {
        return view('auth.login');
    }

    /**
     * Handle an incoming authentication request.
     */
    public function store(LoginRequest $request): RedirectResponse
    {
        $request->authenticate();
        $request->session()->regenerate();

        // Update last login timestamp if the column exists
        $user = auth()->user();
        if (method_exists($user, 'updateLastLogin') && \Schema::hasColumn('users', 'last_login_at')) {
            try {
                $user->updateLastLogin();
            } catch (\Exception $e) {
                // Silently continue if last_login_at column doesn't exist
                \Log::info('Could not update last login timestamp: ' . $e->getMessage());
            }
        }

        // Check if this is an AJAX request (for backward compatibility)
        if ($request->expectsJson()) {
            $remember = $request->filled('remember') ? 1 : 0;
            $role = Role::where('name', $user->role)->first();

            if ($role && $role->permissions->count() > 0) {
                $first_role = $role->permissions->pluck('name')->all()[0];
                $page = explode('-', $first_role);
                if ($page[0] == 'reports') {
                    $first_role = $role->permissions->pluck('name')->all()[1];
                    $page = explode('-', $first_role);
                }
                $redirect_url = url($page[0]);
            } else {
                $redirect_url = url('/dashboard');
            }

            return response()->json([
                'remember' => $remember,
                'redirect' => $redirect_url,
                'message' => __('Logged In Successfully'),
            ]);
        }

        // For standard form submissions, redirect to intended page or dashboard
        return redirect()->intended('/dashboard');
    }

    /**
     * Destroy an authenticated session.
     */
    public function destroy(Request $request): RedirectResponse
    {
        Auth::guard('web')->logout();

        $request->session()->invalidate();

        $request->session()->regenerateToken();

        return redirect('/');
    }
}
