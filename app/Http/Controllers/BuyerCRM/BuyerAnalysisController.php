<?php

namespace App\Http\Controllers\BuyerCRM;

use App\Http\Controllers\Controller;
use App\Models\BuyerCRM\BuyerProfile;
use App\Models\BuyerCRM\BuyerMeeting;
use App\Models\BuyerCRM\BuyerPipeline;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

/**
 * Buyer Analysis Controller
 * 
 * Handles buyer analysis, filtering, and reporting functionality
 */
class BuyerAnalysisController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display the main analysis dashboard.
     */
    public function index(Request $request): View
    {
        $query = BuyerProfile::with(['assignedUser', 'currentPipeline', 'score']);

        // Apply filters
        $filters = $this->applyFilters($query, $request);

        // Get filtered results
        $buyers = $query->paginate(config('buyer-crm.pagination.per_page', 15));

        // Get analysis statistics
        $stats = $this->getAnalysisStatistics($request);

        // Get filter options
        $filterOptions = $this->getFilterOptions();

        return view('buyer-crm.analysis.index', compact('buyers', 'stats', 'filterOptions', 'filters'));
    }

    /**
     * Get buyers by region analysis.
     */
    public function byRegion(Request $request): JsonResponse
    {
        $regionData = BuyerProfile::select('country', DB::raw('count(*) as count'))
            ->groupBy('country')
            ->orderBy('count', 'desc')
            ->get()
            ->map(function ($item) {
                $buyer = new BuyerProfile(['country' => $item->country]);
                return [
                    'country' => $item->country,
                    'region' => $buyer->getRegionAttribute(),
                    'count' => $item->count,
                ];
            })
            ->groupBy('region')
            ->map(function ($countries, $region) {
                return [
                    'region' => $region,
                    'total_buyers' => $countries->sum('count'),
                    'countries' => $countries->map(function ($country) {
                        return [
                            'country' => $country['country'],
                            'count' => $country['count'],
                        ];
                    })->values(),
                ];
            })
            ->values();

        return response()->json([
            'success' => true,
            'data' => $regionData
        ]);
    }

    /**
     * Get buyers by product category analysis.
     */
    public function byProduct(Request $request): JsonResponse
    {
        $productData = BuyerProfile::whereNotNull('interest')
            ->get()
            ->flatMap(function ($buyer) {
                return $buyer->interest ?? [];
            })
            ->countBy()
            ->sortDesc()
            ->take(20)
            ->map(function ($count, $product) {
                return [
                    'product' => $product,
                    'count' => $count,
                    'percentage' => round(($count / BuyerProfile::count()) * 100, 2),
                ];
            })
            ->values();

        return response()->json([
            'success' => true,
            'data' => $productData
        ]);
    }

    /**
     * Get buyers by activity analysis.
     */
    public function byActivity(Request $request): JsonResponse
    {
        $days = $request->get('days', 30);
        $cutoffDate = now()->subDays($days);

        // Get buyers with their last contact date
        $activityData = BuyerProfile::with(['meetings' => function ($query) use ($cutoffDate) {
                $query->where('meeting_date', '>=', $cutoffDate)
                      ->orderBy('meeting_date', 'desc');
            }])
            ->get()
            ->map(function ($buyer) use ($cutoffDate) {
                $lastMeeting = $buyer->meetings->first();
                $daysSinceContact = $lastMeeting 
                    ? $lastMeeting->meeting_date->diffInDays(now())
                    : null;

                return [
                    'id' => $buyer->id,
                    'name' => $buyer->name,
                    'company' => $buyer->company,
                    'priority' => $buyer->priority,
                    'last_contact' => $lastMeeting?->meeting_date,
                    'days_since_contact' => $daysSinceContact,
                    'meeting_count' => $buyer->meetings->count(),
                    'needs_attention' => $buyer->priority === 'Hot' && (!$lastMeeting || $daysSinceContact > 15),
                ];
            });

        // Group by activity level
        $grouped = [
            'highly_active' => $activityData->filter(fn($b) => $b['days_since_contact'] !== null && $b['days_since_contact'] <= 7)->values(),
            'moderately_active' => $activityData->filter(fn($b) => $b['days_since_contact'] !== null && $b['days_since_contact'] > 7 && $b['days_since_contact'] <= 30)->values(),
            'low_activity' => $activityData->filter(fn($b) => $b['days_since_contact'] !== null && $b['days_since_contact'] > 30)->values(),
            'no_contact' => $activityData->filter(fn($b) => $b['days_since_contact'] === null)->values(),
            'needs_attention' => $activityData->filter(fn($b) => $b['needs_attention'])->values(),
        ];

        return response()->json([
            'success' => true,
            'data' => $grouped,
            'summary' => [
                'total_buyers' => $activityData->count(),
                'highly_active' => $grouped['highly_active']->count(),
                'moderately_active' => $grouped['moderately_active']->count(),
                'low_activity' => $grouped['low_activity']->count(),
                'no_contact' => $grouped['no_contact']->count(),
                'needs_attention' => $grouped['needs_attention']->count(),
            ]
        ]);
    }

    /**
     * Apply filters to the buyer query.
     */
    private function applyFilters($query, Request $request): array
    {
        $appliedFilters = [];

        // Region filter
        if ($request->filled('region')) {
            $region = $request->region;
            $appliedFilters['region'] = $region;
            
            $regionCountries = $this->getCountriesByRegion($region);
            $query->whereIn('country', $regionCountries);
        }

        // Country filter
        if ($request->filled('country')) {
            $appliedFilters['country'] = $request->country;
            $query->byCountry($request->country);
        }

        // Product interest filter
        if ($request->filled('product')) {
            $appliedFilters['product'] = $request->product;
            $query->whereJsonContains('interest', $request->product);
        }

        // Buyer type filter
        if ($request->filled('type')) {
            $appliedFilters['type'] = $request->type;
            $query->byType($request->type);
        }

        // Priority filter
        if ($request->filled('priority')) {
            $appliedFilters['priority'] = $request->priority;
            $query->byPriority($request->priority);
        }

        // Status filter
        if ($request->filled('status')) {
            $appliedFilters['status'] = $request->status;
            $query->where('status', $request->status);
        }

        // Pipeline stage filter
        if ($request->filled('pipeline_stage')) {
            $appliedFilters['pipeline_stage'] = $request->pipeline_stage;
            $query->whereHas('currentPipeline', function ($q) use ($request) {
                $q->where('current_stage', $request->pipeline_stage);
            });
        }

        // Last contacted filter
        if ($request->filled('last_contacted')) {
            $appliedFilters['last_contacted'] = $request->last_contacted;
            $days = (int) $request->last_contacted;
            
            if ($days === 0) {
                // Never contacted
                $query->whereDoesntHave('meetings');
            } else {
                // Contacted within X days
                $query->whereHas('meetings', function ($q) use ($days) {
                    $q->where('meeting_date', '>=', now()->subDays($days));
                });
            }
        }

        // Assigned user filter
        if ($request->filled('assigned_to')) {
            $appliedFilters['assigned_to'] = $request->assigned_to;
            $query->assignedTo($request->assigned_to);
        }

        // Rating filter
        if ($request->filled('min_rating')) {
            $appliedFilters['min_rating'] = $request->min_rating;
            $query->where('rating', '>=', $request->min_rating);
        }

        return $appliedFilters;
    }

    /**
     * Get analysis statistics.
     */
    private function getAnalysisStatistics(Request $request): array
    {
        $baseQuery = BuyerProfile::query();
        $this->applyFilters($baseQuery, $request);

        return [
            'total_buyers' => $baseQuery->count(),
            'by_priority' => [
                'hot' => (clone $baseQuery)->where('priority', 'Hot')->count(),
                'warm' => (clone $baseQuery)->where('priority', 'Warm')->count(),
                'cold' => (clone $baseQuery)->where('priority', 'Cold')->count(),
            ],
            'by_status' => [
                'active' => (clone $baseQuery)->where('status', 'Active')->count(),
                'inactive' => (clone $baseQuery)->where('status', 'Inactive')->count(),
                'blocked' => (clone $baseQuery)->where('status', 'Blocked')->count(),
            ],
            'by_type' => [
                'brand' => (clone $baseQuery)->where('type', 'Brand')->count(),
                'retailer' => (clone $baseQuery)->where('type', 'Retailer')->count(),
                'wholesaler' => (clone $baseQuery)->where('type', 'Wholesaler')->count(),
                'agent' => (clone $baseQuery)->where('type', 'Agent')->count(),
            ],
            'avg_rating' => round((clone $baseQuery)->whereNotNull('rating')->avg('rating'), 2),
            'conversion_rate' => $this->getConversionRate($baseQuery),
        ];
    }

    /**
     * Get filter options for dropdowns.
     */
    private function getFilterOptions(): array
    {
        return [
            'regions' => ['Asia', 'Europe', 'North America', 'South America', 'Africa', 'Oceania', 'Other'],
            'countries' => BuyerProfile::distinct()->pluck('country')->sort()->values(),
            'products' => BuyerProfile::whereNotNull('interest')
                ->get()
                ->flatMap(fn($buyer) => $buyer->interest ?? [])
                ->unique()
                ->sort()
                ->values(),
            'types' => ['Brand', 'Retailer', 'Wholesaler', 'Agent'],
            'priorities' => ['Hot', 'Warm', 'Cold'],
            'statuses' => ['Active', 'Inactive', 'Blocked'],
            'pipeline_stages' => BuyerPipeline::$stages,
        ];
    }

    /**
     * Get countries by region.
     */
    private function getCountriesByRegion(string $region): array
    {
        $regions = [
            'Asia' => ['Bangladesh', 'India', 'Pakistan', 'China', 'Japan', 'South Korea', 'Thailand', 'Vietnam', 'Indonesia', 'Malaysia', 'Singapore', 'Philippines'],
            'Europe' => ['Germany', 'France', 'Italy', 'Spain', 'United Kingdom', 'Netherlands', 'Belgium', 'Sweden', 'Denmark', 'Norway', 'Finland'],
            'North America' => ['United States', 'Canada', 'Mexico'],
            'South America' => ['Brazil', 'Argentina', 'Chile', 'Colombia', 'Peru'],
            'Africa' => ['South Africa', 'Egypt', 'Nigeria', 'Kenya', 'Morocco'],
            'Oceania' => ['Australia', 'New Zealand'],
        ];

        return $regions[$region] ?? [];
    }

    /**
     * Calculate conversion rate for filtered buyers.
     */
    private function getConversionRate($baseQuery): float
    {
        $totalBuyers = (clone $baseQuery)->count();
        
        if ($totalBuyers === 0) {
            return 0;
        }

        $convertedBuyers = (clone $baseQuery)->whereHas('currentPipeline', function ($q) {
            $q->where('current_stage', 'Converted');
        })->count();

        return round(($convertedBuyers / $totalBuyers) * 100, 2);
    }
}
