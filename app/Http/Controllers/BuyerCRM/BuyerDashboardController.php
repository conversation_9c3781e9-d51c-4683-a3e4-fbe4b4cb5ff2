<?php

namespace App\Http\Controllers\BuyerCRM;

use App\Http\Controllers\Controller;
use App\Models\BuyerCRM\BuyerProfile;
use App\Models\BuyerCRM\BuyerPipeline;
use App\Models\BuyerCRM\BuyerMeeting;
use App\Models\BuyerCRM\BuyerFollowUp;
use App\Models\BuyerCRM\BuyerDocument;
use App\Models\BuyerCRM\BuyerScore;
use App\Models\BuyerCRM\BuyerDropReason;
use App\Models\BuyerCRM\MarketingCampaign;
use App\Models\BuyerCRM\MarketingCampaignBuyer;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

/**
 * Buyer Dashboard Controller
 * 
 * Handles dashboard analytics and reporting for the Buyer CRM system
 */
class BuyerDashboardController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display the main dashboard.
     */
    public function index(): View
    {
        // Get cached dashboard data or compute it
        $dashboardData = Cache::remember('buyer_crm_dashboard', config('buyer-crm.cache.dashboard_ttl', 3600), function () {
            return $this->getDashboardData();
        });

        return view('buyer-crm.dashboard.index', $dashboardData);
    }

    /**
     * Show analytics dashboard.
     */
    public function analytics(Request $request)
    {
        $analytics = Cache::remember('buyer_crm_analytics', config('buyer-crm.cache.analytics_ttl', 1800), function () {
            return $this->getAnalyticsData();
        });

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'analytics' => $analytics
            ]);
        }

        return view('buyer-crm.reports.analytics', compact('analytics'));
    }

    /**
     * Get dashboard statistics.
     */
    public function stats(): JsonResponse
    {
        $stats = $this->getBasicStats();

        return response()->json([
            'success' => true,
            'stats' => $stats
        ]);
    }

    /**
     * Get chart data for dashboard.
     */
    public function charts(Request $request): JsonResponse
    {
        $chartType = $request->get('type', 'all');
        
        $charts = [];

        if ($chartType === 'all' || $chartType === 'pipeline') {
            $charts['pipeline'] = $this->getPipelineChartData();
        }

        if ($chartType === 'all' || $chartType === 'regional') {
            $charts['regional'] = $this->getRegionalChartData();
        }

        if ($chartType === 'all' || $chartType === 'trends') {
            $charts['trends'] = $this->getTrendsChartData();
        }

        if ($chartType === 'all' || $chartType === 'engagement') {
            $charts['engagement'] = $this->getEngagementChartData();
        }

        return response()->json([
            'success' => true,
            'charts' => $charts
        ]);
    }

    /**
     * Get main dashboard data.
     */
    private function getDashboardData(): array
    {
        return [
            'stats' => $this->getBasicStats(),
            'recent_activities' => $this->getRecentActivities(),
            'hot_buyers_alert' => $this->getHotBuyersAlert(),
            'upcoming_tasks' => $this->getUpcomingTasks(),
            'pipeline_summary' => $this->getPipelineSummary(),
            'top_performers' => $this->getTopPerformers(),
        ];
    }

    /**
     * Get comprehensive analytics data.
     */
    private function getAnalyticsData(): array
    {
        return [
            'overview' => $this->getBasicStats(),
            'pipeline_analysis' => $this->getPipelineAnalysis(),
            'regional_distribution' => $this->getRegionalDistribution(),
            'engagement_metrics' => $this->getEngagementMetrics(),
            'campaign_performance' => $this->getCampaignPerformance(),
            'monthly_trends' => $this->getMonthlyTrends(),
            'risk_analysis' => $this->getRiskAnalysis(),
        ];
    }

    /**
     * Get basic statistics.
     */
    private function getBasicStats(): array
    {
        return [
            'total_buyers' => BuyerProfile::count(),
            'active_buyers' => BuyerProfile::active()->count(),
            'hot_buyers' => BuyerProfile::hot()->count(),
            'converted_buyers' => BuyerProfile::converted()->count(),
            'total_meetings' => BuyerMeeting::count(),
            'meetings_this_month' => BuyerMeeting::whereMonth('meeting_date', now()->month)
                                                    ->whereYear('meeting_date', now()->year)
                                                    ->count(),
            'pending_follow_ups' => BuyerFollowUp::pending()->count(),
            'overdue_tasks' => BuyerFollowUp::overdue()->count(),
            'active_campaigns' => MarketingCampaign::active()->count(),
            'total_documents' => BuyerDocument::count(),
            'high_engagement_buyers' => BuyerScore::highEngagement()->count(),
            'dropped_buyers' => BuyerProfile::dropped()->count(),
        ];
    }

    /**
     * Get recent activities.
     */
    private function getRecentActivities(): array
    {
        $activities = [];

        // Recent meetings
        $recentMeetings = BuyerMeeting::with(['buyer', 'addedBy'])
            ->orderBy('meeting_date', 'desc')
            ->limit(5)
            ->get();

        foreach ($recentMeetings as $meeting) {
            $activities[] = [
                'type' => 'meeting',
                'icon' => $meeting->getInteractionTypeIcon(),
                'title' => "Meeting with {$meeting->buyer->name}",
                'description' => $meeting->getSummaryExcerpt(100),
                'user' => $meeting->addedBy->name,
                'date' => $meeting->meeting_date,
                'url' => route('buyer-crm.meetings.show', $meeting),
            ];
        }

        // Recent follow-ups completed
        $recentFollowUps = BuyerFollowUp::with(['buyer', 'assignedUser'])
            ->completed()
            ->orderBy('completed_at', 'desc')
            ->limit(5)
            ->get();

        foreach ($recentFollowUps as $followUp) {
            $activities[] = [
                'type' => 'follow_up',
                'icon' => $followUp->getTaskTypeIcon(),
                'title' => "Completed: {$followUp->task_type} for {$followUp->buyer->name}",
                'description' => $followUp->note,
                'user' => $followUp->assignedUser->name,
                'date' => $followUp->completed_at,
                'url' => route('buyer-crm.follow-ups.show', $followUp),
            ];
        }

        // Sort by date and limit
        usort($activities, function ($a, $b) {
            return $b['date'] <=> $a['date'];
        });

        return array_slice($activities, 0, 10);
    }

    /**
     * Get hot buyers that need attention.
     */
    private function getHotBuyersAlert(): array
    {
        $hotBuyersNotContacted = BuyerProfile::hot()
            ->with(['meetings', 'assignedUser'])
            ->get()
            ->filter(function ($buyer) {
                return !$buyer->hasRecentContact(config('buyer-crm.pipeline.hot_buyer_alert_days', 15));
            })
            ->take(10)
            ->map(function ($buyer) {
                return [
                    'id' => $buyer->id,
                    'name' => $buyer->name,
                    'company' => $buyer->company,
                    'assigned_to' => $buyer->assignedUser?->name,
                    'last_contact' => $buyer->getLastContactDateAttribute(),
                    'days_since_contact' => $buyer->getLastContactDateAttribute() 
                        ? $buyer->getLastContactDateAttribute()->diffInDays(now())
                        : null,
                    'url' => route('buyer-crm.buyers.show', $buyer),
                ];
            })
            ->values()
            ->toArray();

        return $hotBuyersNotContacted;
    }

    /**
     * Get upcoming tasks.
     */
    private function getUpcomingTasks(): array
    {
        return BuyerFollowUp::with(['buyer', 'assignedUser'])
            ->pending()
            ->where('task_date', '>=', now())
            ->where('task_date', '<=', now()->addDays(7))
            ->orderBy('task_date')
            ->limit(10)
            ->get()
            ->map(function ($task) {
                return [
                    'id' => $task->id,
                    'type' => $task->task_type,
                    'buyer_name' => $task->buyer->name,
                    'assigned_to' => $task->assignedUser->name,
                    'task_date' => $task->task_date,
                    'priority' => $task->getPriority(),
                    'note' => $task->note,
                    'url' => route('buyer-crm.follow-ups.show', $task),
                ];
            })
            ->toArray();
    }

    /**
     * Get pipeline summary.
     */
    private function getPipelineSummary(): array
    {
        $summary = [];
        
        foreach (BuyerPipeline::$stages as $stage) {
            $count = BuyerPipeline::latestForEachBuyer()
                ->where('current_stage', $stage)
                ->count();
                
            $summary[] = [
                'stage' => $stage,
                'count' => $count,
                'percentage' => BuyerProfile::count() > 0 
                    ? round(($count / BuyerProfile::count()) * 100, 1)
                    : 0,
            ];
        }

        return $summary;
    }

    /**
     * Get top performers.
     */
    private function getTopPerformers(): array
    {
        return BuyerScore::with('buyer')
            ->highEngagement()
            ->orderBy('score', 'desc')
            ->limit(10)
            ->get()
            ->map(function ($score) {
                return [
                    'buyer_id' => $score->buyer->id,
                    'buyer_name' => $score->buyer->name,
                    'company' => $score->buyer->company,
                    'score' => $score->score,
                    'engagement_level' => $score->engagement_level,
                    'current_stage' => $score->buyer->getCurrentStageAttribute(),
                    'url' => route('buyer-crm.buyers.show', $score->buyer),
                ];
            })
            ->toArray();
    }

    /**
     * Get pipeline analysis.
     */
    private function getPipelineAnalysis(): array
    {
        $totalBuyers = BuyerProfile::count();
        $converted = BuyerProfile::converted()->count();
        $dropped = BuyerProfile::dropped()->count();
        
        return [
            'total_buyers' => $totalBuyers,
            'conversion_rate' => $totalBuyers > 0 ? round(($converted / $totalBuyers) * 100, 2) : 0,
            'drop_rate' => $totalBuyers > 0 ? round(($dropped / $totalBuyers) * 100, 2) : 0,
            'active_in_pipeline' => $totalBuyers - $converted - $dropped,
            'stage_distribution' => $this->getPipelineSummary(),
        ];
    }

    /**
     * Get regional distribution.
     */
    private function getRegionalDistribution(): array
    {
        return BuyerProfile::select('country', DB::raw('count(*) as count'))
            ->groupBy('country')
            ->orderBy('count', 'desc')
            ->get()
            ->map(function ($item) {
                $buyer = new BuyerProfile(['country' => $item->country]);
                return [
                    'country' => $item->country,
                    'region' => $buyer->getRegionAttribute(),
                    'count' => $item->count,
                ];
            })
            ->groupBy('region')
            ->map(function ($countries, $region) {
                return [
                    'region' => $region,
                    'total_buyers' => $countries->sum('count'),
                    'countries' => $countries->values(),
                ];
            })
            ->values()
            ->toArray();
    }

    /**
     * Get engagement metrics.
     */
    private function getEngagementMetrics(): array
    {
        return [
            'high_engagement' => BuyerScore::highEngagement()->count(),
            'medium_engagement' => BuyerScore::mediumEngagement()->count(),
            'low_engagement' => BuyerScore::lowEngagement()->count(),
            'average_score' => round(BuyerScore::avg('score'), 2),
            'meetings_this_month' => BuyerMeeting::whereMonth('meeting_date', now()->month)
                                                    ->whereYear('meeting_date', now()->year)
                                                    ->count(),
            'follow_ups_completed' => BuyerFollowUp::completed()
                                                   ->whereMonth('completed_at', now()->month)
                                                   ->whereYear('completed_at', now()->year)
                                                   ->count(),
            'documents_uploaded' => BuyerDocument::recent(30)->count(),
        ];
    }

    /**
     * Get campaign performance.
     */
    private function getCampaignPerformance(): array
    {
        $campaigns = MarketingCampaign::with('campaignBuyers')->get();
        
        $totalCampaigns = $campaigns->count();
        $totalTargeted = MarketingCampaignBuyer::count();
        $totalContacted = MarketingCampaignBuyer::contacted()->count();
        $totalSuccess = MarketingCampaignBuyer::success()->count();

        return [
            'total_campaigns' => $totalCampaigns,
            'active_campaigns' => MarketingCampaign::active()->count(),
            'total_targeted_buyers' => $totalTargeted,
            'contact_rate' => $totalTargeted > 0 ? round(($totalContacted / $totalTargeted) * 100, 2) : 0,
            'success_rate' => $totalTargeted > 0 ? round(($totalSuccess / $totalTargeted) * 100, 2) : 0,
            'recent_campaigns' => $campaigns->sortByDesc('created_at')->take(5)->values(),
        ];
    }

    /**
     * Get monthly trends.
     */
    private function getMonthlyTrends(): array
    {
        $trends = [];
        
        for ($i = 11; $i >= 0; $i--) {
            $month = now()->subMonths($i);
            
            $trends[] = [
                'month' => $month->format('M Y'),
                'new_buyers' => BuyerProfile::whereYear('created_at', $month->year)
                    ->whereMonth('created_at', $month->month)
                    ->count(),
                'meetings' => BuyerMeeting::whereYear('meeting_date', $month->year)
                    ->whereMonth('meeting_date', $month->month)
                    ->count(),
                'conversions' => BuyerPipeline::where('current_stage', 'Converted')
                    ->whereYear('moved_at', $month->year)
                    ->whereMonth('moved_at', $month->month)
                    ->count(),
                'drops' => BuyerDropReason::whereYear('dropped_at', $month->year)
                    ->whereMonth('dropped_at', $month->month)
                    ->count(),
            ];
        }

        return $trends;
    }

    /**
     * Get risk analysis.
     */
    private function getRiskAnalysis(): array
    {
        return [
            'high_risk_buyers' => BuyerProfile::needsAttention()->count(),
            'recent_drops' => BuyerDropReason::recent(30)->count(),
            'high_impact_drops' => BuyerDropReason::highImpact()->count(),
            'addressable_drops' => BuyerDropReason::whereIn('reason', [
                'Pricing Issue', 'Product Mismatch', 'Timeline Issues', 'Quality Concerns'
            ])->count(),
            'overdue_follow_ups' => BuyerFollowUp::overdue()->count(),
            'stale_scores' => BuyerScore::stale(48)->count(),
        ];
    }

    /**
     * Get pipeline chart data.
     */
    private function getPipelineChartData(): array
    {
        $data = [];
        $labels = [];
        $counts = [];

        foreach (BuyerPipeline::$stages as $stage) {
            $count = BuyerPipeline::latestForEachBuyer()
                ->where('current_stage', $stage)
                ->count();
                
            $labels[] = $stage;
            $counts[] = $count;
        }

        return [
            'labels' => $labels,
            'datasets' => [
                [
                    'label' => 'Buyers by Stage',
                    'data' => $counts,
                    'backgroundColor' => [
                        '#6B7280', '#3B82F6', '#F59E0B', '#F97316', 
                        '#8B5CF6', '#10B981', '#EF4444'
                    ],
                ]
            ]
        ];
    }

    /**
     * Get regional chart data.
     */
    private function getRegionalChartData(): array
    {
        $regional = $this->getRegionalDistribution();
        
        return [
            'labels' => array_column($regional, 'region'),
            'datasets' => [
                [
                    'label' => 'Buyers by Region',
                    'data' => array_column($regional, 'total_buyers'),
                    'backgroundColor' => [
                        '#3B82F6', '#10B981', '#F59E0B', '#EF4444', 
                        '#8B5CF6', '#F97316', '#6B7280'
                    ],
                ]
            ]
        ];
    }

    /**
     * Get trends chart data.
     */
    private function getTrendsChartData(): array
    {
        $trends = $this->getMonthlyTrends();
        
        return [
            'labels' => array_column($trends, 'month'),
            'datasets' => [
                [
                    'label' => 'New Buyers',
                    'data' => array_column($trends, 'new_buyers'),
                    'borderColor' => '#3B82F6',
                    'backgroundColor' => 'rgba(59, 130, 246, 0.1)',
                ],
                [
                    'label' => 'Meetings',
                    'data' => array_column($trends, 'meetings'),
                    'borderColor' => '#10B981',
                    'backgroundColor' => 'rgba(16, 185, 129, 0.1)',
                ],
                [
                    'label' => 'Conversions',
                    'data' => array_column($trends, 'conversions'),
                    'borderColor' => '#F59E0B',
                    'backgroundColor' => 'rgba(245, 158, 11, 0.1)',
                ]
            ]
        ];
    }

    /**
     * Get engagement chart data.
     */
    private function getEngagementChartData(): array
    {
        return [
            'labels' => ['High', 'Medium', 'Low'],
            'datasets' => [
                [
                    'label' => 'Engagement Levels',
                    'data' => [
                        BuyerScore::highEngagement()->count(),
                        BuyerScore::mediumEngagement()->count(),
                        BuyerScore::lowEngagement()->count(),
                    ],
                    'backgroundColor' => ['#10B981', '#F59E0B', '#EF4444'],
                ]
            ]
        ];
    }
}
