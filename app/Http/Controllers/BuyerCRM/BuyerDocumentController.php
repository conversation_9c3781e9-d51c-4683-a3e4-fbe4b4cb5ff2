<?php

namespace App\Http\Controllers\BuyerCRM;

use App\Http\Controllers\Controller;
use App\Models\BuyerCRM\BuyerDocument;
use App\Models\BuyerCRM\BuyerProfile;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Response;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

/**
 * Buyer Document Controller
 * 
 * Handles document upload, download, and management for buyers
 */
class BuyerDocumentController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }



    /**
     * Display the specified document.
     */
    public function show(BuyerDocument $document): View
    {
        $document->load(['buyer', 'uploadedBy']);

        // Get related documents from the same buyer
        $relatedDocuments = BuyerDocument::where('buyer_id', $document->buyer_id)
            ->where('id', '!=', $document->id)
            ->orderBy('created_at', 'desc')
            ->limit(6)
            ->get();

        return view('buyer-crm.documents.show', compact('document', 'relatedDocuments'));
    }

    /**
     * Download the specified document.
     */
    public function download(BuyerDocument $document)
    {
        $filePath = storage_path('app/' . $document->file_path);

        if (!file_exists($filePath)) {
            abort(404, 'File not found');
        }

        return response()->download($filePath, $document->file_name);
    }

    /**
     * Display a listing of documents.
     */
    public function index(Request $request): View
    {
        $query = BuyerDocument::with(['buyer', 'uploadedBy']);

        // Apply filters
        if ($request->filled('buyer_id')) {
            $query->forBuyer($request->buyer_id);
        }

        if ($request->filled('document_type')) {
            $query->byType($request->document_type);
        }

        if ($request->filled('uploaded_by')) {
            $query->uploadedBy($request->uploaded_by);
        }

        // Special filters
        if ($request->filled('filter')) {
            switch ($request->filter) {
                case 'recent':
                    $query->recent(30);
                    break;
                case 'my_uploads':
                    $query->uploadedBy(auth()->id());
                    break;
            }
        }

        // Sort
        $sortBy = $request->get('sort_by', 'created_at');
        $sortDirection = $request->get('sort_direction', 'desc');
        $query->orderBy($sortBy, $sortDirection);

        $documents = $query->paginate(config('buyer-crm.pagination.per_page', 15));

        // Get filter options
        $buyers = BuyerProfile::select('id', 'name', 'company')->get();
        $users = User::select('id', 'name')->get();

        return view('buyer-crm.documents.index', compact('documents', 'buyers', 'users'));
    }

    /**
     * Show the form for creating a new document.
     */
    public function create(Request $request): View
    {
        $buyers = BuyerProfile::select('id', 'name', 'company')->get();
        $selectedBuyer = $request->buyer_id ? BuyerProfile::find($request->buyer_id) : null;
        $maxFileSize = config('buyer-crm.documents.max_file_size', 10485760); // 10MB default
        $allowedTypes = BuyerDocument::getAllowedExtensions();

        return view('buyer-crm.documents.create', compact('buyers', 'selectedBuyer', 'maxFileSize', 'allowedTypes'));
    }

    /**
     * Store a newly created document.
     */
    public function store(Request $request): RedirectResponse
    {
        $maxFileSize = config('buyer-crm.documents.max_file_size', 10485760);
        $allowedTypes = implode(',', BuyerDocument::getAllowedExtensions());

        $validated = $request->validate([
            'buyer_id' => ['required', 'exists:buyer_profiles,id'],
            'document_type' => ['required', Rule::in(BuyerDocument::$documentTypes)],
            'file' => [
                'required',
                'file',
                'max:' . ($maxFileSize / 1024), // Convert to KB for validation
                'mimes:' . $allowedTypes
            ],
            'description' => ['nullable', 'string', 'max:1000'],
        ]);

        $file = $request->file('file');
        $buyer = BuyerProfile::find($validated['buyer_id']);

        // Generate unique filename
        $originalName = $file->getClientOriginalName();
        $extension = $file->getClientOriginalExtension();
        $filename = Str::slug(pathinfo($originalName, PATHINFO_FILENAME)) . '_' . time() . '.' . $extension;

        // Store file in buyer-specific directory
        $storagePath = "buyer-documents/{$buyer->id}";
        $filePath = $file->storeAs($storagePath, $filename, 'public');

        // Get next version for this document type
        $version = BuyerDocument::getNextVersion($buyer->id, $validated['document_type']);

        // Create document record
        $document = BuyerDocument::create([
            'buyer_id' => $buyer->id,
            'document_type' => $validated['document_type'],
            'file_name' => $originalName,
            'file_path' => $filePath,
            'file_size' => $file->getSize(),
            'mime_type' => $file->getMimeType(),
            'uploaded_by' => auth()->id(),
            'version' => $version,
            'description' => $validated['description'],
        ]);

        return redirect()
            ->route('buyer-crm.documents.index', ['buyer_id' => $buyer->id])
            ->with('success', 'Document uploaded successfully.');
    }

    /**
     * Show the form for editing the document.
     */
    public function edit(BuyerDocument $document): View
    {
        $buyers = BuyerProfile::select('id', 'name', 'company')->get();

        return view('buyer-crm.documents.edit', compact('document', 'buyers'));
    }

    /**
     * Update the specified document.
     */
    public function update(Request $request, BuyerDocument $document): RedirectResponse
    {
        $validated = $request->validate([
            'buyer_id' => ['required', 'exists:buyer_profiles,id'],
            'document_type' => ['required', Rule::in(BuyerDocument::$documentTypes)],
            'description' => ['nullable', 'string', 'max:1000'],
            'version' => ['required', 'string', 'max:10'],
        ]);

        $document->update($validated);

        return redirect()
            ->route('buyer-crm.documents.index', ['buyer_id' => $document->buyer_id])
            ->with('success', 'Document updated successfully.');
    }

    /**
     * Remove the specified document.
     */
    public function destroy(BuyerDocument $document): RedirectResponse
    {
        $buyerId = $document->buyer_id;
        $document->delete(); // File will be deleted automatically via model boot method

        return redirect()
            ->route('buyer-crm.documents.index', ['buyer_id' => $buyerId])
            ->with('success', 'Document deleted successfully.');
    }



    /**
     * Display documents for a specific buyer.
     */
    public function byBuyer(BuyerProfile $buyer): View
    {
        $documents = $buyer->documents()
            ->with('uploadedBy')
            ->orderBy('created_at', 'desc')
            ->paginate(config('buyer-crm.pagination.per_page', 15));

        // Get document statistics for this buyer
        $stats = [
            'total_documents' => $buyer->documents()->count(),
            'by_type' => [],
            'total_size' => $buyer->documents()->sum('file_size'),
            'recent_uploads' => $buyer->documents()->recent(7)->count(),
        ];

        // Documents by type
        foreach (BuyerDocument::$documentTypes as $type) {
            $stats['by_type'][$type] = $buyer->documents()->byType($type)->count();
        }

        return view('buyer-crm.documents.by-buyer', compact('buyer', 'documents', 'stats'));
    }

    /**
     * Get document statistics.
     */
    public function statistics(): JsonResponse
    {
        $stats = [
            'total_documents' => BuyerDocument::count(),
            'total_size' => BuyerDocument::sum('file_size'),
            'recent_uploads' => BuyerDocument::recent(7)->count(),
            'by_type' => [],
            'by_month' => [],
            'top_uploaders' => [],
        ];

        // Documents by type
        foreach (BuyerDocument::$documentTypes as $type) {
            $stats['by_type'][$type] = BuyerDocument::byType($type)->count();
        }

        // Documents by month (last 6 months)
        for ($i = 5; $i >= 0; $i--) {
            $month = now()->subMonths($i);
            $stats['by_month'][$month->format('M Y')] = BuyerDocument::whereYear('created_at', $month->year)
                ->whereMonth('created_at', $month->month)
                ->count();
        }

        // Top uploaders
        $topUploaders = BuyerDocument::with('uploadedBy')
            ->selectRaw('uploaded_by, COUNT(*) as document_count')
            ->groupBy('uploaded_by')
            ->orderBy('document_count', 'desc')
            ->limit(5)
            ->get()
            ->map(function ($item) {
                return [
                    'name' => $item->uploadedBy->name ?? 'Unknown',
                    'count' => $item->document_count,
                ];
            });

        $stats['top_uploaders'] = $topUploaders;

        return response()->json([
            'success' => true,
            'statistics' => $stats
        ]);
    }

    /**
     * Bulk delete documents.
     */
    public function bulkDelete(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'document_ids' => ['required', 'array'],
            'document_ids.*' => ['exists:buyer_documents,id'],
        ]);

        $documents = BuyerDocument::whereIn('id', $validated['document_ids'])->get();
        $deletedCount = 0;

        foreach ($documents as $document) {
            $document->delete();
            $deletedCount++;
        }

        return response()->json([
            'success' => true,
            'message' => "{$deletedCount} documents deleted successfully.",
            'deleted_count' => $deletedCount,
        ]);
    }
}
