<?php

namespace App\Http\Controllers\BuyerCRM;

use App\Http\Controllers\Controller;
use App\Models\BuyerCRM\BuyerDropReason;
use App\Models\BuyerCRM\BuyerProfile;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Validation\Rule;
use Carbon\Carbon;

/**
 * Buyer Drop Reason Controller
 * 
 * Handles buyer drop tracking, review scheduling, and re-engagement workflows
 */
class BuyerDropReasonController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display a listing of buyer drop reasons.
     */
    public function index(Request $request): View
    {
        $query = BuyerDropReason::with(['buyer', 'droppedBy']);

        // Apply filters
        if ($request->filled('reason')) {
            $query->byReason($request->reason);
        }

        if ($request->filled('impact_level')) {
            $query->byImpactLevel($request->impact_level);
        }

        if ($request->filled('dropped_by')) {
            $query->droppedBy($request->dropped_by);
        }

        if ($request->filled('buyer_id')) {
            $query->where('buyer_id', $request->buyer_id);
        }

        if ($request->filled('date_from') && $request->filled('date_to')) {
            $query->dateRange(
                Carbon::parse($request->date_from),
                Carbon::parse($request->date_to)
            );
        }

        // Special filters
        if ($request->filled('filter')) {
            switch ($request->filter) {
                case 'high_impact':
                    $query->highImpact();
                    break;
                case 'due_for_review':
                    $query->dueForReview();
                    break;
                case 'recent':
                    $query->recent(30);
                    break;
                case 'addressable':
                    $query->whereIn('reason', ['Pricing Issue', 'Product Mismatch', 'Timeline Issues', 'Quality Concerns']);
                    break;
                case 'no_reengagement':
                    $query->reEngagementNotAttempted();
                    break;
                case 'my_drops':
                    $query->droppedBy(auth()->id());
                    break;
            }
        }

        // Sort
        $sortBy = $request->get('sort_by', 'dropped_at');
        $sortDirection = $request->get('sort_direction', 'desc');
        $query->orderBy($sortBy, $sortDirection);

        $dropReasons = $query->paginate(config('buyer-crm.pagination.per_page', 15));

        // Get filter options
        $buyers = BuyerProfile::select('id', 'name', 'company')->get();
        $users = User::select('id', 'name')->get();

        // Get statistics
        $stats = $this->getDropStatistics();

        return view('buyer-crm.drop-reasons.index', compact('dropReasons', 'buyers', 'users', 'stats'));
    }

    /**
     * Show the form for creating a new drop reason.
     */
    public function create(Request $request): View
    {
        $buyers = BuyerProfile::select('id', 'company', 'contact_person')->orderBy('company')->get();
        $selectedBuyer = $request->buyer_id ? BuyerProfile::find($request->buyer_id) : null;

        return view('buyer-crm.drop-reasons.create', compact('buyers', 'selectedBuyer'));
    }

    /**
     * Store a newly created drop reason.
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'buyer_id' => ['required', 'exists:buyer_profiles,id'],
            'reason' => ['required', Rule::in(BuyerDropReason::$reasons)],
            'detailed_reason' => ['nullable', 'string', 'max:2000'],
            'impact_level' => ['required', Rule::in(BuyerDropReason::$impactLevels)],
            'next_review_date' => ['nullable', 'date', 'after:today'],
        ]);

        $validated['dropped_by'] = auth()->id();
        $validated['dropped_at'] = now();

        // Set default review date if not provided
        if (!$validated['next_review_date']) {
            $validated['next_review_date'] = now()->addMonths(3)->toDateString();
        }

        $dropReason = BuyerDropReason::create($validated);

        // Update buyer's pipeline to 'Dropped' if not already
        $buyer = BuyerProfile::find($validated['buyer_id']);
        $currentStage = $buyer->getCurrentStageAttribute();
        
        if ($currentStage !== 'Dropped') {
            $buyer->pipelines()->create([
                'current_stage' => 'Dropped',
                'moved_at' => now(),
                'note' => "Buyer dropped: {$validated['reason']}",
                'moved_by' => auth()->id(),
            ]);
        }

        return redirect()
            ->route('buyer-crm.drop-reasons.show', $dropReason)
            ->with('success', 'Drop reason recorded successfully.');
    }

    /**
     * Display the specified drop reason.
     */
    public function show(BuyerDropReason $dropReason): View
    {
        $dropReason->load(['buyer', 'droppedBy']);

        return view('buyer-crm.drop-reasons.show', compact('dropReason'));
    }

    /**
     * Show the form for editing the drop reason.
     */
    public function edit(BuyerDropReason $dropReason): View
    {
        $buyers = BuyerProfile::select('id', 'company', 'contact_person')->orderBy('company')->get();

        return view('buyer-crm.drop-reasons.edit', compact('dropReason', 'buyers'));
    }

    /**
     * Update the specified drop reason.
     */
    public function update(Request $request, BuyerDropReason $dropReason): RedirectResponse
    {
        $validated = $request->validate([
            'buyer_id' => ['required', 'exists:buyer_profiles,id'],
            'reason' => ['required', Rule::in(BuyerDropReason::$reasons)],
            'detailed_reason' => ['nullable', 'string', 'max:2000'],
            'impact_level' => ['required', Rule::in(BuyerDropReason::$impactLevels)],
            'next_review_date' => ['nullable', 'date'],
            're_engagement_attempted' => ['boolean'],
        ]);

        $dropReason->update($validated);

        return redirect()
            ->route('buyer-crm.drop-reasons.show', $dropReason)
            ->with('success', 'Drop reason updated successfully.');
    }

    /**
     * Remove the specified drop reason.
     */
    public function destroy(BuyerDropReason $dropReason): RedirectResponse
    {
        $dropReason->delete();

        return redirect()
            ->route('buyer-crm.drop-reasons.index')
            ->with('success', 'Drop reason deleted successfully.');
    }

    /**
     * Display drop reasons for a specific buyer.
     */
    public function byBuyer(BuyerProfile $buyer): View
    {
        $dropReasons = $buyer->dropReasons()
            ->with('droppedBy')
            ->orderBy('dropped_at', 'desc')
            ->paginate(config('buyer-crm.pagination.per_page', 15));

        return view('buyer-crm.drop-reasons.by-buyer', compact('buyer', 'dropReasons'));
    }

    /**
     * Schedule review for a drop reason.
     */
    public function scheduleReview(Request $request, BuyerDropReason $dropReason): JsonResponse
    {
        $validated = $request->validate([
            'review_date' => ['required', 'date', 'after:today'],
        ]);

        $dropReason->scheduleReview(Carbon::parse($validated['review_date']));

        return response()->json([
            'success' => true,
            'message' => 'Review scheduled successfully.',
            'review_date' => $dropReason->next_review_date->format('Y-m-d'),
        ]);
    }

    /**
     * Attempt re-engagement for a dropped buyer.
     */
    public function attemptReengagement(Request $request, BuyerDropReason $dropReason): JsonResponse
    {
        $validated = $request->validate([
            'notes' => ['nullable', 'string', 'max:1000'],
        ]);

        // Mark re-engagement as attempted
        $dropReason->markReEngagementAttempted();

        // Create a follow-up task for re-engagement
        $dropReason->buyer->followUps()->create([
            'task_type' => 'Call',
            'task_date' => now()->addDays(1),
            'status' => 'Pending',
            'assigned_to' => auth()->id(),
            'note' => "Re-engagement attempt for dropped buyer. Reason: {$dropReason->reason}. " . ($validated['notes'] ?? ''),
        ]);

        // Schedule next review in 1 month
        $dropReason->scheduleReview(now()->addMonth());

        return response()->json([
            'success' => true,
            'message' => 'Re-engagement attempt initiated successfully.',
            'follow_up_created' => true,
            'next_review_date' => $dropReason->next_review_date->format('Y-m-d'),
        ]);
    }

    /**
     * Get drop statistics.
     */
    private function getDropStatistics(): array
    {
        return [
            'total_drops' => BuyerDropReason::count(),
            'high_impact_drops' => BuyerDropReason::highImpact()->count(),
            'due_for_review' => BuyerDropReason::dueForReview()->count(),
            'recent_drops' => BuyerDropReason::recent(30)->count(),
            'reengagement_attempted' => BuyerDropReason::reEngagementAttempted()->count(),
            'by_reason' => [],
            'by_impact_level' => [],
        ];
    }

    /**
     * Get comprehensive drop analytics.
     */
    public function analytics(): JsonResponse
    {
        $stats = [
            'overview' => $this->getDropStatistics(),
            'trends' => $this->getDropTrends(),
            'top_reasons' => $this->getTopDropReasons(),
            'reengagement_success' => $this->getReengagementSuccess(),
            'impact_analysis' => $this->getImpactAnalysis(),
        ];

        return response()->json([
            'success' => true,
            'analytics' => $stats
        ]);
    }

    /**
     * Get drop trends over time.
     */
    private function getDropTrends(): array
    {
        $trends = [];
        
        for ($i = 11; $i >= 0; $i--) {
            $month = now()->subMonths($i);
            $count = BuyerDropReason::whereYear('dropped_at', $month->year)
                ->whereMonth('dropped_at', $month->month)
                ->count();
                
            $trends[] = [
                'month' => $month->format('M Y'),
                'count' => $count,
            ];
        }

        return $trends;
    }

    /**
     * Get top drop reasons.
     */
    private function getTopDropReasons(): array
    {
        return BuyerDropReason::selectRaw('reason, COUNT(*) as count')
            ->groupBy('reason')
            ->orderBy('count', 'desc')
            ->get()
            ->map(function ($item) {
                return [
                    'reason' => $item->reason,
                    'count' => $item->count,
                    'percentage' => round(($item->count / BuyerDropReason::count()) * 100, 2),
                ];
            })
            ->toArray();
    }

    /**
     * Get re-engagement success metrics.
     */
    private function getReengagementSuccess(): array
    {
        $totalAttempts = BuyerDropReason::reEngagementAttempted()->count();
        
        // This would need to be enhanced with actual success tracking
        // For now, we'll provide basic metrics
        return [
            'total_attempts' => $totalAttempts,
            'pending_attempts' => BuyerDropReason::reEngagementNotAttempted()
                ->dueForReview()
                ->count(),
            'addressable_drops' => BuyerDropReason::whereIn('reason', [
                'Pricing Issue', 'Product Mismatch', 'Timeline Issues', 'Quality Concerns'
            ])->count(),
        ];
    }

    /**
     * Get impact analysis.
     */
    private function getImpactAnalysis(): array
    {
        return [
            'high_impact' => BuyerDropReason::highImpact()->count(),
            'medium_impact' => BuyerDropReason::mediumImpact()->count(),
            'low_impact' => BuyerDropReason::lowImpact()->count(),
            'high_impact_addressable' => BuyerDropReason::highImpact()
                ->whereIn('reason', ['Pricing Issue', 'Product Mismatch', 'Timeline Issues', 'Quality Concerns'])
                ->count(),
        ];
    }

    /**
     * Mark drop reason as re-engaged and create follow-up.
     */
    public function reEngage(Request $request, BuyerDropReason $dropReason): RedirectResponse
    {
        $request->validate([
            'notes' => 'nullable|string|max:1000',
            'follow_up_date' => 'nullable|date|after:today',
            'follow_up_type' => 'nullable|string|in:Call,Email,Meeting,Other',
        ]);

        try {
            // Mark as re-engaged
            $dropReason->update([
                're_engagement_attempted_at' => now(),
                're_engagement_notes' => $request->notes,
                're_engagement_attempted' => true,
            ]);

            // Create follow-up task if requested
            if ($request->filled('follow_up_date')) {
                $dropReason->buyer->followUps()->create([
                    'title' => 'Re-engagement Follow-up',
                    'description' => $request->notes ?? 'Follow-up after re-engagement attempt',
                    'due_date' => $request->follow_up_date,
                    'priority' => 'High',
                    'status' => 'Pending',
                    'type' => $request->follow_up_type ?? 'Call',
                    'assigned_to' => auth()->id(),
                    'created_by' => auth()->id(),
                ]);
            }

            // Move buyer back to active pipeline if they were dropped
            $currentPipeline = $dropReason->buyer->currentPipeline;
            if ($currentPipeline && $currentPipeline->current_stage === 'Dropped') {
                $dropReason->buyer->pipelines()->create([
                    'current_stage' => 'Contacted', // Move back to contacted stage
                    'moved_at' => now(),
                    'notes' => 'Re-engaged after drop reason: ' . $dropReason->drop_reason,
                    'moved_by' => auth()->id(),
                ]);
            }

            return redirect()
                ->route('buyer-crm.drop-reasons.show', $dropReason)
                ->with('success', 'Re-engagement attempt recorded successfully.');

        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->with('error', 'Failed to record re-engagement: ' . $e->getMessage());
        }
    }
}
