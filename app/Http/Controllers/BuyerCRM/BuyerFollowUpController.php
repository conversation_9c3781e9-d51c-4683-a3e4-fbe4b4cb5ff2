<?php

namespace App\Http\Controllers\BuyerCRM;

use App\Http\Controllers\Controller;
use App\Models\BuyerCRM\BuyerFollowUp;
use App\Models\BuyerCRM\BuyerProfile;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Validation\Rule;
use Carbon\Carbon;

/**
 * Buyer Follow-Up Controller
 * 
 * Handles CRUD operations for buyer follow-up tasks
 */
class BuyerFollowUpController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display a listing of follow-up tasks.
     */
    public function index(Request $request): View
    {
        $query = BuyerFollowUp::with(['buyer', 'assignedUser']);

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('task_type')) {
            $query->byType($request->task_type);
        }

        if ($request->filled('assigned_to')) {
            $query->assignedTo($request->assigned_to);
        }

        if ($request->filled('buyer_id')) {
            $query->forBuyer($request->buyer_id);
        }

        if ($request->filled('date_from') && $request->filled('date_to')) {
            $query->dateRange(
                Carbon::parse($request->date_from),
                Carbon::parse($request->date_to)
            );
        }

        // Special filters
        if ($request->filled('filter')) {
            switch ($request->filter) {
                case 'overdue':
                    $query->overdue();
                    break;
                case 'today':
                    $query->today();
                    break;
                case 'upcoming':
                    $query->upcoming(7);
                    break;
                case 'my_tasks':
                    $query->assignedTo(auth()->id());
                    break;
            }
        }

        // Sort
        $sortBy = $request->get('sort_by', 'task_date');
        $sortDirection = $request->get('sort_direction', 'asc');
        $query->orderBy($sortBy, $sortDirection);

        $followUps = $query->paginate(config('buyer-crm.pagination.per_page', 15));

        // Get filter options
        $buyers = BuyerProfile::select('id', 'name', 'company')->get();
        $users = User::select('id', 'name')->get();

        return view('buyer-crm.follow-ups.index', compact('followUps', 'buyers', 'users'));
    }

    /**
     * Show the form for creating a new follow-up task.
     */
    public function create(Request $request): View
    {
        $buyers = BuyerProfile::select('id', 'company', 'contact_person')->orderBy('company')->get();
        $users = User::select('id', 'name')->get();
        $selectedBuyer = $request->buyer_id ? BuyerProfile::find($request->buyer_id) : null;

        return view('buyer-crm.follow-ups.create', compact('buyers', 'users', 'selectedBuyer'));
    }

    /**
     * Store a newly created follow-up task.
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'buyer_id' => ['required', 'exists:buyer_profiles,id'],
            'task_type' => ['required', Rule::in(BuyerFollowUp::$taskTypes)],
            'task_date' => ['required', 'date', 'after_or_equal:today'],
            'assigned_to' => ['required', 'exists:users,id'],
            'note' => ['nullable', 'string', 'max:1000'],
        ]);

        $followUp = BuyerFollowUp::create($validated);

        return redirect()
            ->route('buyer-crm.follow-ups.show', $followUp)
            ->with('success', 'Follow-up task created successfully.');
    }

    /**
     * Display the specified follow-up task.
     */
    public function show(BuyerFollowUp $followUp): View
    {
        $followUp->load(['buyer', 'assignedUser']);

        return view('buyer-crm.follow-ups.show', compact('followUp'));
    }

    /**
     * Show the form for editing the follow-up task.
     */
    public function edit(BuyerFollowUp $followUp): View
    {
        $buyers = BuyerProfile::select('id', 'company', 'contact_person')->orderBy('company')->get();
        $users = User::select('id', 'name')->get();

        return view('buyer-crm.follow-ups.edit', compact('followUp', 'buyers', 'users'));
    }

    /**
     * Update the specified follow-up task.
     */
    public function update(Request $request, BuyerFollowUp $followUp): RedirectResponse
    {
        $validated = $request->validate([
            'buyer_id' => ['required', 'exists:buyer_profiles,id'],
            'task_type' => ['required', Rule::in(BuyerFollowUp::$taskTypes)],
            'task_date' => ['required', 'date'],
            'status' => ['required', Rule::in(BuyerFollowUp::$statuses)],
            'assigned_to' => ['required', 'exists:users,id'],
            'note' => ['nullable', 'string', 'max:1000'],
        ]);

        // If marking as completed, set completed_at
        if ($validated['status'] === 'Completed' && $followUp->status !== 'Completed') {
            $validated['completed_at'] = now();
        }

        $followUp->update($validated);

        return redirect()
            ->route('buyer-crm.follow-ups.show', $followUp)
            ->with('success', 'Follow-up task updated successfully.');
    }

    /**
     * Remove the specified follow-up task.
     */
    public function destroy(BuyerFollowUp $followUp): RedirectResponse
    {
        $followUp->delete();

        return redirect()
            ->route('buyer-crm.follow-ups.index')
            ->with('success', 'Follow-up task deleted successfully.');
    }

    /**
     * Display follow-ups for a specific buyer.
     */
    public function byBuyer(BuyerProfile $buyer): View
    {
        $followUps = $buyer->followUps()
            ->with('assignedUser')
            ->orderBy('task_date', 'desc')
            ->paginate(config('buyer-crm.pagination.per_page', 15));

        return view('buyer-crm.follow-ups.by-buyer', compact('buyer', 'followUps'));
    }

    /**
     * Mark a follow-up task as completed.
     */
    public function complete(BuyerFollowUp $followUp): JsonResponse
    {
        if ($followUp->status === 'Completed') {
            return response()->json([
                'success' => false,
                'message' => 'Task is already completed.'
            ], 422);
        }

        $followUp->markAsCompleted();

        return response()->json([
            'success' => true,
            'message' => 'Task marked as completed successfully.',
            'followUp' => [
                'id' => $followUp->id,
                'status' => $followUp->status,
                'completed_at' => $followUp->completed_at?->format('Y-m-d H:i:s'),
                'status_color' => $followUp->getStatusColor(),
            ]
        ]);
    }

    /**
     * Display current user's tasks.
     */
    public function myTasks(Request $request): View
    {
        $query = BuyerFollowUp::with(['buyer', 'assignedUser'])
            ->assignedTo(auth()->id());

        // Apply status filter
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        } else {
            // Default to pending tasks
            $query->pending();
        }

        // Apply date filter
        if ($request->filled('filter')) {
            switch ($request->filter) {
                case 'overdue':
                    $query->overdue();
                    break;
                case 'today':
                    $query->today();
                    break;
                case 'upcoming':
                    $query->upcoming(7);
                    break;
            }
        }

        $followUps = $query->orderBy('task_date', 'asc')
            ->paginate(config('buyer-crm.pagination.per_page', 15));

        // Get task statistics
        $stats = [
            'total' => BuyerFollowUp::assignedTo(auth()->id())->count(),
            'pending' => BuyerFollowUp::assignedTo(auth()->id())->pending()->count(),
            'overdue' => BuyerFollowUp::assignedTo(auth()->id())->overdue()->count(),
            'today' => BuyerFollowUp::assignedTo(auth()->id())->today()->count(),
            'completed_this_week' => BuyerFollowUp::assignedTo(auth()->id())
                ->completed()
                ->whereBetween('completed_at', [now()->startOfWeek(), now()->endOfWeek()])
                ->count(),
        ];

        return view('buyer-crm.follow-ups.my-tasks', compact('followUps', 'stats'));
    }
}
