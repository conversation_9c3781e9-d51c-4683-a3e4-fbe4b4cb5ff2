<?php

namespace App\Http\Controllers\BuyerCRM;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\DB;
use App\Models\BuyerCRM\BuyerInvoice;
use App\Models\BuyerCRM\GarmentOrder;
use App\Models\BuyerCRM\BuyerFinancialAccount;

class BuyerInvoiceController extends Controller
{
    /**
     * Display a listing of invoices.
     */
    public function index(Request $request): View
    {
        $query = BuyerInvoice::with(['buyer', 'garmentOrder'])
            ->orderBy('created_at', 'desc');

        // Apply filters
        if ($request->filled('buyer_id')) {
            $query->where('buyer_id', $request->buyer_id);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('invoice_date', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('invoice_date', '<=', $request->date_to);
        }

        $invoices = $query->paginate(15);

        return view('buyer-crm.invoices.index', compact('invoices'));
    }

    /**
     * Show the form for creating a new invoice.
     */
    public function create(Request $request): View
    {
        $garmentOrderId = $request->get('garment_order_id');
        $garmentOrder = null;

        if ($garmentOrderId) {
            $garmentOrder = GarmentOrder::with(['buyer', 'variants'])->findOrFail($garmentOrderId);
        }

        return view('buyer-crm.invoices.create', compact('garmentOrder'));
    }

    /**
     * Store a newly created invoice.
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'garment_order_id' => 'required|exists:garment_orders,id',
            'tax_rate' => 'nullable|numeric|min:0|max:100',
            'discount_rate' => 'nullable|numeric|min:0|max:100',
            'notes' => 'nullable|string|max:1000',
        ]);

        try {
            $garmentOrder = GarmentOrder::with(['buyer', 'variants'])->findOrFail($validated['garment_order_id']);

            // Check if invoice already exists for this order
            $existingInvoice = BuyerInvoice::where('garment_order_id', $garmentOrder->id)->first();
            if ($existingInvoice) {
                return back()->with('error', 'An invoice already exists for this order.');
            }

            $invoice = BuyerInvoice::createFromOrder($garmentOrder, [
                'tax_rate' => $validated['tax_rate'] ?? 0,
                'discount_rate' => $validated['discount_rate'] ?? 0,
                'notes' => $validated['notes'] ?? null,
            ]);

            return redirect()
                ->route('invoices.show', $invoice)
                ->with('success', 'Invoice created successfully.');

        } catch (\Exception $e) {
            return back()
                ->withInput()
                ->with('error', 'Failed to create invoice: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified invoice.
     */
    public function show(BuyerInvoice $invoice): View
    {
        $invoice->load(['buyer', 'garmentOrder.variants', 'creator']);
        return view('buyer-crm.invoices.show', compact('invoice'));
    }

    /**
     * Generate and download invoice PDF.
     */
    public function download(BuyerInvoice $invoice): Response
    {
        $invoice->load(['buyer', 'garmentOrder.variants', 'creator']);

        // For now, return HTML that can be printed as PDF
        return response()
            ->view('buyer-crm.invoices.pdf', compact('invoice'))
            ->header('Content-Type', 'text/html');
    }

    /**
     * Generate invoice from garment order.
     */
    public function generateFromOrder(GarmentOrder $garmentOrder): RedirectResponse
    {
        try {
            // Check if invoice already exists
            $existingInvoice = BuyerInvoice::where('garment_order_id', $garmentOrder->id)->first();
            if ($existingInvoice) {
                return redirect()
                    ->route('invoices.show', $existingInvoice)
                    ->with('info', 'Invoice already exists for this order.');
            }

            $invoice = BuyerInvoice::createFromOrder($garmentOrder);

            return redirect()
                ->route('invoices.show', $invoice)
                ->with('success', 'Invoice generated successfully.');

        } catch (\Exception $e) {
            return back()->with('error', 'Failed to generate invoice: ' . $e->getMessage());
        }
    }

    /**
     * Mark invoice as sent.
     */
    public function markAsSent(BuyerInvoice $invoice): RedirectResponse
    {
        $invoice->markAsSent();

        return back()->with('success', 'Invoice marked as sent.');
    }

    /**
     * Mark invoice as paid.
     */
    public function markAsPaid(Request $request, BuyerInvoice $invoice): RedirectResponse
    {
        $validated = $request->validate([
            'paid_amount' => 'nullable|numeric|min:0',
            'payment_method' => 'nullable|string|max:100',
            'payment_reference' => 'nullable|string|max:100',
            'bank_id' => 'nullable|exists:banks,id',
            'notes' => 'nullable|string|max:500',
        ]);

        try {
            DB::beginTransaction();

            $paidAmount = $validated['paid_amount'] ?? $invoice->total_amount;
            $invoice->markAsPaid($paidAmount);

            // Create payment transaction in buyer financial system
            $account = BuyerFinancialAccount::getOrCreateForBuyer($invoice->buyer_id);
            $account->addPaymentTransaction($paidAmount, [
                'payment_method' => $validated['payment_method'] ?? null,
                'payment_reference' => $validated['payment_reference'] ?? null,
                'notes' => $validated['notes'] ?? null,
                'description' => "Payment for Invoice #{$invoice->invoice_number}",
            ]);

            // If Bank Transfer is selected, credit the bank account
            if ($validated['payment_method'] === 'Bank Transfer' && !empty($validated['bank_id'])) {
                $bank = \App\Models\Bank::findOrFail($validated['bank_id']);

                // Update bank balance
                $bank->increment('balance', $paidAmount);

                // Create a transfer record for audit trail
                \App\Models\Transfer::create([
                    'user_id' => auth()->id(),
                    'bank_to' => $validated['bank_id'],
                    'amount' => $paidAmount,
                    'transfer_type' => 'deposit',
                    'adjust_type' => 'credit',
                    'date' => now()->format('Y-m-d'),
                    'note' => "Payment received for Invoice #{$invoice->invoice_number} from {$invoice->buyer->company}",
                    'meta' => json_encode([
                        'invoice_id' => $invoice->id,
                        'buyer_id' => $invoice->buyer_id,
                        'payment_reference' => $validated['payment_reference'],
                    ]),
                ]);
            }

            DB::commit();

            $message = 'Invoice marked as paid and payment recorded.';
            if ($validated['payment_method'] === 'Bank Transfer' && !empty($validated['bank_id'])) {
                $message .= ' Bank account has been credited.';
            }

            return back()->with('success', $message);

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', 'Failed to process payment: ' . $e->getMessage());
        }
    }

    /**
     * Cancel the invoice.
     */
    public function cancel(BuyerInvoice $invoice): RedirectResponse
    {
        if ($invoice->status === 'Paid') {
            return back()->with('error', 'Cannot cancel a paid invoice.');
        }

        $invoice->update(['status' => 'Cancelled']);

        return back()->with('success', 'Invoice cancelled successfully.');
    }
}
