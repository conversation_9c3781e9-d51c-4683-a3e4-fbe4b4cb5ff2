<?php

namespace App\Http\Controllers\BuyerCRM;

use App\Http\Controllers\Controller;
use App\Models\BuyerCRM\BuyerMeeting;
use App\Models\BuyerCRM\BuyerProfile;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Validation\Rule;
use Carbon\Carbon;

/**
 * Buyer Meeting Controller
 * 
 * Handles CRUD operations for buyer meetings and interactions
 */
class BuyerMeetingController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display a listing of meetings.
     */
    public function index(Request $request): View
    {
        $query = BuyerMeeting::with(['buyer', 'addedBy']);

        // Apply filters
        if ($request->filled('buyer_id')) {
            $query->forBuyer($request->buyer_id);
        }

        if ($request->filled('interaction_type')) {
            $query->byType($request->interaction_type);
        }

        if ($request->filled('added_by')) {
            $query->addedBy($request->added_by);
        }

        if ($request->filled('date_from') && $request->filled('date_to')) {
            $query->dateRange(
                Carbon::parse($request->date_from),
                Carbon::parse($request->date_to)
            );
        }

        // Special filters
        if ($request->filled('filter')) {
            switch ($request->filter) {
                case 'recent':
                    $query->recent(30);
                    break;
                case 'this_month':
                    $query->thisMonth();
                    break;
                case 'this_week':
                    $query->thisWeek();
                    break;
                case 'with_followup':
                    $query->withFollowUp();
                    break;
                case 'my_meetings':
                    $query->addedBy(auth()->id());
                    break;
            }
        }

        // Sort
        $sortBy = $request->get('sort_by', 'meeting_date');
        $sortDirection = $request->get('sort_direction', 'desc');
        $query->orderBy($sortBy, $sortDirection);

        $meetings = $query->paginate(config('buyer-crm.pagination.per_page', 15));

        // Get filter options
        $buyers = BuyerProfile::select('id', 'name', 'company')->get();
        $users = User::select('id', 'name')->get();

        return view('buyer-crm.meetings.index', compact('meetings', 'buyers', 'users'));
    }

    /**
     * Show the form for creating a new meeting.
     */
    public function create(Request $request): View
    {
        $buyers = BuyerProfile::select('id', 'company', 'contact_person')->orderBy('company')->get();
        $selectedBuyer = $request->buyer_id ? BuyerProfile::find($request->buyer_id) : null;

        return view('buyer-crm.meetings.create', compact('buyers', 'selectedBuyer'));
    }

    /**
     * Store a newly created meeting.
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'buyer_id' => ['required', 'exists:buyer_profiles,id'],
            'interaction_type' => ['required', Rule::in(BuyerMeeting::$interactionTypes)],
            'summary' => ['required', 'string', 'max:2000'],
            'meeting_date' => ['required', 'date'],
            'duration' => ['nullable', 'integer', 'min:1', 'max:1440'], // Max 24 hours
            'action_points' => ['nullable', 'string', 'max:2000'],
            'next_meeting_date' => ['nullable', 'date', 'after:meeting_date'],
        ]);

        $validated['added_by'] = auth()->id();

        $meeting = BuyerMeeting::create($validated);

        // If next meeting is scheduled, create a follow-up task
        if ($meeting->next_meeting_date) {
            $this->createFollowUpTask($meeting);
        }

        return redirect()
            ->route('buyer-crm.meetings.show', $meeting)
            ->with('success', 'Meeting recorded successfully.');
    }

    /**
     * Display the specified meeting.
     */
    public function show(BuyerMeeting $meeting): View
    {
        $meeting->load(['buyer', 'addedBy']);

        return view('buyer-crm.meetings.show', compact('meeting'));
    }

    /**
     * Show the form for editing the meeting.
     */
    public function edit(BuyerMeeting $meeting): View
    {
        $buyers = BuyerProfile::select('id', 'company', 'contact_person')->orderBy('company')->get();

        return view('buyer-crm.meetings.edit', compact('meeting', 'buyers'));
    }

    /**
     * Update the specified meeting.
     */
    public function update(Request $request, BuyerMeeting $meeting): RedirectResponse
    {
        $validated = $request->validate([
            'buyer_id' => ['required', 'exists:buyer_profiles,id'],
            'interaction_type' => ['required', Rule::in(BuyerMeeting::$interactionTypes)],
            'summary' => ['required', 'string', 'max:2000'],
            'meeting_date' => ['required', 'date'],
            'duration' => ['nullable', 'integer', 'min:1', 'max:1440'],
            'action_points' => ['nullable', 'string', 'max:2000'],
            'next_meeting_date' => ['nullable', 'date', 'after:meeting_date'],
        ]);

        $oldNextMeetingDate = $meeting->next_meeting_date;
        $meeting->update($validated);

        // Handle follow-up task creation/update
        if ($meeting->next_meeting_date && $meeting->next_meeting_date != $oldNextMeetingDate) {
            $this->createFollowUpTask($meeting);
        }

        return redirect()
            ->route('buyer-crm.meetings.show', $meeting)
            ->with('success', 'Meeting updated successfully.');
    }

    /**
     * Remove the specified meeting.
     */
    public function destroy(BuyerMeeting $meeting): RedirectResponse
    {
        $meeting->delete();

        return redirect()
            ->route('buyer-crm.meetings.index')
            ->with('success', 'Meeting deleted successfully.');
    }

    /**
     * Display meetings for a specific buyer.
     */
    public function byBuyer(BuyerProfile $buyer): View
    {
        $meetings = $buyer->meetings()
            ->with('addedBy')
            ->orderBy('meeting_date', 'desc')
            ->paginate(config('buyer-crm.pagination.per_page', 15));

        // Get meeting statistics for this buyer
        $stats = [
            'total_meetings' => $buyer->meetings()->count(),
            'this_month' => $buyer->meetings()->thisMonth()->count(),
            'avg_duration' => $buyer->meetings()->whereNotNull('duration')->avg('duration'),
            'last_meeting' => $buyer->meetings()->latest('meeting_date')->first()?->meeting_date,
            'next_meeting' => $buyer->meetings()->withFollowUp()
                ->where('next_meeting_date', '>', now())
                ->orderBy('next_meeting_date')
                ->first()?->next_meeting_date,
        ];

        return view('buyer-crm.meetings.by-buyer', compact('buyer', 'meetings', 'stats'));
    }

    /**
     * Get meeting statistics.
     */
    public function statistics(): JsonResponse
    {
        $stats = [
            'total_meetings' => BuyerMeeting::count(),
            'this_month' => BuyerMeeting::thisMonth()->count(),
            'this_week' => BuyerMeeting::thisWeek()->count(),
            'with_followup' => BuyerMeeting::withFollowUp()->count(),
            'avg_duration' => BuyerMeeting::whereNotNull('duration')->avg('duration'),
            'by_type' => [],
            'recent_activity' => [],
        ];

        // Meetings by type
        foreach (BuyerMeeting::$interactionTypes as $type) {
            $stats['by_type'][$type] = BuyerMeeting::byType($type)->count();
        }

        // Recent activity (last 7 days)
        $recentMeetings = BuyerMeeting::with(['buyer', 'addedBy'])
            ->where('meeting_date', '>=', now()->subDays(7))
            ->orderBy('meeting_date', 'desc')
            ->limit(10)
            ->get()
            ->map(function ($meeting) {
                return [
                    'id' => $meeting->id,
                    'buyer_name' => $meeting->buyer->name,
                    'interaction_type' => $meeting->interaction_type,
                    'meeting_date' => $meeting->meeting_date->format('Y-m-d H:i'),
                    'added_by' => $meeting->addedBy->name,
                    'summary_excerpt' => $meeting->getSummaryExcerpt(50),
                ];
            });

        $stats['recent_activity'] = $recentMeetings;

        return response()->json([
            'success' => true,
            'statistics' => $stats
        ]);
    }

    /**
     * Create a follow-up task for the next meeting.
     */
    private function createFollowUpTask(BuyerMeeting $meeting): void
    {
        if (!$meeting->next_meeting_date) {
            return;
        }

        $meeting->buyer->followUps()->create([
            'task_type' => 'Meeting',
            'task_date' => $meeting->next_meeting_date,
            'status' => 'Pending',
            'assigned_to' => auth()->id(),
            'note' => "Follow-up meeting scheduled from interaction on {$meeting->meeting_date->format('Y-m-d')}",
        ]);
    }
}
