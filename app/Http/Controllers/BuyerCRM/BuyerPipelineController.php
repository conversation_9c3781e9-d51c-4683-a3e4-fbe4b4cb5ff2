<?php

namespace App\Http\Controllers\BuyerCRM;

use App\Http\Controllers\Controller;
use App\Models\BuyerCRM\BuyerProfile;
use App\Models\BuyerCRM\BuyerPipeline;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Validation\Rule;

/**
 * Buyer Pipeline Controller
 * 
 * Handles buyer pipeline stage management and history
 */
class BuyerPipelineController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display pipeline overview for all buyers.
     */
    public function index(Request $request): View
    {
        $stage = $request->get('stage');
        $search = $request->get('search');

        $buyers = BuyerProfile::with(['currentPipeline', 'latestScore'])
            ->when($stage, function ($query, $stage) {
                return $query->whereHas('currentPipeline', function ($q) use ($stage) {
                    $q->where('current_stage', $stage);
                });
            })
            ->when($search, function ($query, $search) {
                return $query->where('company_name', 'like', "%{$search}%")
                           ->orWhere('contact_person', 'like', "%{$search}%");
            })
            ->orderBy('updated_at', 'desc')
            ->paginate(15);

        $stages = BuyerPipeline::$stages;
        $stageStats = [];

        foreach ($stages as $stageItem) {
            // Count buyers currently in this stage using a simpler approach
            $stageStats[$stageItem] = BuyerProfile::whereHas('currentPipeline', function ($query) use ($stageItem) {
                $query->where('current_stage', $stageItem);
            })->count();
        }

        return view('buyer-crm.pipeline.index', compact('buyers', 'stages', 'stageStats', 'stage', 'search'));
    }

    /**
     * Display the buyer's pipeline information.
     */
    public function show(BuyerProfile $buyer): View
    {
        $buyer->load([
            'pipelines' => function ($query) {
                $query->with('movedBy')->orderBy('moved_at', 'desc');
            },
            'currentPipeline.movedBy'
        ]);

        $stages = BuyerPipeline::$stages;
        $currentStage = $buyer->currentPipeline?->current_stage ?? 'Lead Captured';

        return view('buyer-crm.pipeline.show', compact('buyer', 'stages', 'currentStage'));
    }

    /**
     * Move buyer to a new pipeline stage.
     */
    public function moveStage(Request $request, BuyerProfile $buyer)
    {
        $request->validate([
            'stage' => ['sometimes', 'string'],
            'notes' => ['nullable', 'string', 'max:1000'],
        ]);

        $currentPipeline = $buyer->currentPipeline;
        $currentStage = $currentPipeline?->current_stage ?? 'Lead Captured';

        // Auto-determine next stage if not provided
        if ($request->has('stage')) {
            $newStage = $request->stage;
        } else {
            $stages = BuyerPipeline::$stages;
            $currentIndex = array_search($currentStage, $stages);

            if ($currentIndex === false || $currentIndex >= count($stages) - 1) {
                if ($request->expectsJson()) {
                    return response()->json(['success' => false, 'message' => 'Cannot move to next stage.'], 422);
                }
                return redirect()->back()->with('error', 'Cannot move to next stage.');
            }

            $newStage = $stages[$currentIndex + 1];
        }

        // Don't create duplicate entries for the same stage
        if ($currentStage === $newStage) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => "Buyer is already in '{$newStage}' stage."
                ], 422);
            }
            return redirect()->back()->with('error', "Buyer is already in '{$newStage}' stage.");
        }

        // Create new pipeline entry
        $pipeline = $buyer->pipelines()->create([
            'current_stage' => $newStage,
            'moved_at' => now(),
            'notes' => $request->notes,
            'moved_by' => auth()->id(),
        ]);

        // If moving to Dropped stage, create a drop reason entry
        if ($newStage === 'Dropped') {
            // Create drop reason if method exists
            if (method_exists($this, 'createDropReason')) {
                $this->createDropReason($buyer, $request->notes);
            }
        }

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => "Buyer moved to '{$newStage}' stage successfully.",
                'pipeline' => [
                    'id' => $pipeline->id,
                    'current_stage' => $pipeline->current_stage,
                    'moved_at' => $pipeline->moved_at->format('Y-m-d H:i:s'),
                    'notes' => $pipeline->notes,
                    'moved_by' => $pipeline->movedBy->name ?? 'Unknown',
                ]
            ]);
        }

        return redirect()->back()->with('success', "Buyer moved to '{$newStage}' stage successfully.");
    }

    /**
     * Get buyer's pipeline history.
     */
    public function history(BuyerProfile $buyer): JsonResponse
    {
        $pipelines = $buyer->pipelines()
            ->with('movedBy')
            ->orderBy('moved_at', 'desc')
            ->get()
            ->map(function ($pipeline) {
                return [
                    'id' => $pipeline->id,
                    'current_stage' => $pipeline->current_stage,
                    'moved_at' => $pipeline->moved_at->format('Y-m-d H:i:s'),
                    'moved_at_human' => $pipeline->moved_at->diffForHumans(),
                    'note' => $pipeline->note,
                    'moved_by' => $pipeline->movedBy->name ?? 'Unknown',
                    'progress_percentage' => $pipeline->getProgressPercentage(),
                    'stage_color' => $pipeline->getStageColor(),
                    'stage_icon' => $pipeline->getStageIcon(),
                    'is_terminal' => $pipeline->isTerminalStage(),
                    'is_converted' => $pipeline->isConverted(),
                    'is_dropped' => $pipeline->isDropped(),
                ];
            });

        return response()->json([
            'success' => true,
            'pipelines' => $pipelines
        ]);
    }

    /**
     * Get available pipeline stages.
     */
    public function stages(): JsonResponse
    {
        $stages = collect(BuyerPipeline::$stages)->map(function ($stage, $index) {
            $pipeline = new BuyerPipeline(['current_stage' => $stage]);
            
            return [
                'name' => $stage,
                'index' => $index,
                'progress_percentage' => $pipeline->getProgressPercentage(),
                'color' => $pipeline->getStageColor(),
                'icon' => $pipeline->getStageIcon(),
                'is_terminal' => $pipeline->isTerminalStage(),
            ];
        });

        return response()->json([
            'success' => true,
            'stages' => $stages
        ]);
    }

    /**
     * Get pipeline statistics.
     */
    public function statistics(): JsonResponse
    {
        $stats = [];
        
        foreach (BuyerPipeline::$stages as $stage) {
            $count = BuyerPipeline::latestForEachBuyer()
                ->inStage($stage)
                ->count();
                
            $stats[$stage] = $count;
        }

        $totalBuyers = BuyerProfile::count();
        $conversionRate = $totalBuyers > 0 ? round(($stats['Converted'] / $totalBuyers) * 100, 2) : 0;
        $dropRate = $totalBuyers > 0 ? round(($stats['Dropped'] / $totalBuyers) * 100, 2) : 0;

        return response()->json([
            'success' => true,
            'statistics' => [
                'stage_counts' => $stats,
                'total_buyers' => $totalBuyers,
                'conversion_rate' => $conversionRate,
                'drop_rate' => $dropRate,
            ]
        ]);
    }

    /**
     * Create a drop reason entry when buyer is moved to Dropped stage.
     */
    private function createDropReason(BuyerProfile $buyer, ?string $note): void
    {
        $buyer->dropReasons()->create([
            'reason' => 'Other', // Default reason, can be updated later
            'detailed_reason' => $note,
            'impact_level' => 'Medium', // Default impact level
            'dropped_by' => auth()->id(),
            'dropped_at' => now(),
            'next_review_date' => now()->addMonths(3), // Review in 3 months
            're_engagement_attempted' => false,
        ]);
    }
}
