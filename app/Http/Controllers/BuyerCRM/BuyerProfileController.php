<?php

namespace App\Http\Controllers\BuyerCRM;

use App\Http\Controllers\Controller;
use App\Models\BuyerCRM\BuyerProfile;
use App\Http\Requests\BuyerCRM\StoreBuyerProfileRequest;
use App\Http\Requests\BuyerCRM\UpdateBuyerProfileRequest;
use App\Services\BuyerCRM\BuyerScoringService;
use App\Models\User;
use App\Traits\HasSubscriptionFeatures;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;

/**
 * Buyer Profile Controller
 * 
 * Handles CRUD operations for buyer profiles
 */
class BuyerProfileController extends Controller
{
    use HasSubscriptionFeatures;

    protected BuyerScoringService $scoringService;

    public function __construct(BuyerScoringService $scoringService)
    {
        $this->scoringService = $scoringService;
        $this->middleware('auth');
        $this->middleware('subscription');
        $this->middleware('feature.gate:buyer_crm');
    }

    /**
     * Display a listing of buyer profiles.
     */
    public function index(Request $request): View
    {
        $query = BuyerProfile::with(['assignedUser', 'currentPipeline', 'score']);

        // Apply filters
        if ($request->filled('search')) {
            $query->search($request->search);
        }

        if ($request->filled('country')) {
            $query->byCountry($request->country);
        }

        if ($request->filled('type')) {
            $query->byType($request->type);
        }

        if ($request->filled('priority')) {
            $query->byPriority($request->priority);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('assigned_to')) {
            $query->assignedTo($request->assigned_to);
        }

        // Sort
        $sortBy = $request->get('sort_by', 'created_at');
        $sortDirection = $request->get('sort_direction', 'desc');
        $query->orderBy($sortBy, $sortDirection);

        $buyers = $query->paginate(config('buyer-crm.pagination.per_page', 15));

        // Get filter options
        $countries = BuyerProfile::distinct()->pluck('country')->sort();
        $users = User::select('id', 'name')->get();

        return view('buyer-crm.buyers.index', compact('buyers', 'countries', 'users'));
    }

    /**
     * Show the form for creating a new buyer profile.
     */
    public function create(): View
    {
        $users = User::select('id', 'name')->get();
        return view('buyer-crm.buyers.create', compact('users'));
    }

    /**
     * Store a newly created buyer profile.
     */
    public function store(StoreBuyerProfileRequest $request): RedirectResponse
    {
        // Check usage limits before creating
        $limitCheck = $this->checkUsageLimit('buyers', $request);
        if ($limitCheck) {
            return $limitCheck;
        }

        $buyer = BuyerProfile::create($request->validated());

        // Create initial pipeline entry
        $buyer->pipelines()->create([
            'current_stage' => 'Lead Captured',
            'moved_at' => now(),
            'note' => 'Initial lead capture',
            'moved_by' => auth()->id(),
        ]);

        // Update usage tracking
        $this->incrementUsage('buyers');

        return redirect()
            ->route('buyer-crm.buyers.show', $buyer)
            ->with('success', 'Buyer profile created successfully.');
    }

    /**
     * Display the specified buyer profile.
     */
    public function show(BuyerProfile $buyer): View
    {
        $buyer->load([
            'assignedUser',
            'currentPipeline',
            'score',
            'meetings' => function ($query) {
                $query->latest('meeting_date')->limit(5);
            },
            'followUps' => function ($query) {
                $query->where('status', 'Pending')->latest('task_date')->limit(5);
            },
            'documents' => function ($query) {
                $query->latest()->limit(5);
            }
        ]);

        return view('buyer-crm.buyers.show', compact('buyer'));
    }

    /**
     * Show the form for editing the buyer profile.
     */
    public function edit(BuyerProfile $buyer): View
    {
        $users = User::select('id', 'name')->get();
        return view('buyer-crm.buyers.edit', compact('buyer', 'users'));
    }

    /**
     * Update the specified buyer profile.
     */
    public function update(UpdateBuyerProfileRequest $request, BuyerProfile $buyer): RedirectResponse
    {
        $buyer->update($request->validated());

        return redirect()
            ->route('buyer-crm.buyers.show', $buyer)
            ->with('success', 'Buyer profile updated successfully.');
    }

    /**
     * Remove the specified buyer profile.
     */
    public function destroy(BuyerProfile $buyer): RedirectResponse
    {
        $buyer->delete();

        // Update usage tracking
        $this->decrementUsage('buyers');

        return redirect()
            ->route('buyer-crm.buyers.index')
            ->with('success', 'Buyer profile deleted successfully.');
    }

    /**
     * Display buyer score details.
     */
    public function score(BuyerProfile $buyer): View
    {
        $buyer->load('score');
        
        if (!$buyer->score) {
            $this->scoringService->calculateScore($buyer);
            $buyer->refresh();
        }

        return view('buyer-crm.buyers.score', compact('buyer'));
    }

    /**
     * Calculate and update buyer score.
     */
    public function calculateScore(BuyerProfile $buyer): JsonResponse
    {
        $score = $this->scoringService->calculateScore($buyer);

        return response()->json([
            'success' => true,
            'score' => $score->score,
            'engagement_level' => $score->engagement_level,
            'factors' => $score->factors,
            'message' => 'Score calculated successfully.'
        ]);
    }

    /**
     * Search buyers for AJAX requests.
     */
    public function search(Request $request): JsonResponse
    {
        $query = BuyerProfile::query();

        if ($request->filled('q')) {
            $query->search($request->q);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        $buyers = $query->select('id', 'name', 'company', 'email', 'priority', 'status')
            ->limit(20)
            ->get();

        return response()->json($buyers);
    }
}
