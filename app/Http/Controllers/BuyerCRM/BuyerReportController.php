<?php

namespace App\Http\Controllers\BuyerCRM;

use App\Http\Controllers\Controller;
use App\Models\BuyerCRM\BuyerProfile;
use App\Models\BuyerCRM\BuyerPipeline;
use App\Models\BuyerCRM\BuyerMeeting;
use App\Models\BuyerCRM\BuyerFollowUp;
use App\Models\BuyerCRM\BuyerScore;
use App\Models\BuyerCRM\BuyerDropReason;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Carbon\Carbon;

class BuyerReportController extends Controller
{
    /**
     * Show export dashboard.
     */
    public function export(Request $request)
    {
        // Handle export request
        if ($request->has('format')) {
            return $this->handleExport($request);
        }

        // Show export dashboard
        $exportOptions = $this->getExportOptions();
        
        return view('buyer-crm.reports.export', compact('exportOptions'));
    }

    /**
     * Handle export request.
     */
    private function handleExport(Request $request)
    {
        $format = $request->get('format', 'csv');
        $type = $request->get('type', 'buyers');
        $dateRange = $request->get('date_range', '30');
        $region = $request->get('region');
        $stage = $request->get('stage');

        // Get data based on type
        $data = $this->getExportData($type, $dateRange, $region, $stage);
        
        // Generate export based on format
        switch ($format) {
            case 'pdf':
                return $this->exportPdf($data, $type);
            case 'excel':
                return $this->exportExcel($data, $type);
            case 'csv':
            default:
                return $this->exportCsv($data, $type);
        }
    }

    /**
     * Get export data based on type and filters.
     */
    private function getExportData($type, $dateRange, $region = null, $stage = null)
    {
        $startDate = Carbon::now()->subDays((int)$dateRange);
        
        switch ($type) {
            case 'buyers':
                return $this->getBuyersData($startDate, $region, $stage);
            case 'pipeline':
                return $this->getPipelineData($startDate, $region, $stage);
            case 'meetings':
                return $this->getMeetingsData($startDate, $region);
            case 'follow-ups':
                return $this->getFollowUpsData($startDate, $region);
            case 'scoring':
                return $this->getScoringData($startDate, $region);
            case 'drop-reasons':
                return $this->getDropReasonsData($startDate, $region);
            default:
                return collect();
        }
    }

    /**
     * Get buyers data for export.
     */
    private function getBuyersData($startDate, $region = null, $stage = null)
    {
        $query = BuyerProfile::with(['currentPipeline', 'latestScore'])
            ->where('created_at', '>=', $startDate);

        if ($region) {
            $query->where('region', $region);
        }

        if ($stage) {
            $query->whereHas('currentPipeline', function($q) use ($stage) {
                $q->where('current_stage', $stage);
            });
        }

        return $query->get()->map(function($buyer) {
            return [
                'ID' => $buyer->id,
                'Company' => $buyer->company,
                'Contact Person' => $buyer->contact_person,
                'Email' => $buyer->email,
                'Phone' => $buyer->phone,
                'Region' => $buyer->region,
                'Country' => $buyer->country,
                'Priority' => $buyer->priority,
                'Status' => $buyer->status,
                'Current Stage' => $buyer->currentPipeline->current_stage ?? 'No Pipeline',
                'Engagement Score' => $buyer->latestScore->score ?? 0,
                'Created Date' => $buyer->created_at->format('Y-m-d H:i:s'),
            ];
        });
    }

    /**
     * Get pipeline data for export.
     */
    private function getPipelineData($startDate, $region = null, $stage = null)
    {
        $query = BuyerPipeline::with(['buyer', 'movedBy'])
            ->where('moved_at', '>=', $startDate);

        if ($region) {
            $query->whereHas('buyer', function($q) use ($region) {
                $q->where('region', $region);
            });
        }

        if ($stage) {
            $query->where('current_stage', $stage);
        }

        return $query->get()->map(function($pipeline) {
            return [
                'ID' => $pipeline->id,
                'Buyer' => $pipeline->buyer->company,
                'Contact Person' => $pipeline->buyer->contact_person,
                'Current Stage' => $pipeline->current_stage,
                'Moved Date' => $pipeline->moved_at->format('Y-m-d H:i:s'),
                'Notes' => $pipeline->notes,
                'Moved By' => $pipeline->movedBy->name ?? 'System',
            ];
        });
    }

    /**
     * Get meetings data for export.
     */
    private function getMeetingsData($startDate, $region = null)
    {
        $query = BuyerMeeting::with(['buyer', 'organizer'])
            ->where('meeting_date', '>=', $startDate);

        if ($region) {
            $query->whereHas('buyer', function($q) use ($region) {
                $q->where('region', $region);
            });
        }

        return $query->get()->map(function($meeting) {
            return [
                'ID' => $meeting->id,
                'Buyer' => $meeting->buyer->company,
                'Contact Person' => $meeting->buyer->contact_person,
                'Meeting Type' => $meeting->meeting_type,
                'Meeting Date' => $meeting->meeting_date->format('Y-m-d H:i:s'),
                'Duration' => $meeting->duration . ' minutes',
                'Location' => $meeting->location,
                'Outcome' => $meeting->outcome,
                'Organizer' => $meeting->organizer->name ?? 'Unknown',
                'Created Date' => $meeting->created_at->format('Y-m-d H:i:s'),
            ];
        });
    }

    /**
     * Get follow-ups data for export.
     */
    private function getFollowUpsData($startDate, $region = null)
    {
        $query = BuyerFollowUp::with(['buyer', 'assignedTo', 'createdBy'])
            ->where('created_at', '>=', $startDate);

        if ($region) {
            $query->whereHas('buyer', function($q) use ($region) {
                $q->where('region', $region);
            });
        }

        return $query->get()->map(function($followUp) {
            return [
                'ID' => $followUp->id,
                'Buyer' => $followUp->buyer->company,
                'Contact Person' => $followUp->buyer->contact_person,
                'Title' => $followUp->title,
                'Type' => $followUp->type,
                'Priority' => $followUp->priority,
                'Status' => $followUp->status,
                'Due Date' => $followUp->due_date ? $followUp->due_date->format('Y-m-d') : 'No due date',
                'Assigned To' => $followUp->assignedTo->name ?? 'Unassigned',
                'Created By' => $followUp->createdBy->name ?? 'Unknown',
                'Created Date' => $followUp->created_at->format('Y-m-d H:i:s'),
            ];
        });
    }

    /**
     * Get scoring data for export.
     */
    private function getScoringData($startDate, $region = null)
    {
        $query = BuyerScore::with('buyer')
            ->where('calculated_at', '>=', $startDate);

        if ($region) {
            $query->whereHas('buyer', function($q) use ($region) {
                $q->where('region', $region);
            });
        }

        return $query->get()->map(function($score) {
            return [
                'ID' => $score->id,
                'Buyer' => $score->buyer->company,
                'Contact Person' => $score->buyer->contact_person,
                'Score' => $score->score,
                'Engagement Level' => $score->engagement_level,
                'Calculated Date' => $score->calculated_at->format('Y-m-d H:i:s'),
            ];
        });
    }

    /**
     * Get drop reasons data for export.
     */
    private function getDropReasonsData($startDate, $region = null)
    {
        $query = BuyerDropReason::with(['buyer', 'createdBy'])
            ->where('created_at', '>=', $startDate);

        if ($region) {
            $query->whereHas('buyer', function($q) use ($region) {
                $q->where('region', $region);
            });
        }

        return $query->get()->map(function($dropReason) {
            return [
                'ID' => $dropReason->id,
                'Buyer' => $dropReason->buyer->company,
                'Contact Person' => $dropReason->buyer->contact_person,
                'Drop Reason' => $dropReason->drop_reason,
                'Impact Level' => $dropReason->impact_level,
                'Drop Date' => $dropReason->drop_date ? $dropReason->drop_date->format('Y-m-d') : 'No date',
                'Review Date' => $dropReason->review_date ? $dropReason->review_date->format('Y-m-d') : 'No date',
                'Re-engagement Attempted' => $dropReason->re_engagement_attempted_at ? 'Yes' : 'No',
                'Created By' => $dropReason->createdBy->name ?? 'Unknown',
                'Created Date' => $dropReason->created_at->format('Y-m-d H:i:s'),
            ];
        });
    }

    /**
     * Export data as CSV.
     */
    private function exportCsv($data, $type)
    {
        $filename = "buyer-crm-{$type}-" . date('Y-m-d') . '.csv';
        
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function() use ($data) {
            $file = fopen('php://output', 'w');
            
            if ($data->isNotEmpty()) {
                // Write headers
                fputcsv($file, array_keys($data->first()));
                
                // Write data
                foreach ($data as $row) {
                    fputcsv($file, $row);
                }
            }
            
            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Export data as Excel (CSV with Excel headers).
     */
    private function exportExcel($data, $type)
    {
        $filename = "buyer-crm-{$type}-" . date('Y-m-d') . '.xlsx';
        
        $headers = [
            'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        // For now, return CSV with Excel headers
        // In production, you might want to use a library like PhpSpreadsheet
        return $this->exportCsv($data, $type)->withHeaders($headers);
    }

    /**
     * Export data as PDF.
     */
    private function exportPdf($data, $type)
    {
        $filename = "buyer-crm-{$type}-" . date('Y-m-d') . '.pdf';
        
        // For now, return a simple text response
        // In production, you might want to use a library like DomPDF or TCPDF
        $content = "Buyer CRM {$type} Report\n";
        $content .= "Generated: " . date('Y-m-d H:i:s') . "\n\n";
        
        if ($data->isNotEmpty()) {
            $headers = array_keys($data->first());
            $content .= implode("\t", $headers) . "\n";
            
            foreach ($data as $row) {
                $content .= implode("\t", $row) . "\n";
            }
        }

        return response($content, 200, [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ]);
    }

    /**
     * Get export options for the dashboard.
     */
    private function getExportOptions()
    {
        return [
            'types' => [
                'buyers' => 'Buyer Profiles',
                'pipeline' => 'Pipeline Data',
                'meetings' => 'Meetings',
                'follow-ups' => 'Follow-ups',
                'scoring' => 'Engagement Scores',
                'drop-reasons' => 'Drop Reasons',
            ],
            'formats' => [
                'csv' => 'CSV',
                'excel' => 'Excel',
                'pdf' => 'PDF',
            ],
            'date_ranges' => [
                '7' => 'Last 7 days',
                '30' => 'Last 30 days',
                '90' => 'Last 90 days',
                '365' => 'Last year',
            ],
            'regions' => BuyerProfile::distinct('region')->pluck('region')->filter(),
            'stages' => BuyerPipeline::$stages ?? [],
        ];
    }
}
