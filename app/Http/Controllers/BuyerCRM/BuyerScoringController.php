<?php

namespace App\Http\Controllers\BuyerCRM;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\BuyerCRM\BuyerScore;
use App\Models\BuyerCRM\BuyerProfile;

class BuyerScoringController extends Controller
{
    /**
     * Display buyer scoring dashboard.
     */
    public function index(Request $request)
    {
        $scores = BuyerScore::with(['buyer'])
            ->when($request->engagement_level, function ($query, $level) {
                return $query->where('engagement_level', $level);
            })
            ->when($request->search, function ($query, $search) {
                return $query->whereHas('buyer', function ($q) use ($search) {
                    $q->where('company_name', 'like', "%{$search}%")
                      ->orWhere('contact_person', 'like', "%{$search}%");
                });
            })
            ->orderBy('score', 'desc')
            ->paginate(15);

        $stats = [
            'total_scored_buyers' => BuyerScore::count(),
            'high_engagement' => BuyerScore::where('engagement_level', 'High')->count(),
            'medium_engagement' => BuyerScore::where('engagement_level', 'Medium')->count(),
            'low_engagement' => BuyerScore::where('engagement_level', 'Low')->count(),
            'average_score' => BuyerScore::avg('score') ?? 0,
        ];

        return view('buyer-crm.scoring.index', compact('scores', 'stats'));
    }

    /**
     * Recalculate score for a specific buyer.
     */
    public function recalculate(BuyerProfile $buyer)
    {
        try {
            // Calculate new score based on buyer activities
            $meetingsCount = $buyer->meetings()->count();
            $followUpsCount = $buyer->followUps()->count();
            $completedFollowUps = $buyer->followUps()->where('status', 'Completed')->count();
            $documentsCount = $buyer->documents()->count();

            // Simple scoring algorithm
            $score = 0;
            $score += min($meetingsCount * 10, 40); // Max 40 points for meetings
            $score += min($followUpsCount * 5, 25); // Max 25 points for follow-ups
            $score += min($completedFollowUps * 10, 25); // Max 25 points for completed follow-ups
            $score += min($documentsCount * 2, 10); // Max 10 points for documents

            // Determine engagement level
            $engagementLevel = 'Low';
            if ($score >= 80) {
                $engagementLevel = 'High';
            } elseif ($score >= 50) {
                $engagementLevel = 'Medium';
            }

            // Update or create score record
            BuyerScore::updateOrCreate(
                ['buyer_id' => $buyer->id],
                [
                    'score' => $score,
                    'engagement_level' => $engagementLevel,
                    'calculated_at' => now(),
                ]
            );

            return redirect()->back()->with('success', 'Buyer engagement score recalculated successfully.');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to recalculate score: ' . $e->getMessage());
        }
    }

    /**
     * Recalculate scores for all buyers.
     */
    public function recalculateAll()
    {
        try {
            $buyers = BuyerProfile::all();
            $updated = 0;

            foreach ($buyers as $buyer) {
                // Calculate new score based on buyer activities
                $meetingsCount = $buyer->meetings()->count();
                $followUpsCount = $buyer->followUps()->count();
                $completedFollowUps = $buyer->followUps()->where('status', 'Completed')->count();
                $documentsCount = $buyer->documents()->count();

                // Simple scoring algorithm
                $score = 0;
                $score += min($meetingsCount * 10, 40); // Max 40 points for meetings
                $score += min($followUpsCount * 5, 25); // Max 25 points for follow-ups
                $score += min($completedFollowUps * 10, 25); // Max 25 points for completed follow-ups
                $score += min($documentsCount * 2, 10); // Max 10 points for documents

                // Determine engagement level
                $engagementLevel = 'Low';
                if ($score >= 80) {
                    $engagementLevel = 'High';
                } elseif ($score >= 50) {
                    $engagementLevel = 'Medium';
                }

                // Update or create score record
                BuyerScore::updateOrCreate(
                    ['buyer_id' => $buyer->id],
                    [
                        'score' => $score,
                        'engagement_level' => $engagementLevel,
                        'calculated_at' => now(),
                    ]
                );

                $updated++;
            }

            return redirect()->back()->with('success', "Successfully recalculated engagement scores for {$updated} buyers.");
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to recalculate scores: ' . $e->getMessage());
        }
    }
}
