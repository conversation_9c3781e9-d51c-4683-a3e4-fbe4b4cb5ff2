<?php

namespace App\Http\Controllers\BuyerCRM;

use App\Http\Controllers\Controller;
use App\Models\BuyerCRM\GarmentOrder;
use App\Models\BuyerCRM\GarmentOrderVariant;
use App\Models\BuyerCRM\BuyerProfile;
use App\Models\BuyerCRM\OrderSizeRatio;
use App\Models\BuyerCRM\OrderProductionChecklist;
use App\Models\BuyerCRM\BuyerFinancialAccount;
use App\Models\BuyerCRM\BuyerFinancialTransaction;
use App\Models\User;
use App\Http\Requests\BuyerCRM\GarmentOrderRequest;
use App\Traits\HasSubscriptionFeatures;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\DB;

class GarmentOrderController extends Controller
{
    use HasSubscriptionFeatures;

    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('subscription');
        $this->middleware('feature.gate:garment_orders');
        $this->middleware('usage.limit:garment_orders')->only(['store']);
    }

    /**
     * Display a listing of garment orders.
     */
    public function index(Request $request): View
    {
        $query = GarmentOrder::with(['buyer', 'merchandiser', 'creator', 'invoices'])
            ->orderBy('created_at', 'desc');

        // Apply filters
        if ($request->filled('buyer_id')) {
            $query->where('buyer_id', $request->buyer_id);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('product_category')) {
            $query->where('product_category', $request->product_category);
        }

        if ($request->filled('season')) {
            $query->where('season', $request->season);
        }

        if ($request->filled('order_type')) {
            $query->where('order_type', $request->order_type);
        }

        if ($request->filled('merchandiser_id')) {
            $query->where('merchandiser_id', $request->merchandiser_id);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('order_date', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('order_date', '<=', $request->date_to);
        }

        if ($request->filled('delivery_from')) {
            $query->whereDate('delivery_date', '>=', $request->delivery_from);
        }

        if ($request->filled('delivery_to')) {
            $query->whereDate('delivery_date', '<=', $request->delivery_to);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('order_no', 'like', "%{$search}%")
                  ->orWhere('style_no', 'like', "%{$search}%")
                  ->orWhere('garment_type', 'like', "%{$search}%")
                  ->orWhereHas('buyer', function ($buyerQuery) use ($search) {
                      $buyerQuery->where('company', 'like', "%{$search}%");
                  });
            });
        }

        $orders = $query->paginate(15);

        // Get filter options
        $buyers = BuyerProfile::select('id', 'company', 'contact_person')
            ->orderBy('company')
            ->get();

        $merchandisers = User::select('id', 'name')
            ->orderBy('name')
            ->get();

        return view('buyer-crm.garment-orders.index', compact(
            'orders',
            'buyers',
            'merchandisers'
        ));
    }

    /**
     * Show the form for creating a new garment order.
     */
    public function create(): View
    {
        $buyers = BuyerProfile::select('id', 'company', 'contact_person')
            ->where('status', 'Active')
            ->orderBy('company')
            ->get();

        $merchandisers = User::select('id', 'name')
            ->where('role', 'merchandiser')
            ->orderBy('name')
            ->get();

        // Generate order number for the form
        $orderNumber = GarmentOrder::generateOrderNumber();

        return view('buyer-crm.garment-orders.create-enhanced', compact(
            'buyers',
            'merchandisers',
            'orderNumber'
        ));
    }

    /**
     * Store a newly created garment order.
     */
    public function store(Request $request): RedirectResponse
    {
        try {
            DB::beginTransaction();

            // Validate the request - Enhanced form with variants
            $validated = $request->validate([
                'buyer_id' => 'required|exists:buyer_profiles,id',
                'merchandiser_id' => 'nullable|exists:users,id',
                'order_no' => 'required|string|unique:garment_orders,order_no',
                'style_no' => 'required|string',
                'product_category' => 'required|string',
                'garment_type' => 'required|string',
                'season' => 'required|string',
                'order_type' => 'required|string',
                'fabric_type' => 'required|string',
                'fabric_color' => 'required|string',
                'wash_type' => 'required|string',
                'construction_type' => 'nullable|string',
                'gsm' => 'nullable|numeric|min:0',
                'shrinkage' => 'nullable|numeric|min:0|max:100',
                'stitch_type' => 'nullable|string',
                'order_date' => 'required|date',
                'delivery_date' => 'nullable|date|after_or_equal:order_date',
                'special_instructions' => 'nullable|string',
                'total_quantity' => 'required|integer|min:1',
                'total_value' => 'nullable|numeric|min:0',
                'variants_data' => 'nullable|json',
                'selected_sizes' => 'nullable|json',
                'selected_colors' => 'nullable|json',
            ]);

            // Create the garment order - Enhanced form with variants
            $orderData = [
                'buyer_id' => $validated['buyer_id'],
                'merchandiser_id' => $validated['merchandiser_id'] ?? null,
                'created_by' => auth()->id(),
                'order_no' => $validated['order_no'],
                'style_no' => $validated['style_no'],
                'product_category' => $validated['product_category'],
                'garment_type' => $validated['garment_type'],
                'season' => $validated['season'],
                'order_type' => $validated['order_type'],
                'fabric_type' => $validated['fabric_type'],
                'fabric_color' => $validated['fabric_color'],
                'wash_type' => $validated['wash_type'],
                'construction_type' => $validated['construction_type'] ?? null,
                'gsm' => $validated['gsm'] ?? null,
                'shrinkage' => $validated['shrinkage'] ?? null,
                'stitch_type' => $validated['stitch_type'] ?? null,
                'order_date' => $validated['order_date'],
                'delivery_date' => $validated['delivery_date'] ?? null,
                'special_instructions' => $validated['special_instructions'] ?? null,
                'total_quantity' => $validated['total_quantity'],
                'unit_price' => null, // Will be calculated from variants
                'final_price' => null, // Will be set during costing
                'total_value' => $validated['total_value'] ?? null,
                'status' => 'Draft',
                'production_ready' => false,
            ];

            $order = GarmentOrder::create($orderData);

            // Update usage tracking
            $this->incrementUsage('garment_orders');

            // Create order variants if provided
            if (!empty($validated['variants_data'] ?? null)) {
                $variantsData = json_decode($validated['variants_data'], true);
                if (!empty($variantsData)) {
                    $this->createOrderVariants($order->id, $variantsData);
                }

                // Create size ratios for backward compatibility
                if (!empty($validated['selected_sizes'] ?? null)) {
                    $selectedSizes = json_decode($validated['selected_sizes'], true);
                    if (!empty($selectedSizes)) {
                        $this->createSizeRatiosFromVariants($order->id, $selectedSizes, $variantsData);
                    }
                }
            }

            // Create production checklist
            OrderProductionChecklist::create([
                'garment_order_id' => $order->id,
                'created_by' => auth()->id(),
            ]);

            // Create financial transaction for the order
            if ($order->total_value && $order->total_value > 0) {
                $financialAccount = BuyerFinancialAccount::getOrCreateForBuyer($order->buyer_id);
                $financialAccount->addOrderTransaction($order);
            }

            DB::commit();

            // Determine success message
            $variantCount = 0;
            if (!empty($validated['variants_data'] ?? null)) {
                $variantsData = json_decode($validated['variants_data'], true);
                $variantCount = count($variantsData ?? []);
            }

            $message = $variantCount > 0
                ? "Garment order created successfully with {$variantCount} variants."
                : "Garment order created successfully!";

            return redirect()
                ->route('garment-orders.show', $order)
                ->with('success', $message);

        } catch (\Exception $e) {
            DB::rollBack();
            return back()
                ->withInput()
                ->with('error', 'Failed to create garment order: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified garment order.
     */
    public function show(GarmentOrder $garmentOrder): View
    {
        $garmentOrder->load([
            'buyer',
            'merchandiser',
            'creator',
            'sizeRatios' => function ($query) {
                $query->orderBy('sort_order');
            },
            'variants' => function ($query) {
                $query->orderBy('size_sort_order')->orderBy('color_sort_order');
            },
            'techPacks' => function ($query) {
                $query->where('is_active', true)->orderBy('created_at', 'desc');
            },
            'bomItems' => function ($query) {
                $query->where('is_current_version', true)->orderBy('item_category');
            },
            'costing' => function ($query) {
                $query->where('is_current_version', true);
            },
            'deliveryBatches' => function ($query) {
                $query->orderBy('batch_sequence');
            },
            'sopCompliance',
            'productionChecklist',
            'invoices'
        ]);

        // Get summary data
        $sizeBreakdown = OrderSizeRatio::getSizeBreakdownSummary($garmentOrder->id);
        
        return view('buyer-crm.garment-orders.show', compact(
            'garmentOrder',
            'sizeBreakdown'
        ));
    }

    /**
     * Show the form for editing the specified garment order.
     */
    public function edit(GarmentOrder $garmentOrder): View
    {
        $buyers = BuyerProfile::select('id', 'company', 'contact_person')
            ->orderBy('company')
            ->get();

        $merchandisers = User::select('id', 'name')
            ->orderBy('name')
            ->get();

        $garmentOrder->load([
            'sizeRatios' => function ($query) {
                $query->orderBy('sort_order');
            },
            'variants' => function ($query) {
                $query->orderBy('size_sort_order')->orderBy('color_sort_order');
            }
        ]);

        // Check if this is an enhanced order with variants
        $hasVariants = $garmentOrder->variants->count() > 0;

        return view('buyer-crm.garment-orders.edit', compact(
            'garmentOrder',
            'buyers',
            'merchandisers',
            'hasVariants'
        ));
    }

    /**
     * Update the specified garment order.
     */
    public function update(GarmentOrderRequest $request, GarmentOrder $garmentOrder): RedirectResponse
    {
        try {
            DB::beginTransaction();

            $garmentOrder->update($request->validated());

            // Update size ratios if provided
            if ($request->has('size_ratios')) {
                $this->updateSizeRatios($garmentOrder->id, $request->size_ratios);
            }

            // Recalculate total value
            $garmentOrder->calculateTotalValue();

            DB::commit();

            return redirect()
                ->route('buyer-crm.garment-orders.show', $garmentOrder)
                ->with('success', 'Garment order updated successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()
                ->withInput()
                ->with('error', 'Failed to update garment order: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified garment order.
     */
    public function destroy(GarmentOrder $garmentOrder): RedirectResponse
    {
        try {
            // Check if order can be deleted
            if ($garmentOrder->status === 'In Production' || $garmentOrder->status === 'Completed') {
                return back()->with('error', 'Cannot delete orders that are in production or completed.');
            }

            $garmentOrder->delete();

            // Update usage tracking
            $this->decrementUsage('garment_orders');

            return redirect()
                ->route('garment-orders.index')
                ->with('success', 'Garment order deleted successfully.');

        } catch (\Exception $e) {
            return back()->with('error', 'Failed to delete garment order: ' . $e->getMessage());
        }
    }

    /**
     * Get orders for AJAX requests.
     */
    public function search(Request $request): JsonResponse
    {
        $query = GarmentOrder::with(['buyer'])
            ->select('id', 'order_no', 'style_no', 'buyer_id', 'status', 'total_quantity');

        if ($request->filled('term')) {
            $term = $request->term;
            $query->where(function ($q) use ($term) {
                $q->where('order_no', 'like', "%{$term}%")
                  ->orWhere('style_no', 'like', "%{$term}%");
            });
        }

        if ($request->filled('buyer_id')) {
            $query->where('buyer_id', $request->buyer_id);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        $orders = $query->limit(20)->get();

        return response()->json($orders);
    }

    /**
     * Duplicate an existing order.
     */
    public function duplicate(GarmentOrder $garmentOrder): RedirectResponse
    {
        try {
            DB::beginTransaction();

            $newOrder = $garmentOrder->replicate();
            $newOrder->order_no = GarmentOrder::generateOrderNumber();
            $newOrder->status = 'Draft';
            $newOrder->production_ready = false;
            $newOrder->order_date = now();
            $newOrder->delivery_date = null;
            $newOrder->confirmed_date = null;
            $newOrder->created_by = auth()->id();
            $newOrder->save();

            // Duplicate size ratios
            foreach ($garmentOrder->sizeRatios as $sizeRatio) {
                $newSizeRatio = $sizeRatio->replicate();
                $newSizeRatio->garment_order_id = $newOrder->id;
                $newSizeRatio->save();
            }

            // Create production checklist
            OrderProductionChecklist::create([
                'garment_order_id' => $newOrder->id,
                'created_by' => auth()->id(),
            ]);

            DB::commit();

            return redirect()
                ->route('buyer-crm.garment-orders.edit', $newOrder)
                ->with('success', 'Order duplicated successfully. Please review and update the details.');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', 'Failed to duplicate order: ' . $e->getMessage());
        }
    }

    /**
     * Create size ratios for an order.
     */
    private function createSizeRatios(int $orderId, array $sizeRatios): void
    {
        foreach ($sizeRatios as $index => $sizeData) {
            OrderSizeRatio::create([
                'garment_order_id' => $orderId,
                'size_label' => $sizeData['size_label'],
                'size_type' => $sizeData['size_type'],
                'sort_order' => $index + 1,
                'ratio' => $sizeData['ratio'],
                'manual_quantity' => $sizeData['manual_quantity'] ?? null,
                'is_manual_override' => !empty($sizeData['manual_quantity']),
                'calculated_quantity' => 0,
                'final_quantity' => $sizeData['manual_quantity'] ?? 0,
            ]);
        }

        // Recalculate quantities
        OrderSizeRatio::recalculateForOrder($orderId);
    }

    /**
     * Update size ratios for an order.
     */
    private function updateSizeRatios(int $orderId, array $sizeRatios): void
    {
        // Delete existing size ratios
        OrderSizeRatio::where('garment_order_id', $orderId)->delete();

        // Create new size ratios
        $this->createSizeRatios($orderId, $sizeRatios);
    }

    /**
     * Create order variants from the matrix data.
     */
    private function createOrderVariants(int $garmentOrderId, array $variantsData): void
    {
        foreach ($variantsData as $variantData) {
            GarmentOrderVariant::create([
                'garment_order_id' => $garmentOrderId,
                'size_label' => $variantData['size_label'],
                'size_type' => $variantData['size_type'],
                'size_sort_order' => $variantData['size_sort_order'],
                'color_name' => $variantData['color_name'],
                'color_code' => $variantData['color_code'] ?? null,
                'color_sort_order' => $variantData['color_sort_order'],
                'quantity' => $variantData['quantity'],
                'unit_price' => $variantData['unit_price'] ?? null,
                'total_price' => $variantData['total_price'] ?? null,
                'delivery_date' => $variantData['delivery_date'] ?? null,
                'status' => 'pending',
                'is_active' => true,
                'created_by' => auth()->id(),
            ]);
        }
    }

    /**
     * Create size ratios from variants for backward compatibility.
     */
    private function createSizeRatiosFromVariants(int $garmentOrderId, array $selectedSizes, array $variantsData): void
    {
        $sizeQuantities = [];

        // Calculate total quantities per size
        foreach ($variantsData as $variant) {
            $size = $variant['size_label'];
            if (!isset($sizeQuantities[$size])) {
                $sizeQuantities[$size] = 0;
            }
            $sizeQuantities[$size] += $variant['quantity'];
        }

        // Create size ratios
        foreach ($selectedSizes as $index => $size) {
            OrderSizeRatio::create([
                'garment_order_id' => $garmentOrderId,
                'size_label' => $size,
                'size_type' => $variantsData[0]['size_type'] ?? 'text',
                'sort_order' => $index + 1,
                'ratio' => 1, // Default ratio
                'calculated_quantity' => $sizeQuantities[$size] ?? 0,
                'final_quantity' => $sizeQuantities[$size] ?? 0,
                'is_manual_override' => true,
            ]);
        }
    }
}
