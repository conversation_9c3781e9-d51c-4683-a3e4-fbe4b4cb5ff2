<?php

namespace App\Http\Controllers\BuyerCRM;

use App\Http\Controllers\Controller;
use App\Models\BuyerCRM\GarmentOrder;
use App\Models\BuyerCRM\OrderTechPack;
use App\Models\BuyerCRM\OrderCosting;
use App\Models\BuyerCRM\OrderDeliveryBatch;
use App\Models\BuyerCRM\OrderSopCompliance;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\JsonResponse;
use Carbon\Carbon;

class GarmentOrderReportsController extends Controller
{
    /**
     * Display reports for a specific garment order.
     */
    public function index(int $garmentOrderId): View
    {
        $garmentOrder = GarmentOrder::with([
            'buyer',
            'merchandiser',
            'variants',
            'sizeRatios',
            'techPacks' => function ($query) {
                $query->where('is_active', true);
            },
            'bomItems' => function ($query) {
                $query->where('is_current_version', true);
            },
            'costing' => function ($query) {
                $query->where('is_current_version', true);
            },
            'deliveryBatches',
            'sopCompliance',
            'productionChecklist'
        ])->findOrFail($garmentOrderId);

        // Calculate order statistics
        $stats = [
            'total_variants' => $garmentOrder->variants->count(),
            'total_quantity' => $garmentOrder->total_quantity,
            'total_value' => $garmentOrder->total_value,
            'tech_packs_count' => $garmentOrder->techPacks->count(),
            'approved_tech_packs' => $garmentOrder->techPacks->where('sample_status', 'approved')->count(),
            'bom_items_count' => $garmentOrder->bomItems->count(),
            'approved_bom_items' => $garmentOrder->bomItems->where('status', 'approved')->count(),
            'delivery_batches_count' => $garmentOrder->deliveryBatches->count(),
            'completed_batches' => $garmentOrder->deliveryBatches->where('shipment_status', 'delivered')->count(),
            'sop_compliance_items' => $garmentOrder->sopCompliance->count(),
            'compliant_items' => $garmentOrder->sopCompliance->where('is_non_compliant', false)->count(),
        ];

        // Calculate progress percentages
        $progress = [
            'tech_packs' => $stats['tech_packs_count'] > 0 ?
                round(($stats['approved_tech_packs'] / $stats['tech_packs_count']) * 100, 1) : 0,
            'bom_items' => $stats['bom_items_count'] > 0 ?
                round(($stats['approved_bom_items'] / $stats['bom_items_count']) * 100, 1) : 0,
            'delivery_batches' => $stats['delivery_batches_count'] > 0 ?
                round(($stats['completed_batches'] / $stats['delivery_batches_count']) * 100, 1) : 0,
            'sop_compliance' => $stats['sop_compliance_items'] > 0 ?
                round(($stats['compliant_items'] / $stats['sop_compliance_items']) * 100, 1) : 0,
            'production_checklist' => $garmentOrder->productionChecklist?->completion_percentage ?? 0,
        ];

        // Overall completion percentage
        $overallProgress = round(array_sum($progress) / count($progress), 1);

        return view('buyer-crm.garment-orders.reports.index', compact(
            'garmentOrder',
            'stats',
            'progress',
            'overallProgress'
        ));
    }

    /**
     * Display the garment orders dashboard.
     */
    public function dashboard(): View
    {
        // Get summary statistics
        $stats = [
            'total_orders' => GarmentOrder::count(),
            'active_orders' => GarmentOrder::whereIn('status', ['Draft', 'Confirmed', 'In Production'])->count(),
            'completed_orders' => GarmentOrder::where('status', 'Completed')->count(),
            'overdue_orders' => GarmentOrder::overdue()->count(),
            'pending_samples' => OrderTechPack::where('sample_status', 'pending')->where('is_active', true)->count(),
            'pending_costing' => OrderCosting::where('status', 'pending_approval')->where('is_current_version', true)->count(),
            'overdue_batches' => OrderDeliveryBatch::overdue()->count(),
            'non_compliant_items' => OrderSopCompliance::where('is_non_compliant', true)->count(),
        ];

        // Get recent orders
        $recentOrders = GarmentOrder::with(['buyer', 'merchandiser'])
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        // Get orders by status
        $ordersByStatus = GarmentOrder::selectRaw('status, COUNT(*) as count')
            ->groupBy('status')
            ->pluck('count', 'status')
            ->toArray();

        // Get orders by product category
        $ordersByCategory = GarmentOrder::selectRaw('product_category, COUNT(*) as count')
            ->groupBy('product_category')
            ->pluck('count', 'product_category')
            ->toArray();

        // Get monthly order trends (last 12 months)
        $monthlyTrends = [];
        for ($i = 11; $i >= 0; $i--) {
            $date = Carbon::now()->subMonths($i);
            $monthlyTrends[] = [
                'month' => $date->format('M Y'),
                'orders' => GarmentOrder::whereYear('created_at', $date->year)
                    ->whereMonth('created_at', $date->month)
                    ->count(),
                'value' => GarmentOrder::whereYear('created_at', $date->year)
                    ->whereMonth('created_at', $date->month)
                    ->sum('total_value') ?? 0,
            ];
        }

        return view('buyer-crm.garment-orders.reports.dashboard', compact(
            'stats',
            'recentOrders',
            'ordersByStatus',
            'ordersByCategory',
            'monthlyTrends'
        ));
    }

    /**
     * Get dashboard data for AJAX requests.
     */
    public function dashboardData(Request $request): JsonResponse
    {
        $dateFrom = $request->get('date_from', Carbon::now()->subDays(30)->format('Y-m-d'));
        $dateTo = $request->get('date_to', Carbon::now()->format('Y-m-d'));

        $query = GarmentOrder::whereBetween('created_at', [$dateFrom, $dateTo]);

        $data = [
            'total_orders' => $query->count(),
            'total_value' => $query->sum('total_value') ?? 0,
            'avg_order_value' => $query->avg('total_value') ?? 0,
            'orders_by_status' => $query->selectRaw('status, COUNT(*) as count')
                ->groupBy('status')
                ->pluck('count', 'status'),
            'orders_by_buyer' => $query->with('buyer')
                ->get()
                ->groupBy('buyer.company')
                ->map(function ($orders) {
                    return $orders->count();
                }),
        ];

        return response()->json($data);
    }

    /**
     * Export orders report.
     */
    public function export(Request $request)
    {
        $request->validate([
            'format' => 'required|in:csv,excel,pdf',
            'date_from' => 'nullable|date',
            'date_to' => 'nullable|date|after_or_equal:date_from',
            'status' => 'nullable|string',
            'buyer_id' => 'nullable|integer',
        ]);

        $query = GarmentOrder::with(['buyer', 'merchandiser']);

        // Apply filters
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('buyer_id')) {
            $query->where('buyer_id', $request->buyer_id);
        }

        $orders = $query->orderBy('created_at', 'desc')->get();

        // Prepare export data
        $exportData = $orders->map(function ($order) {
            return [
                'Order No' => $order->order_no,
                'Style No' => $order->style_no,
                'Buyer' => $order->buyer?->company,
                'Merchandiser' => $order->merchandiser?->name,
                'Product Category' => $order->product_category,
                'Garment Type' => $order->garment_type,
                'Season' => $order->season,
                'Order Type' => $order->order_type,
                'Total Quantity' => $order->total_quantity,
                'Unit Price' => $order->unit_price,
                'Total Value' => $order->total_value,
                'Status' => $order->status,
                'Order Date' => $order->order_date?->format('Y-m-d'),
                'Delivery Date' => $order->delivery_date?->format('Y-m-d'),
                'Created At' => $order->created_at->format('Y-m-d H:i:s'),
            ];
        });

        return response()->json([
            'success' => true,
            'data' => $exportData,
            'filename' => 'garment_orders_' . now()->format('Y_m_d_H_i_s'),
            'format' => $request->format,
        ]);
    }

    /**
     * Get production status report.
     */
    public function productionStatus(Request $request): JsonResponse
    {
        $orders = GarmentOrder::with(['productionChecklist', 'deliveryBatches'])
            ->whereIn('status', ['Confirmed', 'In Production'])
            ->get();

        $productionData = $orders->map(function ($order) {
            $checklist = $order->productionChecklist;
            $batches = $order->deliveryBatches;

            return [
                'order_no' => $order->order_no,
                'buyer' => $order->buyer?->company,
                'style_no' => $order->style_no,
                'total_quantity' => $order->total_quantity,
                'production_ready' => $order->production_ready,
                'checklist_completion' => $checklist?->completion_percentage ?? 0,
                'production_status' => $checklist?->status ?? 'not_started',
                'batches_count' => $batches->count(),
                'completed_batches' => $batches->where('shipment_status', 'delivered')->count(),
                'overdue_batches' => $batches->filter(function ($batch) {
                    return $batch->isOverdue();
                })->count(),
            ];
        });

        return response()->json($productionData);
    }

    /**
     * Get quality status report.
     */
    public function qualityStatus(Request $request): JsonResponse
    {
        $orders = GarmentOrder::with(['techPacks', 'sopCompliance', 'deliveryBatches'])
            ->get();

        $qualityData = $orders->map(function ($order) {
            $techPacks = $order->techPacks->where('is_active', true);
            $sopItems = $order->sopCompliance;
            $batches = $order->deliveryBatches;

            return [
                'order_no' => $order->order_no,
                'buyer' => $order->buyer?->company,
                'style_no' => $order->style_no,
                'samples_pending' => $techPacks->where('sample_status', 'pending')->count(),
                'samples_approved' => $techPacks->where('sample_status', 'approved')->count(),
                'samples_rejected' => $techPacks->where('sample_status', 'rejected')->count(),
                'sop_total' => $sopItems->count(),
                'sop_compliant' => $sopItems->whereIn('compliance_status', ['completed', 'verified'])->count(),
                'sop_non_compliant' => $sopItems->where('is_non_compliant', true)->count(),
                'quality_approved_batches' => $batches->where('quality_approved', true)->count(),
                'total_batches' => $batches->count(),
            ];
        });

        return response()->json($qualityData);
    }

    /**
     * Get delivery performance report.
     */
    public function deliveryPerformance(Request $request): JsonResponse
    {
        $batches = OrderDeliveryBatch::with(['garmentOrder.buyer'])
            ->get();

        $deliveryData = $batches->map(function ($batch) {
            $isOverdue = $batch->isOverdue();
            $daysUntilDelivery = $batch->daysUntilDelivery();

            return [
                'order_no' => $batch->garmentOrder->order_no,
                'buyer' => $batch->garmentOrder->buyer?->company,
                'batch_no' => $batch->batch_no,
                'quantity' => $batch->quantity,
                'delivery_date' => $batch->delivery_date->format('Y-m-d'),
                'revised_delivery_date' => $batch->revised_delivery_date?->format('Y-m-d'),
                'actual_delivery_date' => $batch->actual_delivery_date?->format('Y-m-d'),
                'shipment_status' => $batch->shipment_status,
                'production_status' => $batch->production_status,
                'is_overdue' => $isOverdue,
                'days_until_delivery' => $daysUntilDelivery,
                'has_delay' => $batch->has_delay,
                'delay_reason' => $batch->delay_reason,
            ];
        });

        return response()->json($deliveryData);
    }

    /**
     * Get costing analysis report.
     */
    public function costingAnalysis(Request $request): JsonResponse
    {
        $costings = OrderCosting::with(['garmentOrder.buyer'])
            ->where('is_current_version', true)
            ->where('status', 'approved')
            ->get();

        $costingData = $costings->map(function ($costing) {
            return [
                'order_no' => $costing->garmentOrder->order_no,
                'buyer' => $costing->garmentOrder->buyer?->company,
                'style_no' => $costing->garmentOrder->style_no,
                'total_cost' => $costing->total_cost_per_piece,
                'material_cost' => $costing->total_material_cost,
                'manufacturing_cost' => $costing->total_manufacturing_cost,
                'additional_cost' => $costing->total_additional_costs,
                'calculated_price' => $costing->calculated_unit_price,
                'final_price' => $costing->final_negotiated_price,
                'margin_percentage' => $costing->actual_margin_percentage,
                'margin_amount' => $costing->actual_margin_amount,
                'currency' => $costing->currency,
            ];
        });

        return response()->json($costingData);
    }
}
