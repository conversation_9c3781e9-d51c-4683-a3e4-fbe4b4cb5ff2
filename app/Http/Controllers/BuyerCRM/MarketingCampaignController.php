<?php

namespace App\Http\Controllers\BuyerCRM;

use App\Http\Controllers\Controller;
use App\Models\BuyerCRM\MarketingCampaign;
use App\Models\BuyerCRM\MarketingCampaignBuyer;
use App\Models\BuyerCRM\BuyerProfile;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Validation\Rule;
use Carbon\Carbon;

/**
 * Marketing Campaign Controller
 * 
 * Handles CRUD operations for marketing campaigns and buyer assignments
 */
class MarketingCampaignController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display a listing of marketing campaigns.
     */
    public function index(Request $request): View
    {
        $query = MarketingCampaign::with(['assignedUser']);

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('assigned_to')) {
            $query->assignedTo($request->assigned_to);
        }

        if ($request->filled('target_market')) {
            $query->byTargetMarket($request->target_market);
        }

        if ($request->filled('date_from') && $request->filled('date_to')) {
            $query->dateRange(
                Carbon::parse($request->date_from),
                Carbon::parse($request->date_to)
            );
        }

        // Special filters
        if ($request->filled('filter')) {
            switch ($request->filter) {
                case 'active':
                    $query->active();
                    break;
                case 'running':
                    $query->running();
                    break;
                case 'current':
                    $query->current();
                    break;
                case 'upcoming':
                    $query->upcoming();
                    break;
                case 'completed':
                    $query->completed();
                    break;
                case 'my_campaigns':
                    $query->assignedTo(auth()->id());
                    break;
            }
        }

        // Sort
        $sortBy = $request->get('sort_by', 'created_at');
        $sortDirection = $request->get('sort_direction', 'desc');
        $query->orderBy($sortBy, $sortDirection);

        $campaigns = $query->paginate(config('buyer-crm.pagination.per_page', 15));

        // Get filter options
        $users = User::select('id', 'name')->get();
        $targetMarkets = MarketingCampaign::distinct()->pluck('target_market')->filter()->sort();

        return view('buyer-crm.campaigns.index', compact('campaigns', 'users', 'targetMarkets'));
    }

    /**
     * Show the form for creating a new campaign.
     */
    public function create(): View
    {
        $users = User::select('id', 'name')->get();

        return view('buyer-crm.campaigns.create', compact('users'));
    }

    /**
     * Store a newly created campaign.
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'title' => ['required', 'string', 'max:255'],
            'description' => ['nullable', 'string', 'max:2000'],
            'target_market' => ['required', 'string', 'max:255'],
            'product_focus' => ['required', 'string', 'max:255'],
            'goal' => ['required', 'string', 'max:2000'],
            'budget' => ['nullable', 'numeric', 'min:0'],
            'start_date' => ['required', 'date', 'after_or_equal:today'],
            'end_date' => ['required', 'date', 'after:start_date'],
            'assigned_to' => ['required', 'exists:users,id'],
            'status' => ['required', Rule::in(MarketingCampaign::$statuses)],
            'notes' => ['nullable', 'string', 'max:2000'],
        ]);

        $campaign = MarketingCampaign::create($validated);

        return redirect()
            ->route('buyer-crm.campaigns.show', $campaign)
            ->with('success', 'Marketing campaign created successfully.');
    }

    /**
     * Display the specified campaign.
     */
    public function show(MarketingCampaign $campaign): View
    {
        $campaign->load(['assignedUser', 'campaignBuyers.buyer']);

        $statistics = $campaign->getStatistics();

        // Get available buyers not in this campaign
        $campaignBuyerIds = $campaign->campaignBuyers->pluck('buyer_id');
        $availableBuyers = BuyerProfile::whereNotIn('id', $campaignBuyerIds)
            ->select('id', 'company', 'contact_person')
            ->orderBy('company')
            ->get();

        return view('buyer-crm.campaigns.show', compact('campaign', 'statistics', 'availableBuyers'));
    }

    /**
     * Show the form for editing the campaign.
     */
    public function edit(MarketingCampaign $campaign): View
    {
        $users = User::select('id', 'name')->get();

        return view('buyer-crm.campaigns.edit', compact('campaign', 'users'));
    }

    /**
     * Update the specified campaign.
     */
    public function update(Request $request, MarketingCampaign $campaign): RedirectResponse
    {
        $validated = $request->validate([
            'title' => ['required', 'string', 'max:255'],
            'description' => ['nullable', 'string', 'max:2000'],
            'target_market' => ['required', 'string', 'max:255'],
            'product_focus' => ['required', 'string', 'max:255'],
            'goal' => ['required', 'string', 'max:2000'],
            'budget' => ['nullable', 'numeric', 'min:0'],
            'start_date' => ['required', 'date'],
            'end_date' => ['required', 'date', 'after:start_date'],
            'assigned_to' => ['required', 'exists:users,id'],
            'status' => ['required', Rule::in(MarketingCampaign::$statuses)],
            'notes' => ['nullable', 'string', 'max:2000'],
        ]);

        $campaign->update($validated);

        return redirect()
            ->route('buyer-crm.campaigns.show', $campaign)
            ->with('success', 'Marketing campaign updated successfully.');
    }

    /**
     * Remove the specified campaign.
     */
    public function destroy(MarketingCampaign $campaign): RedirectResponse
    {
        $campaign->delete();

        return redirect()
            ->route('buyer-crm.campaigns.index')
            ->with('success', 'Marketing campaign deleted successfully.');
    }

    /**
     * Assign buyers to a campaign.
     */
    public function assignBuyers(Request $request, MarketingCampaign $campaign): JsonResponse
    {
        $validated = $request->validate([
            'buyer_ids' => ['required', 'array'],
            'buyer_ids.*' => ['exists:buyer_profiles,id'],
        ]);

        $assignedCount = 0;
        $skippedCount = 0;

        foreach ($validated['buyer_ids'] as $buyerId) {
            // Check if buyer is already assigned to this campaign
            $exists = MarketingCampaignBuyer::where('campaign_id', $campaign->id)
                ->where('buyer_id', $buyerId)
                ->exists();

            if (!$exists) {
                MarketingCampaignBuyer::create([
                    'campaign_id' => $campaign->id,
                    'buyer_id' => $buyerId,
                    'stage' => 'Targeted',
                ]);
                $assignedCount++;
            } else {
                $skippedCount++;
            }
        }

        return response()->json([
            'success' => true,
            'message' => "Assigned {$assignedCount} buyers to campaign. Skipped {$skippedCount} already assigned buyers.",
            'assigned_count' => $assignedCount,
            'skipped_count' => $skippedCount,
        ]);
    }

    /**
     * Display campaign buyers.
     */
    public function campaignBuyers(MarketingCampaign $campaign): View
    {
        $campaignBuyers = $campaign->campaignBuyers()
            ->with('buyer')
            ->orderBy('created_at', 'desc')
            ->paginate(config('buyer-crm.pagination.per_page', 15));

        $statistics = $campaign->getStatistics();

        return view('buyer-crm.campaigns.buyers', compact('campaign', 'campaignBuyers', 'statistics'));
    }

    /**
     * Update buyer stage in campaign.
     */
    public function updateBuyerStage(Request $request, MarketingCampaign $campaign, BuyerProfile $buyer): JsonResponse
    {
        $validated = $request->validate([
            'stage' => ['required', Rule::in(MarketingCampaignBuyer::$stages)],
            'notes' => ['nullable', 'string', 'max:1000'],
        ]);

        $campaignBuyer = MarketingCampaignBuyer::where('campaign_id', $campaign->id)
            ->where('buyer_id', $buyer->id)
            ->first();

        if (!$campaignBuyer) {
            return response()->json([
                'success' => false,
                'message' => 'Buyer is not assigned to this campaign.'
            ], 404);
        }

        $campaignBuyer->updateStage($validated['stage'], $validated['notes']);

        return response()->json([
            'success' => true,
            'message' => 'Buyer stage updated successfully.',
            'campaign_buyer' => [
                'id' => $campaignBuyer->id,
                'stage' => $campaignBuyer->stage,
                'stage_color' => $campaignBuyer->getStageColor(),
                'stage_icon' => $campaignBuyer->getStageIcon(),
                'contacted_at' => $campaignBuyer->contacted_at?->format('Y-m-d H:i:s'),
                'response_received_at' => $campaignBuyer->response_received_at?->format('Y-m-d H:i:s'),
                'notes' => $campaignBuyer->notes,
            ]
        ]);
    }

    /**
     * Get campaign statistics.
     */
    public function statistics(): JsonResponse
    {
        $stats = [
            'total_campaigns' => MarketingCampaign::count(),
            'active_campaigns' => MarketingCampaign::active()->count(),
            'running_campaigns' => MarketingCampaign::running()->count(),
            'completed_campaigns' => MarketingCampaign::completed()->count(),
            'total_budget' => MarketingCampaign::sum('budget'),
            'by_status' => [],
            'recent_campaigns' => [],
        ];

        // Campaigns by status
        foreach (MarketingCampaign::$statuses as $status) {
            $stats['by_status'][$status] = MarketingCampaign::where('status', $status)->count();
        }

        // Recent campaigns
        $recentCampaigns = MarketingCampaign::with('assignedUser')
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get()
            ->map(function ($campaign) {
                return [
                    'id' => $campaign->id,
                    'title' => $campaign->title,
                    'status' => $campaign->status,
                    'assigned_to' => $campaign->assignedUser->name,
                    'start_date' => $campaign->start_date->format('Y-m-d'),
                    'end_date' => $campaign->end_date->format('Y-m-d'),
                    'statistics' => $campaign->getStatistics(),
                ];
            });

        $stats['recent_campaigns'] = $recentCampaigns;

        return response()->json([
            'success' => true,
            'statistics' => $stats
        ]);
    }
}
