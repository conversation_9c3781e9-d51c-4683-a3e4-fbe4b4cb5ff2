<?php

namespace App\Http\Controllers\BuyerCRM;

use App\Http\Controllers\Controller;
use App\Models\BuyerCRM\OrderBomItem;
use App\Models\BuyerCRM\GarmentOrder;
use App\Models\Party;
use App\Http\Requests\BuyerCRM\OrderBomItemRequest;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\DB;

class OrderBomItemController extends Controller
{
    /**
     * Display BOM items for a garment order.
     */
    public function index(Request $request, int $garmentOrderId): View|JsonResponse
    {
        $order = GarmentOrder::findOrFail($garmentOrderId);
        
        $query = OrderBomItem::where('garment_order_id', $garmentOrderId)
            ->with(['supplier', 'creator'])
            ->where('is_current_version', true);

        // Apply filters
        if ($request->filled('item_category')) {
            $query->where('item_category', $request->item_category);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('supplier_id')) {
            $query->where('supplier_id', $request->supplier_id);
        }

        if ($request->filled('is_critical')) {
            $query->where('is_critical', $request->boolean('is_critical'));
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('item_name', 'like', "%{$search}%")
                  ->orWhere('item_code', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        $bomItems = $query->orderBy('item_category')->orderBy('item_name')->get();

        // Get BOM summary
        $summary = OrderBomItem::getBomSummary($garmentOrderId);

        // Get suppliers for filter
        $suppliers = Party::where('type', 'supplier')
            ->select('id', 'name', 'address')
            ->orderBy('name')
            ->get();

        if ($request->wantsJson()) {
            return response()->json([
                'bom_items' => $bomItems,
                'summary' => $summary,
            ]);
        }

        return view('buyer-crm.garment-orders.bom-items.index', compact(
            'order',
            'bomItems',
            'summary',
            'suppliers'
        ));
    }

    /**
     * Show the form for creating a new BOM item.
     */
    public function create(int $garmentOrderId): View
    {
        $order = GarmentOrder::findOrFail($garmentOrderId);
        
        $suppliers = Party::where('type', 'supplier')
            ->select('id', 'name', 'address')
            ->orderBy('name')
            ->get();
        
        return view('buyer-crm.garment-orders.bom-items.create', compact(
            'order',
            'suppliers'
        ));
    }

    /**
     * Store newly created BOM items.
     */
    public function store(OrderBomItemRequest $request): RedirectResponse|JsonResponse
    {
        try {
            DB::beginTransaction();

            if ($request->has('items')) {
                // Bulk creation
                foreach ($request->items as $itemData) {
                    OrderBomItem::create(array_merge($itemData, [
                        'garment_order_id' => $request->garment_order_id,
                        'created_by' => auth()->id(),
                    ]));
                }
            } else {
                // Single creation
                OrderBomItem::create($request->validated());
            }

            DB::commit();

            if ($request->wantsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'BOM items created successfully.',
                ]);
            }

            return redirect()
                ->route('buyer-crm.garment-orders.bom-items.index', $request->garment_order_id)
                ->with('success', 'BOM items created successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            
            if ($request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to create BOM items: ' . $e->getMessage(),
                ], 422);
            }

            return back()
                ->withInput()
                ->with('error', 'Failed to create BOM items: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified BOM item.
     */
    public function show(OrderBomItem $bomItem): View
    {
        $bomItem->load(['garmentOrder', 'supplier', 'creator']);
        
        return view('buyer-crm.garment-orders.bom-items.show', compact('bomItem'));
    }

    /**
     * Show the form for editing the specified BOM item.
     */
    public function edit(OrderBomItem $bomItem): View
    {
        $order = $bomItem->garmentOrder;
        
        $suppliers = Party::where('type', 'supplier')
            ->select('id', 'name', 'address')
            ->orderBy('name')
            ->get();
        
        return view('buyer-crm.garment-orders.bom-items.edit', compact(
            'bomItem',
            'order',
            'suppliers'
        ));
    }

    /**
     * Update the specified BOM item.
     */
    public function update(OrderBomItemRequest $request, OrderBomItem $bomItem): RedirectResponse|JsonResponse
    {
        try {
            DB::beginTransaction();

            $bomItem->update($request->validated());

            DB::commit();

            if ($request->wantsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'BOM item updated successfully.',
                    'bom_item' => $bomItem->fresh(['supplier']),
                ]);
            }

            return redirect()
                ->route('buyer-crm.garment-orders.bom-items.show', $bomItem)
                ->with('success', 'BOM item updated successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            
            if ($request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to update BOM item: ' . $e->getMessage(),
                ], 422);
            }

            return back()
                ->withInput()
                ->with('error', 'Failed to update BOM item: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified BOM item.
     */
    public function destroy(OrderBomItem $bomItem): RedirectResponse|JsonResponse
    {
        try {
            $garmentOrderId = $bomItem->garment_order_id;
            $bomItem->delete();

            if (request()->wantsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'BOM item deleted successfully.',
                ]);
            }

            return redirect()
                ->route('buyer-crm.garment-orders.bom-items.index', $garmentOrderId)
                ->with('success', 'BOM item deleted successfully.');

        } catch (\Exception $e) {
            if (request()->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to delete BOM item: ' . $e->getMessage(),
                ], 422);
            }

            return back()->with('error', 'Failed to delete BOM item: ' . $e->getMessage());
        }
    }

    /**
     * Create a new version of the BOM item.
     */
    public function createVersion(OrderBomItemRequest $request, OrderBomItem $bomItem): JsonResponse
    {
        try {
            DB::beginTransaction();

            $newVersion = $bomItem->createNewVersion($request->validated());

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'New BOM version created successfully.',
                'bom_item' => $newVersion->load(['supplier']),
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to create new version: ' . $e->getMessage(),
            ], 422);
        }
    }

    /**
     * Update order quantity for BOM item.
     */
    public function updateOrderQuantity(Request $request, OrderBomItem $bomItem): JsonResponse
    {
        $request->validate([
            'order_quantity' => 'required|numeric|min:0|max:999999.99',
            'order_date' => 'nullable|date|before_or_equal:today',
            'delivery_date' => 'nullable|date|after_or_equal:order_date',
        ]);

        try {
            $bomItem->update([
                'order_quantity' => $request->order_quantity,
                'order_date' => $request->order_date ?? now(),
                'delivery_date' => $request->delivery_date,
                'status' => 'ordered',
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Order quantity updated successfully.',
                'bom_item' => $bomItem->fresh(),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update order quantity: ' . $e->getMessage(),
            ], 422);
        }
    }

    /**
     * Update received quantity for BOM item.
     */
    public function updateReceivedQuantity(Request $request, OrderBomItem $bomItem): JsonResponse
    {
        $request->validate([
            'received_quantity' => 'required|numeric|min:0|max:' . ($bomItem->order_quantity ?? 999999.99),
        ]);

        try {
            $bomItem->update([
                'received_quantity' => $request->received_quantity,
                'status' => $request->received_quantity >= $bomItem->order_quantity ? 'received' : 'ordered',
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Received quantity updated successfully.',
                'bom_item' => $bomItem->fresh(),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update received quantity: ' . $e->getMessage(),
            ], 422);
        }
    }

    /**
     * Get BOM summary for an order.
     */
    public function summary(int $garmentOrderId): JsonResponse
    {
        $summary = OrderBomItem::getBomSummary($garmentOrderId);
        
        return response()->json($summary);
    }

    /**
     * Export BOM items to Excel/CSV.
     */
    public function export(Request $request, int $garmentOrderId)
    {
        // This would typically use a package like Laravel Excel
        // For now, return JSON data that can be processed by frontend
        
        $bomItems = OrderBomItem::where('garment_order_id', $garmentOrderId)
            ->with(['supplier'])
            ->where('is_current_version', true)
            ->orderBy('item_category')
            ->orderBy('item_name')
            ->get();

        $exportData = $bomItems->map(function ($item) {
            return [
                'Item Name' => $item->item_name,
                'Category' => ucfirst($item->item_category),
                'Description' => $item->description,
                'Unit' => $item->unit,
                'Consumption' => $item->consumption_per_piece,
                'Rate' => $item->rate_per_unit,
                'Total Cost' => $item->final_cost_per_piece,
                'Supplier' => $item->supplier?->name,
                'Status' => ucfirst($item->status),
                'Critical' => $item->is_critical ? 'Yes' : 'No',
            ];
        });

        return response()->json([
            'success' => true,
            'data' => $exportData,
            'filename' => 'BOM_Order_' . $garmentOrderId . '_' . now()->format('Y-m-d'),
        ]);
    }

    /**
     * Approve a BOM item.
     */
    public function approve(Request $request, int $garmentOrderId): JsonResponse
    {
        $request->validate([
            'item_id' => 'required|exists:order_bom_items,id',
            'comments' => 'nullable|string|max:1000',
        ]);

        try {
            $bomItem = OrderBomItem::where('id', $request->item_id)
                ->where('garment_order_id', $garmentOrderId)
                ->firstOrFail();

            $bomItem->update([
                'status' => 'approved',
                'approved_by' => auth()->id(),
                'approved_at' => now(),
                'approval_comments' => $request->comments,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'BOM item approved successfully.',
                'bom_item' => $bomItem->fresh(),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to approve BOM item: ' . $e->getMessage(),
            ], 422);
        }
    }

    /**
     * Reject a BOM item.
     */
    public function reject(Request $request, int $garmentOrderId): JsonResponse
    {
        $request->validate([
            'item_id' => 'required|exists:order_bom_items,id',
            'reason' => 'required|string|max:1000',
        ]);

        try {
            $bomItem = OrderBomItem::where('id', $request->item_id)
                ->where('garment_order_id', $garmentOrderId)
                ->firstOrFail();

            $bomItem->update([
                'status' => 'rejected',
                'rejected_by' => auth()->id(),
                'rejected_at' => now(),
                'rejection_reason' => $request->reason,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'BOM item rejected successfully.',
                'bom_item' => $bomItem->fresh(),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to reject BOM item: ' . $e->getMessage(),
            ], 422);
        }
    }

    /**
     * Duplicate a BOM item.
     */
    public function duplicate(Request $request, int $garmentOrderId): JsonResponse
    {
        $request->validate([
            'item_id' => 'required|exists:order_bom_items,id',
        ]);

        try {
            DB::beginTransaction();

            $originalItem = OrderBomItem::where('id', $request->item_id)
                ->where('garment_order_id', $garmentOrderId)
                ->firstOrFail();

            $duplicatedItem = $originalItem->replicate();
            $duplicatedItem->item_name = $originalItem->item_name . ' (Copy)';
            $duplicatedItem->status = 'pending';
            $duplicatedItem->created_by = auth()->id();
            $duplicatedItem->approved_by = null;
            $duplicatedItem->approved_at = null;
            $duplicatedItem->approval_comments = null;
            $duplicatedItem->rejected_by = null;
            $duplicatedItem->rejected_at = null;
            $duplicatedItem->rejection_reason = null;
            $duplicatedItem->save();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'BOM item duplicated successfully.',
                'bom_item' => $duplicatedItem->fresh(),
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => 'Failed to duplicate BOM item: ' . $e->getMessage(),
            ], 422);
        }
    }

    /**
     * Add standard BOM items based on garment type.
     */
    public function addStandard(Request $request, int $garmentOrderId): JsonResponse
    {
        $request->validate([
            'type' => 'required|string|in:shirt,pant,dress,jacket',
        ]);

        try {
            DB::beginTransaction();

            $garmentOrder = GarmentOrder::findOrFail($garmentOrderId);
            $standardItems = $this->getStandardBomItems($request->type);

            foreach ($standardItems as $itemData) {
                OrderBomItem::create(array_merge($itemData, [
                    'garment_order_id' => $garmentOrderId,
                    'created_by' => auth()->id(),
                    'status' => 'pending',
                ]));
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Standard BOM items added successfully.',
                'items_count' => count($standardItems),
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => 'Failed to add standard BOM items: ' . $e->getMessage(),
            ], 422);
        }
    }

    /**
     * Recalculate total costs for all BOM items.
     */
    public function recalculate(Request $request, int $garmentOrderId): JsonResponse
    {
        try {
            $bomItems = OrderBomItem::where('garment_order_id', $garmentOrderId)->get();

            $totalCost = 0;
            foreach ($bomItems as $item) {
                $itemTotal = $item->required_quantity * $item->unit_cost;
                $totalCost += $itemTotal;
            }

            // Update the garment order with total BOM cost
            $garmentOrder = GarmentOrder::findOrFail($garmentOrderId);
            $garmentOrder->update(['bom_total_cost' => $totalCost]);

            return response()->json([
                'success' => true,
                'message' => 'BOM costs recalculated successfully.',
                'total_cost' => $totalCost,
                'items_count' => $bomItems->count(),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to recalculate BOM costs: ' . $e->getMessage(),
            ], 422);
        }
    }

    /**
     * Get standard BOM items for different garment types.
     */
    private function getStandardBomItems(string $type): array
    {
        $standardItems = [
            'shirt' => [
                ['item_name' => 'Main Fabric', 'item_category' => 'fabric', 'unit' => 'yards', 'required_quantity' => 2.5, 'unit_cost' => 8.00],
                ['item_name' => 'Thread', 'item_category' => 'notions', 'unit' => 'spools', 'required_quantity' => 2, 'unit_cost' => 1.50],
                ['item_name' => 'Buttons', 'item_category' => 'notions', 'unit' => 'pieces', 'required_quantity' => 8, 'unit_cost' => 0.25],
                ['item_name' => 'Interfacing', 'item_category' => 'fabric', 'unit' => 'yards', 'required_quantity' => 0.5, 'unit_cost' => 3.00],
                ['item_name' => 'Label', 'item_category' => 'notions', 'unit' => 'pieces', 'required_quantity' => 2, 'unit_cost' => 0.50],
            ],
            'pant' => [
                ['item_name' => 'Main Fabric', 'item_category' => 'fabric', 'unit' => 'yards', 'required_quantity' => 3.0, 'unit_cost' => 10.00],
                ['item_name' => 'Thread', 'item_category' => 'notions', 'unit' => 'spools', 'required_quantity' => 2, 'unit_cost' => 1.50],
                ['item_name' => 'Zipper', 'item_category' => 'notions', 'unit' => 'pieces', 'required_quantity' => 1, 'unit_cost' => 2.00],
                ['item_name' => 'Button', 'item_category' => 'notions', 'unit' => 'pieces', 'required_quantity' => 1, 'unit_cost' => 0.50],
                ['item_name' => 'Pocket Lining', 'item_category' => 'fabric', 'unit' => 'yards', 'required_quantity' => 0.5, 'unit_cost' => 4.00],
                ['item_name' => 'Waistband Interfacing', 'item_category' => 'fabric', 'unit' => 'yards', 'required_quantity' => 0.3, 'unit_cost' => 3.00],
            ],
            'dress' => [
                ['item_name' => 'Main Fabric', 'item_category' => 'fabric', 'unit' => 'yards', 'required_quantity' => 4.0, 'unit_cost' => 12.00],
                ['item_name' => 'Lining', 'item_category' => 'fabric', 'unit' => 'yards', 'required_quantity' => 3.5, 'unit_cost' => 6.00],
                ['item_name' => 'Thread', 'item_category' => 'notions', 'unit' => 'spools', 'required_quantity' => 3, 'unit_cost' => 1.50],
                ['item_name' => 'Zipper', 'item_category' => 'notions', 'unit' => 'pieces', 'required_quantity' => 1, 'unit_cost' => 3.00],
                ['item_name' => 'Interfacing', 'item_category' => 'fabric', 'unit' => 'yards', 'required_quantity' => 0.5, 'unit_cost' => 3.00],
            ],
            'jacket' => [
                ['item_name' => 'Main Fabric', 'item_category' => 'fabric', 'unit' => 'yards', 'required_quantity' => 3.5, 'unit_cost' => 15.00],
                ['item_name' => 'Lining', 'item_category' => 'fabric', 'unit' => 'yards', 'required_quantity' => 3.0, 'unit_cost' => 8.00],
                ['item_name' => 'Thread', 'item_category' => 'notions', 'unit' => 'spools', 'required_quantity' => 4, 'unit_cost' => 1.50],
                ['item_name' => 'Buttons', 'item_category' => 'notions', 'unit' => 'pieces', 'required_quantity' => 6, 'unit_cost' => 0.75],
                ['item_name' => 'Shoulder Pads', 'item_category' => 'notions', 'unit' => 'pairs', 'required_quantity' => 1, 'unit_cost' => 4.00],
                ['item_name' => 'Interfacing', 'item_category' => 'fabric', 'unit' => 'yards', 'required_quantity' => 1.0, 'unit_cost' => 3.00],
            ],
        ];

        return $standardItems[$type] ?? [];
    }
}
