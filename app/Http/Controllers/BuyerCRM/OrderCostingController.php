<?php

namespace App\Http\Controllers\BuyerCRM;

use App\Http\Controllers\Controller;
use App\Models\BuyerCRM\OrderCosting;
use App\Models\BuyerCRM\GarmentOrder;
use App\Models\BuyerCRM\OrderBomItem;
use App\Http\Requests\BuyerCRM\OrderCostingRequest;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\DB;

class OrderCostingController extends Controller
{
    /**
     * Display costing for a garment order.
     */
    public function index(Request $request, int $garmentOrderId): View|JsonResponse
    {
        $order = GarmentOrder::findOrFail($garmentOrderId);
        
        $costing = OrderCosting::where('garment_order_id', $garmentOrderId)
            ->with(['creator', 'updater', 'approver'])
            ->where('is_current_version', true)
            ->first();

        if ($request->wantsJson()) {
            return response()->json([
                'costing' => $costing,
                'order' => $order,
            ]);
        }

        return view('buyer-crm.garment-orders.costing.index', compact(
            'order',
            'costing'
        ));
    }

    /**
     * Show the form for creating new costing.
     */
    public function create(int $garmentOrderId): View
    {
        $order = GarmentOrder::with(['bomItems' => function ($query) {
            $query->where('is_current_version', true);
        }])->findOrFail($garmentOrderId);
        
        // Check if costing already exists
        $existingCosting = OrderCosting::where('garment_order_id', $garmentOrderId)
            ->where('is_current_version', true)
            ->first();

        if ($existingCosting) {
            return redirect()
                ->route('buyer-crm.garment-orders.costing.edit', [$garmentOrderId, $existingCosting])
                ->with('info', 'Costing already exists. You can edit it or create a new version.');
        }

        // Auto-calculate BOM costs
        $bomSummary = OrderBomItem::getBomSummary($garmentOrderId);
        
        return view('buyer-crm.garment-orders.costing.create', compact(
            'order',
            'bomSummary'
        ));
    }

    /**
     * Store newly created costing.
     */
    public function store(OrderCostingRequest $request): RedirectResponse|JsonResponse
    {
        try {
            DB::beginTransaction();

            $costing = OrderCosting::create($request->validated());

            // Update BOM costs from related items
            $costing->updateBomCosts();
            $costing->recalculateAll();

            DB::commit();

            if ($request->wantsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Costing created successfully.',
                    'costing' => $costing->fresh(['creator']),
                ]);
            }

            return redirect()
                ->route('buyer-crm.garment-orders.costing.show', [$request->garment_order_id, $costing])
                ->with('success', 'Costing created successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            
            if ($request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to create costing: ' . $e->getMessage(),
                ], 422);
            }

            return back()
                ->withInput()
                ->with('error', 'Failed to create costing: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified costing.
     */
    public function show(int $garmentOrderId, OrderCosting $costing): View
    {
        $order = GarmentOrder::findOrFail($garmentOrderId);
        $costing->load(['creator', 'updater', 'approver']);
        
        return view('buyer-crm.garment-orders.costing.show', compact(
            'order',
            'costing'
        ));
    }

    /**
     * Show the form for editing the specified costing.
     */
    public function edit(int $garmentOrderId, OrderCosting $costing): View
    {
        $order = GarmentOrder::with(['bomItems' => function ($query) {
            $query->where('is_current_version', true);
        }])->findOrFail($garmentOrderId);
        
        // Auto-calculate BOM costs
        $bomSummary = OrderBomItem::getBomSummary($garmentOrderId);
        
        return view('buyer-crm.garment-orders.costing.edit', compact(
            'order',
            'costing',
            'bomSummary'
        ));
    }

    /**
     * Update the specified costing.
     */
    public function update(OrderCostingRequest $request, int $garmentOrderId, OrderCosting $costing): RedirectResponse|JsonResponse
    {
        try {
            DB::beginTransaction();

            $costing->update($request->validated());

            // Update BOM costs and recalculate
            $costing->updateBomCosts();
            $costing->recalculateAll();

            DB::commit();

            if ($request->wantsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Costing updated successfully.',
                    'costing' => $costing->fresh(['updater']),
                ]);
            }

            return redirect()
                ->route('buyer-crm.garment-orders.costing.show', [$garmentOrderId, $costing])
                ->with('success', 'Costing updated successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            
            if ($request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to update costing: ' . $e->getMessage(),
                ], 422);
            }

            return back()
                ->withInput()
                ->with('error', 'Failed to update costing: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified costing.
     */
    public function destroy(int $garmentOrderId, OrderCosting $costing): RedirectResponse|JsonResponse
    {
        try {
            // Check if costing can be deleted
            if ($costing->status === 'approved') {
                if (request()->wantsJson()) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Cannot delete approved costing.',
                    ], 422);
                }
                return back()->with('error', 'Cannot delete approved costing.');
            }

            $costing->delete();

            if (request()->wantsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Costing deleted successfully.',
                ]);
            }

            return redirect()
                ->route('buyer-crm.garment-orders.costing.index', $garmentOrderId)
                ->with('success', 'Costing deleted successfully.');

        } catch (\Exception $e) {
            if (request()->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to delete costing: ' . $e->getMessage(),
                ], 422);
            }

            return back()->with('error', 'Failed to delete costing: ' . $e->getMessage());
        }
    }

    /**
     * Approve the costing.
     */
    public function approve(Request $request, int $garmentOrderId, OrderCosting $costing): JsonResponse
    {
        $request->validate([
            'comments' => 'nullable|string|max:2000',
        ]);

        try {
            DB::beginTransaction();

            $costing->approve(auth()->id(), $request->comments);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Costing approved successfully.',
                'costing' => $costing->fresh(['approver']),
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to approve costing: ' . $e->getMessage(),
            ], 422);
        }
    }

    /**
     * Reject the costing.
     */
    public function reject(Request $request, int $garmentOrderId, OrderCosting $costing): JsonResponse
    {
        $request->validate([
            'comments' => 'required|string|max:2000',
        ]);

        try {
            $costing->reject(auth()->id(), $request->comments);

            return response()->json([
                'success' => true,
                'message' => 'Costing rejected successfully.',
                'costing' => $costing->fresh(['approver']),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to reject costing: ' . $e->getMessage(),
            ], 422);
        }
    }

    /**
     * Create a new version of the costing.
     */
    public function createVersion(OrderCostingRequest $request, int $garmentOrderId, OrderCosting $costing): JsonResponse
    {
        try {
            DB::beginTransaction();

            $newVersion = $costing->createNewVersion($request->validated());
            $newVersion->updateBomCosts();
            $newVersion->recalculateAll();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'New costing version created successfully.',
                'costing' => $newVersion->load(['creator']),
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to create new version: ' . $e->getMessage(),
            ], 422);
        }
    }

    /**
     * Recalculate all costs.
     */
    public function recalculate(int $garmentOrderId, OrderCosting $costing): JsonResponse
    {
        try {
            $costing->updateBomCosts();
            $costing->recalculateAll();

            return response()->json([
                'success' => true,
                'message' => 'Costing recalculated successfully.',
                'costing' => $costing->fresh(),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to recalculate costing: ' . $e->getMessage(),
            ], 422);
        }
    }

    /**
     * Get costing comparison between versions.
     */
    public function compare(Request $request, int $garmentOrderId): JsonResponse
    {
        $request->validate([
            'version1' => 'required|integer|exists:order_costing,version',
            'version2' => 'required|integer|exists:order_costing,version',
        ]);

        $costing1 = OrderCosting::where('garment_order_id', $garmentOrderId)
            ->where('version', $request->version1)
            ->first();

        $costing2 = OrderCosting::where('garment_order_id', $garmentOrderId)
            ->where('version', $request->version2)
            ->first();

        if (!$costing1 || !$costing2) {
            return response()->json([
                'success' => false,
                'message' => 'One or both costing versions not found.',
            ], 404);
        }

        return response()->json([
            'success' => true,
            'comparison' => [
                'version1' => $costing1,
                'version2' => $costing2,
                'differences' => $this->calculateDifferences($costing1, $costing2),
            ],
        ]);
    }

    /**
     * Calculate differences between two costing versions.
     */
    private function calculateDifferences(OrderCosting $costing1, OrderCosting $costing2): array
    {
        $fields = [
            'fabric_final_cost',
            'total_material_cost',
            'total_manufacturing_cost',
            'total_additional_costs',
            'total_cost_per_piece',
            'calculated_unit_price',
            'final_negotiated_price',
            'actual_margin_percentage',
        ];

        $differences = [];
        foreach ($fields as $field) {
            $value1 = $costing1->$field ?? 0;
            $value2 = $costing2->$field ?? 0;
            $difference = $value2 - $value1;
            $percentageChange = $value1 != 0 ? (($difference / $value1) * 100) : 0;

            $differences[$field] = [
                'old_value' => $value1,
                'new_value' => $value2,
                'difference' => $difference,
                'percentage_change' => round($percentageChange, 2),
            ];
        }

        return $differences;
    }
}
