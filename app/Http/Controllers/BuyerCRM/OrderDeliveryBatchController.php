<?php

namespace App\Http\Controllers\BuyerCRM;

use App\Http\Controllers\Controller;
use App\Models\BuyerCRM\OrderDeliveryBatch;
use App\Models\BuyerCRM\GarmentOrder;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\DB;

class OrderDeliveryBatchController extends Controller
{
    /**
     * Display delivery batches for a garment order.
     */
    public function index(Request $request, int $garmentOrderId): View|JsonResponse
    {
        $order = GarmentOrder::findOrFail($garmentOrderId);
        
        $query = OrderDeliveryBatch::where('garment_order_id', $garmentOrderId)
            ->with(['creator', 'updater']);

        // Apply filters
        if ($request->filled('shipment_status')) {
            $query->where('shipment_status', $request->shipment_status);
        }

        if ($request->filled('delivery_date_from')) {
            $query->where('delivery_date', '>=', $request->delivery_date_from);
        }

        if ($request->filled('delivery_date_to')) {
            $query->where('delivery_date', '<=', $request->delivery_date_to);
        }

        $deliveryBatches = $query->orderBy('batch_sequence')->get();

        // Get delivery summary
        $summary = OrderDeliveryBatch::getDeliverySummary($garmentOrderId);

        if ($request->wantsJson()) {
            return response()->json([
                'delivery_batches' => $deliveryBatches,
                'summary' => $summary,
            ]);
        }

        return view('buyer-crm.garment-orders.delivery-batches.index', compact(
            'order',
            'deliveryBatches',
            'summary'
        ));
    }

    /**
     * Show the form for creating a new delivery batch.
     */
    public function create(int $garmentOrderId): View
    {
        $order = GarmentOrder::findOrFail($garmentOrderId);
        
        return view('buyer-crm.garment-orders.delivery-batches.create', compact('order'));
    }

    /**
     * Store a newly created delivery batch.
     */
    public function store(Request $request): RedirectResponse|JsonResponse
    {
        $request->validate([
            'garment_order_id' => 'required|exists:garment_orders,id',
            'batch_sequence' => 'required|integer|min:1',
            'quantity' => 'required|integer|min:1',
            'delivery_date' => 'required|date',
            'shipment_status' => 'required|in:pending,in_transit,delivered,cancelled',
            'notes' => 'nullable|string|max:1000',
        ]);

        try {
            DB::beginTransaction();

            $deliveryBatch = OrderDeliveryBatch::create([
                'garment_order_id' => $request->garment_order_id,
                'batch_sequence' => $request->batch_sequence,
                'quantity' => $request->quantity,
                'delivery_date' => $request->delivery_date,
                'shipment_status' => $request->shipment_status,
                'notes' => $request->notes,
                'created_by' => auth()->id(),
            ]);

            DB::commit();

            if ($request->wantsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Delivery batch created successfully.',
                    'delivery_batch' => $deliveryBatch,
                ]);
            }

            return redirect()
                ->route('garment-orders.delivery-batches.index', $request->garment_order_id)
                ->with('success', 'Delivery batch created successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            
            if ($request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to create delivery batch: ' . $e->getMessage(),
                ], 500);
            }

            return back()
                ->withInput()
                ->with('error', 'Failed to create delivery batch: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified delivery batch.
     */
    public function show(OrderDeliveryBatch $deliveryBatch): View
    {
        $deliveryBatch->load(['garmentOrder', 'creator', 'updater']);
        
        return view('buyer-crm.garment-orders.delivery-batches.show', compact('deliveryBatch'));
    }

    /**
     * Show the form for editing the specified delivery batch.
     */
    public function edit(OrderDeliveryBatch $deliveryBatch): View
    {
        $deliveryBatch->load(['garmentOrder']);
        
        return view('buyer-crm.garment-orders.delivery-batches.edit', compact('deliveryBatch'));
    }

    /**
     * Update the specified delivery batch.
     */
    public function update(Request $request, OrderDeliveryBatch $deliveryBatch): RedirectResponse|JsonResponse
    {
        $request->validate([
            'batch_sequence' => 'required|integer|min:1',
            'quantity' => 'required|integer|min:1',
            'delivery_date' => 'required|date',
            'shipment_status' => 'required|in:pending,in_transit,delivered,cancelled',
            'completion_percentage' => 'nullable|numeric|min:0|max:100',
            'notes' => 'nullable|string|max:1000',
        ]);

        try {
            DB::beginTransaction();

            $deliveryBatch->update([
                'batch_sequence' => $request->batch_sequence,
                'quantity' => $request->quantity,
                'delivery_date' => $request->delivery_date,
                'shipment_status' => $request->shipment_status,
                'completion_percentage' => $request->completion_percentage,
                'notes' => $request->notes,
                'updated_by' => auth()->id(),
            ]);

            DB::commit();

            if ($request->wantsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Delivery batch updated successfully.',
                    'delivery_batch' => $deliveryBatch->fresh(),
                ]);
            }

            return redirect()
                ->route('garment-orders.delivery-batches.show', $deliveryBatch)
                ->with('success', 'Delivery batch updated successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            
            if ($request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to update delivery batch: ' . $e->getMessage(),
                ], 500);
            }

            return back()
                ->withInput()
                ->with('error', 'Failed to update delivery batch: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified delivery batch.
     */
    public function destroy(OrderDeliveryBatch $deliveryBatch): RedirectResponse|JsonResponse
    {
        try {
            DB::beginTransaction();

            $garmentOrderId = $deliveryBatch->garment_order_id;
            $deliveryBatch->delete();

            DB::commit();

            if (request()->wantsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Delivery batch deleted successfully.',
                ]);
            }

            return redirect()
                ->route('garment-orders.delivery-batches.index', $garmentOrderId)
                ->with('success', 'Delivery batch deleted successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            
            if (request()->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to delete delivery batch: ' . $e->getMessage(),
                ], 500);
            }

            return back()
                ->with('error', 'Failed to delete delivery batch: ' . $e->getMessage());
        }
    }

    /**
     * Update shipment status.
     */
    public function updateStatus(Request $request, OrderDeliveryBatch $deliveryBatch): JsonResponse
    {
        $request->validate([
            'shipment_status' => 'required|in:pending,in_transit,delivered,cancelled',
            'completion_percentage' => 'nullable|numeric|min:0|max:100',
            'notes' => 'nullable|string|max:1000',
        ]);

        try {
            $deliveryBatch->update([
                'shipment_status' => $request->shipment_status,
                'completion_percentage' => $request->completion_percentage,
                'notes' => $request->notes,
                'updated_by' => auth()->id(),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Shipment status updated successfully.',
                'delivery_batch' => $deliveryBatch->fresh(),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update shipment status: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get delivery summary for an order.
     */
    public function summary(int $garmentOrderId): JsonResponse
    {
        $summary = OrderDeliveryBatch::getDeliverySummary($garmentOrderId);
        
        return response()->json([
            'success' => true,
            'summary' => $summary,
        ]);
    }

    /**
     * Export delivery batches.
     */
    public function export(int $garmentOrderId)
    {
        $order = GarmentOrder::findOrFail($garmentOrderId);
        $deliveryBatches = OrderDeliveryBatch::where('garment_order_id', $garmentOrderId)
            ->orderBy('batch_sequence')
            ->get();

        // Implementation for export (CSV, Excel, etc.)
        // This would typically use a package like Laravel Excel
        
        return response()->json([
            'success' => true,
            'message' => 'Export functionality to be implemented.',
        ]);
    }
}
