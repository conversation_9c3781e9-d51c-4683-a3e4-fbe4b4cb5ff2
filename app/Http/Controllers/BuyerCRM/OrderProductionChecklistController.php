<?php

namespace App\Http\Controllers\BuyerCRM;

use App\Http\Controllers\Controller;
use App\Models\BuyerCRM\OrderProductionChecklist;
use App\Models\BuyerCRM\GarmentOrder;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\DB;

class OrderProductionChecklistController extends Controller
{
    /**
     * Display production checklist for a garment order.
     */
    public function index(Request $request, int $garmentOrderId): View|JsonResponse
    {
        $order = GarmentOrder::findOrFail($garmentOrderId);
        
        $checklist = OrderProductionChecklist::where('garment_order_id', $garmentOrderId)
            ->with(['creator', 'updater'])
            ->first();

        if (!$checklist) {
            // Create default checklist if it doesn't exist
            $checklist = OrderProductionChecklist::create([
                'garment_order_id' => $garmentOrderId,
                'created_by' => auth()->id(),
            ]);
        }

        // Get checklist summary
        $summary = $this->getChecklistSummary($checklist);

        if ($request->wantsJson()) {
            return response()->json([
                'checklist' => $checklist,
                'summary' => $summary,
            ]);
        }

        return view('buyer-crm.garment-orders.production-checklist.index', compact(
            'order',
            'checklist',
            'summary'
        ));
    }

    /**
     * Update the production checklist.
     */
    public function update(Request $request, int $garmentOrderId): RedirectResponse|JsonResponse
    {
        $request->validate([
            'pre_production_meeting' => 'boolean',
            'pre_production_meeting_date' => 'nullable|date',
            'pre_production_meeting_notes' => 'nullable|string',
            
            'fabric_approval' => 'boolean',
            'fabric_approval_date' => 'nullable|date',
            'fabric_approval_notes' => 'nullable|string',
            
            'trim_approval' => 'boolean',
            'trim_approval_date' => 'nullable|date',
            'trim_approval_notes' => 'nullable|string',
            
            'fit_sample_approval' => 'boolean',
            'fit_sample_approval_date' => 'nullable|date',
            'fit_sample_approval_notes' => 'nullable|string',
            
            'pp_sample_approval' => 'boolean',
            'pp_sample_approval_date' => 'nullable|date',
            'pp_sample_approval_notes' => 'nullable|string',
            
            'top_sample_approval' => 'boolean',
            'top_sample_approval_date' => 'nullable|date',
            'top_sample_approval_notes' => 'nullable|string',
            
            'production_start' => 'boolean',
            'production_start_date' => 'nullable|date',
            'production_start_notes' => 'nullable|string',
            
            'inline_inspection' => 'boolean',
            'inline_inspection_date' => 'nullable|date',
            'inline_inspection_notes' => 'nullable|string',
            
            'final_inspection' => 'boolean',
            'final_inspection_date' => 'nullable|date',
            'final_inspection_notes' => 'nullable|string',
            
            'packing_approval' => 'boolean',
            'packing_approval_date' => 'nullable|date',
            'packing_approval_notes' => 'nullable|string',
            
            'shipment_ready' => 'boolean',
            'shipment_ready_date' => 'nullable|date',
            'shipment_ready_notes' => 'nullable|string',
            
            'overall_notes' => 'nullable|string',
        ]);

        try {
            DB::beginTransaction();

            $checklist = OrderProductionChecklist::where('garment_order_id', $garmentOrderId)->first();
            
            if (!$checklist) {
                $checklist = OrderProductionChecklist::create([
                    'garment_order_id' => $garmentOrderId,
                    'created_by' => auth()->id(),
                ]);
            }

            $checklist->update(array_merge($request->validated(), [
                'updated_by' => auth()->id(),
            ]));

            // Calculate completion percentage
            $completionPercentage = $this->calculateCompletionPercentage($checklist);
            $checklist->update(['completion_percentage' => $completionPercentage]);

            DB::commit();

            if ($request->wantsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Production checklist updated successfully.',
                    'checklist' => $checklist->fresh(),
                    'completion_percentage' => $completionPercentage,
                ]);
            }

            return redirect()
                ->route('garment-orders.production-checklist.index', $garmentOrderId)
                ->with('success', 'Production checklist updated successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            
            if ($request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to update production checklist: ' . $e->getMessage(),
                ], 500);
            }

            return back()
                ->withInput()
                ->with('error', 'Failed to update production checklist: ' . $e->getMessage());
        }
    }

    /**
     * Update a specific checklist item.
     */
    public function updateItem(Request $request, int $garmentOrderId): JsonResponse
    {
        $request->validate([
            'item' => 'required|string',
            'status' => 'required|boolean',
            'date' => 'nullable|date',
            'notes' => 'nullable|string',
        ]);

        try {
            $checklist = OrderProductionChecklist::where('garment_order_id', $garmentOrderId)->first();
            
            if (!$checklist) {
                $checklist = OrderProductionChecklist::create([
                    'garment_order_id' => $garmentOrderId,
                    'created_by' => auth()->id(),
                ]);
            }

            $updateData = [
                $request->item => $request->status,
                $request->item . '_date' => $request->date,
                $request->item . '_notes' => $request->notes,
                'updated_by' => auth()->id(),
            ];

            $checklist->update($updateData);

            // Calculate completion percentage
            $completionPercentage = $this->calculateCompletionPercentage($checklist);
            $checklist->update(['completion_percentage' => $completionPercentage]);

            return response()->json([
                'success' => true,
                'message' => 'Checklist item updated successfully.',
                'checklist' => $checklist->fresh(),
                'completion_percentage' => $completionPercentage,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update checklist item: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get checklist summary.
     */
    public function summary(int $garmentOrderId): JsonResponse
    {
        $checklist = OrderProductionChecklist::where('garment_order_id', $garmentOrderId)->first();
        
        if (!$checklist) {
            return response()->json([
                'success' => true,
                'summary' => [
                    'total_items' => 11,
                    'completed_items' => 0,
                    'completion_percentage' => 0,
                    'pending_items' => 11,
                ],
            ]);
        }

        $summary = $this->getChecklistSummary($checklist);
        
        return response()->json([
            'success' => true,
            'summary' => $summary,
        ]);
    }

    /**
     * Reset checklist.
     */
    public function reset(int $garmentOrderId): JsonResponse
    {
        try {
            DB::beginTransaction();

            $checklist = OrderProductionChecklist::where('garment_order_id', $garmentOrderId)->first();
            
            if ($checklist) {
                $checklist->update([
                    'pre_production_meeting' => false,
                    'pre_production_meeting_date' => null,
                    'pre_production_meeting_notes' => null,
                    'fabric_approval' => false,
                    'fabric_approval_date' => null,
                    'fabric_approval_notes' => null,
                    'trim_approval' => false,
                    'trim_approval_date' => null,
                    'trim_approval_notes' => null,
                    'fit_sample_approval' => false,
                    'fit_sample_approval_date' => null,
                    'fit_sample_approval_notes' => null,
                    'pp_sample_approval' => false,
                    'pp_sample_approval_date' => null,
                    'pp_sample_approval_notes' => null,
                    'top_sample_approval' => false,
                    'top_sample_approval_date' => null,
                    'top_sample_approval_notes' => null,
                    'production_start' => false,
                    'production_start_date' => null,
                    'production_start_notes' => null,
                    'inline_inspection' => false,
                    'inline_inspection_date' => null,
                    'inline_inspection_notes' => null,
                    'final_inspection' => false,
                    'final_inspection_date' => null,
                    'final_inspection_notes' => null,
                    'packing_approval' => false,
                    'packing_approval_date' => null,
                    'packing_approval_notes' => null,
                    'shipment_ready' => false,
                    'shipment_ready_date' => null,
                    'shipment_ready_notes' => null,
                    'completion_percentage' => 0,
                    'overall_notes' => null,
                    'updated_by' => auth()->id(),
                ]);
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Production checklist reset successfully.',
                'checklist' => $checklist ? $checklist->fresh() : null,
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to reset checklist: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Calculate completion percentage.
     */
    private function calculateCompletionPercentage(OrderProductionChecklist $checklist): float
    {
        $checklistItems = [
            'pre_production_meeting',
            'fabric_approval',
            'trim_approval',
            'fit_sample_approval',
            'pp_sample_approval',
            'top_sample_approval',
            'production_start',
            'inline_inspection',
            'final_inspection',
            'packing_approval',
            'shipment_ready',
        ];

        $completedItems = 0;
        $totalItems = count($checklistItems);

        foreach ($checklistItems as $item) {
            if ($checklist->$item) {
                $completedItems++;
            }
        }

        return $totalItems > 0 ? round(($completedItems / $totalItems) * 100, 2) : 0;
    }

    /**
     * Get checklist summary.
     */
    private function getChecklistSummary(OrderProductionChecklist $checklist): array
    {
        $checklistItems = [
            'pre_production_meeting',
            'fabric_approval',
            'trim_approval',
            'fit_sample_approval',
            'pp_sample_approval',
            'top_sample_approval',
            'production_start',
            'inline_inspection',
            'final_inspection',
            'packing_approval',
            'shipment_ready',
        ];

        $completedItems = 0;
        $totalItems = count($checklistItems);

        foreach ($checklistItems as $item) {
            if ($checklist->$item) {
                $completedItems++;
            }
        }

        $completionPercentage = $totalItems > 0 ? round(($completedItems / $totalItems) * 100, 2) : 0;

        return [
            'total_items' => $totalItems,
            'completed_items' => $completedItems,
            'pending_items' => $totalItems - $completedItems,
            'completion_percentage' => $completionPercentage,
        ];
    }
}
