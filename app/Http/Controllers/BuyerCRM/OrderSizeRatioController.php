<?php

namespace App\Http\Controllers\BuyerCRM;

use App\Http\Controllers\Controller;
use App\Models\BuyerCRM\OrderSizeRatio;
use App\Models\BuyerCRM\GarmentOrder;
use App\Http\Requests\BuyerCRM\OrderSizeRatioRequest;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\DB;

class OrderSizeRatioController extends Controller
{
    /**
     * Display a listing of size ratios for all garment orders.
     */
    public function index(Request $request): View|JsonResponse
    {
        // Get all garment orders for the dropdown
        $garmentOrders = GarmentOrder::with(['buyer'])
            ->select('id', 'order_no', 'style_no', 'buyer_id')
            ->orderBy('created_at', 'desc')
            ->get();

        // Get selected order ID from request or default to first order
        $selectedOrderId = $request->get('order_id');
        $selectedOrder = null;
        $sizeRatios = collect();
        $summary = null;

        if ($selectedOrderId) {
            $selectedOrder = GarmentOrder::find($selectedOrderId);
            if ($selectedOrder) {
                $sizeRatios = OrderSizeRatio::where('garment_order_id', $selectedOrderId)
                    ->orderBy('sort_order')
                    ->get();
                $summary = OrderSizeRatio::getSizeBreakdownSummary($selectedOrderId);
            }
        } elseif ($garmentOrders->isNotEmpty()) {
            // Default to first order if no order selected
            $selectedOrder = $garmentOrders->first();
            $selectedOrderId = $selectedOrder->id;
            $sizeRatios = OrderSizeRatio::where('garment_order_id', $selectedOrderId)
                ->orderBy('sort_order')
                ->get();
            $summary = OrderSizeRatio::getSizeBreakdownSummary($selectedOrderId);
        }

        if ($request->wantsJson()) {
            return response()->json([
                'size_ratios' => $sizeRatios,
                'summary' => $summary,
                'selected_order' => $selectedOrder,
            ]);
        }

        return view('buyer-crm.garment-orders.size-ratios.index', compact(
            'garmentOrders',
            'selectedOrder',
            'selectedOrderId',
            'sizeRatios',
            'summary'
        ));
    }

    /**
     * Show the form for creating new size ratios.
     */
    public function create(Request $request): View
    {
        // Get all garment orders for the dropdown
        $garmentOrders = GarmentOrder::with(['buyer'])
            ->select('id', 'order_no', 'style_no', 'buyer_id')
            ->orderBy('created_at', 'desc')
            ->get();

        // Get selected order from request
        $selectedOrderId = $request->get('order_id');
        $selectedOrder = null;

        if ($selectedOrderId) {
            $selectedOrder = GarmentOrder::find($selectedOrderId);
        }

        return view('buyer-crm.garment-orders.size-ratios.create', compact(
            'garmentOrders',
            'selectedOrder',
            'selectedOrderId'
        ));
    }

    /**
     * Store newly created size ratios.
     */
    public function store(OrderSizeRatioRequest $request): RedirectResponse|JsonResponse
    {
        try {
            DB::beginTransaction();

            if ($request->has('sizes')) {
                // Bulk creation
                foreach ($request->sizes as $sizeData) {
                    OrderSizeRatio::create(array_merge($sizeData, [
                        'garment_order_id' => $request->garment_order_id,
                    ]));
                }
            } else {
                // Single creation
                OrderSizeRatio::create($request->validated());
            }

            // Recalculate quantities for the order
            OrderSizeRatio::recalculateForOrder($request->garment_order_id);

            DB::commit();

            if ($request->wantsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Size ratios created successfully.',
                ]);
            }

            return redirect()
                ->route('buyer-crm.garment-orders.size-ratios.index', $request->garment_order_id)
                ->with('success', 'Size ratios created successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            
            if ($request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to create size ratios: ' . $e->getMessage(),
                ], 422);
            }

            return back()
                ->withInput()
                ->with('error', 'Failed to create size ratios: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified size ratio.
     */
    public function show(OrderSizeRatio $sizeRatio): JsonResponse
    {
        return response()->json($sizeRatio);
    }

    /**
     * Show the form for editing the specified size ratio.
     */
    public function edit(OrderSizeRatio $sizeRatio): View
    {
        $garmentOrder = $sizeRatio->garmentOrder;

        return view('buyer-crm.garment-orders.size-ratios.edit', compact(
            'sizeRatio',
            'garmentOrder'
        ));
    }

    /**
     * Update the specified size ratio.
     */
    public function update(OrderSizeRatioRequest $request, OrderSizeRatio $sizeRatio): RedirectResponse|JsonResponse
    {
        try {
            DB::beginTransaction();

            $sizeRatio->update($request->validated());

            // Recalculate quantities for the order
            OrderSizeRatio::recalculateForOrder($sizeRatio->garment_order_id);

            DB::commit();

            if ($request->wantsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Size ratio updated successfully.',
                    'size_ratio' => $sizeRatio->fresh(),
                ]);
            }

            return redirect()
                ->route('buyer-crm.garment-orders.size-ratios.index', $sizeRatio->garment_order_id)
                ->with('success', 'Size ratio updated successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            
            if ($request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to update size ratio: ' . $e->getMessage(),
                ], 422);
            }

            return back()
                ->withInput()
                ->with('error', 'Failed to update size ratio: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified size ratio.
     */
    public function destroy(OrderSizeRatio $sizeRatio): RedirectResponse|JsonResponse
    {
        try {
            DB::beginTransaction();

            $garmentOrderId = $sizeRatio->garment_order_id;
            $sizeRatio->delete();

            // Recalculate quantities for the order
            OrderSizeRatio::recalculateForOrder($garmentOrderId);

            DB::commit();

            if (request()->wantsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Size ratio deleted successfully.',
                ]);
            }

            return redirect()
                ->route('buyer-crm.garment-orders.size-ratios.index', $garmentOrderId)
                ->with('success', 'Size ratio deleted successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            
            if (request()->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to delete size ratio: ' . $e->getMessage(),
                ], 422);
            }

            return back()->with('error', 'Failed to delete size ratio: ' . $e->getMessage());
        }
    }

    /**
     * Recalculate quantities for all size ratios of an order.
     */
    public function recalculate(Request $request): JsonResponse
    {
        $request->validate([
            'garment_order_id' => 'required|exists:garment_orders,id',
        ]);

        try {
            $garmentOrderId = $request->garment_order_id;
            OrderSizeRatio::recalculateForOrder($garmentOrderId);

            $summary = OrderSizeRatio::getSizeBreakdownSummary($garmentOrderId);

            return response()->json([
                'success' => true,
                'message' => 'Quantities recalculated successfully.',
                'summary' => $summary,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to recalculate quantities: ' . $e->getMessage(),
            ], 422);
        }
    }

    /**
     * Set manual override for a size ratio.
     */
    public function setManualOverride(Request $request, OrderSizeRatio $sizeRatio): JsonResponse
    {
        $request->validate([
            'manual_quantity' => 'required|integer|min:0|max:999999',
        ]);

        try {
            $sizeRatio->setManualQuantity($request->manual_quantity);

            return response()->json([
                'success' => true,
                'message' => 'Manual override set successfully.',
                'size_ratio' => $sizeRatio->fresh(),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to set manual override: ' . $e->getMessage(),
            ], 422);
        }
    }

    /**
     * Remove manual override for a size ratio.
     */
    public function removeManualOverride(OrderSizeRatio $sizeRatio): JsonResponse
    {
        try {
            $sizeRatio->removeManualOverride();

            return response()->json([
                'success' => true,
                'message' => 'Manual override removed successfully.',
                'size_ratio' => $sizeRatio->fresh(),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to remove manual override: ' . $e->getMessage(),
            ], 422);
        }
    }

    /**
     * Update ratio for a size ratio.
     */
    public function updateRatio(Request $request): JsonResponse
    {
        $request->validate([
            'size_id' => 'required|exists:order_size_ratios,id',
            'ratio' => 'required|numeric|min:0',
            'garment_order_id' => 'required|exists:garment_orders,id',
        ]);

        try {
            DB::beginTransaction();

            $sizeRatio = OrderSizeRatio::findOrFail($request->size_id);
            $sizeRatio->update(['ratio' => $request->ratio]);

            // Recalculate quantities for the order
            OrderSizeRatio::recalculateForOrder($request->garment_order_id);

            $sizeRatio->refresh();

            DB::commit();

            return response()->json([
                'success' => true,
                'calculated_quantity' => $sizeRatio->calculated_quantity,
                'final_quantity' => $sizeRatio->final_quantity,
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Failed to update ratio: ' . $e->getMessage(),
            ], 422);
        }
    }

    /**
     * Update manual quantity for a size ratio.
     */
    public function updateManualQuantity(Request $request): JsonResponse
    {
        $request->validate([
            'size_id' => 'required|exists:order_size_ratios,id',
            'manual_quantity' => 'nullable|integer|min:0',
            'garment_order_id' => 'required|exists:garment_orders,id',
        ]);

        try {
            DB::beginTransaction();

            $sizeRatio = OrderSizeRatio::findOrFail($request->size_id);
            $sizeRatio->update([
                'manual_quantity' => $request->manual_quantity,
                'is_manual_override' => !empty($request->manual_quantity),
            ]);

            // Recalculate quantities for the order
            OrderSizeRatio::recalculateForOrder($request->garment_order_id);

            $sizeRatio->refresh();

            DB::commit();

            return response()->json([
                'success' => true,
                'final_quantity' => $sizeRatio->final_quantity,
                'is_manual_override' => $sizeRatio->is_manual_override,
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Failed to update manual quantity: ' . $e->getMessage(),
            ], 422);
        }
    }

    /**
     * Bulk create standard size ratios.
     */
    public function bulkCreate(Request $request): JsonResponse
    {
        $request->validate([
            'sizes' => 'required|array',
            'sizes.*.label' => 'required|string',
            'sizes.*.type' => 'required|in:text,numeric',
            'sizes.*.ratio' => 'required|numeric|min:0',
            'garment_order_id' => 'required|exists:garment_orders,id',
        ]);

        try {
            DB::beginTransaction();

            $garmentOrderId = $request->garment_order_id;
            $createdCount = 0;

            foreach ($request->sizes as $sizeData) {
                // Check if size already exists
                $exists = OrderSizeRatio::where('garment_order_id', $garmentOrderId)
                    ->where('size_label', $sizeData['label'])
                    ->exists();

                if (!$exists) {
                    OrderSizeRatio::create([
                        'garment_order_id' => $garmentOrderId,
                        'size_label' => $sizeData['label'],
                        'size_type' => $sizeData['type'],
                        'ratio' => $sizeData['ratio'],
                        'sort_order' => OrderSizeRatio::where('garment_order_id', $garmentOrderId)->count() + 1,
                    ]);
                    $createdCount++;
                }
            }

            // Recalculate quantities for the order
            OrderSizeRatio::recalculateForOrder($garmentOrderId);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => "Successfully created {$createdCount} size ratios.",
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Failed to create size ratios: ' . $e->getMessage(),
            ], 422);
        }
    }

    /**
     * Clear all size ratios for an order.
     */
    public function clearAll(Request $request): JsonResponse
    {
        $request->validate([
            'garment_order_id' => 'required|exists:garment_orders,id',
        ]);

        try {
            DB::beginTransaction();

            OrderSizeRatio::where('garment_order_id', $request->garment_order_id)->delete();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'All size ratios cleared successfully.',
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Failed to clear size ratios: ' . $e->getMessage(),
            ], 422);
        }
    }
}
