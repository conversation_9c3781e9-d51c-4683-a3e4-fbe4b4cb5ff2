<?php

namespace App\Http\Controllers\BuyerCRM;

use App\Http\Controllers\Controller;
use App\Models\BuyerCRM\OrderSopCompliance;
use App\Models\BuyerCRM\GarmentOrder;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\DB;

class OrderSopComplianceController extends Controller
{
    /**
     * Display SOP compliance for a garment order.
     */
    public function index(Request $request, int $garmentOrderId): View|JsonResponse
    {
        $order = GarmentOrder::findOrFail($garmentOrderId);
        
        $query = OrderSopCompliance::where('garment_order_id', $garmentOrderId)
            ->with(['creator', 'updater']);

        // Apply filters
        if ($request->filled('compliance_category')) {
            $query->where('compliance_category', $request->compliance_category);
        }

        if ($request->filled('compliance_status')) {
            $query->where('compliance_status', $request->compliance_status);
        }

        if ($request->filled('is_critical')) {
            $query->where('is_critical', $request->boolean('is_critical'));
        }

        $sopCompliances = $query->orderBy('compliance_category')->orderBy('requirement_name')->get();

        // Get compliance summary
        $summary = OrderSopCompliance::getComplianceSummary($garmentOrderId);

        if ($request->wantsJson()) {
            return response()->json([
                'sop_compliances' => $sopCompliances,
                'summary' => $summary,
            ]);
        }

        return view('buyer-crm.garment-orders.sop-compliance.index', compact(
            'order',
            'sopCompliances',
            'summary'
        ));
    }

    /**
     * Show the form for creating a new SOP compliance item.
     */
    public function create(int $garmentOrderId): View
    {
        $order = GarmentOrder::findOrFail($garmentOrderId);
        
        return view('buyer-crm.garment-orders.sop-compliance.create', compact('order'));
    }

    /**
     * Store a newly created SOP compliance item.
     */
    public function store(Request $request): RedirectResponse|JsonResponse
    {
        $request->validate([
            'garment_order_id' => 'required|exists:garment_orders,id',
            'compliance_category' => 'required|in:quality,safety,environmental,social,documentation,testing',
            'requirement_name' => 'required|string|max:255',
            'requirement_description' => 'nullable|string',
            'compliance_status' => 'required|in:not_started,in_progress,completed,failed,waived',
            'is_critical' => 'boolean',
            'target_completion_date' => 'nullable|date',
            'notes' => 'nullable|string',
        ]);

        try {
            DB::beginTransaction();

            $sopCompliance = OrderSopCompliance::create([
                'garment_order_id' => $request->garment_order_id,
                'compliance_category' => $request->compliance_category,
                'requirement_name' => $request->requirement_name,
                'requirement_description' => $request->requirement_description,
                'compliance_status' => $request->compliance_status,
                'is_critical' => $request->boolean('is_critical'),
                'target_completion_date' => $request->target_completion_date,
                'notes' => $request->notes,
                'created_by' => auth()->id(),
            ]);

            DB::commit();

            if ($request->wantsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'SOP compliance item created successfully.',
                    'sop_compliance' => $sopCompliance,
                ]);
            }

            return redirect()
                ->route('garment-orders.sop-compliance.index', $request->garment_order_id)
                ->with('success', 'SOP compliance item created successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            
            if ($request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to create SOP compliance item: ' . $e->getMessage(),
                ], 500);
            }

            return back()
                ->withInput()
                ->with('error', 'Failed to create SOP compliance item: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified SOP compliance item.
     */
    public function show(OrderSopCompliance $sopCompliance): View
    {
        $sopCompliance->load(['garmentOrder', 'creator', 'updater']);
        
        return view('buyer-crm.garment-orders.sop-compliance.show', compact('sopCompliance'));
    }

    /**
     * Show the form for editing the specified SOP compliance item.
     */
    public function edit(OrderSopCompliance $sopCompliance): View
    {
        $sopCompliance->load(['garmentOrder']);
        
        return view('buyer-crm.garment-orders.sop-compliance.edit', compact('sopCompliance'));
    }

    /**
     * Update the specified SOP compliance item.
     */
    public function update(Request $request, OrderSopCompliance $sopCompliance): RedirectResponse|JsonResponse
    {
        $request->validate([
            'compliance_category' => 'required|in:quality,safety,environmental,social,documentation,testing',
            'requirement_name' => 'required|string|max:255',
            'requirement_description' => 'nullable|string',
            'compliance_status' => 'required|in:not_started,in_progress,completed,failed,waived',
            'is_critical' => 'boolean',
            'target_completion_date' => 'nullable|date',
            'actual_completion_date' => 'nullable|date',
            'compliance_percentage' => 'nullable|numeric|min:0|max:100',
            'notes' => 'nullable|string',
        ]);

        try {
            DB::beginTransaction();

            $sopCompliance->update([
                'compliance_category' => $request->compliance_category,
                'requirement_name' => $request->requirement_name,
                'requirement_description' => $request->requirement_description,
                'compliance_status' => $request->compliance_status,
                'is_critical' => $request->boolean('is_critical'),
                'target_completion_date' => $request->target_completion_date,
                'actual_completion_date' => $request->actual_completion_date,
                'compliance_percentage' => $request->compliance_percentage,
                'notes' => $request->notes,
                'updated_by' => auth()->id(),
            ]);

            DB::commit();

            if ($request->wantsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'SOP compliance item updated successfully.',
                    'sop_compliance' => $sopCompliance->fresh(),
                ]);
            }

            return redirect()
                ->route('garment-orders.sop-compliance.show', $sopCompliance)
                ->with('success', 'SOP compliance item updated successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            
            if ($request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to update SOP compliance item: ' . $e->getMessage(),
                ], 500);
            }

            return back()
                ->withInput()
                ->with('error', 'Failed to update SOP compliance item: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified SOP compliance item.
     */
    public function destroy(OrderSopCompliance $sopCompliance): RedirectResponse|JsonResponse
    {
        try {
            DB::beginTransaction();

            $garmentOrderId = $sopCompliance->garment_order_id;
            $sopCompliance->delete();

            DB::commit();

            if (request()->wantsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'SOP compliance item deleted successfully.',
                ]);
            }

            return redirect()
                ->route('garment-orders.sop-compliance.index', $garmentOrderId)
                ->with('success', 'SOP compliance item deleted successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            
            if (request()->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to delete SOP compliance item: ' . $e->getMessage(),
                ], 500);
            }

            return back()
                ->with('error', 'Failed to delete SOP compliance item: ' . $e->getMessage());
        }
    }

    /**
     * Update compliance status.
     */
    public function updateStatus(Request $request, OrderSopCompliance $sopCompliance): JsonResponse
    {
        $request->validate([
            'compliance_status' => 'required|in:not_started,in_progress,completed,failed,waived',
            'compliance_percentage' => 'nullable|numeric|min:0|max:100',
            'actual_completion_date' => 'nullable|date',
            'notes' => 'nullable|string',
        ]);

        try {
            $sopCompliance->update([
                'compliance_status' => $request->compliance_status,
                'compliance_percentage' => $request->compliance_percentage,
                'actual_completion_date' => $request->actual_completion_date,
                'notes' => $request->notes,
                'updated_by' => auth()->id(),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Compliance status updated successfully.',
                'sop_compliance' => $sopCompliance->fresh(),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update compliance status: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Add standard SOP requirements.
     */
    public function addStandardRequirements(Request $request, int $garmentOrderId): JsonResponse
    {
        $request->validate([
            'category' => 'required|in:quality,safety,environmental,social,documentation,testing',
        ]);

        try {
            DB::beginTransaction();

            $standardRequirements = $this->getStandardRequirements($request->category);
            
            foreach ($standardRequirements as $requirement) {
                OrderSopCompliance::create([
                    'garment_order_id' => $garmentOrderId,
                    'compliance_category' => $request->category,
                    'requirement_name' => $requirement['name'],
                    'requirement_description' => $requirement['description'],
                    'compliance_status' => 'not_started',
                    'is_critical' => $requirement['is_critical'],
                    'created_by' => auth()->id(),
                ]);
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Standard SOP requirements added successfully.',
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to add standard requirements: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get compliance summary for an order.
     */
    public function summary(int $garmentOrderId): JsonResponse
    {
        $summary = OrderSopCompliance::getComplianceSummary($garmentOrderId);
        
        return response()->json([
            'success' => true,
            'summary' => $summary,
        ]);
    }

    /**
     * Get standard requirements by category.
     */
    private function getStandardRequirements(string $category): array
    {
        $requirements = [
            'quality' => [
                ['name' => 'Fabric Quality Check', 'description' => 'Inspect fabric quality and specifications', 'is_critical' => true],
                ['name' => 'Stitching Quality', 'description' => 'Check stitching quality and consistency', 'is_critical' => true],
                ['name' => 'Finishing Quality', 'description' => 'Verify finishing and pressing quality', 'is_critical' => false],
            ],
            'safety' => [
                ['name' => 'Chemical Safety', 'description' => 'Ensure chemical safety compliance', 'is_critical' => true],
                ['name' => 'Worker Safety', 'description' => 'Verify worker safety protocols', 'is_critical' => true],
            ],
            'environmental' => [
                ['name' => 'Waste Management', 'description' => 'Proper waste disposal and management', 'is_critical' => false],
                ['name' => 'Water Usage', 'description' => 'Monitor and control water usage', 'is_critical' => false],
            ],
            'social' => [
                ['name' => 'Fair Labor', 'description' => 'Ensure fair labor practices', 'is_critical' => true],
                ['name' => 'Working Hours', 'description' => 'Monitor working hours compliance', 'is_critical' => true],
            ],
            'documentation' => [
                ['name' => 'Certificates', 'description' => 'Collect required certificates', 'is_critical' => true],
                ['name' => 'Test Reports', 'description' => 'Obtain necessary test reports', 'is_critical' => true],
            ],
            'testing' => [
                ['name' => 'Fabric Testing', 'description' => 'Conduct fabric testing', 'is_critical' => true],
                ['name' => 'Color Fastness', 'description' => 'Test color fastness', 'is_critical' => true],
            ],
        ];

        return $requirements[$category] ?? [];
    }
}
