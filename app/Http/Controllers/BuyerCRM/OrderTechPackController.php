<?php

namespace App\Http\Controllers\BuyerCRM;

use App\Http\Controllers\Controller;
use App\Models\BuyerCRM\OrderTechPack;
use App\Models\BuyerCRM\GarmentOrder;
use App\Http\Requests\BuyerCRM\OrderTechPackRequest;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpFoundation\StreamedResponse;

class OrderTechPackController extends Controller
{
    /**
     * Display tech packs for a garment order.
     */
    public function index(Request $request, int $garmentOrderId): View|JsonResponse
    {
        $order = GarmentOrder::findOrFail($garmentOrderId);
        
        $query = OrderTechPack::where('garment_order_id', $garmentOrderId)
            ->with(['uploader', 'approver'])
            ->where('is_active', true);

        // Apply filters
        if ($request->filled('file_category')) {
            $query->where('file_category', $request->file_category);
        }

        if ($request->filled('sample_type')) {
            $query->where('sample_type', $request->sample_type);
        }

        if ($request->filled('sample_status')) {
            $query->where('sample_status', $request->sample_status);
        }

        if ($request->filled('is_current_version')) {
            $query->where('is_current_version', $request->boolean('is_current_version'));
        }

        $techPacks = $query->orderBy('created_at', 'desc')->get();

        // Get tech pack status summary
        $status = OrderTechPack::getTechPackStatus($garmentOrderId);

        if ($request->wantsJson()) {
            return response()->json([
                'tech_packs' => $techPacks,
                'status' => $status,
            ]);
        }

        return view('buyer-crm.garment-orders.tech-packs.index', compact(
            'order',
            'techPacks',
            'status'
        ));
    }

    /**
     * Show the form for creating a new tech pack.
     */
    public function create(int $garmentOrderId): View
    {
        $order = GarmentOrder::findOrFail($garmentOrderId);
        
        return view('buyer-crm.garment-orders.tech-packs.create', compact('order'));
    }

    /**
     * Store a newly created tech pack.
     */
    public function store(OrderTechPackRequest $request): RedirectResponse|JsonResponse
    {
        try {
            DB::beginTransaction();

            $validated = $request->validated();

            if ($request->hasFile('file')) {
                $file = $request->file('file');
                
                // Generate file path
                $path = 'garment-orders/' . $request->garment_order_id . '/tech-packs';
                $fileName = time() . '_' . $file->getClientOriginalName();
                $filePath = $file->storeAs($path, $fileName, 'public');
                
                $validated['file_path'] = $filePath;
            }

            $techPack = OrderTechPack::create($validated);

            DB::commit();

            if ($request->wantsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Tech pack uploaded successfully.',
                    'tech_pack' => $techPack->load(['uploader']),
                ]);
            }

            return redirect()
                ->route('buyer-crm.garment-orders.tech-packs.index', $request->garment_order_id)
                ->with('success', 'Tech pack uploaded successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            
            if ($request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to upload tech pack: ' . $e->getMessage(),
                ], 422);
            }

            return back()
                ->withInput()
                ->with('error', 'Failed to upload tech pack: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified tech pack.
     */
    public function show(OrderTechPack $techPack): View
    {
        $techPack->load(['garmentOrder', 'uploader', 'approver']);
        
        return view('buyer-crm.garment-orders.tech-packs.show', compact('techPack'));
    }

    /**
     * Show the form for editing the specified tech pack.
     */
    public function edit(OrderTechPack $techPack): View
    {
        $order = $techPack->garmentOrder;
        
        return view('buyer-crm.garment-orders.tech-packs.edit', compact(
            'techPack',
            'order'
        ));
    }

    /**
     * Update the specified tech pack.
     */
    public function update(OrderTechPackRequest $request, OrderTechPack $techPack): RedirectResponse|JsonResponse
    {
        try {
            DB::beginTransaction();

            $validated = $request->validated();

            // Handle file replacement
            if ($request->hasFile('file')) {
                // Delete old file
                if ($techPack->file_path && Storage::disk('public')->exists($techPack->file_path)) {
                    Storage::disk('public')->delete($techPack->file_path);
                }

                $file = $request->file('file');
                $path = 'garment-orders/' . $techPack->garment_order_id . '/tech-packs';
                $fileName = time() . '_' . $file->getClientOriginalName();
                $filePath = $file->storeAs($path, $fileName, 'public');
                
                $validated['file_path'] = $filePath;
            }

            $techPack->update($validated);

            DB::commit();

            if ($request->wantsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Tech pack updated successfully.',
                    'tech_pack' => $techPack->fresh(['uploader', 'approver']),
                ]);
            }

            return redirect()
                ->route('buyer-crm.garment-orders.tech-packs.show', $techPack)
                ->with('success', 'Tech pack updated successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            
            if ($request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to update tech pack: ' . $e->getMessage(),
                ], 422);
            }

            return back()
                ->withInput()
                ->with('error', 'Failed to update tech pack: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified tech pack.
     */
    public function destroy(OrderTechPack $techPack): RedirectResponse|JsonResponse
    {
        try {
            $garmentOrderId = $techPack->garment_order_id;
            
            // Delete file from storage
            if ($techPack->file_path && Storage::disk('public')->exists($techPack->file_path)) {
                Storage::disk('public')->delete($techPack->file_path);
            }

            $techPack->delete();

            if (request()->wantsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Tech pack deleted successfully.',
                ]);
            }

            return redirect()
                ->route('buyer-crm.garment-orders.tech-packs.index', $garmentOrderId)
                ->with('success', 'Tech pack deleted successfully.');

        } catch (\Exception $e) {
            if (request()->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to delete tech pack: ' . $e->getMessage(),
                ], 422);
            }

            return back()->with('error', 'Failed to delete tech pack: ' . $e->getMessage());
        }
    }

    /**
     * Download the tech pack file.
     */
    public function download(OrderTechPack $techPack): StreamedResponse|RedirectResponse
    {
        try {
            if (!$techPack->file_path || !Storage::disk('public')->exists($techPack->file_path)) {
                return back()->with('error', 'File not found.');
            }

            return Storage::disk('public')->download($techPack->file_path, $techPack->file_name);

        } catch (\Exception $e) {
            return back()->with('error', 'Failed to download file: ' . $e->getMessage());
        }
    }

    /**
     * Approve a sample.
     */
    public function approveSample(Request $request, OrderTechPack $techPack): JsonResponse
    {
        $request->validate([
            'comments' => 'nullable|string|max:1000',
        ]);

        try {
            $techPack->approveSample(auth()->id(), $request->comments);

            return response()->json([
                'success' => true,
                'message' => 'Sample approved successfully.',
                'tech_pack' => $techPack->fresh(['approver']),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to approve sample: ' . $e->getMessage(),
            ], 422);
        }
    }

    /**
     * Reject a sample.
     */
    public function rejectSample(Request $request, OrderTechPack $techPack): JsonResponse
    {
        $request->validate([
            'comments' => 'required|string|max:1000',
        ]);

        try {
            $techPack->rejectSample(auth()->id(), $request->comments);

            return response()->json([
                'success' => true,
                'message' => 'Sample rejected successfully.',
                'tech_pack' => $techPack->fresh(['approver']),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to reject sample: ' . $e->getMessage(),
            ], 422);
        }
    }

    /**
     * Create a new version of the tech pack.
     */
    public function createVersion(OrderTechPackRequest $request, OrderTechPack $techPack): JsonResponse
    {
        try {
            DB::beginTransaction();

            $validated = $request->validated();

            if ($request->hasFile('file')) {
                $file = $request->file('file');
                $path = 'garment-orders/' . $techPack->garment_order_id . '/tech-packs';
                $fileName = time() . '_' . $file->getClientOriginalName();
                $filePath = $file->storeAs($path, $fileName, 'public');
                
                $validated['file_path'] = $filePath;
            }

            $newVersion = $techPack->createNewVersion($validated);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'New version created successfully.',
                'tech_pack' => $newVersion->load(['uploader']),
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to create new version: ' . $e->getMessage(),
            ], 422);
        }
    }

    /**
     * Get tech pack status for an order.
     */
    public function status(int $garmentOrderId): JsonResponse
    {
        $status = OrderTechPack::getTechPackStatus($garmentOrderId);
        
        return response()->json($status);
    }
}
