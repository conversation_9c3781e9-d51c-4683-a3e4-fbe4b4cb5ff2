<?php

namespace App\Http\Controllers;

use App\Models\Party;
use App\Models\BuyerUse;
use Illuminate\Http\Request;
use App\Services\BuyerStockService;
use Illuminate\Support\Facades\Auth;

class BuyerStockController extends Controller
{
    protected $stockService;

    public function __construct(BuyerStockService $stockService)
    {
        $this->stockService = $stockService;
        $this->middleware('auth');
    }

    /**
     * Display buyer's stock dashboard.
     */
    public function index(Request $request)
    {
        $user = Auth::user();

        // Check if user is a buyer
        $party = Party::where('user_id', $user->id)->where('type', 'buyer')->first();

        if (!$party) {
            abort(403, 'Access denied. You are not authorized to view this page.');
        }

        $stockData = $this->stockService->calculateBuyerStock($party->id);

        // Apply filters
        if ($request->filled('item')) {
            $stockData = $stockData->filter(function ($item) use ($request) {
                return stripos($item['item'], $request->item) !== false;
            });
        }

        if ($request->filled('color')) {
            $stockData = $stockData->filter(function ($item) use ($request) {
                return stripos($item['color'], $request->color) !== false;
            });
        }

        if ($request->filled('size')) {
            $stockData = $stockData->filter(function ($item) use ($request) {
                return stripos($item['size'], $request->size) !== false;
            });
        }

        // Get all available options for dropdowns
        $allStockData = $this->stockService->calculateBuyerStock($party->id);
        $availableItems = $allStockData->pluck('item')->unique()->filter()->sort()->values();
        $availableColors = $allStockData->pluck('color')->unique()->filter()->sort()->values();
        $availableSizes = $allStockData->pluck('size')->unique()->filter()->sort()->values();

        $summary = $this->stockService->getBuyerStockSummary($party->id);
        $recentUsage = $this->stockService->getRecentUsageHistory($party->id, 5);

        return view('buyer.stock.index', compact(
            'stockData',
            'summary',
            'recentUsage',
            'party',
            'availableItems',
            'availableColors',
            'availableSizes'
        ));
    }

    /**
     * Show form to record new usage.
     */
    public function createUsage()
    {
        $user = Auth::user();
        $party = Party::where('user_id', $user->id)->where('type', 'buyer')->first();

        if (!$party) {
            abort(403, 'Access denied.');
        }

        // Get all stock data for initial style dropdown
        $stockData = $this->stockService->calculateBuyerStock($party->id);
        $availableStyles = $stockData->pluck('style')->unique()->sort()->values();

        return view('buyer.stock.create-usage', compact('availableStyles', 'party'));
    }

    /**
     * Store new usage record.
     */
    public function storeUsage(Request $request)
    {
        $user = Auth::user();
        $party = Party::where('user_id', $user->id)->where('type', 'buyer')->first();
        
        if (!$party) {
            abort(403, 'Access denied.');
        }

        $request->validate([
            'style' => 'required|string|max:255',
            'color' => 'nullable|string|max:255',
            'item' => 'nullable|string|max:255',
            'size' => 'nullable|string|max:255',
            'qty_used' => 'required|integer|min:1',
            'usage_date' => 'required|date|before_or_equal:today',
            'purpose' => 'nullable|string|max:500',
            'remarks' => 'nullable|string|max:1000',
        ]);

        // Check stock availability
        $availability = $this->stockService->checkStockAvailability(
            $party->id,
            $request->style,
            $request->color,
            $request->item,
            $request->size,
            $request->qty_used
        );

        if (!$availability['available']) {
            return response()->json([
                'message' => $availability['message']
            ], 400);
        }

        // Create usage record
        BuyerUse::create([
            'party_id' => $party->id,
            'user_id' => $user->id,
            'style' => $request->style,
            'color' => $request->color,
            'item' => $request->item,
            'size' => $request->size,
            'qty_used' => $request->qty_used,
            'usage_date' => $request->usage_date,
            'purpose' => $request->purpose,
            'remarks' => $request->remarks,
        ]);

        return response()->json([
            'message' => __('Usage recorded successfully'),
            'redirect' => route('buyer.stock.index')
        ]);
    }

    /**
     * Get available options for dropdowns (AJAX).
     */
    public function getDropdownOptions(Request $request)
    {
        $user = Auth::user();
        $party = Party::where('user_id', $user->id)->where('type', 'buyer')->first();

        if (!$party) {
            return response()->json(['error' => 'Access denied'], 403);
        }

        $stockData = $this->stockService->calculateBuyerStock($party->id);

        // Filter based on current selections
        $filteredData = $stockData;

        if ($request->filled('style')) {
            $filteredData = $filteredData->filter(function ($item) use ($request) {
                return $item['style'] == $request->style;
            });
        }

        if ($request->filled('color')) {
            $filteredData = $filteredData->filter(function ($item) use ($request) {
                return $item['color'] == $request->color;
            });
        }

        if ($request->filled('item')) {
            $filteredData = $filteredData->filter(function ($item) use ($request) {
                return $item['item'] == $request->item;
            });
        }

        // Get unique values for each field
        $colors = $filteredData->pluck('color')->unique()->filter()->sort()->values();
        $items = $filteredData->pluck('item')->unique()->filter()->sort()->values();
        $sizes = $filteredData->pluck('size')->unique()->filter()->sort()->values();

        // Get available stock for current selection
        $availableStock = 0;
        if ($request->filled('style') && $request->filled('color') && $request->filled('item') && $request->filled('size')) {
            $variation = $stockData->first(function ($item) use ($request) {
                return $item['style'] == $request->style &&
                       $item['color'] == $request->color &&
                       $item['item'] == $request->item &&
                       $item['size'] == $request->size;
            });
            $availableStock = $variation ? $variation['available_stock'] : 0;
        }

        return response()->json([
            'colors' => $colors,
            'items' => $items,
            'sizes' => $sizes,
            'available_stock' => $availableStock
        ]);
    }

    /**
     * Get available stock for a specific variation (AJAX).
     */
    public function getAvailableStock(Request $request)
    {
        $user = Auth::user();
        $party = Party::where('user_id', $user->id)->where('type', 'buyer')->first();

        if (!$party) {
            return response()->json(['error' => 'Access denied'], 403);
        }

        $stockData = $this->stockService->calculateBuyerStock($party->id);

        $variation = $stockData->first(function ($item) use ($request) {
            return $item['style'] == $request->style &&
                   $item['color'] == $request->color &&
                   $item['item'] == $request->item &&
                   $item['size'] == $request->size;
        });

        return response()->json([
            'available_stock' => $variation ? $variation['available_stock'] : 0
        ]);
    }

    /**
     * Show usage history.
     */
    public function usageHistory(Request $request)
    {
        $user = Auth::user();
        $party = Party::where('user_id', $user->id)->where('type', 'buyer')->first();

        if (!$party) {
            abort(403, 'Access denied.');
        }

        $query = BuyerUse::with('user')->where('party_id', $party->id);

        // Apply filters
        if ($request->filled('start_date') && $request->filled('end_date')) {
            $query->betweenDates($request->start_date, $request->end_date);
        }

        if ($request->filled('style')) {
            $query->where('style', 'like', '%' . $request->style . '%');
        }

        // Calculate total quantity used for all filtered records
        $totalQuantityUsed = $query->sum('qty_used');

        $usageHistory = $query->orderBy('usage_date', 'desc')
                             ->orderBy('created_at', 'desc')
                             ->paginate(20);

        return view('buyer.stock.usage-history', compact('usageHistory', 'party', 'totalQuantityUsed'));
    }
}
