<?php

namespace App\Http\Controllers;

use App\Models\Cutting;
use Illuminate\Http\Request;
use Carbon\Carbon;

class CuttingController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $query = Cutting::query();

        // Apply date range filter if provided
        if ($request->filled('from_date')) {
            $query->whereDate('production_date', '>=', $request->from_date);
        }

        if ($request->filled('to_date')) {
            $query->whereDate('production_date', '<=', $request->to_date);
        }

        // Apply month filter if provided
        if ($request->filled('month')) {
            $month = Carbon::parse($request->month);
            $query->whereMonth('production_date', $month->month)
                  ->whereYear('production_date', $month->year);
        }

        // Apply search filter if provided
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('style', 'like', "%{$search}%")
                  ->orWhere('item', 'like', "%{$search}%")
                  ->orWhere('color', 'like', "%{$search}%")
                  ->orWhere('order_id', 'like', "%{$search}%");
            });
        }

        $cuttings = $query->latest('production_date')->paginate(10);

        // Calculate date range statistics for quick filters
        $dateRangeStats = $this->calculateDateRangeStatistics($request);

        // Preserve query parameters in pagination links
        $cuttings->appends($request->query());

        // Set the pagination path to use filter route when filters are active
        if ($request->filled('from_date') || $request->filled('to_date') ||
            $request->filled('month') || $request->filled('search')) {
            $cuttings->withPath(route('cuttings.filter'));
        }

        return view('pages.cutting.index', compact('cuttings', 'dateRangeStats'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        return view('pages.cutting.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        Cutting::create($request->all());
        return redirect()->route('cuttings.index')
            ->with('success', 'Cutting created successfully.');
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Cutting  $cutting
     * @return \Illuminate\Http\Response
     */
    public function show(Cutting $cutting)
    {
        return view('pages.cutting.show', compact('cutting'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\Cutting  $cutting
     * @return \Illuminate\Http\Response
     */
    public function edit(Cutting $cutting)
    {
        return view('pages.cutting.edit', compact('cutting'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Cutting  $cutting
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Cutting $cutting)
    {
        // dd($request->all());
        $cutting->update($request->all());
        return redirect()->route('cuttings.index')
            ->with('success', 'Cutting updated successfully');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Cutting  $cutting
     * @return \Illuminate\Http\Response
     */
    public function destroy(Cutting $cutting)
    {
        $cutting->delete();
        return redirect()->route('cuttings.index')
            ->with('success', 'Cutting deleted successfully');
    }

    /**
     * Filter cuttings based on request parameters
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function filter(Request $request)
    {
        $query = Cutting::query();

        // Apply date range filter if provided
        if ($request->filled('from_date')) {
            $query->whereDate('production_date', '>=', $request->from_date);
        }

        if ($request->filled('to_date')) {
            $query->whereDate('production_date', '<=', $request->to_date);
        }

        // Apply month filter if provided
        if ($request->filled('month')) {
            $month = Carbon::parse($request->month);
            $query->whereMonth('production_date', $month->month)
                  ->whereYear('production_date', $month->year);
        }

        // Apply search filter if provided
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('style', 'like', "%{$search}%")
                  ->orWhere('item', 'like', "%{$search}%")
                  ->orWhere('color', 'like', "%{$search}%")
                  ->orWhere('order_id', 'like', "%{$search}%");
            });
        }

        $cuttings = $query->latest('production_date')->paginate($request->per_page ?? 10);

        // Calculate date range statistics for quick filters
        $dateRangeStats = $this->calculateDateRangeStatistics($request);

        // Preserve query parameters in pagination links
        $cuttings->appends($request->query());

        // Set the pagination path to use filter route when filters are active
        if ($request->filled('from_date') || $request->filled('to_date') ||
            $request->filled('month') || $request->filled('search')) {
            $cuttings->withPath(route('cuttings.filter'));
        }

        if ($request->ajax()) {
            return response()->json([
                'data' => view('pages.cutting.data', compact('cuttings'))->render(),
                'pagination' => $cuttings->links()->render(),
                'dateRangeStats' => $dateRangeStats
            ]);
        }

        // For non-AJAX requests (like pagination), return the view with filtered data
        return view('pages.cutting.index', compact('cuttings', 'dateRangeStats'));
    }

    /**
     * Calculate date range statistics for quick filters
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    private function calculateDateRangeStatistics(Request $request)
    {
        // Determine if we have a specific date range from quick filters
        $fromDate = $request->filled('from_date') ? $request->from_date : null;
        $toDate = $request->filled('to_date') ? $request->to_date : null;
        $month = $request->filled('month') ? $request->month : null;

        // If no date filters are applied, return current filtered results stats
        if (!$fromDate && !$toDate && !$month) {
            return [
                'hasDateRange' => false,
                'label' => 'All Cuttings',
                'count' => 0,
                'totalQuantity' => 0,
                'uniqueOrders' => 0
            ];
        }

        // Build query for the specific date range
        $query = Cutting::query();

        if ($fromDate && $toDate) {
            // Date range filter (for quick filters like Last 7 days, Last 30 days)
            $query->whereDate('production_date', '>=', $fromDate)
                  ->whereDate('production_date', '<=', $toDate);

            $from = Carbon::parse($fromDate);
            $to = Carbon::parse($toDate);
            $diffDays = $from->diffInDays($to) + 1;

            if ($diffDays == 7) {
                $label = 'Last 7 Days';
            } elseif ($diffDays >= 28 && $diffDays <= 31) {
                $label = 'Last 30 Days';
            } else {
                $label = $from->format('M j') . ' - ' . $to->format('M j');
            }
        } elseif ($month) {
            // Month filter (for This month, Last month)
            $monthDate = Carbon::parse($month);
            $query->whereMonth('production_date', $monthDate->month)
                  ->whereYear('production_date', $monthDate->year);

            $now = Carbon::now();
            if ($monthDate->isSameMonth($now)) {
                $label = 'This Month';
            } elseif ($monthDate->isSameMonth($now->subMonth())) {
                $label = 'Last Month';
            } else {
                $label = $monthDate->format('F Y');
            }
        } else {
            // Single date boundary
            if ($fromDate) {
                $query->whereDate('production_date', '>=', $fromDate);
                $label = 'From ' . Carbon::parse($fromDate)->format('M j');
            } elseif ($toDate) {
                $query->whereDate('production_date', '<=', $toDate);
                $label = 'Until ' . Carbon::parse($toDate)->format('M j');
            }
        }

        // Apply search filter if present (but not date filters)
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('style', 'like', "%{$search}%")
                  ->orWhere('item', 'like', "%{$search}%")
                  ->orWhere('color', 'like', "%{$search}%")
                  ->orWhere('order_id', 'like', "%{$search}%");
            });
        }

        $dateRangeCuttings = $query->get();

        return [
            'hasDateRange' => true,
            'label' => $label,
            'count' => $dateRangeCuttings->count(),
            'totalQuantity' => $dateRangeCuttings->sum('total'),
            'uniqueOrders' => $dateRangeCuttings->unique('order_id')->count()
        ];
    }
}
