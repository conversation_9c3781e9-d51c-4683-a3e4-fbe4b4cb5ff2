<?php

namespace App\Http\Controllers;

use App\Models\CuttingMarker;
use App\Models\CuttingPanel;
use App\Models\CuttingAccessory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class CuttingMarkerController extends Controller
{
    public function __construct()
    {
        // $this->middleware('permission:cuttings-read')->only('index', 'show');
        // $this->middleware('permission:cuttings-create')->only('create', 'store');
        // $this->middleware('permission:cuttings-update')->only('edit', 'update');
        // $this->middleware('permission:cuttings-delete')->only('destroy');
    }

    /**
     * Display a listing of cutting markers
     */
    public function index(Request $request)
    {
        $markers = CuttingMarker::with(['creator', 'panels', 'accessories'])
            ->when($request->status, function ($query, $status) {
                return $query->where('status', $status);
            })
            ->when($request->search, function ($query, $search) {
                return $query->where('name', 'like', "%{$search}%")
                           ->orWhere('description', 'like', "%{$search}%");
            })
            ->latest()
            ->paginate(15);

        return view('pages.cutting-markers.index', compact('markers'));
    }

    /**
     * Show the form for creating a new cutting marker
     */
    public function create()
    {
        $availableSizes = CuttingMarker::getAvailableSizes();
        $commonAccessories = CuttingAccessory::getCommonAccessories();

        return view('pages.cutting-markers.create', compact('availableSizes', 'commonAccessories'));
    }

    /**
     * Store a newly created cutting marker
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'fabric_width' => 'required|numeric|min:1|max:200',
            'shrinkage_percentage' => 'required|numeric|min:0|max:100',
            'wastage_percentage' => 'required|numeric|min:0|max:100',
            'total_set_count' => 'required|integer|min:1',
            'sizes' => 'required|array|min:1',
            'sizes.*.size' => 'required|string',
            'sizes.*.front_width' => 'required|numeric|min:0.1',
            'sizes.*.back_width' => 'required|numeric|min:0.1',
            'sizes.*.length' => 'required|numeric|min:0.1',
            'sizes.*.ratio' => 'required|integer|min:1',
            'accessories' => 'nullable|array',
            'accessories.*.name' => 'required_with:accessories|string',
            'accessories.*.width' => 'required_with:accessories|numeric|min:0.1',
            'accessories.*.length' => 'required_with:accessories|numeric|min:0.1',
            'accessories.*.quantity_per_pant' => 'required_with:accessories|integer|min:1',
        ]);

        DB::beginTransaction();

        try {
            // Create the cutting marker
            $marker = CuttingMarker::create([
                'name' => $request->name,
                'description' => $request->description,
                'fabric_width' => $request->fabric_width,
                'shrinkage_percentage' => $request->shrinkage_percentage,
                'wastage_percentage' => $request->wastage_percentage,
                'total_set_count' => $request->total_set_count,
                'size_ratios' => $request->sizes,
                'accessories_data' => $request->accessories ?? [],
                'created_by' => Auth::id(),
                'updated_by' => Auth::id(),
            ]);

            // Generate panels for each size
            $totalPantsProducible = 0;
            foreach ($request->sizes as $sizeData) {
                $pantsForThisSize = $sizeData['ratio'] * $request->total_set_count;
                $totalPantsProducible += $pantsForThisSize;

                $marker->generatePanelsForSize(
                    $sizeData['size'],
                    [
                        'width' => max($sizeData['front_width'], $sizeData['back_width']),
                        'length' => $sizeData['length']
                    ],
                    $pantsForThisSize
                );
            }

            // Create accessories
            if ($request->accessories) {
                foreach ($request->accessories as $accessoryData) {
                    $marker->accessories()->create([
                        'name' => $accessoryData['name'],
                        'width' => $accessoryData['width'],
                        'length' => $accessoryData['length'],
                        'quantity_per_pant' => $accessoryData['quantity_per_pant'],
                        'total_quantity' => $accessoryData['quantity_per_pant'] * $totalPantsProducible,
                        'color_code' => '#FF6B6B',
                    ]);
                }
            }

            // Update marker with calculated values
            $marker->update([
                'total_pants_producible' => $totalPantsProducible,
                'total_panels_count' => $marker->panels()->count(),
            ]);

            // Run the layout algorithm
            $this->runLayoutAlgorithm($marker);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Cutting marker created successfully!',
                'marker_id' => $marker->id,
                'redirect_url' => route('cutting-markers.show', $marker),
            ]);

        } catch (\Exception $e) {
            DB::rollback();

            return response()->json([
                'success' => false,
                'message' => 'Error creating cutting marker: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Display the specified cutting marker
     */
    public function show(CuttingMarker $cuttingMarker)
    {
        $cuttingMarker->load(['panels', 'accessories', 'creator', 'updater']);

        return view('pages.cutting-markers.show', compact('cuttingMarker'));
    }

    /**
     * Show the form for editing the specified cutting marker
     */
    public function edit(CuttingMarker $cuttingMarker)
    {
        $cuttingMarker->load(['panels', 'accessories']);
        $availableSizes = CuttingMarker::getAvailableSizes();
        $commonAccessories = CuttingAccessory::getCommonAccessories();

        return view('pages.cutting-markers.edit', compact('cuttingMarker', 'availableSizes', 'commonAccessories'));
    }

    /**
     * Update the specified cutting marker
     */
    public function update(Request $request, CuttingMarker $cuttingMarker)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'fabric_width' => 'required|numeric|min:1|max:200',
            'shrinkage_percentage' => 'required|numeric|min:0|max:100',
            'wastage_percentage' => 'required|numeric|min:0|max:100',
            'total_set_count' => 'required|integer|min:1',
            'status' => 'required|in:draft,active,archived',
        ]);

        $cuttingMarker->update([
            'name' => $request->name,
            'description' => $request->description,
            'fabric_width' => $request->fabric_width,
            'shrinkage_percentage' => $request->shrinkage_percentage,
            'wastage_percentage' => $request->wastage_percentage,
            'total_set_count' => $request->total_set_count,
            'status' => $request->status,
            'updated_by' => Auth::id(),
        ]);

        // Recalculate layout if fabric width changed
        if ($cuttingMarker->wasChanged('fabric_width')) {
            $this->runLayoutAlgorithm($cuttingMarker);
        }

        return response()->json([
            'success' => true,
            'message' => 'Cutting marker updated successfully!',
        ]);
    }

    /**
     * Remove the specified cutting marker
     */
    public function destroy(CuttingMarker $cuttingMarker)
    {
        $cuttingMarker->delete();

        return response()->json([
            'success' => true,
            'message' => 'Cutting marker deleted successfully!',
        ]);
    }

    /**
     * Run the smart layout algorithm (greedy bin-packing)
     */
    private function runLayoutAlgorithm(CuttingMarker $marker)
    {
        $fabricWidth = $marker->fabric_width;
        $allItems = collect();

        // Collect all panels
        foreach ($marker->panels as $panel) {
            for ($i = 0; $i < $panel->quantity; $i++) {
                $allItems->push([
                    'type' => 'panel',
                    'id' => $panel->id,
                    'width' => $panel->width,
                    'length' => $panel->length,
                    'area' => $panel->width * $panel->length,
                    'instance' => $i,
                ]);
            }
        }

        // Collect all accessories
        foreach ($marker->accessories as $accessory) {
            for ($i = 0; $i < $accessory->total_quantity; $i++) {
                $allItems->push([
                    'type' => 'accessory',
                    'id' => $accessory->id,
                    'width' => $accessory->width,
                    'length' => $accessory->length,
                    'area' => $accessory->width * $accessory->length,
                    'instance' => $i,
                ]);
            }
        }

        // Sort by area (largest first) for better packing
        $allItems = $allItems->sortByDesc('area');

        $rows = [];

        foreach ($allItems as $item) {
            $placed = false;

            // Try to place in existing rows
            for ($rowIndex = 0; $rowIndex < count($rows); $rowIndex++) {
                if ($this->canPlaceInRow($rows[$rowIndex], $item, $fabricWidth)) {
                    $this->placeInRow($rows[$rowIndex], $item, $rowIndex);
                    $placed = true;
                    break;
                }
            }

            // Create new row if couldn't place in existing rows
            if (!$placed) {
                $newRow = [];
                $this->placeInRow($newRow, $item, count($rows));
                $rows[] = $newRow;
            }
        }

        // Update database with new positions
        $this->updateItemPositions($marker, $rows);

        // Calculate and update marker statistics
        $this->updateMarkerStatistics($marker);
    }

    /**
     * Check if item can be placed in a row
     */
    private function canPlaceInRow(array $row, array $item, float $fabricWidth): bool
    {
        $usedWidth = 0;
        foreach ($row as $placedItem) {
            $usedWidth += $placedItem['width'];
        }

        return ($usedWidth + $item['width']) <= $fabricWidth;
    }

    /**
     * Place item in a row
     */
    private function placeInRow(array &$row, array $item, int $rowNumber): void
    {
        $xPosition = 0;
        foreach ($row as $placedItem) {
            $xPosition += $placedItem['width'];
        }

        $item['x_position'] = $xPosition;
        $item['y_position'] = 0; // Will be calculated based on row
        $item['row_number'] = $rowNumber;

        $row[] = $item;
    }

    /**
     * Update item positions in database
     */
    private function updateItemPositions(CuttingMarker $marker, array $rows): void
    {
        $yOffset = 0;

        foreach ($rows as $rowIndex => $row) {
            $maxHeightInRow = 0;

            foreach ($row as $item) {
                $maxHeightInRow = max($maxHeightInRow, $item['length']);

                if ($item['type'] === 'panel') {
                    CuttingPanel::where('id', $item['id'])->update([
                        'row_number' => $rowIndex,
                        'x_position' => $item['x_position'],
                        'y_position' => $yOffset,
                    ]);
                } else {
                    CuttingAccessory::where('id', $item['id'])->update([
                        'row_number' => $rowIndex,
                        'x_position' => $item['x_position'],
                        'y_position' => $yOffset,
                    ]);
                }
            }

            $yOffset += $maxHeightInRow;
        }
    }

    /**
     * Update marker statistics
     */
    private function updateMarkerStatistics(CuttingMarker $marker): void
    {
        $marker->refresh();

        $totalFabricRequired = $marker->calculateTotalFabricRequired();
        $fabricUtilization = $marker->calculateFabricUtilization();

        $marker->update([
            'total_fabric_required' => $totalFabricRequired,
            'fabric_utilization_percentage' => $fabricUtilization,
        ]);
    }

    /**
     * AJAX: Move panel to new position
     */
    public function movePanel(Request $request, CuttingMarker $cuttingMarker)
    {
        $request->validate([
            'panel_id' => 'required|exists:cutting_panels,id',
            'x_position' => 'required|numeric|min:0',
            'y_position' => 'required|numeric|min:0',
            'row_number' => 'required|integer|min:0',
        ]);

        $panel = CuttingPanel::where('id', $request->panel_id)
                            ->where('cutting_marker_id', $cuttingMarker->id)
                            ->first();

        if (!$panel) {
            return response()->json(['success' => false, 'message' => 'Panel not found'], 404);
        }

        // Check if panel fits within fabric width
        if (($request->x_position + $panel->effective_width) > $cuttingMarker->fabric_width) {
            return response()->json([
                'success' => false,
                'message' => 'Panel exceeds fabric width'
            ], 400);
        }

        $panel->update([
            'x_position' => $request->x_position,
            'y_position' => $request->y_position,
            'row_number' => $request->row_number,
        ]);

        // Recalculate marker statistics
        $this->updateMarkerStatistics($cuttingMarker);

        return response()->json([
            'success' => true,
            'message' => 'Panel moved successfully',
            'panel' => $panel->fresh(),
        ]);
    }

    /**
     * AJAX: Rotate panel
     */
    public function rotatePanel(Request $request, CuttingMarker $cuttingMarker)
    {
        $request->validate([
            'panel_id' => 'required|exists:cutting_panels,id',
        ]);

        $panel = CuttingPanel::where('id', $request->panel_id)
                            ->where('cutting_marker_id', $cuttingMarker->id)
                            ->first();

        if (!$panel) {
            return response()->json(['success' => false, 'message' => 'Panel not found'], 404);
        }

        if (!$panel->canRotate()) {
            return response()->json([
                'success' => false,
                'message' => 'Panel rotation is locked due to grain direction'
            ], 400);
        }

        $newRotation = ($panel->rotation + 90) % 360;
        $panel->update(['rotation' => $newRotation]);

        // Check if rotated panel still fits
        if (($panel->x_position + $panel->effective_width) > $cuttingMarker->fabric_width) {
            return response()->json([
                'success' => false,
                'message' => 'Rotated panel exceeds fabric width',
                'requires_reposition' => true,
            ]);
        }

        return response()->json([
            'success' => true,
            'message' => 'Panel rotated successfully',
            'panel' => $panel->fresh(),
        ]);
    }

    /**
     * AJAX: Mirror panel
     */
    public function mirrorPanel(Request $request, CuttingMarker $cuttingMarker)
    {
        $request->validate([
            'panel_id' => 'required|exists:cutting_panels,id',
        ]);

        $panel = CuttingPanel::where('id', $request->panel_id)
                            ->where('cutting_marker_id', $cuttingMarker->id)
                            ->first();

        if (!$panel) {
            return response()->json(['success' => false, 'message' => 'Panel not found'], 404);
        }

        $panel->update(['mirrored' => !$panel->mirrored]);

        return response()->json([
            'success' => true,
            'message' => 'Panel mirrored successfully',
            'panel' => $panel->fresh(),
        ]);
    }

    /**
     * AJAX: Recalculate layout
     */
    public function recalculateLayout(CuttingMarker $cuttingMarker)
    {
        try {
            $this->runLayoutAlgorithm($cuttingMarker);

            return response()->json([
                'success' => true,
                'message' => 'Layout recalculated successfully',
                'redirect_url' => route('cutting-markers.show', $cuttingMarker),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error recalculating layout: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Export marker as PDF (HTML version for now)
     */
    public function exportPdf(CuttingMarker $cuttingMarker)
    {
        $cuttingMarker->load(['panels', 'accessories']);

        // Return HTML view that can be printed as PDF
        return response()
                ->view('pages.cutting-markers.pdf', compact('cuttingMarker'))
                ->header('Content-Type', 'text/html')
                ->header('Content-Disposition', 'inline; filename="' . $cuttingMarker->name . '_marker.html"');
    }

    /**
     * Export marker as JSON
     */
    public function exportJson(CuttingMarker $cuttingMarker)
    {
        $cuttingMarker->load(['panels', 'accessories']);

        $exportData = [
            'marker' => $cuttingMarker->toArray(),
            'panels' => $cuttingMarker->panels->toArray(),
            'accessories' => $cuttingMarker->accessories->toArray(),
            'export_date' => now()->toISOString(),
            'version' => '1.0',
        ];

        $filename = $cuttingMarker->name . '_marker.json';

        return response()->json($exportData)
                         ->header('Content-Disposition', 'attachment; filename="' . $filename . '"');
    }
}
