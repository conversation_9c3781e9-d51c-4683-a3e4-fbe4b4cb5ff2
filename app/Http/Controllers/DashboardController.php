<?php

namespace App\Http\Controllers;

use App\Models\Cutting;
use App\Models\Production;
use App\Models\ProductionSetting;
use App\Models\ItemSize;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Carbon\Carbon;

class DashboardController extends Controller
{
    public function __construct()
    {
        $this->middleware('permission:dashboard-read')->only('index', 'maanIndex');
    }

    public function maanIndex()
    {
        $today = now()->format('Y-m-d');

        // Production KPIs
        $productionStats = $this->getProductionKPIs($today);

        // Production targets and efficiency
        $productionTargets = $this->getProductionTargets($today);

        // Work in progress status
        $wipStatus = $this->getWIPStatus();

        // Production line performance
        $linePerformance = $this->getLinePerformance($today);

        // Quality metrics
        $qualityMetrics = $this->getQualityMetrics($today);

        // Recent production activities
        $recentActivities = $this->getRecentProductionActivities();

        return view('pages.dashboard.index', compact(
            'productionStats',
            'productionTargets',
            'wipStatus',
            'linePerformance',
            'qualityMetrics',
            'recentActivities'
        ));
    }

    public function getDashboardData()
    {
        $today = now()->format('Y-m-d');

        // Get production KPIs data
        $productionStats = $this->getProductionKPIs($today);
        $productionTargets = $this->getProductionTargets($today);
        $wipStatus = $this->getWIPStatus();
        $linePerformance = $this->getLinePerformance($today);
        $qualityMetrics = $this->getQualityMetrics($today);

        $data = array_merge(
            $productionStats,
            $productionTargets,
            $wipStatus,
            $linePerformance,
            $qualityMetrics
        );

        return response()->json($data);
    }

    /**
     * Get production KPIs for the dashboard
     */
    private function getProductionKPIs($date)
    {
        // Get cutting data from the most recent cutting date
        $lastCuttingDate = Cutting::whereNotNull('production_date')->max('production_date');
        $todayCuttingQty = 0;

        if ($lastCuttingDate) {
            $lastDateCuttings = Cutting::whereDate('production_date', $lastCuttingDate)->get();
            foreach ($lastDateCuttings as $cutting) {
                $sizeColumns = ['size_28', 'size_29', 'size_30', 'size_31', 'size_32', 'size_33', 'size_34', 'size_35', 'size_36', 'size_37', 'size_38', 'size_39', 'size_40', 'size_41', 'size_42', 'size_43', 'size_44', 'size_45', 'size_46', 'size_47', 'size_48', 'size_49', 'size_50', 'size_80'];
                foreach ($sizeColumns as $column) {
                    if (!is_null($cutting->$column)) {
                        $todayCuttingQty += (int) $cutting->$column;
                    }
                }
            }
        }

        // Get production data from ItemSize
        $lastProductionDate = ItemSize::max('created_at');
        $todayProductionQty = 0;

        if ($lastProductionDate) {
            $lastDateProductions = ItemSize::whereDate('created_at', date('Y-m-d', strtotime($lastProductionDate)))->get();
            $todayProductionQty = $lastDateProductions->sum('qty');
        }

        // Calculate monthly data
        $monthlyCuttings = Cutting::whereMonth('created_at', now()->month)->get();
        $monthlyCuttingQty = 0;
        foreach ($monthlyCuttings as $cutting) {
            $sizeColumns = ['size_28', 'size_29', 'size_30', 'size_31', 'size_32', 'size_33', 'size_34', 'size_35', 'size_36', 'size_37', 'size_38', 'size_39', 'size_40', 'size_41', 'size_42', 'size_43', 'size_44', 'size_45', 'size_46', 'size_47', 'size_48', 'size_49', 'size_50', 'size_80'];
            foreach ($sizeColumns as $column) {
                $monthlyCuttingQty += (int) $cutting->$column;
            }
        }

        $monthlyProductions = ItemSize::whereMonth('created_at', now()->month)->get();
        $monthlyProductionQty = $monthlyProductions->sum('qty');

        return [
            'today_cutting' => $todayCuttingQty,
            'today_production' => $todayProductionQty,
            'monthly_cutting' => $monthlyCuttingQty,
            'monthly_production' => $monthlyProductionQty,
            'weekly_cutting' => $this->getWeeklyCutting(),
            'weekly_production' => $this->getWeeklyProduction(),
        ];
    }

    /**
     * Get production targets and efficiency metrics
     */
    private function getProductionTargets($date)
    {
        $todayTarget = 1000; // Default target, can be made configurable
        $todayActual = $this->getProductionKPIs($date)['today_production'];
        $efficiency = $todayTarget > 0 ? round(($todayActual / $todayTarget) * 100, 1) : 0;

        $monthlyTarget = $todayTarget * date('t'); // Days in current month
        $monthlyActual = $this->getProductionKPIs($date)['monthly_production'];
        $monthlyEfficiency = $monthlyTarget > 0 ? round(($monthlyActual / $monthlyTarget) * 100, 1) : 0;

        return [
            'daily_target' => $todayTarget,
            'daily_actual' => $todayActual,
            'daily_efficiency' => $efficiency,
            'monthly_target' => $monthlyTarget,
            'monthly_actual' => $monthlyActual,
            'monthly_efficiency' => $monthlyEfficiency,
        ];
    }

    /**
     * Get work-in-progress status
     */
    private function getWIPStatus()
    {
        $totalCutting = Cutting::whereMonth('created_at', now()->month)->count();
        $totalProduction = ItemSize::whereMonth('created_at', now()->month)->count();
        $wipItems = max(0, $totalCutting - $totalProduction);

        return [
            'wip_cutting_pending' => $wipItems,
            'wip_production_completed' => $totalProduction,
            'wip_total_orders' => $totalCutting,
            'wip_completion_rate' => $totalCutting > 0 ? round(($totalProduction / $totalCutting) * 100, 1) : 0,
        ];
    }

    /**
     * Get production line performance (simulated data)
     */
    private function getLinePerformance($date)
    {
        // This would typically come from actual production line data
        return [
            'line_1_efficiency' => rand(85, 98),
            'line_2_efficiency' => rand(80, 95),
            'line_3_efficiency' => rand(88, 96),
            'average_line_efficiency' => rand(84, 96),
            'active_lines' => 3,
            'total_lines' => 3,
        ];
    }

    /**
     * Get quality metrics (simulated data)
     */
    private function getQualityMetrics($date)
    {
        return [
            'quality_pass_rate' => rand(92, 99),
            'defect_rate' => rand(1, 8),
            'rework_rate' => rand(2, 6),
            'first_pass_yield' => rand(90, 97),
        ];
    }

    /**
     * Get recent production activities
     */
    private function getRecentProductionActivities()
    {
        $activities = collect();

        // Recent cutting activities
        $recentCuttings = Cutting::latest()->take(3)->get();
        foreach ($recentCuttings as $cutting) {
            $activities->push([
                'type' => 'cutting',
                'message' => "Cutting completed for Order #{$cutting->order_id}",
                'time' => $cutting->created_at,
                'icon' => 'fas fa-cut',
                'color' => 'info'
            ]);
        }

        // Recent production activities
        $recentProductions = ItemSize::latest()->take(3)->get();
        foreach ($recentProductions as $production) {
            $activities->push([
                'type' => 'production',
                'message' => "Production: {$production->qty} units completed",
                'time' => $production->created_at,
                'icon' => 'fas fa-industry',
                'color' => 'primary'
            ]);
        }

        return $activities->sortByDesc('time')->take(6);
    }

    /**
     * Get weekly cutting data
     */
    private function getWeeklyCutting()
    {
        $startOfWeek = Carbon::now()->startOfWeek();
        $endOfWeek = Carbon::now()->endOfWeek();

        $weeklyCuttings = Cutting::whereBetween('created_at', [$startOfWeek, $endOfWeek])->get();
        $weeklyCuttingQty = 0;

        foreach ($weeklyCuttings as $cutting) {
            $sizeColumns = ['size_28', 'size_29', 'size_30', 'size_31', 'size_32', 'size_33', 'size_34', 'size_35', 'size_36', 'size_37', 'size_38', 'size_39', 'size_40', 'size_41', 'size_42', 'size_43', 'size_44', 'size_45', 'size_46', 'size_47', 'size_48', 'size_49', 'size_50', 'size_80'];
            foreach ($sizeColumns as $column) {
                $weeklyCuttingQty += (int) $cutting->$column;
            }
        }

        return $weeklyCuttingQty;
    }

    /**
     * Get weekly production data
     */
    private function getWeeklyProduction()
    {
        $startOfWeek = Carbon::now()->startOfWeek();
        $endOfWeek = Carbon::now()->endOfWeek();

        return ItemSize::whereBetween('created_at', [$startOfWeek, $endOfWeek])->sum('qty');
    }
}
