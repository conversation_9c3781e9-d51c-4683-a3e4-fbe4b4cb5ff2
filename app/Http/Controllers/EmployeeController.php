<?php

namespace App\Http\Controllers;

use App\Models\Employee;
use App\Models\Designation;
use App\Helpers\HasUploader;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Storage;

class EmployeeController extends Controller
{
    use HasUploader;
    public function __construct()
    {
        $this->middleware('permission:employees-create')->only('store');
        $this->middleware('permission:employees-read')->only('index', 'show');
        $this->middleware('permission:employees-update')->only('update', 'status');
        $this->middleware('permission:employees-delete')->only('destroy');
    }

    public function index()
    {
        $employees = Employee::with('designation:id,name')->latest()->paginate(10);
        return view('pages.employees.index', compact('employees'));
    }

    public function create()
    {
        $designations = Designation::select('id','name')->latest()->get();
        return view('pages.employees.create', compact( 'designations'));
    }

    public function show(Employee $employee)
    {
        $employee->load(['designation', 'attendances' => function($query) {
            $query->orderBy('date', 'desc')->limit(30);
        }]);

        // Get attendance statistics for current month
        $currentMonth = now()->month;
        $currentYear = now()->year;

        $monthlyAttendance = $employee->attendances()
            ->whereMonth('date', $currentMonth)
            ->whereYear('date', $currentYear)
            ->get();

        $attendanceStats = [
            'present_days' => $monthlyAttendance->where('status', 'present')->count(),
            'absent_days' => $monthlyAttendance->where('status', 'absent')->count(),
            'half_days' => $monthlyAttendance->where('status', 'half_day')->count(),
            'late_days' => $monthlyAttendance->filter(function($attendance) {
                return $attendance->in_time && $attendance->in_time > '09:00:00';
            })->count(),
            'total_days' => now()->day, // Days passed in current month
        ];

        // Get recent salary records
        $recentSalaries = $employee->salaries()->latest()->limit(6)->get();

        return view('pages.employees.show', compact('employee', 'attendanceStats', 'recentSalaries'));
    }

    /**
     * Get employee attendance data for calendar
     */
    public function getAttendanceData(Employee $employee, Request $request)
    {
        $year = $request->get('year', date('Y'));
        $month = $request->get('month', date('n'));

        $attendances = $employee->attendances()
            ->whereYear('date', $year)
            ->whereMonth('date', $month)
            ->get()
            ->map(function ($attendance) {
                return [
                    'date' => $attendance->date->format('Y-m-d'),
                    'status' => $attendance->status,
                    'in_time' => $attendance->in_time,
                    'out_time' => $attendance->out_time,
                    'is_late' => $attendance->in_time && $attendance->in_time > '09:00:00',
                    'working_hours' => $attendance->working_hours,
                ];
            });

        return response()->json([
            'success' => true,
            'attendance' => $attendances
        ]);
    }

    /**
     * Show monthly attendance sheet for a single employee
     */
    public function monthlySheet(Employee $employee, Request $request)
    {
        $month = $request->get('month', now()->month);
        $year = $request->get('year', now()->year);

        // Get month name and days in month
        $monthName = date('F', mktime(0, 0, 0, $month, 1));
        $daysInMonth = cal_days_in_month(CAL_GREGORIAN, $month, $year);

        // Load employee with designation
        $employee->load('designation');

        // Get attendance records for the month
        $attendances = \App\Models\Attendance::where('employee_id', $employee->id)
            ->whereYear('date', $year)
            ->whereMonth('date', $month)
            ->get();

        // Organize attendance data by date
        $attendanceData = [];
        foreach ($attendances as $attendance) {
            $date = $attendance->date->format('Y-m-d');
            $attendanceData[$date] = $attendance;
        }

        // Calculate summary statistics
        $summary = [
            'total_present' => $attendances->where('status', 'present')->count(),
            'total_absent' => $attendances->where('status', 'absent')->count(),
            'total_half_days' => $attendances->where('status', 'half_day')->count(),
            'total_late' => $attendances->filter(function($attendance) {
                return $attendance->status === 'present' && $attendance->in_time && $attendance->in_time > '09:00:00';
            })->count(),
            'total_working_hours' => $attendances->sum('working_hours'),
            'total_cost' => $attendances->sum('daily_cost'),
            'attendance_percentage' => 0,
        ];

        if ($daysInMonth > 0) {
            $totalAttendedDays = $summary['total_present'] + ($summary['total_half_days'] * 0.5);
            $summary['attendance_percentage'] = round(($totalAttendedDays / $daysInMonth) * 100, 1);
        }

        return view('pages.employees.monthly-sheet', compact(
            'employee',
            'attendanceData',
            'month',
            'year',
            'monthName',
            'daysInMonth',
            'summary'
        ));
    }

    /**
     * Get salary data for an employee
     */
    public function getSalaryData(Employee $employee)
    {
        // Get salary records for the employee
        $salaries = \App\Models\Salary::where('employee_id', $employee->id)
            ->with(['employee', 'paymentAccount'])
            ->orderBy('year', 'desc')
            ->orderBy('month', 'desc')
            ->get();

        // Calculate summary statistics
        $currentMonth = now()->month;
        $currentYear = now()->year;

        $currentMonthSalary = $salaries->where('month', $currentMonth)
                                     ->where('year', $currentYear)
                                     ->first();

        $totalPaid = $salaries->where('payment_status', 'paid')
                             ->sum('net_salary');

        $pendingPayments = $salaries->where('payment_status', 'pending')
                                  ->sum('net_salary');

        $summary = [
            'current_month' => $currentMonthSalary ? number_format($currentMonthSalary->net_salary ?? $currentMonthSalary->calculated_salary, 0) : '0',
            'total_paid' => number_format($totalPaid, 0),
            'pending_payments' => number_format($pendingPayments, 0),
        ];

        // Format salary data for frontend
        $formattedSalaries = $salaries->map(function ($salary) {
            return [
                'id' => $salary->id,
                'month' => $salary->month,
                'year' => $salary->year,
                'base_salary' => number_format($salary->base_salary, 0),
                'attendance_days' => $salary->attendance_days,
                'overtime_hours' => $salary->overtime_hours,
                'overtime_amount' => number_format($salary->overtime_amount ?? 0, 0),
                'attendance_bonus' => number_format($salary->attendance_bonus ?? 0, 0),
                'advance_amount' => number_format($salary->advance_amount ?? 0, 0),
                'other_deductions' => number_format($salary->other_deductions ?? 0, 0),
                'calculated_salary' => number_format($salary->calculated_salary, 0),
                'net_salary' => number_format($salary->net_salary ?? $salary->calculated_salary, 0),
                'payment_status' => $salary->payment_status,
                'payment_date' => $salary->payment_date ? $salary->payment_date->format('M d, Y') : null,
                'payment_reference' => $salary->payment_reference,
            ];
        });

        return response()->json([
            'success' => true,
            'summary' => $summary,
            'salaries' => $formattedSalaries
        ]);
    }

    public function store(Request $request)
    {
        $request->validate([
            'employee_id' => 'nullable|string|max:50|unique:employees,employee_id',
            'status' => 'boolean',
            'join_date' => 'nullable|date',
            'birth_date' => 'nullable|date',
            'email' => 'nullable|email|max:50',
            'phone' => 'nullable|string|max:20',
            'name' => 'required|string|max:255',
            'address' => 'nullable|string|max:255',
            'gender' => 'nullable|string|in:male,female,other',
            'salary' => 'required|numeric|between:0,9999999.999',
            'basic_salary' => 'nullable|numeric|between:0,9999999.999',
            'wage_type' => 'nullable|string|in:Daily,Monthly',
            'machine_user_id' => 'nullable|string|max:50|unique:employees,machine_user_id',
            'designation_id' => 'required|exists:designations,id',
            'employee_type' => 'nullable|string|in:part_time,full_time',
            'nid_back' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'nid_front' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $nidFront  = $request->nid_front ? $this->upload($request, 'nid_front') : null;
        $nidBack  = $request->nid_back ? $this->upload($request, 'nid_back') : null;

        Employee::create($request->all() + [
            'meta' => [
                'nid_front' => $nidFront,
                'nid_back'  => $nidBack,
            ],
        ]);

        return response()->json([
            'message'  => 'Employee created successfully.',
            'redirect' => route('employees.index'),
        ]);
    }
    

    public function edit(Employee $employee)
    {
        $designations = Designation::select('id','name')->latest()->get();
        return view('pages.employees.edit', compact( 'designations', 'employee'));
    }

    public function update(Request $request, Employee $employee)
    {
        $request->validate([
            'employee_id' => 'nullable|string|max:50|unique:employees,employee_id,' . $employee->id,
            'status' => 'boolean',
            'join_date' => 'nullable|date',
            'birth_date' => 'nullable|date',
            'email' => 'nullable|email|max:50',
            'name' => 'required|string|max:255',
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:255',
            'gender' => 'nullable|string|in:male,female,other',
            'salary' => 'required|numeric|between:0,9999999.999',
            'designation_id' => 'required|exists:designations,id',
            'employee_type' => 'nullable|string|in:part_time,full_time',
            'nid_back' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'nid_front' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $employee->update($request->all() + [
            'meta' => [
                'nid_front' => $request->nid_front ? $this->upload($request, 'nid_front') : $employee->meta['nid_front'],
                'nid_back'  => $request->nid_back ? $this->upload($request, 'nid_back') : $employee->meta['nid_back'],
            ],
        ]);

        return response()->json([
            'message'  => 'Employee updated successfully.',
            'redirect' => route('employees.index'),
        ]);
    }

    public function destroy(Employee $employee)
    {
        if (!empty($employee->meta['nid_front']) && file_exists($employee->meta['nid_front'])) {
            Storage::delete($employee->meta['nid_front']);
        }
        if (!empty($employee->meta['nid_back']) && file_exists($employee->meta['nid_back'])) {
            Storage::delete($employee->meta['nid_back']);
        }

        $employee->delete();

        return response()->json([
            'message'  => 'Employee deleted successfully.',
            'redirect' => route('employees.index'),
        ]);
    }
}
