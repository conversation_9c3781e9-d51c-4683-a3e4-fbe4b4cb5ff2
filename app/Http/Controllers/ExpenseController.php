<?php

namespace App\Http\Controllers;

use App\Models\Expense;
use Illuminate\Http\Request;

class ExpenseController extends Controller
{
    public function __construct()
    {
        $this->middleware('permission:expense-create')->only('maanExpenseStore');
        $this->middleware('permission:expense-read')->only('maanExpenseIndex');
        $this->middleware('permission:expense-update')->only('maanExpenseEdit', 'maanExpenseUpdate');
        $this->middleware('permission:expense-delete')->only('maanExpenseDelete');
    }
    /**
     * Display a listing of the expense.
     */
    public function maanExpenseIndex()
    {
        $expenses = Expense::latest()->paginate(10); // expense info
        return view('pages.accounts.general.expense.index',compact('expenses'));
    }

    /**
     * Store a newly created expense in storage.
     */
    public function maanExpenseStore( Request $request)
    {
        //request validation
        $request->validate([
            'category_name'          => 'required|string|unique:expenses',
            'expense_description'    => 'nullable|string',
        ]);

        $expense = Expense::create($request->all());

        sendNotification($expense->id, route('expense.index'), __('New Expense has been created.'));
        return response()->json([
            'message'   => __('Expense created successfully'),
            'redirect'  => route('expense.index')
        ]);
    }

    /**
     * Show the form for editing the expense.
     */
    public function maanExpenseEdit($id)
    {
        $expense = Expense::findOrFail($id);
        return view('pages.accounts.general.expense.edit',compact('expense'));
    }

    /**
     * Update the expense from storage.
     */
    public function maanExpenseUpdate(Request $request, $id)
    {
        $request->validate([
            'category_name'          => 'required|string|unique:expenses,category_name,'.$id,
            'expense_description'    => 'nullable|string',
        ]);

        $expense  = Expense::findOrFail($id);

        if ($expense->party_id) {
            return response()->json([
                'message'=> __('You can not update / delete this from here, because this is generated by an accessory order.'),
            ], 406);
        }
        
        $expense->update($request->all());
        return response()->json([
            'message'   => __('Expense updated successfully'),
            'redirect'  => route('expense.index')
        ]);
    }

    /**
     * Remove the expense from storage.
     */
    public function maanExpenseDelete($id)
    {
        $expense = Expense::findOrFail($id);

        if ($expense->party_id) {
            return response()->json([
                'message'=> __('You can not update / delete this from here, because this is generated by an accessory order.'),
            ], 406);
        }

        $expense->delete();
        return response()->json([
            'message'   => __('Expense deleted successfully'),
            'redirect'  => route('expense.index')
        ]);
    }
}
