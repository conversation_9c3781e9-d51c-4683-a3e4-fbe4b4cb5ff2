<?php

namespace App\Http\Controllers;

use App\Models\Employee;
use App\Models\Attendance;
use App\Models\Designation;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class HrController extends Controller
{
    public function __construct()
    {
        $this->middleware('permission:hr-dashboard-read')->only('dashboard', 'getDashboardData');
    }
    /**
     * HR Dashboard
     */
    public function dashboard()
    {
        $today = Carbon::today();
        $currentMonth = Carbon::now()->month;
        $currentYear = Carbon::now()->year;

        // Employee Statistics
        $totalEmployees = Employee::where('status', 1)->count();
        $newEmployeesThisMonth = Employee::whereMonth('created_at', $currentMonth)
                                          ->whereYear('created_at', $currentYear)
                                          ->count();

        // Attendance Statistics
        $todayAttendance = Attendance::whereDate('date', $today)->count();
        $attendanceRate = $totalEmployees > 0 ? round(($todayAttendance / $totalEmployees) * 100, 1) : 0;

        $presentToday = Attendance::whereDate('date', $today)
                                   ->where('status', 'present')
                                   ->count();

        $absentToday = Attendance::whereDate('date', $today)
                                  ->where('status', 'absent')
                                  ->count();

        // Cost Statistics (using attendance daily costs as proxy)
        $monthlyCosts = Attendance::whereMonth('date', $currentMonth)
                                  ->whereYear('date', $currentYear)
                                  ->sum('daily_cost');

        $todayCosts = Attendance::whereDate('date', $today)->sum('daily_cost');

        // Recent Activities
        $recentEmployees = Employee::latest()->take(5)->get();
        $recentAttendances = Attendance::with('employee')
                                        ->latest()
                                        ->take(10)
                                        ->get();

        // Monthly Attendance Trend (last 6 months)
        $attendanceTrend = [];
        for ($i = 5; $i >= 0; $i--) {
            $month = Carbon::now()->subMonths($i);
            $monthlyAttendance = Attendance::whereMonth('date', $month->month)
                                           ->whereYear('date', $month->year)
                                           ->where('status', 'present')
                                           ->count();

            $attendanceTrend[] = [
                'month' => $month->format('M Y'),
                'attendance' => $monthlyAttendance
            ];
        }

        // Department wise employee count (using designation as department)
        $departmentStats = Employee::join('designations', 'employees.designation_id', '=', 'designations.id')
                                    ->select('designations.name as department', DB::raw('count(*) as count'))
                                    ->where('employees.status', 1)
                                    ->groupBy('designations.name')
                                    ->get();

        return view('hr.dashboard', compact(
            'totalEmployees',
            'newEmployeesThisMonth',
            'todayAttendance',
            'attendanceRate',
            'presentToday',
            'absentToday',
            'monthlyCosts',
            'todayCosts',
            'recentEmployees',
            'recentAttendances',
            'attendanceTrend',
            'departmentStats'
        ))->with('todayCost', $todayCosts);
    }

    /**
     * Get HR dashboard data for AJAX
     */
    public function getDashboardData()
    {
        $today = Carbon::today();
        $currentMonth = Carbon::now()->month;
        $currentYear = Carbon::now()->year;

        $data = [
            'total_employees' => Employee::where('status', 1)->count(),
            'today_attendance' => Attendance::whereDate('date', $today)->count(),
            'present_today' => Attendance::whereDate('date', $today)->where('status', 'present')->count(),
            'absent_today' => Attendance::whereDate('date', $today)->where('status', 'absent')->count(),
            'monthly_costs' => Attendance::whereMonth('date', $currentMonth)->whereYear('date', $currentYear)->sum('daily_cost'),
            'today_costs' => Attendance::whereDate('date', $today)->sum('daily_cost'),
        ];

        return response()->json($data);
    }
}
