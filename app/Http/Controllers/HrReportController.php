<?php

namespace App\Http\Controllers;

use App\Models\Employee;
use App\Models\Attendance;
use App\Models\Designation;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class HrReportController extends Controller
{
    public function __construct()
    {
        $this->middleware('permission:employees-read');
    }

    /**
     * Attendance Summary Report
     */
    public function attendanceSummary(Request $request)
    {
        $month = $request->get('month', now()->format('Y-m'));
        $year = $request->get('year', now()->year);
        $employee_id = $request->get('employee');

        // Parse month if provided
        if ($month) {
            $monthDate = Carbon::createFromFormat('Y-m', $month);
            $year = $monthDate->year;
            $monthNum = $monthDate->month;
        } else {
            $monthNum = now()->month;
        }

        // Base query
        $query = Attendance::with(['employee.designation'])
            ->whereYear('date', $year)
            ->whereMonth('date', $monthNum);

        if ($employee_id) {
            $query->where('employee_id', $employee_id);
        }

        $attendances = $query->get();

        // Group by employee
        $employeeStats = $attendances->groupBy('employee_id')->map(function ($group) {
            $employee = $group->first()->employee;

            // Skip if employee is null (deleted employee)
            if (!$employee) {
                return null;
            }

            return [
                'employee' => $employee,
                'total_days' => $group->count(),
                'present_days' => $group->where('status', 'present')->count(),
                'half_days' => $group->where('status', 'half_day')->count(),
                'absent_days' => $group->where('status', 'absent')->count(),
                'total_cost' => $group->sum('daily_cost'),
                'attendance_rate' => $group->count() > 0 ? round(($group->where('status', 'present')->count() / $group->count()) * 100, 1) : 0,
            ];
        })->filter(); // Remove null entries

        // Overall summary
        $summary = [
            'total_employees' => $employeeStats->count(),
            'total_attendance_records' => $attendances->count(),
            'total_present' => $attendances->where('status', 'present')->count(),
            'total_half_days' => $attendances->where('status', 'half_day')->count(),
            'total_absent' => $attendances->where('status', 'absent')->count(),
            'total_cost' => $attendances->sum('daily_cost'),
            'average_attendance_rate' => $employeeStats->avg('attendance_rate'),
        ];

        return view('hr.reports.attendance-summary', compact(
            'employeeStats', 
            'summary', 
            'month', 
            'year', 
            'employee_id'
        ));
    }

    /**
     * Employee Performance Report
     */
    public function employeePerformance(Request $request)
    {
        $year = $request->get('year', now()->year);
        $employee_id = $request->get('employee');

        // Get employees with their performance data
        $query = Employee::with(['designation', 'attendances' => function($q) use ($year) {
            $q->whereYear('date', $year);
        }]);

        if ($employee_id) {
            $query->where('id', $employee_id);
        }

        $employees = $query->where('status', 1)->get();

        $performanceData = $employees->map(function ($employee) {
            $attendances = $employee->attendances;
            $totalDays = $attendances->count();
            $presentDays = $attendances->where('status', 'present')->count();
            $halfDays = $attendances->where('status', 'half_day')->count();
            $absentDays = $attendances->where('status', 'absent')->count();

            return [
                'employee' => $employee,
                'total_days' => $totalDays,
                'present_days' => $presentDays,
                'half_days' => $halfDays,
                'absent_days' => $absentDays,
                'attendance_rate' => $totalDays > 0 ? round(($presentDays / $totalDays) * 100, 1) : 0,
                'total_cost' => $attendances->sum('daily_cost'),
                'monthly_avg_cost' => $attendances->sum('daily_cost') / 12,
                'performance_score' => $this->calculatePerformanceScore($presentDays, $halfDays, $absentDays, $totalDays),
            ];
        });

        return view('hr.reports.employee-performance', compact('performanceData', 'year', 'employee_id'));
    }

    /**
     * Cost Analysis Report
     */
    public function costAnalysis(Request $request)
    {
        $year = $request->get('year', now()->year);
        $month = $request->get('month');

        // Monthly cost breakdown
        $monthlyCosts = Attendance::selectRaw('
                MONTH(date) as month,
                YEAR(date) as year,
                SUM(daily_cost) as total_cost,
                COUNT(*) as total_records,
                AVG(daily_cost) as avg_daily_cost
            ')
            ->whereYear('date', $year)
            ->when($month, function($q) use ($month) {
                $monthDate = Carbon::createFromFormat('Y-m', $month);
                return $q->whereMonth('date', $monthDate->month);
            })
            ->groupBy('year', 'month')
            ->orderBy('month')
            ->get();

        // Department-wise costs
        $departmentCosts = Attendance::join('employees', 'attendances.employee_id', '=', 'employees.id')
            ->join('designations', 'employees.designation_id', '=', 'designations.id')
            ->selectRaw('
                designations.name as department,
                SUM(attendances.daily_cost) as total_cost,
                COUNT(attendances.id) as total_records,
                AVG(attendances.daily_cost) as avg_cost
            ')
            ->whereYear('attendances.date', $year)
            ->when($month, function($q) use ($month) {
                $monthDate = Carbon::createFromFormat('Y-m', $month);
                return $q->whereMonth('attendances.date', $monthDate->month);
            })
            ->groupBy('designations.id', 'designations.name')
            ->get();

        // Top cost employees
        $topCostEmployees = Attendance::join('employees', 'attendances.employee_id', '=', 'employees.id')
            ->join('designations', 'employees.designation_id', '=', 'designations.id')
            ->selectRaw('
                employees.id,
                employees.name,
                employees.employee_id,
                designations.name as designation,
                SUM(attendances.daily_cost) as total_cost,
                COUNT(attendances.id) as total_days,
                AVG(attendances.daily_cost) as avg_daily_cost
            ')
            ->whereYear('attendances.date', $year)
            ->when($month, function($q) use ($month) {
                $monthDate = Carbon::createFromFormat('Y-m', $month);
                return $q->whereMonth('attendances.date', $monthDate->month);
            })
            ->groupBy('employees.id', 'employees.name', 'employees.employee_id', 'designations.name')
            ->orderByDesc('total_cost')
            ->take(10)
            ->get();

        // Summary
        $summary = [
            'total_cost' => $monthlyCosts->sum('total_cost'),
            'avg_monthly_cost' => $monthlyCosts->avg('total_cost'),
            'highest_month_cost' => $monthlyCosts->max('total_cost'),
            'lowest_month_cost' => $monthlyCosts->min('total_cost'),
        ];

        return view('hr.reports.cost-analysis', compact(
            'monthlyCosts', 
            'departmentCosts', 
            'topCostEmployees', 
            'summary', 
            'year', 
            'month'
        ));
    }

    /**
     * Calculate performance score based on attendance
     */
    private function calculatePerformanceScore($present, $halfDays, $absent, $total)
    {
        if ($total == 0) return 0;

        $presentScore = ($present / $total) * 100;
        $halfDayScore = ($halfDays / $total) * 50;
        $absentPenalty = ($absent / $total) * 20;

        $score = $presentScore + $halfDayScore - $absentPenalty;
        
        return max(0, min(100, round($score, 1)));
    }
}
