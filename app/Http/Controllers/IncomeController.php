<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Income;
use Illuminate\Http\Request;

class IncomeController extends Controller
{
    public function __construct()
    {
        $this->middleware('permission:income-create')->only('maanIncomeStore');
        $this->middleware('permission:income-read')->only('maanIncomeIndex');
        $this->middleware('permission:income-update')->only('maanIncomeEdit', 'maanIncomeUpdate');
        $this->middleware('permission:income-delete')->only('maanIncomeDelete');
    }
    /**
     * Display a listing of the Income.
     */
    public function maanIncomeIndex()
    {
        $incomes = Income::latest()->paginate(10); // income info
        return view('pages.accounts.general.income.index',compact('incomes'));
    }

    public function maanIncomeStore( Request $request)
    {
        $request->validate([
            'category_name'         => 'required|string|unique:incomes',
            'income_description'    => 'nullable|string',
        ]);

        $income = Income::create($request->all());
        sendNotification($income->id, route('income.index'), __('New income has been created.'));
        return response()->json([
            'message'   => __('Income created successfully'),
            'redirect'  => route('income.index')
        ]);
    }

    public function maanIncomeEdit($id)
    {
        $income = Income::findOrFail($id);
        return view('pages.accounts.general.income.edit',compact('income'));
    }

    public function maanIncomeUpdate(Request $request, $id)
    {
        $request->validate([
            'category_name' => 'required|string|unique:incomes,category_name,'.$id,
            'income_description' => 'nullable|string',
        ]);

        $income = Income::findOrFail($id);

        if ($income->party_id) {
            return response()->json([
                'message'=> __('You can not update / delete this from here, because this is generated by an order.'),
            ], 406);
        }
        
        $income->update($request->all());
        return response()->json([
            'message'   => __('Income updated successfully'),
            'redirect'  => route('income.index')
        ]);
    }

    public function maanIncomeDelete($id)
    {
        $income = Income::findOrFail($id);

        if ($income->party_id) {
            return response()->json([
                'message'=> __('You can not update / delete this from, because this is generated by an order.'),
            ], 406);
        }

        $income->delete();
        return response()->json([
            'message'   => __('Income deleted successfully'),
            'redirect'  => route('income.index')
        ]);
    }

}
