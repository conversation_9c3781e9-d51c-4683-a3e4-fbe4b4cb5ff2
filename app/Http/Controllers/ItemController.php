<?php

namespace App\Http\Controllers;

use App\Models\Item;
use Illuminate\Http\Request;
use Carbon\Carbon;

class ItemController extends Controller
{
    public function index(Request $request)
    {
        $query = Item::with('itemSizes');

        // Apply date range filter if provided
        if ($request->filled('from_date')) {
            $query->whereDate('date', '>=', $request->from_date);
        }

        if ($request->filled('to_date')) {
            $query->whereDate('date', '<=', $request->to_date);
        }

        // Apply month filter if provided
        if ($request->filled('month')) {
            $month = Carbon::parse($request->month);
            $query->whereMonth('date', $month->month)
                  ->whereYear('date', $month->year);
        }

        // Apply search filter if provided
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('id', 'like', "%{$search}%")
                  ->orWhereHas('itemSizes', function($subQuery) use ($search) {
                      $subQuery->where('color', 'like', "%{$search}%")
                               ->orWhere('part', 'like', "%{$search}%")
                               ->orWhere('size', 'like', "%{$search}%")
                               ->orWhere('type', 'like', "%{$search}%")
                               ->orWhere('style', 'like', "%{$search}%");
                  });
            });
        }

        // Get total count for the filtered results (before pagination)
        $filteredCount = $query->count();

        $items = $query->orderBy('date', 'desc')->paginate(10);

        // Calculate monthly total quantity
        $monthlyData = $this->calculateMonthlyQuantity($request);

        // Calculate date range statistics for quick filters
        $dateRangeStats = $this->calculateDateRangeStatistics($request);

        // Preserve query parameters in pagination links
        $items->appends($request->query());

        // Set the pagination path to use filter route when filters are active
        if ($request->filled('from_date') || $request->filled('to_date') ||
            $request->filled('month') || $request->filled('search')) {
            $items->withPath(route('items.filter'));
        }

        return view('items.index', compact('items', 'filteredCount', 'monthlyData', 'dateRangeStats'));
    }

    public function create()
    {
        return view('items.create');
    }

    public function store(Request $request)
    {
        $item = Item::create($request->only('date'));
        return redirect()->route('items.edit', $item->id); // Redirect to edit to add item sizes
    }

    public function show(Item $item)
    {
        return view('items.show', compact('item'));
    }

    public function edit(Item $item)
    {
        return view('items.edit', compact('item'));
    }

    public function update(Request $request, Item $item)
    {
        $item->update($request->only('date'));
        return redirect()->route('items.index');
    }

    public function destroy(Item $item)
    {
        $item->delete();
        return redirect()->route('items.index');
    }

    /**
     * Filter items based on request parameters
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function filter(Request $request)
    {
        $query = Item::with('itemSizes');

        // Apply date range filter if provided
        if ($request->filled('from_date')) {
            $query->whereDate('date', '>=', $request->from_date);
        }

        if ($request->filled('to_date')) {
            $query->whereDate('date', '<=', $request->to_date);
        }

        // Apply month filter if provided
        if ($request->filled('month')) {
            $month = Carbon::parse($request->month);
            $query->whereMonth('date', $month->month)
                  ->whereYear('date', $month->year);
        }

        // Apply search filter if provided
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('id', 'like', "%{$search}%")
                  ->orWhereHas('itemSizes', function($subQuery) use ($search) {
                      $subQuery->where('color', 'like', "%{$search}%")
                               ->orWhere('part', 'like', "%{$search}%")
                               ->orWhere('size', 'like', "%{$search}%")
                               ->orWhere('type', 'like', "%{$search}%")
                               ->orWhere('style', 'like', "%{$search}%");
                  });
            });
        }

        // Get total count for the filtered results (before pagination)
        $filteredCount = $query->count();

        $items = $query->orderBy('date', 'desc')->paginate($request->per_page ?? 10);

        // Calculate monthly total quantity
        $monthlyData = $this->calculateMonthlyQuantity($request);

        // Calculate date range statistics for quick filters
        $dateRangeStats = $this->calculateDateRangeStatistics($request);

        // Preserve query parameters in pagination links
        $items->appends($request->query());

        // Set the pagination path to use filter route when filters are active
        if ($request->filled('from_date') || $request->filled('to_date') ||
            $request->filled('month') || $request->filled('search')) {
            $items->withPath(route('items.filter'));
        }

        if ($request->ajax()) {
            return response()->json([
                'data' => view('items.data', compact('items'))->render(),
                'pagination' => $items->links()->render(),
                'filteredCount' => $filteredCount,
                'totalQuantity' => $items->sum(function($item) { return $item->itemSizes->sum('qty'); }),
                'uniqueColors' => $items->flatMap(function($item) { return $item->itemSizes; })->unique('color')->count(),
                'monthlyQuantity' => $monthlyData['quantity'],
                'monthlyLabel' => $monthlyData['label'],
                'dateRangeStats' => $dateRangeStats
            ]);
        }

        // For non-AJAX requests (like pagination), return the view with filtered data
        return view('items.index', compact('items', 'filteredCount', 'monthlyData', 'dateRangeStats'));
    }

    /**
     * Calculate monthly total quantity
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    private function calculateMonthlyQuantity(Request $request)
    {
        // Determine which month to calculate for
        if ($request->filled('month')) {
            // Use the selected month from the filter
            $targetMonth = Carbon::parse($request->month);
        } else {
            // Use current month if no month filter is applied
            $targetMonth = Carbon::now();
        }

        // Query items for the specific month
        $monthlyItems = Item::with('itemSizes')
            ->whereMonth('date', $targetMonth->month)
            ->whereYear('date', $targetMonth->year)
            ->get();

        // Calculate total quantity for the month
        $monthlyQuantity = $monthlyItems->sum(function($item) {
            return $item->itemSizes->sum('qty');
        });

        // Format the label
        $monthLabel = $targetMonth->format('F Y') . ' Qty';

        return [
            'quantity' => $monthlyQuantity,
            'label' => $monthLabel,
            'month' => $targetMonth->format('Y-m')
        ];
    }

    /**
     * Calculate date range statistics for quick filters
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    private function calculateDateRangeStatistics(Request $request)
    {
        // Determine if we have a specific date range from quick filters
        $fromDate = $request->filled('from_date') ? $request->from_date : null;
        $toDate = $request->filled('to_date') ? $request->to_date : null;
        $month = $request->filled('month') ? $request->month : null;

        // If no date filters are applied, return current filtered results stats
        if (!$fromDate && !$toDate && !$month) {
            return [
                'hasDateRange' => false,
                'label' => 'All Items',
                'count' => 0,
                'quantity' => 0,
                'uniqueColors' => 0
            ];
        }

        // Build query for the specific date range
        $query = Item::with('itemSizes');

        if ($fromDate && $toDate) {
            // Date range filter (for quick filters like Last 7 days, Last 30 days)
            $query->whereDate('date', '>=', $fromDate)
                  ->whereDate('date', '<=', $toDate);

            $from = Carbon::parse($fromDate);
            $to = Carbon::parse($toDate);
            $diffDays = $from->diffInDays($to) + 1;

            if ($diffDays == 7) {
                $label = 'Last 7 Days';
            } elseif ($diffDays >= 28 && $diffDays <= 31) {
                $label = 'Last 30 Days';
            } else {
                $label = $from->format('M j') . ' - ' . $to->format('M j');
            }
        } elseif ($month) {
            // Month filter (for This month, Last month)
            $monthDate = Carbon::parse($month);
            $query->whereMonth('date', $monthDate->month)
                  ->whereYear('date', $monthDate->year);

            $now = Carbon::now();
            if ($monthDate->isSameMonth($now)) {
                $label = 'This Month';
            } elseif ($monthDate->isSameMonth($now->subMonth())) {
                $label = 'Last Month';
            } else {
                $label = $monthDate->format('F Y');
            }
        } else {
            // Single date boundary
            if ($fromDate) {
                $query->whereDate('date', '>=', $fromDate);
                $label = 'From ' . Carbon::parse($fromDate)->format('M j');
            } elseif ($toDate) {
                $query->whereDate('date', '<=', $toDate);
                $label = 'Until ' . Carbon::parse($toDate)->format('M j');
            }
        }

        // Apply search filter if present (but not date filters)
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('id', 'like', "%{$search}%")
                  ->orWhereHas('itemSizes', function($subQuery) use ($search) {
                      $subQuery->where('color', 'like', "%{$search}%")
                               ->orWhere('part', 'like', "%{$search}%")
                               ->orWhere('size', 'like', "%{$search}%")
                               ->orWhere('type', 'like', "%{$search}%")
                               ->orWhere('style', 'like', "%{$search}%");
                  });
            });
        }

        $dateRangeItems = $query->get();

        return [
            'hasDateRange' => true,
            'label' => $label,
            'count' => $dateRangeItems->count(),
            'quantity' => $dateRangeItems->sum(function($item) {
                return $item->itemSizes->sum('qty');
            }),
            'uniqueColors' => $dateRangeItems->flatMap(function($item) {
                return $item->itemSizes;
            })->unique('color')->count()
        ];
    }
}
