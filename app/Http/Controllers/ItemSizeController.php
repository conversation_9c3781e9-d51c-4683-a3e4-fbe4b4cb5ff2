<?php

namespace App\Http\Controllers;

use App\Models\Item;
use App\Models\ItemSize;
use Illuminate\Http\Request;

class ItemSizeController extends Controller
{
    public function store(Request $request, Item $item)
    {
        $item->itemSizes()->create($request->all());
        return redirect()->back();
    }

    public function destroy(ItemSize $itemSize)
    {
        $itemSize->delete();
        return redirect()->back();
    }
}
