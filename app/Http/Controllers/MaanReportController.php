<?php

namespace App\Http\Controllers;

use App\Models\Accessory;
use App\Models\AccessoryOrder;
use App\Models\Expense;
use App\Models\Income;
use App\Models\Order;
use App\Models\Party;
use App\Models\Production;
use App\Models\Voucher;
use Illuminate\Http\Request;
use App\Models\Cutting;


class MaanReportController extends Controller
{
    public function order()
    {
        $orders = Order::with('party', 'merchandiser', 'bank')
            ->whereYear('created_at', now()->year)
            ->whereMonth('created_at', now()->month)
            ->latest()
            ->get();
        return view('pages.report.orders', compact('orders'));
    }
    public function cutting()
    {
        $cuttings = Cutting::whereYear('created_at', now()->year)
            ->whereMonth('created_at', now()->month)
            ->latest()
            ->get();
        return view('pages.report.cuttings', compact('cuttings'));
    }
    public function cuttingFilter(Request $request)
    {
        $cuttings = Cutting::when(request('days') == 'daily', function ($q) {
                $q->whereDate('created_at', now()->format('Y-m-d'));
            })
            ->when(request('size_30') != '', function ($q) {
                $q->where('size_30', '>=', request('size_30'));
            })
            ->when(request('size_32') != '', function ($q) {
                $q->where('size_32', '>=', request('size_32'));
            })
            ->when(request('size_34') != '', function ($q) {
                $q->where('size_34', '>=', request('size_34'));
            })
            ->when(request('size_36') != '', function ($q) {
                $q->where('size_36', '>=', request('size_36'));
            })
            ->when(request('size_38') != '', function ($q) {
                $q->where('size_38', '>=', request('size_38'));
            })
            ->when(request('days') == 'weekly', function ($q) {
                $q->whereBetween('created_at', [now()->startOfWeek()->format('Y-m-d'), now()->endOfWeek()->format('Y-m-d')]);
            })
            ->when(request('days') == 'monthly', function ($q) {
                $q->whereMonth('created_at', now()->format('m'));
            })
            ->when(request('days') == 'yearly', function ($q) {
                $q->whereYear('created_at', now()->format('Y'));
            })

            ->latest()
            ->get();
        return response()->json([
            'data' => view('pages.report.cutting-datas', compact('cuttings'))->render(),
        ]);
    }
    public function accessories()
    {
        $accessoryOrders = AccessoryOrder::whereYear('created_at', now()->year)
            ->whereMonth('created_at', now()->month)
            ->latest()
            ->get();
        $accessories = Accessory::all();
        $parties = Party::all();

        return view('pages.report.accessopries', compact('accessoryOrders', 'accessories', 'parties'));
    }
    public function accessoriesFilter(Request $request)
    {
        $accessoryOrders = AccessoryOrder::when(request('days') == 'daily', function ($q) {
                $q->whereDate('created_at', now()->format('Y-m-d'));
            })
            ->when(request('party_id') != '', function ($q) {
                //party id is multiple

                $q->whereIn('party_id', request('party_id')); // Updated to handle multiple party IDs
            })
            ->when(request('accessory_id') != '', function ($q) {
                $q->whereIn('accessory_id', request('accessory_id'));
            })
            //type
            ->when(request('type') != '', function ($q) {
                $q->where('type', request('type'));
            })
            ->when(request('days') == 'weekly', function ($q) {
                $q->whereBetween('created_at', [now()->startOfWeek()->format('Y-m-d'), now()->endOfWeek()->format('Y-m-d')]);
            })
            ->when(request('days') == 'monthly', function ($q) {
                $q->whereMonth('created_at', now()->format('m'));
            })
            ->when(request('days') == 'yearly', function ($q) {
                $q->whereYear('created_at', now()->format('Y'));
            })

            ->latest()
            ->get();
        return response()->json([
            'data' => view('pages.report.accessories-datas', compact('accessoryOrders'))->render(),
        ]);
    }

    public function orderFilter(Request $request)
    {
        $orders = Order::with('party', 'merchandiser', 'bank')
            ->when(request('days') == 'daily', function ($q) {
                $q->whereDate('created_at', now()->format('Y-m-d'));
            })
            ->when(request('days') == 'weekly', function ($q) {
                $q->whereBetween('created_at', [now()->startOfWeek()->format('Y-m-d'), now()->endOfWeek()->format('Y-m-d')]);
            })
            ->when(request('days') == 'monthly', function ($q) {
                $q->whereMonth('created_at', now()->format('m'));
            })
            ->when(request('days') == 'yearly', function ($q) {
                $q->whereYear('created_at', now()->format('Y'));
            })

            ->latest()
            ->get();
        return response()->json([
            'data' => view('pages.report.order-datas', compact('orders'))->render(),
        ]);
    }


    public function production()
    {
        $productions1 = Production::with('order:id,party_id,order_no', 'order.party:id,name,type')
            ->whereDate('created_at', now()->format('Y-m-d'))
            ->latest()
            ->get();
        $productions = collect($productions1)->groupBy('order_id');

        return view('pages.report.productions', compact('productions'));
    }

    public function productionFilter(Request $request)
    {
        $productions1 = Production::with('order:id,party_id,order_no', 'order.party:id,name,type')
            ->when(request('days') == 'daily', function ($q) {
                $q->whereDate('created_at', now()->format('Y-m-d'));
            })
            ->when(request('days') == 'weekly', function ($q) {
                $q->whereBetween('created_at', [now()->startOfWeek()->format('Y-m-d'), now()->endOfWeek()->format('Y-m-d')]);
            })
            ->when(request('days') == 'monthly', function ($q) {
                $q->whereMonth('created_at', now()->format('m'));
            })
            ->when(request('days') == 'yearly', function ($q) {
                $q->whereYear('created_at', now()->format('Y'));
            })
            ->latest()
            ->get();

        $productions = collect($productions1)->groupBy('order_id');

        return response()->json([
            'data' => view('pages.report.production-datas', compact('productions'))->render(),
        ]);
    }

    public function collection()
    {
        $incomes = Income::with('party')->whereHas('party')
            ->whereYear('created_at', now()->year)
            ->whereMonth('created_at', now()->month)
            ->latest()
            ->get();

        return view('pages.report.due-collections.index', compact('incomes'));
    }

    public function collectionFilter(Request $request)
    {
        $incomes = Income::with('party')
            ->where('total_bill', '>', 0)
            ->when($request->from_date, function ($q) {
                $q->whereDate('created_at', '>=', request('from_date'));
            })
            ->when($request->to_date, function ($q) {
                $q->whereDate('created_at', '<=', request('to_date'));
            })
            ->latest()
            ->get();

        return response()->json([
            'data' => view('pages.report.due-collections.datas', compact('incomes'))->render(),
        ]);
    }

    public function transaction()
    {
        $transactions = Voucher::whereIn('type', ['debit', 'credit'])
            ->with([
                'income' => function ($query) {
                    $query->select('id', 'category_name');
                },
                'expense' => function ($query) {
                    $query->select('id', 'category_name');
                },
                'party' => function ($query) {
                    $query->select('id', 'name', 'type');
                },
                'user' => function ($query) {
                    $query->select('id', 'name');
                },
            ])
            ->whereYear('created_at', now()->year)
            ->whereMonth('created_at', now()->month)
            ->latest()
            ->get();
            $parties = Party::all();
            $expenses = Expense::all();
        return view('pages.report.transactions', compact('transactions','parties','expenses'));
    }

    public function transactionFilter(Request $request)
    {
        $transactions = Voucher::whereIn('type', ['debit', 'credit'])->with([
            'income' => function ($query) {
                $query->select('id', 'category_name');
            },
            'expense' => function ($query) {
                $query->select('id', 'category_name');
            },
            'party' => function ($query) {
                $query->select('id', 'name', 'type');
            },
            'user' => function ($query) {
                $query->select('id', 'name');
            },
        ])
            ->when($request->from_date, function ($q) {
                $q->whereDate('date', '>=', request('from_date'));
            })
            ->when($request->type, function ($q) {
                $q->where('type', request('type'));
            })
            ->when(request('expense_id') != '', function ($q) {
                $q->where(function ($subQuery) {
                    $subQuery->whereIn('expense_id', request('expense_id'));
                });
            })
            ->when(request('party_id') != '', function ($q) {
                $q->where(function ($subQuery) {
                    $subQuery->whereIn('party_id', request('party_id'));

                    if (in_array('-', request('party_id'))) {
                        $subQuery->orWhereNull('party_id');
                    }
                });
            })
            ->when($request->to_date, function ($q) {
                $q->whereDate('date', '<=', request('to_date'));
            })
            ->latest()
            ->get();

        return response()->json([
            'data' => view('pages.report.transaction-datas', compact('transactions'))->render(),
        ]);
    }

    public function cashbook()
    {
        $vouchers = Voucher::where('payment_method', 'cash')
            ->whereIn('type', ['debit', 'credit'])
            ->whereDate('created_at', today())
            ->with('income', 'expense')
            ->get();

        $yesterday_data = Voucher::where('payment_method', 'cash')
            ->whereIn('type', ['debit', 'credit'])
            ->whereDate('created_at', '<', today())
            ->with('income', 'expense')
            ->latest()
            ->first();

        return view('pages.report.daily-cashbook', compact('vouchers', 'yesterday_data'));
    }

    public function cashbookFilter(Request $request)
    {
        $vouchers = Voucher::where('payment_method', 'cash')
            ->whereIn('type', ['debit', 'credit'])
            ->with('income', 'expense')
            ->when($request->from_date, function ($q) {
                $q->whereDate('date', '>=', request('from_date'));
            })
            ->when($request->to_date, function ($q) {
                $q->whereDate('date', '<=', request('to_date'));
            })
            ->get();

        $yesterday_data = Voucher::where('payment_method', 'cash')
            ->whereIn('type', ['debit', 'credit'])
            ->whereDate('created_at', '<', today())
            ->with('income', 'expense')
            ->latest()
            ->first();

        return response()->json([
            'data' => view('pages.report.daily-cashbook-datas', compact('vouchers', 'yesterday_data'))->render(),
        ]);
    }

    public function payableDues()
    {
        $expenses = Expense::with('party')->whereHas('party')
            ->whereYear('created_at', now()->year)
            ->whereMonth('created_at', now()->month)
            ->latest()
            ->get();

        return view('pages.report.payable-dues.index', compact('expenses'));
    }

    public function payableDuesFilter(Request $request)
    {
        $expenses = Expense::with('party')
            ->where('total_bill', '>', 0)
            ->when($request->from_date, function ($q) {
                $q->whereDate('created_at', '>=', request('from_date'));
            })
            ->when($request->to_date, function ($q) {
                $q->whereDate('created_at', '<=', request('to_date'));
            })
            ->latest()
            ->get();

        return response()->json([
            'data' => view('pages.report.payable-dues.datas', compact('expenses'))->render(),
        ]);
    }

    public function partyDues()
    {
        $due_amount = Party::whereType(request('party_type'))->sum('due_amount');
        $parties = Party::with('user')->whereType(request('party_type'))
            ->where('due_amount', '>', 0)
            ->latest()
            ->paginate();

        return view('pages.report.party-dues.index', compact('parties', 'due_amount'));
    }

    public function partyDuesFilter(Request $request)
    {
        $parties = Party::with('user')->where('due_amount', '>', 0)
            ->when($request->per_page, function ($query) use ($request) {
                $query->where('type', $request->party_type);
            })
            ->when($request->search, function ($q) use ($request) {
                $q->where(function ($query) use ($request) {
                    $query->where('name', 'like', '%' . $request->search . '%')
                        ->orWhere('remarks', 'like', '%' . $request->search . '%');
                });
            })
            ->latest()
            ->paginate($request->per_page ?? 10);

        if ($request->ajax()) {
            return response()->json([
                'data' => view('pages.report.party-dues.datas', compact('parties'))->render(),
            ]);
        }

        return redirect(url()->previous());
    }
}
