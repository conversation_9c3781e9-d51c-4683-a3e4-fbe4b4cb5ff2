<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Helpers\HasUploader;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;

class MannUserProfileController extends Controller
{
    use HasUploader;

    public function index()
    {
        $user = User::where('id',Auth::user()->id)->first();
        return view('pages.profile.index',compact('user'));
    }

    public function update(Request $request, $id)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email',
            'image' => 'nullable|image',
        ]);
        $user = User::findOrFail($id);

        if($request->password || $request->current_password){
            if(Hash::check($request->current_password,$user->password)){
                $request->validate([
                    'current_password' => 'required|string',
                    'password' => 'required|string|confirmed',
                ]);
            } else {
                return response()->json(__('Current Password does not match with old password'),404);
            }
        }
        $user->update($request->except('image','password') + [
            'image'     => $request->image ? $this->upload($request, 'image', $user->image) : $user->image,
            'password'  => $request->password ? Hash::make($request->password) : $user->password,
        ]);

        return response()->json([
            'message'   => __('User updated successfully'),
            'redirect'  => route('user-profile.index')
        ]);
    }
}
