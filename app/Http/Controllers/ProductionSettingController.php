<?php

namespace App\Http\Controllers;

use App\Models\ProductionSetting;
use Illuminate\Http\Request;

class ProductionSettingController extends Controller
{
    public function __construct()
    {
        // Add any middleware if needed
    }

    public function index()
    {
        $settings = ProductionSetting::getProductionTargetSettings();
        return view('pages.production-settings.index', compact('settings'));
    }

    public function update(Request $request)
    {
        $request->validate([
            'daily_floor_cost' => 'required|numeric|min:0',
            'daily_electricity_cost' => 'required|numeric|min:0',
            'daily_depreciation_cost' => 'required|numeric|min:0',
            'per_piece_cost' => 'required|numeric|min:0.01',
        ]);

        ProductionSetting::setValue('daily_floor_cost', $request->daily_floor_cost, 'Per day floor cost');
        ProductionSetting::setValue('daily_electricity_cost', $request->daily_electricity_cost, 'Per day electricity cost');
        ProductionSetting::setValue('daily_depreciation_cost', $request->daily_depreciation_cost, 'Per day depreciation cost');
        ProductionSetting::setValue('per_piece_cost', $request->per_piece_cost, 'Per piece cost in taka');

        return response()->json([
            'message' => 'Production settings updated successfully!',
            'redirect' => route('production-settings.index')
        ]);
    }
}
