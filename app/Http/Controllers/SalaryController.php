<?php

namespace App\Http\Controllers;

use App\Models\Employee;
use App\Models\Salary;
use App\Models\AdvanceSalary;
use App\Models\Attendance;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class SalaryController extends Controller
{
    public function __construct()
    {
        $this->middleware('permission:salaries-read')->only('index', 'show');
        $this->middleware('permission:salaries-create')->only('create', 'store');
        $this->middleware('permission:salaries-update')->only('edit', 'update');
        $this->middleware('permission:salaries-delete')->only('destroy');
    }

    /**
     * Display a listing of salaries
     */
    public function index(Request $request)
    {
        $query = Salary::with(['employee.designation']);

        // Filter by month/year
        if ($request->filled('month') && $request->filled('year')) {
            $query->where('month', $request->month)
                  ->where('year', $request->year);
        }

        // Filter by employee
        if ($request->filled('employee_id')) {
            $query->where('employee_id', $request->employee_id);
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        $salaries = $query->latest('year')->latest('month')->paginate(20);
        $employees = Employee::where('status', 1)->get();

        return view('pages.salary.index', compact('salaries', 'employees'));
    }

    /**
     * Show the form for generating salary
     */
    public function generate()
    {
        $employees = Employee::where('status', 1)->get();
        return view('pages.salary.generate', compact('employees'));
    }

    /**
     * Show the form for creating a new salary
     */
    public function create()
    {
        $employees = Employee::where('status', 1)->get();
        return view('pages.salary.create', compact('employees'));
    }

    /**
     * Store a newly created salary
     */
    public function store(Request $request)
    {
        $request->validate([
            'employee_id' => 'required|exists:employees,id',
            'year' => 'required|integer|min:2020|max:2030',
            'month' => 'required|integer|min:1|max:12',
            'base_salary' => 'required|numeric|min:0',
            'overtime_hours' => 'nullable|numeric|min:0',
            'attendance_bonus' => 'nullable|numeric|min:0',
            'advance_amount' => 'nullable|numeric|min:0',
        ]);

        // Check if salary already exists for this employee and month
        $existingSalary = Salary::where('employee_id', $request->employee_id)
                                ->where('year', $request->year)
                                ->where('month', $request->month)
                                ->first();

        if ($existingSalary) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Salary for this employee and month already exists.'
                ], 422);
            }
            return back()->withErrors(['month' => 'Salary for this employee and month already exists.']);
        }

        $employee = Employee::find($request->employee_id);

        try {
            // Calculate comprehensive salary using enhanced system
            $salaryData = Salary::calculateComprehensiveSalary(
                $employee,
                $request->year,
                $request->month,
                $request->overtime_hours ?? 0,
                $request->advance_amount ?? 0,
                $request->other_deductions ?? 0
            );
        } catch (\Exception $e) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Error calculating salary: ' . $e->getMessage()
                ], 500);
            }
            return back()->withErrors(['error' => 'Error calculating salary: ' . $e->getMessage()]);
        }

        // Override base salary if provided
        if ($request->base_salary) {
            $salaryData['base_salary'] = $request->base_salary;
            // Recalculate with new base salary
            $workingDaysPerMonth = \App\Models\SalarySetting::get('working_days_per_month', 30);
            $dailyRate = $request->base_salary / $workingDaysPerMonth;
            $attendanceBasedSalary = $dailyRate * $salaryData['attendance_days'];
            $salaryData['calculated_salary'] = $attendanceBasedSalary + $salaryData['overtime_amount'] + $salaryData['attendance_bonus'];
            $salaryData['net_salary'] = $salaryData['calculated_salary'] - $salaryData['advance_amount'] - $salaryData['other_deductions'];
        }

        try {
            $salary = Salary::create([
                'employee_id' => $request->employee_id,
                'user_id' => auth()->id(),
                'year' => $request->year,
                'month' => $request->month,
                'month_number' => $request->month,
                'base_salary' => $salaryData['base_salary'],
                'attendance_days' => $salaryData['attendance_days'],
                'total_working_days' => $salaryData['total_working_days'],
                'absent_days' => $salaryData['absent_days'],
                'late_days' => $salaryData['late_days'],
                'overtime_hours' => $salaryData['overtime_hours'],
                'overtime_rate' => $salaryData['overtime_rate'],
                'overtime_amount' => $salaryData['overtime_amount'],
                'attendance_bonus' => $salaryData['attendance_bonus'],
                'attendance_bonus_eligible' => $salaryData['attendance_bonus_eligible'],
                'calculated_salary' => $salaryData['calculated_salary'],
                'advance_amount' => $salaryData['advance_amount'],
                'other_deductions' => $salaryData['other_deductions'],
                'net_salary' => $salaryData['net_salary'],
                'payment_status' => 'unpaid',
                'notes' => $request->notes,
                'remarks' => $request->remarks,
            ]);
        } catch (\Exception $e) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Error creating salary record: ' . $e->getMessage()
                ], 500);
            }
            return back()->withErrors(['error' => 'Error creating salary record: ' . $e->getMessage()]);
        }

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Salary created successfully.',
                'salary' => $salary
            ]);
        }

        return redirect()->route('salaries.index')->with('success', 'Salary created successfully.');
    }

    /**
     * Display the specified salary
     */
    public function show(Salary $salary)
    {
        $salary->load(['employee.designation']);
        return view('pages.salary.show', compact('salary'));
    }

    /**
     * Show the form for editing the specified salary
     */
    public function edit(Salary $salary)
    {
        $employees = Employee::where('status', 1)->get();
        return view('pages.salary.edit', compact('salary', 'employees'));
    }

    /**
     * Update the specified salary
     */
    public function update(Request $request, Salary $salary)
    {
        $request->validate([
            'base_salary' => 'required|numeric|min:0',
            'attendance_days' => 'nullable|numeric|min:0|max:31',
            'overtime_hours' => 'nullable|numeric|min:0',
            'attendance_bonus' => 'nullable|numeric|min:0',
            'advance_amount' => 'nullable|numeric|min:0',
            'other_deductions' => 'nullable|numeric|min:0',
        ]);

        // Get comprehensive salary calculation
        $salaryData = Salary::calculateComprehensiveSalary(
            $salary->employee,
            $salary->year,
            $salary->month,
            $request->overtime_hours ?? 0,
            $request->advance_amount ?? 0,
            $request->other_deductions ?? 0
        );

        // Override with manual inputs if provided
        if ($request->base_salary) {
            $salaryData['base_salary'] = $request->base_salary;
        }
        if ($request->attendance_days) {
            $salaryData['attendance_days'] = $request->attendance_days;
        }
        if ($request->attendance_bonus !== null) {
            $salaryData['attendance_bonus'] = $request->attendance_bonus;
            $salaryData['attendance_bonus_eligible'] = $request->attendance_bonus > 0;
        }

        // Recalculate totals with manual overrides
        $workingDaysPerMonth = \App\Models\SalarySetting::get('working_days_per_month', 30);
        $dailyRate = $salaryData['base_salary'] / $workingDaysPerMonth;
        $attendanceBasedSalary = $dailyRate * $salaryData['attendance_days'];
        $salaryData['calculated_salary'] = $attendanceBasedSalary + $salaryData['overtime_amount'] + $salaryData['attendance_bonus'];
        $salaryData['net_salary'] = $salaryData['calculated_salary'] - $salaryData['advance_amount'] - $salaryData['other_deductions'];

        $salary->update([
            'base_salary' => $salaryData['base_salary'],
            'attendance_days' => $salaryData['attendance_days'],
            'overtime_hours' => $salaryData['overtime_hours'],
            'overtime_rate' => $salaryData['overtime_rate'],
            'overtime_amount' => $salaryData['overtime_amount'],
            'attendance_bonus' => $salaryData['attendance_bonus'],
            'attendance_bonus_eligible' => $salaryData['attendance_bonus_eligible'],
            'calculated_salary' => $salaryData['calculated_salary'],
            'advance_amount' => $salaryData['advance_amount'],
            'other_deductions' => $salaryData['other_deductions'],
            'net_salary' => $salaryData['net_salary'],
            'notes' => $request->notes,
            'remarks' => $request->remarks,
        ]);

        return redirect()->route('salaries.index')->with('success', 'Salary updated successfully.');
    }

    /**
     * Remove the specified salary
     */
    public function destroy(Salary $salary)
    {
        $salary->delete();
        return redirect()->route('salaries.index')->with('success', 'Salary deleted successfully.');
    }

    /**
     * Mark salary as paid with payment processing
     */
    public function markAsPaid(Request $request, Salary $salary)
    {
        $request->validate([
            'payment_account_id' => 'required|exists:banks,id',
            'payment_reference' => 'nullable|string|max:255',
        ]);

        $paymentAccount = \App\Models\Bank::find($request->payment_account_id);

        // Check if account has sufficient balance
        if ($paymentAccount->balance < $salary->net_salary) {
            return response()->json([
                'success' => false,
                'message' => 'Insufficient balance in selected account.',
            ], 400);
        }

        // Process payment transaction
        try {
            \DB::transaction(function () use ($salary, $paymentAccount, $request) {
                // Deduct amount from bank account
                $paymentAccount->decrement('balance', $salary->net_salary);

                // Update salary record
                $salary->update([
                    'payment_status' => 'paid',
                    'payment_date' => now(),
                    'payment_account_id' => $request->payment_account_id,
                    'payment_reference' => $request->payment_reference,
                ]);

                // Create accounting entry (voucher)
                \App\Models\Voucher::create([
                    'type' => 'expense',
                    'date' => now(),
                    'amount' => $salary->net_salary,
                    'bank_id' => $request->payment_account_id,
                    'user_id' => auth()->id(),
                    'particulars' => "Salary payment for {$salary->employee->name} - " .
                                   date('F Y', mktime(0, 0, 0, $salary->month, 1, $salary->year)),
                    'payment_method' => 'bank',
                    'bill_amount' => $salary->net_salary,
                    'current_balance' => $paymentAccount->balance,
                    'prev_balance' => $paymentAccount->balance + $salary->net_salary,
                    'meta' => json_encode([
                        'salary_id' => $salary->id,
                        'employee_id' => $salary->employee_id,
                        'payment_reference' => $request->payment_reference,
                    ]),
                ]);
            });

            return response()->json([
                'success' => true,
                'message' => 'Salary payment processed successfully.',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Payment processing failed: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Calculate attendance-based salary
     */
    private function calculateAttendanceBasedSalary($employee, $year, $month)
    {
        $startDate = Carbon::create($year, $month, 1)->startOfMonth();
        $endDate = Carbon::create($year, $month, 1)->endOfMonth();

        $attendances = Attendance::where('employee_id', $employee->id)
                                ->whereBetween('date', [$startDate, $endDate])
                                ->get();

        $presentDays = $attendances->where('status', 'present')->count();
        $halfDays = $attendances->where('status', 'half_day')->count();
        $totalDays = $startDate->daysInMonth;

        // Half days count as 0.5
        $effectivePresentDays = $presentDays + ($halfDays * 0.5);

        return [
            'present_days' => $effectivePresentDays,
            'total_days' => $totalDays,
            'attendance_percentage' => ($effectivePresentDays / $totalDays) * 100,
        ];
    }

    /**
     * AJAX: Get employee salary info with enhanced data
     */
    public function getEmployeeInfo(Request $request)
    {
        $employee = Employee::with('designation')->find($request->employee_id);

        if (!$employee) {
            return response()->json(['success' => false, 'message' => 'Employee not found']);
        }

        $year = $request->year ?? date('Y');
        $month = $request->month ?? date('n');

        // Get comprehensive attendance data
        $attendanceData = Salary::getAttendanceData($employee, $year, $month);

        return response()->json([
            'success' => true,
            'employee' => [
                'id' => $employee->id,
                'name' => $employee->name,
                'designation' => $employee->designation->name ?? 'N/A',
                'basic_salary' => $employee->basic_salary ?? $employee->salary,
                'attendance_days' => $attendanceData['present_days'],
                'total_working_days' => $attendanceData['total_days'],
                'absent_days' => $attendanceData['absent_days'],
                'late_days' => $attendanceData['late_days'],
                'half_days' => $attendanceData['half_days'],
                'attendance_percentage' => round($attendanceData['attendance_percentage'], 2),
            ]
        ]);
    }

    /**
     * AJAX: Get available bank accounts for payment
     */
    public function getPaymentAccounts()
    {
        $accounts = \App\Models\Bank::where('status', 1)
                                   ->select('id', 'bank_name', 'account_number', 'balance')
                                   ->get();

        return response()->json([
            'success' => true,
            'accounts' => $accounts
        ]);
    }

    /**
     * AJAX: Calculate salary preview with enhanced calculation
     */
    public function calculatePreview(Request $request)
    {
        $baseSalary = $request->base_salary ?? 0;
        $attendanceDays = $request->attendance_days ?? 26;
        $overtimeHours = $request->overtime_hours ?? 0;
        $attendanceBonus = $request->attendance_bonus ?? 0;
        $advanceAmount = $request->advance_amount ?? 0;
        $otherDeductions = $request->other_deductions ?? 0;

        // Get salary settings
        $workingDaysPerMonth = \App\Models\SalarySetting::get('working_days_per_month', 30);
        $overtimeRate = \App\Models\SalarySetting::get('overtime_rate_per_hour', 50);

        // Calculate components
        $dailyRate = $baseSalary / $workingDaysPerMonth;
        $attendanceBasedSalary = $dailyRate * $attendanceDays;
        $overtimeAmount = $overtimeHours * $overtimeRate;
        $calculatedSalary = $attendanceBasedSalary + $overtimeAmount + $attendanceBonus;
        $finalSalary = $calculatedSalary - $advanceAmount - $otherDeductions;

        return response()->json([
            'success' => true,
            'calculation' => [
                'base_salary' => number_format($baseSalary, 2),
                'attendance_days' => $attendanceDays,
                'daily_rate' => number_format($dailyRate, 2),
                'attendance_based_salary' => number_format($attendanceBasedSalary, 2),
                'overtime_hours' => $overtimeHours,
                'overtime_rate' => number_format($overtimeRate, 2),
                'overtime_amount' => number_format($overtimeAmount, 2),
                'attendance_bonus' => number_format($attendanceBonus, 2),
                'calculated_salary' => number_format($calculatedSalary, 2),
                'advance_amount' => number_format($advanceAmount, 2),
                'other_deductions' => number_format($otherDeductions, 2),
                'final_salary' => number_format($finalSalary, 2),
            ]
        ]);
    }

    /**
     * Print individual salary report
     */
    public function printIndividual(Request $request)
    {
        $salary = Salary::with(['employee.designation', 'paymentAccount'])->find($request->salary_id);

        if (!$salary) {
            abort(404, 'Salary record not found.');
        }

        return view('pages.salary.print', compact('salary'));
    }
}
