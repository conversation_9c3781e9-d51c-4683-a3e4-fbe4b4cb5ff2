<?php

namespace App\Http\Controllers\Stock;

use App\Http\Controllers\Controller;
use App\Models\Stock\StockAlert;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class StockAlertController extends Controller
{
    public function __construct()
    {
        // Permission middleware removed - accessible to all authenticated users
    }

    /**
     * Display a listing of stock alerts
     */
    public function index(Request $request)
    {
        try {
            // Get filter parameters
            $alertType = $request->get('alert_type');
            $priority = $request->get('priority');
            $status = $request->get('status', 'active');
            $itemId = $request->get('item_id');
            $locationId = $request->get('location_id');

            // Build query using raw DB queries
            $query = DB::table('stock_alerts')
                ->leftJoin('stock_items', 'stock_alerts.item_id', '=', 'stock_items.id')
                ->leftJoin('stock_locations', 'stock_alerts.location_id', '=', 'stock_locations.id')
                ->select(
                    'stock_alerts.*',
                    'stock_items.name as item_name',
                    'stock_items.sku as item_sku',
                    'stock_locations.name as location_name',
                    'stock_locations.code as location_code'
                )
                ->orderBy('stock_alerts.priority', 'desc')
                ->orderBy('stock_alerts.alert_date', 'desc');

            // Apply filters
            if ($alertType) {
                $query->where('stock_alerts.alert_type', $alertType);
            }

            if ($priority) {
                $query->where('stock_alerts.priority', $priority);
            }

            if ($status) {
                $query->where('stock_alerts.status', $status);
            }

            if ($itemId) {
                $query->where('stock_alerts.item_id', $itemId);
            }

            if ($locationId) {
                $query->where('stock_alerts.location_id', $locationId);
            }

            // Get paginated results
            $alerts = $query->paginate(50);

            // Convert to objects for view compatibility
            $alerts->getCollection()->transform(function ($alert) {
                $alert->item = (object) [
                    'name' => $alert->item_name,
                    'sku' => $alert->item_sku
                ];
                $alert->location = (object) [
                    'name' => $alert->location_name,
                    'code' => $alert->location_code
                ];
                $alert->alert_date = \Carbon\Carbon::parse($alert->alert_date);
                return $alert;
            });

            // Get summary statistics
            $statistics = $this->getAlertStatistics();

            // Get filter options
            $items = DB::table('stock_items')
                ->where('is_active', true)
                ->orderBy('name')
                ->get();

            $locations = DB::table('stock_locations')
                ->where('is_active', true)
                ->orderBy('name')
                ->get();

            return view('stock.alerts.index', compact(
                'alerts',
                'statistics',
                'items',
                'locations',
                'alertType',
                'priority',
                'status',
                'itemId',
                'locationId'
            ));

        } catch (\Exception $e) {
            \Log::error('Error in stock alerts index: ' . $e->getMessage());
            return view('pages.stock.alerts.index-standalone', [
                'alerts' => collect(),
                'statistics' => $this->getDefaultStatistics(),
                'items' => collect(),
                'locations' => collect(),
                'alertType' => null,
                'priority' => null,
                'status' => 'active',
                'itemId' => null,
                'locationId' => null
            ]);
        }
    }

    /**
     * Display the specified stock alert
     */
    public function show(StockAlert $stockAlert)
    {
        $stockAlert->load(['item', 'location', 'acknowledger', 'resolver']);

        return view('stock.alerts.show', compact('stockAlert'));
    }

    /**
     * Show the form for creating a new stock alert
     */
    public function create()
    {
        $items = DB::table('stock_items')
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        $locations = DB::table('stock_locations')
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        return view('stock.alerts.create', compact('items', 'locations'));
    }

    /**
     * Store a newly created stock alert
     */
    public function store(Request $request)
    {
        try {
            $validatedData = $request->validate([
                'item_id' => 'required|exists:stock_items,id',
                'location_id' => 'nullable|exists:stock_locations,id',
                'alert_type' => 'required|in:low_stock,out_of_stock,overstock,expiry_warning,expiry_critical,negative_stock',
                'title' => 'required|string|max:200',
                'message' => 'required|string',
                'priority' => 'required|in:low,medium,high,critical',
                'current_quantity' => 'required|numeric|min:0',
                'threshold_quantity' => 'required|numeric|min:0',
                'reorder_level' => 'nullable|numeric|min:0',
                'expiry_date' => 'nullable|date',
                'days_to_expiry' => 'nullable|integer|min:0',
            ]);

            // Handle nullable fields - convert empty strings/null to appropriate defaults
            $alertData = array_merge($validatedData, [
                'alert_date' => now(),
                'status' => 'active',
                'reorder_level' => !empty($validatedData['reorder_level']) ? $validatedData['reorder_level'] : 0,
                'days_to_expiry' => !empty($validatedData['days_to_expiry']) ? $validatedData['days_to_expiry'] : null,
                'expiry_date' => !empty($validatedData['expiry_date']) ? $validatedData['expiry_date'] : null,
            ]);

            // Remove empty string values that should be null
            foreach (['expiry_date', 'days_to_expiry', 'reorder_level'] as $field) {
                if (isset($alertData[$field]) && $alertData[$field] === '') {
                    $alertData[$field] = ($field === 'reorder_level') ? 0 : null;
                }
            }

            $alert = StockAlert::create($alertData);

            return redirect()->route('stock.alerts.index')
                ->with('success', 'Stock alert created successfully.');

        } catch (\Illuminate\Validation\ValidationException $e) {
            return back()->withErrors($e->errors())->withInput()
                ->with('error', 'Please check the form for errors.');

        } catch (\Exception $e) {
            \Log::error('Error creating stock alert: ' . $e->getMessage());
            return back()->withInput()
                ->with('error', 'Failed to create stock alert. Please try again.');
        }
    }

    /**
     * Show the form for editing the specified stock alert
     */
    public function edit(StockAlert $stockAlert)
    {
        $items = DB::table('stock_items')
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        $locations = DB::table('stock_locations')
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        return view('stock.alerts.edit', compact('stockAlert', 'items', 'locations'));
    }

    /**
     * Update the specified stock alert
     */
    public function update(Request $request, StockAlert $stockAlert)
    {
        $validatedData = $request->validate([
            'item_id' => 'required|exists:stock_items,id',
            'location_id' => 'nullable|exists:stock_locations,id',
            'alert_type' => 'required|in:low_stock,out_of_stock,overstock,expiry_warning,expiry_critical,negative_stock',
            'title' => 'required|string|max:200',
            'message' => 'required|string',
            'priority' => 'required|in:low,medium,high,critical',
            'current_quantity' => 'required|numeric|min:0',
            'threshold_quantity' => 'required|numeric|min:0',
            'reorder_level' => 'nullable|numeric|min:0',
            'expiry_date' => 'nullable|date',
            'days_to_expiry' => 'nullable|integer|min:0',
        ]);

        try {
            // Handle nullable fields - convert empty strings/null to appropriate defaults
            $updateData = array_merge($validatedData, [
                'reorder_level' => !empty($validatedData['reorder_level']) ? $validatedData['reorder_level'] : 0,
                'days_to_expiry' => !empty($validatedData['days_to_expiry']) ? $validatedData['days_to_expiry'] : null,
                'expiry_date' => !empty($validatedData['expiry_date']) ? $validatedData['expiry_date'] : null,
            ]);

            // Remove empty string values that should be null
            foreach (['expiry_date', 'days_to_expiry', 'reorder_level'] as $field) {
                if (isset($updateData[$field]) && $updateData[$field] === '') {
                    $updateData[$field] = ($field === 'reorder_level') ? 0 : null;
                }
            }

            $stockAlert->update($updateData);

            return redirect()->route('stock.alerts.show', $stockAlert)
                ->with('success', 'Stock alert updated successfully.');

        } catch (\Exception $e) {
            \Log::error('Error updating stock alert: ' . $e->getMessage());
            return back()->withInput()
                ->with('error', 'Failed to update stock alert. Please try again.');
        }
    }

    /**
     * Remove the specified stock alert
     */
    public function destroy(StockAlert $stockAlert)
    {
        try {
            $stockAlert->delete();

            return redirect()->route('stock.alerts.index')
                ->with('success', 'Stock alert deleted successfully.');

        } catch (\Exception $e) {
            \Log::error('Error deleting stock alert: ' . $e->getMessage());
            return back()->with('error', 'Failed to delete stock alert. Please try again.');
        }
    }

    /**
     * Acknowledge a stock alert
     */
    public function acknowledge(StockAlert $stockAlert)
    {
        try {
            if (!$stockAlert->canBeAcknowledged()) {
                return back()->with('error', 'Alert cannot be acknowledged in its current status.');
            }

            $stockAlert->acknowledge(auth()->user());

            return back()->with('success', 'Alert acknowledged successfully.');

        } catch (\Exception $e) {
            \Log::error('Error acknowledging stock alert: ' . $e->getMessage());
            return back()->with('error', 'Failed to acknowledge alert. Please try again.');
        }
    }

    /**
     * Resolve a stock alert
     */
    public function resolve(Request $request, StockAlert $stockAlert)
    {
        $request->validate([
            'resolution_notes' => 'nullable|string|max:500',
        ]);

        try {
            if (!$stockAlert->canBeResolved()) {
                return back()->with('error', 'Alert cannot be resolved in its current status.');
            }

            $stockAlert->resolve(auth()->user(), $request->resolution_notes);

            return back()->with('success', 'Alert resolved successfully.');

        } catch (\Exception $e) {
            \Log::error('Error resolving stock alert: ' . $e->getMessage());
            return back()->with('error', 'Failed to resolve alert. Please try again.');
        }
    }

    /**
     * Dismiss a stock alert
     */
    public function dismiss(StockAlert $stockAlert)
    {
        try {
            if (!$stockAlert->canBeDismissed()) {
                return back()->with('error', 'Alert cannot be dismissed in its current status.');
            }

            $stockAlert->dismiss();

            return back()->with('success', 'Alert dismissed successfully.');

        } catch (\Exception $e) {
            \Log::error('Error dismissing stock alert: ' . $e->getMessage());
            return back()->with('error', 'Failed to dismiss alert. Please try again.');
        }
    }

    /**
     * Get critical alerts for dashboard widget
     */
    public function critical()
    {
        try {
            $criticalAlerts = StockAlert::with(['item', 'location'])
                ->active()
                ->critical()
                ->orderBy('alert_date', 'desc')
                ->limit(10)
                ->get();

            return response()->json($criticalAlerts);

        } catch (\Exception $e) {
            \Log::error('Error getting critical alerts: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to get critical alerts'], 500);
        }
    }

    /**
     * Get alert statistics for dashboard
     */
    public function statistics()
    {
        try {
            $statistics = $this->getAlertStatistics();
            return response()->json($statistics);

        } catch (\Exception $e) {
            \Log::error('Error getting alert statistics: ' . $e->getMessage());
            return response()->json($this->getDefaultStatistics());
        }
    }

    /**
     * Handle bulk actions on alerts
     */
    public function bulkAction(Request $request)
    {
        $request->validate([
            'action' => 'required|in:acknowledge,resolve,dismiss',
            'alert_ids' => 'required|array|min:1',
            'alert_ids.*' => 'exists:stock_alerts,id',
            'resolution_notes' => 'nullable|string|max:500',
        ]);

        try {
            $processedCount = 0;
            $action = $request->action;

            foreach ($request->alert_ids as $alertId) {
                $alert = StockAlert::find($alertId);
                if (!$alert) continue;

                switch ($action) {
                    case 'acknowledge':
                        if ($alert->canBeAcknowledged()) {
                            $alert->acknowledge(auth()->user());
                            $processedCount++;
                        }
                        break;
                    case 'resolve':
                        if ($alert->canBeResolved()) {
                            $alert->resolve(auth()->user(), $request->resolution_notes);
                            $processedCount++;
                        }
                        break;
                    case 'dismiss':
                        if ($alert->canBeDismissed()) {
                            $alert->dismiss();
                            $processedCount++;
                        }
                        break;
                }
            }

            $actionText = ucfirst($action) . 'd';
            return back()->with('success', "{$processedCount} alerts {$actionText} successfully.");

        } catch (\Exception $e) {
            \Log::error('Error in bulk action: ' . $e->getMessage());
            return back()->with('error', 'Failed to process bulk action. Please try again.');
        }
    }

    /**
     * Bulk acknowledge alerts
     */
    public function bulkAcknowledge(Request $request)
    {
        $request->validate([
            'alert_ids' => 'required|array|min:1',
            'alert_ids.*' => 'exists:stock_alerts,id',
        ]);

        try {
            $acknowledgedCount = 0;

            foreach ($request->alert_ids as $alertId) {
                $alert = StockAlert::find($alertId);
                if ($alert && $alert->canBeAcknowledged()) {
                    $alert->acknowledge(auth()->user());
                    $acknowledgedCount++;
                }
            }

            return back()->with('success', "{$acknowledgedCount} alerts acknowledged successfully.");

        } catch (\Exception $e) {
            \Log::error('Error bulk acknowledging alerts: ' . $e->getMessage());
            return back()->with('error', 'Failed to acknowledge alerts. Please try again.');
        }
    }

    /**
     * Bulk resolve alerts
     */
    public function bulkResolve(Request $request)
    {
        $request->validate([
            'alert_ids' => 'required|array|min:1',
            'alert_ids.*' => 'exists:stock_alerts,id',
            'resolution_notes' => 'nullable|string|max:500',
        ]);

        try {
            $resolvedCount = 0;

            foreach ($request->alert_ids as $alertId) {
                $alert = StockAlert::find($alertId);
                if ($alert && $alert->canBeResolved()) {
                    $alert->resolve(auth()->user(), $request->resolution_notes);
                    $resolvedCount++;
                }
            }

            return back()->with('success', "{$resolvedCount} alerts resolved successfully.");

        } catch (\Exception $e) {
            \Log::error('Error bulk resolving alerts: ' . $e->getMessage());
            return back()->with('error', 'Failed to resolve alerts. Please try again.');
        }
    }

    /**
     * Get alert statistics
     */
    private function getAlertStatistics(): array
    {
        try {
            return [
                'total_alerts' => DB::table('stock_alerts')->count(),
                'active_alerts' => DB::table('stock_alerts')->where('status', 'active')->count(),
                'critical_alerts' => DB::table('stock_alerts')->where('status', 'active')->where('priority', 'critical')->count(),
                'high_priority_alerts' => DB::table('stock_alerts')->where('status', 'active')->where('priority', 'high')->count(),
                'acknowledged_alerts' => DB::table('stock_alerts')->where('status', 'acknowledged')->count(),
                'resolved_alerts' => DB::table('stock_alerts')->where('status', 'resolved')->count(),
                'low_stock_alerts' => DB::table('stock_alerts')->where('status', 'active')->where('alert_type', 'low_stock')->count(),
                'out_of_stock_alerts' => DB::table('stock_alerts')->where('status', 'active')->where('alert_type', 'out_of_stock')->count(),
                'expiry_alerts' => DB::table('stock_alerts')->where('status', 'active')->whereIn('alert_type', ['expiry_warning', 'expiry_critical'])->count(),
                'recent_alerts' => DB::table('stock_alerts')->where('alert_date', '>=', now()->subDays(7))->count(),
            ];
        } catch (\Exception $e) {
            return $this->getDefaultStatistics();
        }
    }

    /**
     * Get default statistics when database is unavailable
     */
    private function getDefaultStatistics(): array
    {
        return [
            'total_alerts' => 0,
            'active_alerts' => 0,
            'critical_alerts' => 0,
            'high_priority_alerts' => 0,
            'acknowledged_alerts' => 0,
            'resolved_alerts' => 0,
            'low_stock_alerts' => 0,
            'out_of_stock_alerts' => 0,
            'expiry_alerts' => 0,
            'recent_alerts' => 0,
        ];
    }

    // API Methods

    /**
     * API: Get stock alerts list
     */
    public function apiIndex(Request $request)
    {
        try {
            $query = StockAlert::with(['item', 'location'])
                ->orderBy('priority', 'desc')
                ->orderBy('alert_date', 'desc');

            // Apply filters
            if ($request->has('alert_type')) {
                $query->where('alert_type', $request->alert_type);
            }

            if ($request->has('priority')) {
                $query->where('priority', $request->priority);
            }

            if ($request->has('status')) {
                $query->where('status', $request->status);
            }

            $alerts = $query->paginate($request->get('per_page', 50));

            return response()->json([
                'success' => true,
                'data' => $alerts,
                'statistics' => $this->getAlertStatistics(),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve alerts',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * API: Get active alerts
     */
    public function apiActive(Request $request)
    {
        try {
            $alerts = StockAlert::with(['item', 'location'])
                ->active()
                ->orderBy('priority', 'desc')
                ->orderBy('alert_date', 'desc')
                ->limit($request->get('limit', 20))
                ->get();

            return response()->json([
                'success' => true,
                'data' => $alerts,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve active alerts',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * API: Acknowledge alert
     */
    public function apiAcknowledge(StockAlert $stockAlert)
    {
        try {
            if (!$stockAlert->canBeAcknowledged()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Alert cannot be acknowledged in its current status',
                ], 400);
            }

            $stockAlert->acknowledge(auth()->user());

            return response()->json([
                'success' => true,
                'message' => 'Alert acknowledged successfully',
                'data' => $stockAlert,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to acknowledge alert',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
