<?php

namespace App\Http\Controllers\Stock;

use App\Http\Controllers\Controller;
use App\Models\Stock\StockItem;
use App\Models\Stock\StockItemCategory;
use App\Models\Stock\StockLocation;
use App\Models\Stock\StockAlert;
use App\Models\Stock\StockTransaction;
use App\Models\Stock\StockLocationItem;
use App\Services\Stock\SimpleStockCalculationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Carbon\Carbon;

class StockController extends Controller
{
    public function __construct()
    {
        // Permission middleware removed - accessible to all authenticated users
    }

    /**
     * Display the stock dashboard
     */
    public function index(Request $request)
    {
        try {
            // Get filter parameters
            $categoryId = $request->get('category_id');
            $locationId = $request->get('location_id');
            $itemType = $request->get('item_type');
            $stockStatus = $request->get('stock_status');
            $search = $request->get('search');

            // Build query using raw DB queries to work with existing table structure
            $query = DB::table('stock_items')
                ->leftJoin('stock_item_categories', 'stock_items.category_id', '=', 'stock_item_categories.id')
                ->leftJoin('stock_locations', 'stock_items.location_id', '=', 'stock_locations.id')
                ->select(
                    'stock_items.*',
                    'stock_item_categories.name as category_name',
                    'stock_locations.name as location_name',
                    'stock_locations.code as location_code'
                )
                ->where('stock_items.is_active', true);

            // Apply filters
            if ($categoryId) {
                $query->where('stock_items.category_id', $categoryId);
            }

            if ($locationId) {
                $query->where('stock_items.location_id', $locationId);
            }

            if ($itemType) {
                $query->where('stock_items.type', $itemType);
            }

            // Note: Stock status filtering is now handled post-query with calculated values
            // to ensure consistency with transaction-based calculations
            if ($stockStatus) {
                // Store for post-query filtering
                $filterByStockStatus = $stockStatus;
            }

            if ($search) {
                $query->where(function ($q) use ($search) {
                    $q->where('stock_items.name', 'like', "%{$search}%")
                      ->orWhere('stock_items.sku', 'like', "%{$search}%")
                      ->orWhere('stock_items.description', 'like', "%{$search}%");
                });
            }

            // Get paginated results
            $items = $query->orderBy('stock_items.name')->paginate(50);

            // Convert to objects for view compatibility and calculate transaction-based stock
            $simpleStockService = app(SimpleStockCalculationService::class);

            $items->getCollection()->transform(function ($item) use ($simpleStockService) {
                // Store original database values for logging
                $originalCurrentQuantity = $item->current_quantity ?? 0;
                $originalAvailableQuantity = $item->available_quantity ?? 0;

                // Calculate current stock using transaction-based method
                $calculatedCurrentStock = $simpleStockService->calculateAvailableStock($item->id);
                $calculatedAvailableStock = $calculatedCurrentStock; // Same in simple system

                // Override database values with calculated values
                $item->current_quantity = $calculatedCurrentStock;
                $item->available_quantity = $calculatedAvailableStock;

                // Log the override for debugging
                \Log::info('Stock index page calculation override', [
                    'item_id' => $item->id,
                    'item_name' => $item->name,
                    'original_database_current' => $originalCurrentQuantity,
                    'original_database_available' => $originalAvailableQuantity,
                    'calculated_current_stock' => $calculatedCurrentStock,
                    'calculated_available_stock' => $calculatedAvailableStock,
                    'method' => 'Transaction-based (Stock In - Stock Out)'
                ]);

                $item->category = (object) ['name' => $item->category_name];
                $item->primaryLocation = (object) [
                    'name' => $item->location_name,
                    'code' => $item->location_code
                ];
                $item->item_type = $item->type; // Map type to item_type for view compatibility
                $item->average_cost = $item->cost_price; // Use cost_price as average_cost
                $item->image = null; // Add default image property since it doesn't exist in DB
                return $item;
            });

            // Get summary statistics
            $statistics = $this->getStockStatistics();

            // Get filter options
            $categories = DB::table('stock_item_categories')
                ->where('is_active', true)
                ->orderBy('name')
                ->get();

            $locations = DB::table('stock_locations')
                ->where('is_active', true)
                ->orderBy('name')
                ->get();

            // Get recently updated items from accessory orders (last 7 days)
            $recentlyUpdatedItems = DB::table('stock_transactions')
                ->where('created_at', '>=', now()->subDays(7))
                ->where('notes', 'like', '%accessory order%')
                ->pluck('item_id')
                ->unique()
                ->toArray();

            // Add recently updated flag to items
            foreach ($items as $item) {
                $item->recently_updated = in_array($item->id, $recentlyUpdatedItems);
            }

            // Apply stock status filtering after calculations (if requested)
            if (isset($filterByStockStatus)) {
                $items->getCollection()->transform(function ($collection) use ($filterByStockStatus) {
                    return $collection->filter(function ($item) use ($filterByStockStatus) {
                        switch ($filterByStockStatus) {
                            case 'low_stock':
                                return $item->current_quantity <= ($item->reorder_level ?? 0);
                            case 'out_of_stock':
                                return $item->current_quantity <= 0;
                            case 'overstock':
                                return $item->current_quantity > ($item->max_level ?? PHP_INT_MAX);
                            default:
                                return true;
                        }
                    })->values();
                });
            }

            return view('stock.index', compact(
                'items',
                'statistics',
                'categories',
                'locations',
                'categoryId',
                'locationId',
                'itemType',
                'stockStatus',
                'search'
            ));

        } catch (\Exception $e) {
            \Log::error('Error in stock index: ' . $e->getMessage());

            // Create an empty paginated result instead of a collection
            $emptyItems = new \Illuminate\Pagination\LengthAwarePaginator(
                collect(), // items
                0, // total
                50, // per page
                1, // current page
                ['path' => request()->url(), 'pageName' => 'page']
            );

            return view('stock.index', [
                'items' => $emptyItems,
                'statistics' => $this->getDefaultStatistics(),
                'categories' => collect(),
                'locations' => collect(),
                'categoryId' => null,
                'locationId' => null,
                'itemType' => null,
                'stockStatus' => null,
                'search' => null
            ]);
        }
    }

    /**
     * Show the form for creating a new stock item
     */
    public function create()
    {
        $categories = DB::table('stock_item_categories')
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        $locations = DB::table('stock_locations')
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        return view('stock.create', compact('categories', 'locations'));
    }

    /**
     * Store a newly created stock item
     */
    public function store(Request $request)
    {
        // Debug: Log the incoming request data
        \Log::info('Stock creation form submitted', [
            'all_data' => $request->all(),
            'method' => $request->method(),
            'content_type' => $request->header('Content-Type'),
            'user_agent' => $request->header('User-Agent'),
            'timestamp' => now()->format('Y-m-d H:i:s')
        ]);

        // Handle both field name patterns for compatibility
        $itemType = $request->input('item_type') ?: $request->input('type');
        $locationId = $request->input('primary_location_id') ?: $request->input('location_id');

        // Merge the normalized field names into the request
        $request->merge([
            'item_type' => $itemType,
            'primary_location_id' => $locationId,
        ]);

        $request->validate([
            'category_id' => 'required|exists:stock_item_categories,id',
            'name' => 'required|string|max:191',
            'sku' => 'nullable|string|max:191|unique:stock_items,sku',
            'item_type' => 'required|in:fabric,accessory,wip,finished',
            'description' => 'nullable|string',
            'color' => 'nullable|string|max:191',
            'size' => 'nullable|string|max:191',
            'unit_of_measure' => 'required|string|max:20',
            'current_quantity' => 'required|numeric|min:0',
            'reorder_level' => 'required|numeric|min:0',
            'max_level' => 'nullable|numeric|min:0',
            'cost_price' => 'required|numeric|min:0',
            'primary_location_id' => 'required|exists:stock_locations,id,is_active,1',
        ], [
            'primary_location_id.exists' => 'The selected location is invalid or inactive. Please select a valid location.',
            'category_id.exists' => 'The selected category is invalid. Please select a valid category.',
            'item_type.required' => 'The item type field is required.',
            'item_type.in' => 'The selected item type is invalid.',
        ]);

        try {
            DB::beginTransaction();

            // Create the stock item
            $item = StockItem::create([
                'category_id' => $request->category_id,
                'name' => $request->name,
                'sku' => $request->sku ?: 'SKU-' . uniqid(),
                'type' => $request->item_type,
                'description' => $request->description,
                'color' => $request->color,
                'size' => $request->size,
                'unit_of_measure' => $request->unit_of_measure,
                'current_quantity' => $request->current_quantity,
                'reserved_quantity' => 0,
                'available_quantity' => $request->current_quantity,
                'reorder_level' => $request->reorder_level,
                'max_level' => $request->max_level,
                'cost_price' => $request->cost_price,
                'average_cost' => $request->cost_price,
                'costing_method' => $request->costing_method ?: 'fifo',
                'location_id' => $request->primary_location_id,
                'is_active' => $request->is_active ?? true,
            ]);

            // Try to create location item record if location is set and table exists
            if ($request->primary_location_id && $request->current_quantity > 0) {
                try {
                    if (Schema::hasTable('stock_location_items')) {
                        StockLocationItem::create([
                            'item_id' => $item->id,
                            'location_id' => $request->primary_location_id,
                            'quantity' => $request->current_quantity,
                            'available_quantity' => $request->current_quantity,
                            'average_cost' => $request->cost_price,
                            'total_value' => $request->current_quantity * $request->cost_price,
                            'min_quantity' => $request->reorder_level,
                            'max_quantity' => $request->max_level,
                            'reorder_point' => $request->reorder_level,
                        ]);
                    }
                } catch (\Exception $e) {
                    \Log::warning('Could not create stock location item: ' . $e->getMessage());
                }

                // Try to create opening balance transaction if table exists
                try {
                    if (Schema::hasTable('stock_transactions')) {
                        StockTransaction::create([
                            'item_id' => $item->id,
                            'location_id' => $request->location_id,
                            'transaction_type' => 'in',
                            'quantity' => $request->current_quantity,
                            'unit_cost' => $request->cost_price,
                            'total_cost' => $request->current_quantity * $request->cost_price,
                            'reference_type' => 'opening_balance',
                            'status' => 'posted',
                            'notes' => 'Opening balance',
                            'transaction_date' => now(),
                            'created_by' => auth()->id(),
                        ]);
                    }
                } catch (\Exception $e) {
                    \Log::warning('Could not create stock transaction: ' . $e->getMessage());
                }
            }

            DB::commit();

            return redirect()->route('stock.index')
                ->with('success', 'Stock item created successfully.');

        } catch (\Illuminate\Database\QueryException $e) {
            DB::rollBack();
            \Log::error('Database error creating stock item: ' . $e->getMessage());

            // Handle specific foreign key constraint errors
            if (str_contains($e->getMessage(), 'foreign key constraint fails')) {
                if (str_contains($e->getMessage(), 'location_id')) {
                    return back()->withInput()
                        ->with('error', 'The selected location is invalid. Please select a valid location from the dropdown.');
                } elseif (str_contains($e->getMessage(), 'category_id')) {
                    return back()->withInput()
                        ->with('error', 'The selected category is invalid. Please select a valid category from the dropdown.');
                }
            }

            return back()->withInput()
                ->with('error', 'Failed to create stock item due to a database constraint. Please check your selections and try again.');
        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('Error creating stock item: ' . $e->getMessage() . ' | Line: ' . $e->getLine() . ' | File: ' . $e->getFile());
            return back()->withInput()
                ->with('error', 'Failed to create stock item. Please try again.');
        }
    }

    /**
     * Display the specified stock item
     */
    public function show($id)
    {
        try {
            // Get item with related data using raw queries
            $item = DB::table('stock_items')
                ->leftJoin('stock_item_categories', 'stock_items.category_id', '=', 'stock_item_categories.id')
                ->leftJoin('stock_locations', 'stock_items.location_id', '=', 'stock_locations.id')
                ->select(
                    'stock_items.*',
                    'stock_item_categories.name as category_name',
                    'stock_locations.name as location_name',
                    'stock_locations.code as location_code'
                )
                ->where('stock_items.id', $id)
                ->first();

            if (!$item) {
                return redirect()->route('stock.index')->with('error', 'Stock item not found.');
            }

            // Calculate current stock using transaction-based method
            $simpleStockService = app(\App\Services\Stock\SimpleStockCalculationService::class);
            $calculatedCurrentStock = $simpleStockService->calculateAvailableStock($id);
            $calculatedAvailableStock = $calculatedCurrentStock; // Same in simple system

            // Store original database values for logging
            $originalCurrentQuantity = $item->current_quantity ?? 0;
            $originalAvailableQuantity = $item->available_quantity ?? 0;

            // Override database values with calculated values
            $item->current_quantity = $calculatedCurrentStock;
            $item->available_quantity = $calculatedAvailableStock;

            \Log::info('Stock detail page calculation override', [
                'item_id' => $id,
                'original_database_current' => $originalCurrentQuantity,
                'original_database_available' => $originalAvailableQuantity,
                'calculated_current_stock' => $calculatedCurrentStock,
                'calculated_available_stock' => $calculatedAvailableStock,
                'method' => 'Transaction-based (Stock In - Stock Out)'
            ]);

            // Convert to object for view compatibility
            $stockItem = (object) [
                'id' => $item->id,
                'name' => $item->name,
                'sku' => $item->sku,
                'description' => $item->description,
                'type' => $item->type,
                'item_type' => $item->type, // For view compatibility
                'color' => $item->color,
                'size' => $item->size,
                'unit_of_measure' => $item->unit_of_measure,
                'current_quantity' => $item->current_quantity,
                'reorder_level' => $item->reorder_level,
                'max_level' => $item->max_level,
                'cost_price' => $item->cost_price,
                'average_cost' => $item->cost_price,
                'is_active' => $item->is_active,
                'created_at' => Carbon::parse($item->created_at),
                'updated_at' => Carbon::parse($item->updated_at),
                'image' => null, // Add default image property
                'category' => (object) ['name' => $item->category_name],
                'primaryLocation' => (object) [
                    'name' => $item->location_name,
                    'code' => $item->location_code
                ]
            ];

            // Get recent transactions using Eloquent model for proper date casting
            $recentTransactions = StockTransaction::where('item_id', $id)
                ->with(['location', 'creator'])
                ->orderBy('created_at', 'desc')
                ->limit(10)
                ->get();

            // Get stock movement chart data (last 30 days)
            $chartData = $this->getStockMovementChartData($id);

            return view('stock.show', compact('stockItem', 'recentTransactions', 'chartData'))
                ->with('item', $stockItem);
        } catch (\Exception $e) {
            \Log::error('Error in stock show: ' . $e->getMessage());
            return redirect()->route('stock.index')->with('error', 'Error loading stock item.');
        }
    }

    /**
     * Show the form for editing the specified stock item
     */
    public function edit($id)
    {
        try {
            // Get item data
            $item = DB::table('stock_items')->where('id', $id)->first();

            if (!$item) {
                return redirect()->route('stock.index')->with('error', 'Stock item not found.');
            }

            // Convert to object for view compatibility
            $stockItem = (object) [
                'id' => $item->id,
                'name' => $item->name,
                'sku' => $item->sku,
                'description' => $item->description,
                'type' => $item->type,
                'item_type' => $item->type,
                'color' => $item->color,
                'size' => $item->size,
                'unit_of_measure' => $item->unit_of_measure,
                'current_quantity' => $item->current_quantity,
                'reorder_level' => $item->reorder_level,
                'max_level' => $item->max_level,
                'cost_price' => $item->cost_price,
                'category_id' => $item->category_id,
                'primary_location_id' => $item->location_id,
                'is_active' => $item->is_active,
                'image' => null, // Add default image property
            ];

            $categories = DB::table('stock_item_categories')
                ->where('is_active', true)
                ->orderBy('name')
                ->get();

            $locations = DB::table('stock_locations')
                ->where('is_active', true)
                ->orderBy('name')
                ->get();

            return view('stock.edit', compact('stockItem', 'categories', 'locations'));
        } catch (\Exception $e) {
            \Log::error('Error in stock edit: ' . $e->getMessage());
            return redirect()->route('stock.index')->with('error', 'Error loading stock item.');
        }
    }

    /**
     * Update the specified stock item
     */
    public function update(Request $request, StockItem $stockItem)
    {
        $request->validate([
            'category_id' => 'required|exists:stock_item_categories,id',
            'name' => 'required|string|max:200',
            'sku' => 'required|string|max:50|unique:stock_items,sku,' . $stockItem->id,
            'item_type' => 'required|in:fabric,accessory,wip,finished_goods',
            'description' => 'nullable|string',
            'color' => 'nullable|string|max:50',
            'size' => 'nullable|string|max:20',
            'style_number' => 'nullable|string|max:50',
            'season' => 'nullable|string|max:20',
            'quality_grade' => 'nullable|string|max:10',
            'unit_of_measure' => 'required|string|max:20',
            'reorder_level' => 'required|numeric|min:0',
            'max_level' => 'nullable|numeric|min:0',
            'cost_price' => 'required|numeric|min:0',
            'costing_method' => 'required|in:fifo,lifo,average',
            'primary_location_id' => 'nullable|exists:stock_locations,id',
            'track_batches' => 'boolean',
            'track_expiry' => 'boolean',
            'is_active' => 'boolean',
        ]);

        try {
            $stockItem->update(array_merge($request->all(), [
                'updated_by' => auth()->id(),
            ]));

            return redirect()->route('stock.show', $stockItem)
                ->with('success', 'Stock item updated successfully.');

        } catch (\Exception $e) {
            \Log::error('Error updating stock item: ' . $e->getMessage());
            return back()->withInput()
                ->with('error', 'Failed to update stock item. Please try again.');
        }
    }

    /**
     * Force delete stock item with all dependencies
     */
    public function forceDestroy($id)
    {
        try {
            DB::beginTransaction();

            // Get the stock item
            $stockItem = DB::table('stock_items')->where('id', $id)->first();

            if (!$stockItem) {
                DB::rollBack();
                return back()->with('error', 'Stock item not found.');
            }

            $deletedData = [];

            // 1. Delete stock alerts
            $alertCount = DB::table('stock_alerts')->where('item_id', $id)->count();
            if ($alertCount > 0) {
                DB::table('stock_alerts')->where('item_id', $id)->delete();
                $deletedData[] = "{$alertCount} alert(s)";
            }

            // 2. Delete stock transactions
            $transactionCount = 0;
            try {
                $transactionCount = DB::table('stock_transactions')->where('item_id', $id)->count();
                if ($transactionCount > 0) {
                    DB::table('stock_transactions')->where('item_id', $id)->delete();
                    $deletedData[] = "{$transactionCount} transaction(s)";
                }
            } catch (\Exception $e) {
                \Log::warning('Could not delete stock_transactions: ' . $e->getMessage());
            }

            // 3. Delete stock transfers
            $transferCount = 0;
            try {
                $transferCount = DB::table('stock_transfers')->where('item_id', $id)->count();
                if ($transferCount > 0) {
                    DB::table('stock_transfers')->where('item_id', $id)->delete();
                    $deletedData[] = "{$transferCount} transfer(s)";
                }
            } catch (\Exception $e) {
                \Log::warning('Could not delete stock_transfers: ' . $e->getMessage());
            }

            // 4. Delete stock location items
            $locationItemCount = 0;
            try {
                $locationItemCount = DB::table('stock_location_items')->where('item_id', $id)->count();
                if ($locationItemCount > 0) {
                    DB::table('stock_location_items')->where('item_id', $id)->delete();
                    $deletedData[] = "{$locationItemCount} location item(s)";
                }
            } catch (\Exception $e) {
                \Log::warning('Could not delete stock_location_items: ' . $e->getMessage());
            }

            // 5. Delete physical count items
            $physicalCountCount = 0;
            try {
                $physicalCountCount = DB::table('stock_physical_count_items')->where('item_id', $id)->count();
                if ($physicalCountCount > 0) {
                    DB::table('stock_physical_count_items')->where('item_id', $id)->delete();
                    $deletedData[] = "{$physicalCountCount} physical count item(s)";
                }
            } catch (\Exception $e) {
                \Log::warning('Could not delete stock_physical_count_items: ' . $e->getMessage());
            }

            // 6. Finally delete the stock item
            $deleted = DB::table('stock_items')->where('id', $id)->delete();

            if ($deleted) {
                DB::commit();

                $message = 'Stock item "' . $stockItem->name . '" and all dependencies deleted successfully.';
                if (!empty($deletedData)) {
                    $message .= ' Removed: ' . implode(', ', $deletedData);
                }

                return redirect()->route('stock.index')->with('success', $message);
            } else {
                DB::rollBack();
                return back()->with('error', 'Failed to delete stock item.');
            }

        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('Error force deleting stock item: ' . $e->getMessage());
            return back()->with('error', 'Failed to delete stock item: ' . $e->getMessage());
        }
    }

    /**
     * Bulk force delete multiple stock items with all dependencies
     */
    public function bulkForceDestroy(Request $request)
    {
        $request->validate([
            'item_ids' => 'required|array',
            'item_ids.*' => 'integer|exists:stock_items,id'
        ]);

        $itemIds = $request->input('item_ids');
        $deletedItems = [];
        $errors = [];

        try {
            DB::beginTransaction();

            foreach ($itemIds as $id) {
                try {
                    // Get the stock item
                    $stockItem = DB::table('stock_items')->where('id', $id)->first();

                    if (!$stockItem) {
                        $errors[] = "Item ID {$id} not found";
                        continue;
                    }

                    $deletedData = [];

                    // 1. Delete stock alerts
                    $alertCount = DB::table('stock_alerts')->where('item_id', $id)->count();
                    if ($alertCount > 0) {
                        DB::table('stock_alerts')->where('item_id', $id)->delete();
                        $deletedData[] = "{$alertCount} alert(s)";
                    }

                    // 2. Delete stock transactions
                    try {
                        $transactionCount = DB::table('stock_transactions')->where('item_id', $id)->count();
                        if ($transactionCount > 0) {
                            DB::table('stock_transactions')->where('item_id', $id)->delete();
                            $deletedData[] = "{$transactionCount} transaction(s)";
                        }
                    } catch (\Exception $e) {
                        \Log::warning('Could not delete stock_transactions for item ' . $id . ': ' . $e->getMessage());
                    }

                    // 3. Delete stock transfers
                    try {
                        $transferCount = DB::table('stock_transfers')->where('item_id', $id)->count();
                        if ($transferCount > 0) {
                            DB::table('stock_transfers')->where('item_id', $id)->delete();
                            $deletedData[] = "{$transferCount} transfer(s)";
                        }
                    } catch (\Exception $e) {
                        \Log::warning('Could not delete stock_transfers for item ' . $id . ': ' . $e->getMessage());
                    }

                    // 4. Delete stock location items
                    try {
                        $locationItemCount = DB::table('stock_location_items')->where('item_id', $id)->count();
                        if ($locationItemCount > 0) {
                            DB::table('stock_location_items')->where('item_id', $id)->delete();
                            $deletedData[] = "{$locationItemCount} location item(s)";
                        }
                    } catch (\Exception $e) {
                        \Log::warning('Could not delete stock_location_items for item ' . $id . ': ' . $e->getMessage());
                    }

                    // 5. Delete physical count items
                    try {
                        $physicalCountCount = DB::table('stock_physical_count_items')->where('item_id', $id)->count();
                        if ($physicalCountCount > 0) {
                            DB::table('stock_physical_count_items')->where('item_id', $id)->delete();
                            $deletedData[] = "{$physicalCountCount} physical count item(s)";
                        }
                    } catch (\Exception $e) {
                        \Log::warning('Could not delete stock_physical_count_items for item ' . $id . ': ' . $e->getMessage());
                    }

                    // 6. Finally delete the stock item
                    $deleted = DB::table('stock_items')->where('id', $id)->delete();

                    if ($deleted) {
                        $deletedItems[] = [
                            'name' => $stockItem->name,
                            'dependencies' => $deletedData
                        ];
                    } else {
                        $errors[] = "Failed to delete item: {$stockItem->name}";
                    }

                } catch (\Exception $e) {
                    $errors[] = "Error deleting item ID {$id}: " . $e->getMessage();
                    \Log::error('Error bulk force deleting stock item ' . $id . ': ' . $e->getMessage());
                }
            }

            DB::commit();

            $message = 'Bulk force delete completed. ';
            $message .= count($deletedItems) . ' items deleted successfully.';

            if (!empty($errors)) {
                $message .= ' Errors: ' . implode(', ', $errors);
            }

            return response()->json([
                'success' => true,
                'message' => $message,
                'deleted_count' => count($deletedItems),
                'error_count' => count($errors)
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('Error in bulk force delete: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Bulk force delete failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified stock item (safe delete)
     */
    public function destroy($id)
    {
        try {
            // Get the stock item
            $stockItem = DB::table('stock_items')->where('id', $id)->first();

            if (!$stockItem) {
                return back()->with('error', 'Stock item not found.');
            }

            // Check if item has any transactions
            $transactionCount = 0;
            try {
                $transactionCount = DB::table('stock_transactions')
                    ->where('item_id', $id)
                    ->count();
            } catch (\Exception $e) {
                \Log::warning('Could not check stock_transactions in destroy: ' . $e->getMessage());
            }

            if ($transactionCount > 0) {
                return back()->with('error', 'Cannot delete item with existing transactions. Found ' . $transactionCount . ' transaction(s).');
            }

            // Check if item has any transfers
            $transferCount = 0;
            try {
                $transferCount = DB::table('stock_transfers')
                    ->where('item_id', $id)
                    ->count();
            } catch (\Exception $e) {
                \Log::warning('Could not check stock_transfers in destroy: ' . $e->getMessage());
            }

            if ($transferCount > 0) {
                return back()->with('error', 'Cannot delete item with existing transfers. Found ' . $transferCount . ' transfer(s).');
            }

            // Check if item has any alerts
            $alertCount = DB::table('stock_alerts')
                ->where('item_id', $id)
                ->count();

            if ($alertCount > 0) {
                // Delete related alerts first
                DB::table('stock_alerts')->where('item_id', $id)->delete();
            }

            // Delete the stock item
            $deleted = DB::table('stock_items')->where('id', $id)->delete();

            if ($deleted) {
                return redirect()->route('stock.index')
                    ->with('success', 'Stock item "' . $stockItem->name . '" deleted successfully.');
            } else {
                return back()->with('error', 'Failed to delete stock item.');
            }

        } catch (\Exception $e) {
            \Log::error('Error deleting stock item: ' . $e->getMessage());
            return back()->with('error', 'Failed to delete stock item: ' . $e->getMessage());
        }
    }

    /**
     * Check if stock item can be deleted
     */
    public function checkDeletable($id)
    {
        try {
            $stockItem = DB::table('stock_items')->where('id', $id)->first();

            if (!$stockItem) {
                return response()->json([
                    'success' => false,
                    'message' => 'Stock item not found.'
                ], 404);
            }

            $issues = [];

            // Check transactions
            $transactionCount = 0;
            try {
                $transactionCount = DB::table('stock_transactions')->where('item_id', $id)->count();
            } catch (\Exception $e) {
                \Log::warning('Could not check stock_transactions: ' . $e->getMessage());
            }
            if ($transactionCount > 0) {
                $issues[] = "Has {$transactionCount} transaction(s)";
            }

            // Check transfers
            $transferCount = 0;
            try {
                $transferCount = DB::table('stock_transfers')->where('item_id', $id)->count();
            } catch (\Exception $e) {
                \Log::warning('Could not check stock_transfers: ' . $e->getMessage());
            }
            if ($transferCount > 0) {
                $issues[] = "Has {$transferCount} transfer(s)";
            }

            // Check current quantity
            if ($stockItem->current_quantity > 0) {
                $issues[] = "Current quantity: {$stockItem->current_quantity} {$stockItem->unit_of_measure}";
            }

            return response()->json([
                'success' => true,
                'can_delete' => empty($issues),
                'item_name' => $stockItem->name,
                'issues' => $issues,
                'warnings' => empty($issues) ? [] : ['This item has dependencies that prevent deletion.']
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error checking item: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get stock statistics for dashboard
     */
    private function getStockStatistics(): array
    {
        try {
            return [
                'total_items' => DB::table('stock_items')->where('is_active', true)->count(),
                'total_categories' => DB::table('stock_item_categories')->where('is_active', true)->count(),
                'total_locations' => DB::table('stock_locations')->where('is_active', true)->count(),
                'low_stock_items' => DB::table('stock_items')
                    ->where('is_active', true)
                    ->whereRaw('current_quantity <= reorder_level')
                    ->count(),
                'out_of_stock_items' => DB::table('stock_items')
                    ->where('is_active', true)
                    ->where('current_quantity', '<=', 0)
                    ->count(),
                'overstock_items' => DB::table('stock_items')
                    ->where('is_active', true)
                    ->whereRaw('current_quantity > max_level')
                    ->whereNotNull('max_level')
                    ->count(),
                'total_stock_value' => DB::table('stock_items')
                    ->where('is_active', true)
                    ->sum(DB::raw('current_quantity * cost_price')),
                'active_alerts' => DB::table('stock_alerts')->where('status', 'active')->count(),
                'recent_transactions' => DB::table('stock_transactions')
                    ->where('created_at', '>=', now()->subDays(7))
                    ->count(),
            ];
        } catch (\Exception $e) {
            return $this->getDefaultStatistics();
        }
    }

    /**
     * Get default statistics when database is unavailable
     */
    private function getDefaultStatistics(): array
    {
        return [
            'total_items' => 0,
            'total_categories' => 0,
            'total_locations' => 0,
            'low_stock_items' => 0,
            'out_of_stock_items' => 0,
            'overstock_items' => 0,
            'total_stock_value' => 0,
            'active_alerts' => 0,
            'recent_transactions' => 0,
        ];
    }

    /**
     * Get stock movement chart data
     */
    private function getStockMovementChartData(int $itemId): array
    {
        try {
            $transactions = DB::table('stock_transactions')
                ->where('item_id', $itemId)
                ->where('created_at', '>=', now()->subDays(30))
                ->where('status', 'approved')
                ->orderBy('created_at')
                ->get();

            $chartData = [];
            $runningQuantity = 0;

            foreach ($transactions as $transaction) {
                $effectiveQuantity = $transaction->transaction_type === 'in'
                    ? $transaction->quantity
                    : -$transaction->quantity;
                $runningQuantity += $effectiveQuantity;

                $chartData[] = [
                    'date' => \Carbon\Carbon::parse($transaction->created_at)->format('Y-m-d'),
                    'quantity' => $runningQuantity,
                    'transaction_type' => $transaction->transaction_type,
                ];
            }

            return $chartData;
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * Stock Management Dashboard
     */
    public function dashboard()
    {
        try {
            $metrics = [
                'total_items' => DB::table('stock_items')->where('is_active', true)->count(),
                'total_value' => DB::table('stock_items')
                    ->where('is_active', true)
                    ->sum(DB::raw('current_quantity * cost_price')),
                'low_stock_items' => DB::table('stock_items')
                    ->where('is_active', true)
                    ->whereRaw('current_quantity <= reorder_level')
                    ->count(),
                'transactions_today' => DB::table('stock_transactions')
                    ->whereDate('created_at', today())
                    ->count(),
            ];

            $criticalAlerts = DB::table('stock_alerts')
                ->leftJoin('stock_items', 'stock_alerts.item_id', '=', 'stock_items.id')
                ->select('stock_alerts.*', 'stock_items.name as item_name', 'stock_items.sku as item_sku')
                ->where('stock_alerts.status', 'active')
                ->where('stock_alerts.priority', 'high')
                ->orderBy('stock_alerts.alert_date', 'desc')
                ->limit(5)
                ->get()
                ->map(function ($alert) {
                    $alert->item = (object) ['name' => $alert->item_name, 'sku' => $alert->item_sku];
                    $alert->alert_date = \Carbon\Carbon::parse($alert->alert_date);
                    return $alert;
                });

            $recentTransactions = DB::table('stock_transactions')
                ->leftJoin('stock_items', 'stock_transactions.item_id', '=', 'stock_items.id')
                ->select(
                    'stock_transactions.*',
                    'stock_items.name as item_name',
                    'stock_items.sku as item_sku',
                    'stock_items.unit_of_measure'
                )
                ->orderBy('stock_transactions.created_at', 'desc')
                ->limit(10)
                ->get()
                ->map(function ($transaction) {
                    $transaction->item = (object) [
                        'name' => $transaction->item_name,
                        'sku' => $transaction->item_sku,
                        'unit_of_measure' => $transaction->unit_of_measure
                    ];
                    $transaction->transaction_date = \Carbon\Carbon::parse($transaction->created_at);
                    return $transaction;
                });

            $lowStockItems = DB::table('stock_items')
                ->leftJoin('stock_locations', 'stock_items.location_id', '=', 'stock_locations.id')
                ->select(
                    'stock_items.*',
                    'stock_locations.code as location_code'
                )
                ->where('stock_items.is_active', true)
                ->whereRaw('stock_items.current_quantity <= stock_items.reorder_level')
                ->orderBy('stock_items.current_quantity', 'asc')
                ->limit(10)
                ->get()
                ->map(function ($item) {
                    $item->primaryLocation = (object) ['code' => $item->location_code];
                    $item->image = null; // Add default image property
                    return $item;
                });

            $categoryData = DB::table('stock_items')
                ->leftJoin('stock_item_categories', 'stock_items.category_id', '=', 'stock_item_categories.id')
                ->select(
                    'stock_item_categories.name',
                    DB::raw('SUM(stock_items.current_quantity * stock_items.cost_price) as value')
                )
                ->where('stock_items.is_active', true)
                ->groupBy('stock_item_categories.id', 'stock_item_categories.name')
                ->get()
                ->toArray();

            $pendingApprovals = collect([]);
            $quickStats = [
                'active_transfers' => DB::table('stock_transfers')->where('status', 'in_transit')->count(),
                'physical_counts' => DB::table('stock_physical_counts')->where('status', 'in_progress')->count(),
                'locations' => DB::table('stock_locations')->where('is_active', true)->count(),
                'categories' => DB::table('stock_item_categories')->where('is_active', true)->count(),
            ];

            return view('stock.dashboard', compact(
                'metrics', 'criticalAlerts', 'recentTransactions', 'lowStockItems',
                'categoryData', 'pendingApprovals', 'quickStats'
            ));

        } catch (\Exception $e) {
            \Log::error('Error in stock dashboard: ' . $e->getMessage());
            return view('stock.dashboard', [
                'metrics' => ['total_items' => 0, 'total_value' => 0, 'low_stock_items' => 0, 'transactions_today' => 0],
                'criticalAlerts' => collect([]),
                'recentTransactions' => collect([]),
                'lowStockItems' => collect([]),
                'categoryData' => [],
                'pendingApprovals' => collect([]),
                'quickStats' => ['active_transfers' => 0, 'physical_counts' => 0, 'locations' => 0, 'categories' => 0],
            ]);
        }
    }

    // API Methods

    /**
     * API: Get stock items list
     */
    public function apiIndex(Request $request)
    {
        try {
            $query = StockItem::with(['category', 'primaryLocation'])
                ->where('is_active', true);

            // Apply filters
            if ($request->has('category_id')) {
                $query->where('category_id', $request->category_id);
            }

            if ($request->has('location_id')) {
                $query->where('primary_location_id', $request->location_id);
            }

            if ($request->has('item_type')) {
                $query->where('item_type', $request->item_type);
            }

            if ($request->has('search')) {
                $search = $request->search;
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('sku', 'like', "%{$search}%");
                });
            }

            $items = $query->paginate($request->get('per_page', 50));

            return response()->json([
                'success' => true,
                'data' => $items,
                'statistics' => $this->getStockStatistics(),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve stock items',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * API: Get single stock item
     */
    public function apiShow(StockItem $stockItem)
    {
        try {
            $stockItem->load([
                'category',
                'primaryLocation',
                'locationItems.location',
                'stockTransactions' => function ($query) {
                    $query->with(['location', 'creator'])
                          ->orderBy('created_at', 'desc')
                          ->limit(10);
                },
                'alerts' => function ($query) {
                    $query->active()->orderBy('alert_date', 'desc');
                }
            ]);

            return response()->json([
                'success' => true,
                'data' => $stockItem,
                'chart_data' => $this->getStockMovementChartData($stockItem->id),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve stock item',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * API: Create new stock item
     */
    public function apiStore(Request $request)
    {
        $request->validate([
            'category_id' => 'required|exists:stock_item_categories,id',
            'name' => 'required|string|max:200',
            'sku' => 'nullable|string|max:50|unique:stock_items,sku',
            'item_type' => 'required|in:fabric,accessory,wip,finished_goods',
            'unit_of_measure' => 'required|string|max:20',
            'current_quantity' => 'required|numeric|min:0',
            'reorder_level' => 'required|numeric|min:0',
            'cost_price' => 'required|numeric|min:0',
            'costing_method' => 'required|in:fifo,lifo,average',
        ]);

        try {
            DB::beginTransaction();

            $item = StockItem::create(array_merge($request->all(), [
                'average_cost' => $request->cost_price,
                'available_quantity' => $request->current_quantity,
                'created_by' => auth()->id(),
            ]));

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Stock item created successfully',
                'data' => $item->load(['category', 'primaryLocation']),
            ], 201);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Failed to create stock item',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * API: Update stock item
     */
    public function apiUpdate(Request $request, StockItem $stockItem)
    {
        $request->validate([
            'category_id' => 'required|exists:stock_item_categories,id',
            'name' => 'required|string|max:200',
            'sku' => 'required|string|max:50|unique:stock_items,sku,' . $stockItem->id,
            'item_type' => 'required|in:fabric,accessory,wip,finished_goods',
            'unit_of_measure' => 'required|string|max:20',
            'reorder_level' => 'required|numeric|min:0',
            'cost_price' => 'required|numeric|min:0',
            'costing_method' => 'required|in:fifo,lifo,average',
        ]);

        try {
            $stockItem->update(array_merge($request->all(), [
                'updated_by' => auth()->id(),
            ]));

            return response()->json([
                'success' => true,
                'message' => 'Stock item updated successfully',
                'data' => $stockItem->load(['category', 'primaryLocation']),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update stock item',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * API: Get stock levels summary
     */
    public function apiStockLevels(Request $request)
    {
        try {
            $query = StockLocationItem::with(['item', 'location'])
                ->where('quantity', '>', 0);

            if ($request->has('location_id')) {
                $query->where('location_id', $request->location_id);
            }

            if ($request->has('category_id')) {
                $query->whereHas('item', function ($q) use ($request) {
                    $q->where('category_id', $request->category_id);
                });
            }

            $stockLevels = $query->get();

            return response()->json([
                'success' => true,
                'data' => $stockLevels,
                'summary' => [
                    'total_items' => $stockLevels->count(),
                    'total_quantity' => $stockLevels->sum('quantity'),
                    'total_value' => $stockLevels->sum('total_value'),
                    'low_stock_items' => $stockLevels->where('quantity', '<=', 'min_quantity')->count(),
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve stock levels',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * API: Get stock levels for specific item
     */
    public function apiItemStockLevels(StockItem $stockItem)
    {
        try {
            $stockItem->load(['locationItems.location']);

            return response()->json([
                'success' => true,
                'data' => [
                    'item' => $stockItem,
                    'locations' => $stockItem->locationItems,
                    'summary' => [
                        'total_quantity' => $stockItem->current_quantity,
                        'available_quantity' => $stockItem->available_quantity,
                        'reserved_quantity' => $stockItem->reserved_quantity,
                        'total_value' => $stockItem->total_value,
                        'stock_status' => $stockItem->stock_status,
                    ],
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve item stock levels',
                'error' => $e->getMessage()
            ], 500);
        }
    }

















}
