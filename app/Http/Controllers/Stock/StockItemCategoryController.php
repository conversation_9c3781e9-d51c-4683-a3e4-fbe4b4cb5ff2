<?php

namespace App\Http\Controllers\Stock;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class StockItemCategoryController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('auth');
        // Role middleware removed - accessible to all authenticated users
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $categories = DB::table('stock_item_categories')
            ->orderBy('name')
            ->paginate(20);

        return view('admin.stock.categories.index', compact('categories'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.stock.categories.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:50|unique:stock_item_categories,code',
            'type' => 'required|string',
            'description' => 'nullable|string',
        ]);

        DB::table('stock_item_categories')->insert([
            'name' => $request->name,
            'code' => $request->code,
            'type' => $request->type,
            'description' => $request->description,
            'is_active' => true,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        return redirect()->route('admin.stock.categories.index')
            ->with('success', 'Category created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show($id)
    {
        $category = DB::table('stock_item_categories')->where('id', $id)->first();
        
        if (!$category) {
            return redirect()->route('admin.stock.categories.index')
                ->with('error', 'Category not found.');
        }

        return view('admin.stock.categories.show', compact('category'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit($id)
    {
        $category = DB::table('stock_item_categories')->where('id', $id)->first();
        
        if (!$category) {
            return redirect()->route('admin.stock.categories.index')
                ->with('error', 'Category not found.');
        }

        return view('admin.stock.categories.edit', compact('category'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $id)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:50|unique:stock_item_categories,code,' . $id,
            'type' => 'required|string',
            'description' => 'nullable|string',
        ]);

        DB::table('stock_item_categories')
            ->where('id', $id)
            ->update([
                'name' => $request->name,
                'code' => $request->code,
                'type' => $request->type,
                'description' => $request->description,
                'is_active' => $request->has('is_active'),
                'updated_at' => now(),
            ]);

        return redirect()->route('admin.stock.categories.index')
            ->with('success', 'Category updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        // Check if category is being used
        $itemCount = DB::table('stock_items')->where('category_id', $id)->count();
        
        if ($itemCount > 0) {
            return redirect()->route('admin.stock.categories.index')
                ->with('error', 'Cannot delete category. It is being used by ' . $itemCount . ' items.');
        }

        DB::table('stock_item_categories')->where('id', $id)->delete();

        return redirect()->route('admin.stock.categories.index')
            ->with('success', 'Category deleted successfully.');
    }
}
