<?php

namespace App\Http\Controllers\Stock;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class StockLocationController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('auth');
        // Role middleware removed - accessible to all authenticated users
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $locations = DB::table('stock_locations')
            ->orderBy('name')
            ->paginate(20);

        return view('admin.stock.locations.index', compact('locations'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.stock.locations.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:50|unique:stock_locations,code',
            'type' => 'required|string',
            'address' => 'nullable|string',
            'capacity' => 'nullable|numeric|min:0',
            'manager_name' => 'nullable|string|max:255',
            'contact_number' => 'nullable|string|max:20',
        ]);

        DB::table('stock_locations')->insert([
            'name' => $request->name,
            'code' => $request->code,
            'type' => $request->type,
            'address' => $request->address,
            'capacity' => $request->capacity,
            'manager_name' => $request->manager_name,
            'contact_number' => $request->contact_number,
            'is_active' => true,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        return redirect()->route('admin.stock.locations.index')
            ->with('success', 'Location created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show($id)
    {
        $location = DB::table('stock_locations')->where('id', $id)->first();
        
        if (!$location) {
            return redirect()->route('admin.stock.locations.index')
                ->with('error', 'Location not found.');
        }

        // Get items in this location
        $items = DB::table('stock_items')
            ->where('location_id', $id)
            ->where('is_active', true)
            ->get();

        return view('admin.stock.locations.show', compact('location', 'items'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit($id)
    {
        $location = DB::table('stock_locations')->where('id', $id)->first();
        
        if (!$location) {
            return redirect()->route('admin.stock.locations.index')
                ->with('error', 'Location not found.');
        }

        return view('admin.stock.locations.edit', compact('location'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $id)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:50|unique:stock_locations,code,' . $id,
            'type' => 'required|string',
            'address' => 'nullable|string',
            'capacity' => 'nullable|numeric|min:0',
            'manager_name' => 'nullable|string|max:255',
            'contact_number' => 'nullable|string|max:20',
        ]);

        DB::table('stock_locations')
            ->where('id', $id)
            ->update([
                'name' => $request->name,
                'code' => $request->code,
                'type' => $request->type,
                'address' => $request->address,
                'capacity' => $request->capacity,
                'manager_name' => $request->manager_name,
                'contact_number' => $request->contact_number,
                'is_active' => $request->has('is_active'),
                'updated_at' => now(),
            ]);

        return redirect()->route('admin.stock.locations.index')
            ->with('success', 'Location updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        // Check if location is being used
        $itemCount = DB::table('stock_items')->where('location_id', $id)->count();
        
        if ($itemCount > 0) {
            return redirect()->route('admin.stock.locations.index')
                ->with('error', 'Cannot delete location. It is being used by ' . $itemCount . ' items.');
        }

        DB::table('stock_locations')->where('id', $id)->delete();

        return redirect()->route('admin.stock.locations.index')
            ->with('success', 'Location deleted successfully.');
    }
}
