<?php

namespace App\Http\Controllers\Stock;

use App\Http\Controllers\Controller;
use App\Models\Stock\StockPhysicalCount;
use App\Models\Stock\StockItem;
use App\Models\Stock\StockLocation;
use App\Models\Stock\StockLocationItem;
use App\Services\Stock\StockMovementService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class StockPhysicalCountController extends Controller
{
    protected $stockMovementService;

    public function __construct(StockMovementService $stockMovementService)
    {
        $this->stockMovementService = $stockMovementService;
        // Permission middleware removed - accessible to all authenticated users
    }

    /**
     * Display a listing of physical counts
     */
    public function index(Request $request)
    {
        try {
            // Get filter parameters
            $locationId = $request->get('location_id');
            $itemId = $request->get('item_id');
            $countType = $request->get('count_type');
            $status = $request->get('status');
            $dateFrom = $request->get('date_from');
            $dateTo = $request->get('date_to');
            $hasVariance = $request->get('has_variance');

            // Build query
            $query = StockPhysicalCount::with([
                'item', 
                'location', 
                'counter', 
                'verifier',
                'approver'
            ])->orderBy('count_date', 'desc');

            // Apply filters
            if ($locationId) {
                $query->where('location_id', $locationId);
            }

            if ($itemId) {
                $query->where('item_id', $itemId);
            }

            if ($countType) {
                $query->where('count_type', $countType);
            }

            if ($status) {
                $query->where('status', $status);
            }

            if ($dateFrom) {
                $query->whereDate('count_date', '>=', $dateFrom);
            }

            if ($dateTo) {
                $query->whereDate('count_date', '<=', $dateTo);
            }

            if ($hasVariance) {
                $query->withVariance();
            }

            // Get paginated results
            $counts = $query->paginate(50);

            // Get filter options
            $items = StockItem::active()->orderBy('name')->get();
            $locations = StockLocation::active()->orderBy('name')->get();

            // Get summary statistics
            $statistics = $this->getCountStatistics();

            return view('stock.physical-counts.index', compact(
                'counts',
                'items',
                'locations',
                'statistics',
                'locationId',
                'itemId',
                'countType',
                'status',
                'dateFrom',
                'dateTo',
                'hasVariance'
            ));

        } catch (\Exception $e) {
            \Log::error('Error in physical counts index: ' . $e->getMessage());
            return view('stock.physical-counts.index', [
                'counts' => collect(),
                'items' => collect(),
                'locations' => collect(),
                'statistics' => $this->getDefaultStatistics(),
                'locationId' => null,
                'itemId' => null,
                'countType' => null,
                'status' => null,
                'dateFrom' => null,
                'dateTo' => null,
                'hasVariance' => null
            ]);
        }
    }

    /**
     * Show the form for creating a new physical count
     */
    public function create(Request $request)
    {
        $countType = $request->get('type', 'cycle');
        $locationId = $request->get('location_id');
        
        $items = StockItem::active()->orderBy('name')->get();
        $locations = StockLocation::active()->orderBy('name')->get();

        // If location is specified, get items in that location
        if ($locationId) {
            $locationItems = StockLocationItem::with('item')
                ->where('location_id', $locationId)
                ->where('quantity', '>', 0)
                ->get();
        } else {
            $locationItems = collect();
        }

        return view('stock.physical-counts.create', compact(
            'countType',
            'items',
            'locations',
            'locationId',
            'locationItems'
        ));
    }

    /**
     * Store a newly created physical count
     */
    public function store(Request $request)
    {
        $request->validate([
            'counts' => 'required|array|min:1',
            'counts.*.item_id' => 'required|exists:stock_items,id',
            'counts.*.location_id' => 'required|exists:stock_locations,id',
            'counts.*.physical_quantity' => 'required|numeric|min:0',
            'counts.*.batch_number' => 'nullable|string|max:50',
            'counts.*.lot_number' => 'nullable|string|max:50',
            'counts.*.item_condition' => 'nullable|in:good,damaged,expired,obsolete,missing',
            'counts.*.condition_notes' => 'nullable|string',
            'count_type' => 'required|in:full,cycle,spot,annual',
            'count_date' => 'required|date',
            'notes' => 'nullable|string',
        ]);

        try {
            DB::beginTransaction();

            $createdCounts = [];

            foreach ($request->counts as $countData) {
                // Get current system quantity
                $locationItem = StockLocationItem::where('item_id', $countData['item_id'])
                    ->where('location_id', $countData['location_id'])
                    ->first();

                $systemQuantity = $locationItem ? $locationItem->quantity : 0;
                $unitCost = $locationItem ? $locationItem->average_cost : 0;

                $count = StockPhysicalCount::create(array_merge($countData, [
                    'system_quantity' => $systemQuantity,
                    'unit_cost' => $unitCost,
                    'count_type' => $request->count_type,
                    'count_date' => $request->count_date,
                    'notes' => $request->notes,
                    'status' => 'pending',
                    'counted_by' => auth()->id(),
                    'counted_at' => now(),
                ]));

                $createdCounts[] = $count;
            }

            DB::commit();

            return redirect()->route('stock.physical-counts.index')
                ->with('success', count($createdCounts) . ' physical counts recorded successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('Error creating physical counts: ' . $e->getMessage());
            return back()->withInput()
                ->with('error', 'Failed to record physical counts. Please try again.');
        }
    }

    /**
     * Start a physical count session
     */
    public function start(StockPhysicalCount $stockPhysicalCount)
    {
        try {
            if (!$stockPhysicalCount->canBeStarted()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Count cannot be started in its current status.'
                ], 400);
            }

            $stockPhysicalCount->start(auth()->user());

            return response()->json([
                'success' => true,
                'message' => 'Physical count started successfully.'
            ]);

        } catch (\Exception $e) {
            \Log::error('Error starting physical count: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to start count. Please try again.'
            ], 500);
        }
    }

    /**
     * Display the specified physical count
     */
    public function show(StockPhysicalCount $stockPhysicalCount)
    {
        $stockPhysicalCount->load([
            'item', 
            'location', 
            'counter', 
            'verifier',
            'approver'
        ]);

        return view('stock.physical-counts.show', compact('stockPhysicalCount'));
    }

    /**
     * Verify a physical count
     */
    public function verify(StockPhysicalCount $stockPhysicalCount)
    {
        try {
            if (!$stockPhysicalCount->canBeVerified()) {
                return back()->with('error', 'Count cannot be verified in its current status.');
            }

            $stockPhysicalCount->verify(auth()->user());

            return back()->with('success', 'Physical count verified successfully.');

        } catch (\Exception $e) {
            \Log::error('Error verifying physical count: ' . $e->getMessage());
            return back()->with('error', 'Failed to verify count. Please try again.');
        }
    }

    /**
     * Approve a physical count
     */
    public function approve(StockPhysicalCount $stockPhysicalCount)
    {
        try {
            if (!$stockPhysicalCount->canBeApproved()) {
                return back()->with('error', 'Count cannot be approved in its current status.');
            }

            $stockPhysicalCount->approve(auth()->user());

            return back()->with('success', 'Physical count approved successfully.');

        } catch (\Exception $e) {
            \Log::error('Error approving physical count: ' . $e->getMessage());
            return back()->with('error', 'Failed to approve count. Please try again.');
        }
    }

    /**
     * Post a physical count (create adjustment transaction)
     */
    public function post(StockPhysicalCount $stockPhysicalCount)
    {
        try {
            if (!$stockPhysicalCount->canBePosted()) {
                return back()->with('error', 'Count cannot be posted in its current status.');
            }

            DB::beginTransaction();

            // Post the count and create adjustment transaction if there's variance
            $this->stockMovementService->processPhysicalCount($stockPhysicalCount);

            DB::commit();

            return back()->with('success', 'Physical count posted successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('Error posting physical count: ' . $e->getMessage());
            return back()->with('error', 'Failed to post count. Please try again.');
        }
    }

    /**
     * Reject a physical count
     */
    public function reject(Request $request, StockPhysicalCount $stockPhysicalCount)
    {
        $request->validate([
            'rejection_reason' => 'required|string|max:500',
        ]);

        try {
            if (!$stockPhysicalCount->canBeRejected()) {
                return back()->with('error', 'Count cannot be rejected in its current status.');
            }

            $stockPhysicalCount->reject($request->rejection_reason);

            return back()->with('success', 'Physical count rejected.');

        } catch (\Exception $e) {
            \Log::error('Error rejecting physical count: ' . $e->getMessage());
            return back()->with('error', 'Failed to reject count. Please try again.');
        }
    }

    /**
     * Cancel a physical count
     */
    public function cancel(Request $request, StockPhysicalCount $stockPhysicalCount)
    {
        $request->validate([
            'reason' => 'required|string|max:500',
        ]);

        try {
            if (!$stockPhysicalCount->canBeCancelled()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Count cannot be cancelled in its current status.'
                ], 400);
            }

            $stockPhysicalCount->cancel($request->reason);

            return response()->json([
                'success' => true,
                'message' => 'Physical count cancelled successfully.'
            ]);

        } catch (\Exception $e) {
            \Log::error('Error cancelling physical count: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to cancel count. Please try again.'
            ], 500);
        }
    }

    /**
     * Get items for counting at a specific location
     */
    public function getLocationItems(Request $request)
    {
        $locationId = $request->get('location_id');
        
        if (!$locationId) {
            return response()->json(['error' => 'Location ID is required'], 400);
        }

        try {
            $locationItems = StockLocationItem::with(['item' => function ($query) {
                $query->select('id', 'name', 'sku', 'unit_of_measure');
            }])
            ->where('location_id', $locationId)
            ->where('quantity', '>', 0)
            ->get()
            ->map(function ($locationItem) {
                return [
                    'item_id' => $locationItem->item_id,
                    'item_name' => $locationItem->item->name,
                    'item_sku' => $locationItem->item->sku,
                    'unit_of_measure' => $locationItem->item->unit_of_measure,
                    'system_quantity' => $locationItem->quantity,
                    'average_cost' => $locationItem->average_cost,
                ];
            });

            return response()->json($locationItems);

        } catch (\Exception $e) {
            \Log::error('Error getting location items: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to get location items'], 500);
        }
    }

    /**
     * Get count statistics
     */
    private function getCountStatistics(): array
    {
        try {
            return [
                'total_counts' => StockPhysicalCount::count(),
                'pending_counts' => StockPhysicalCount::pending()->count(),
                'approved_counts' => StockPhysicalCount::approved()->count(),
                'posted_counts' => StockPhysicalCount::posted()->count(),
                'counts_with_variance' => StockPhysicalCount::withVariance()->count(),
                'total_variance_value' => StockPhysicalCount::sum('variance_value'),
                'recent_counts' => StockPhysicalCount::where('count_date', '>=', now()->subDays(7))->count(),
            ];
        } catch (\Exception $e) {
            return $this->getDefaultStatistics();
        }
    }

    /**
     * Get default statistics when database is unavailable
     */
    private function getDefaultStatistics(): array
    {
        return [
            'total_counts' => 0,
            'pending_counts' => 0,
            'approved_counts' => 0,
            'posted_counts' => 0,
            'counts_with_variance' => 0,
            'total_variance_value' => 0,
            'recent_counts' => 0,
        ];
    }
}
