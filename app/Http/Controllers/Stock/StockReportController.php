<?php

namespace App\Http\Controllers\Stock;

use App\Http\Controllers\Controller;
use App\Models\Stock\StockItem;
use App\Models\Stock\StockItemCategory;
use App\Models\Stock\StockLocation;
use App\Models\Stock\StockLocationItem;
use App\Models\Stock\StockTransaction;
use App\Models\Stock\StockAlert;
use App\Models\Stock\StockPhysicalCount;
use App\Services\Stock\SimpleStockCalculationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class StockReportController extends Controller
{
    public function __construct()
    {
        // Permission middleware removed - accessible to all authenticated users
    }

    /**
     * Display the reports dashboard
     */
    public function index()
    {
        try {
            $reportTypes = [
                'stock_summary' => [
                    'title' => 'Stock Summary Report',
                    'description' => 'Current stock levels by category and location',
                    'icon' => 'fas fa-chart-bar',
                    'route' => 'stock.reports.stock-summary'
                ],
                'stock_movement' => [
                    'title' => 'Stock Movement Report',
                    'description' => 'Detailed transaction history and movements',
                    'icon' => 'fas fa-exchange-alt',
                    'route' => 'stock.reports.stock-movement'
                ],
                'stock_valuation' => [
                    'title' => 'Stock Valuation Report',
                    'description' => 'Current stock value by item and location',
                    'icon' => 'fas fa-dollar-sign',
                    'route' => 'stock.reports.stock-valuation'
                ],
                'low_stock' => [
                    'title' => 'Low Stock Report',
                    'description' => 'Items below reorder level',
                    'icon' => 'fas fa-exclamation-triangle',
                    'route' => 'stock.reports.low-stock'
                ],
                'stock_aging' => [
                    'title' => 'Stock Aging Report',
                    'description' => 'Analysis of stock by age and turnover',
                    'icon' => 'fas fa-clock',
                    'route' => 'stock.reports.stock-aging'
                ],
                'variance_analysis' => [
                    'title' => 'Variance Analysis Report',
                    'description' => 'Physical count vs system stock variances',
                    'icon' => 'fas fa-search',
                    'route' => 'stock.reports.variance-analysis'
                ],
            ];

            return view('stock.reports.index', compact('reportTypes'));

        } catch (\Exception $e) {
            \Log::error('Error in stock reports index: ' . $e->getMessage());
            return view('stock.reports.index', [
                'reportTypes' => []
            ]);
        }
    }

    /**
     * Stock Summary Report - Updated to use transaction-based calculations
     */
    public function stockSummary(Request $request)
    {
        try {
            $categoryId = $request->get('category_id');
            $locationId = $request->get('location_id');
            $itemType = $request->get('item_type');

            // Use transaction-based calculation service
            $simpleStockService = app(\App\Services\Stock\SimpleStockCalculationService::class);

            // Get all stock location items (we'll calculate actual quantities)
            $query = StockLocationItem::with(['item.category', 'location']);

            if ($categoryId) {
                $query->whereHas('item', function ($q) use ($categoryId) {
                    $q->where('category_id', $categoryId);
                });
            }

            if ($locationId) {
                $query->where('location_id', $locationId);
            }

            if ($itemType) {
                $query->whereHas('item', function ($q) use ($itemType) {
                    $q->where('item_type', $itemType);
                });
            }

            $stockLocationItems = $query->get();

            // Calculate actual stock quantities using transaction-based method
            $stockData = $stockLocationItems->map(function ($locationItem) use ($simpleStockService) {
                // Calculate current stock using transaction-based method
                $calculatedQuantity = $simpleStockService->calculateAvailableStock($locationItem->item_id, $locationItem->location_id);

                // Only include items with positive calculated stock
                if ($calculatedQuantity > 0) {
                    // Override database values with calculated values
                    $locationItem->quantity = $calculatedQuantity;
                    $locationItem->available_quantity = $calculatedQuantity;
                    $locationItem->total_value = $calculatedQuantity * ($locationItem->average_cost ?? 0);

                    return $locationItem;
                }

                return null;
            })->filter(); // Remove null entries (items with zero stock)

            // Group data for summary
            $summaryByCategory = $stockData->groupBy('item.category.name')
                ->map(function ($items) {
                    return [
                        'total_items' => $items->count(),
                        'total_quantity' => $items->sum('quantity'),
                        'total_value' => $items->sum('total_value'),
                        'low_stock_items' => $items->where('quantity', '<=', 'min_quantity')->count(),
                    ];
                });

            $summaryByLocation = $stockData->groupBy('location.name')
                ->map(function ($items) {
                    return [
                        'total_items' => $items->count(),
                        'total_quantity' => $items->sum('quantity'),
                        'total_value' => $items->sum('total_value'),
                        'capacity_utilization' => 0, // Would need location capacity data
                    ];
                });

            // Get filter options
            $categories = StockItemCategory::active()->orderBy('name')->get();
            $locations = StockLocation::active()->orderBy('name')->get();

            return view('pages.stock.reports.stock-summary', compact(
                'stockData',
                'summaryByCategory',
                'summaryByLocation',
                'categories',
                'locations',
                'categoryId',
                'locationId',
                'itemType'
            ));

        } catch (\Exception $e) {
            \Log::error('Error in stock summary report: ' . $e->getMessage());
            return view('pages.stock.reports.stock-summary-standalone', [
                'stockData' => collect(),
                'summaryByCategory' => collect(),
                'summaryByLocation' => collect(),
                'categories' => collect(),
                'locations' => collect(),
                'categoryId' => null,
                'locationId' => null,
                'itemType' => null
            ]);
        }
    }

    /**
     * Stock Movement Report
     */
    public function stockMovement(Request $request)
    {
        try {
            $itemId = $request->get('item_id');
            $locationId = $request->get('location_id');
            $transactionType = $request->get('transaction_type');
            $dateFrom = $request->get('date_from', now()->subDays(30)->format('Y-m-d'));
            $dateTo = $request->get('date_to', now()->format('Y-m-d'));

            // Build query for stock movements
            $query = StockTransaction::with(['item', 'location', 'creator'])
                ->whereBetween('transaction_date', [$dateFrom, $dateTo])
                ->where('status', 'posted')
                ->orderBy('transaction_date', 'desc');

            if ($itemId) {
                $query->where('item_id', $itemId);
            }

            if ($locationId) {
                $query->where('location_id', $locationId);
            }

            if ($transactionType) {
                $query->where('transaction_type', $transactionType);
            }

            $transactions = $query->paginate(100);

            // Calculate summary statistics
            $summary = [
                'total_transactions' => $transactions->total(),
                'total_in_quantity' => $query->clone()->stockIn()->sum('quantity'),
                'total_out_quantity' => $query->clone()->stockOut()->sum('quantity'),
                'total_in_value' => $query->clone()->stockIn()->sum('total_cost'),
                'total_out_value' => $query->clone()->stockOut()->sum('total_cost'),
            ];

            // Get filter options
            $items = StockItem::active()->orderBy('name')->get();
            $locations = StockLocation::active()->orderBy('name')->get();

            return view('pages.stock.reports.stock-movement', compact(
                'transactions',
                'summary',
                'items',
                'locations',
                'itemId',
                'locationId',
                'transactionType',
                'dateFrom',
                'dateTo'
            ));

        } catch (\Exception $e) {
            \Log::error('Error in stock movement report: ' . $e->getMessage());
            return view('pages.stock.reports.stock-movement-standalone', [
                'transactions' => collect(),
                'summary' => [
                    'total_transactions' => 0,
                    'total_in_quantity' => 0,
                    'total_out_quantity' => 0,
                    'total_in_value' => 0,
                    'total_out_value' => 0,
                ],
                'items' => collect(),
                'locations' => collect(),
                'itemId' => null,
                'locationId' => null,
                'transactionType' => null,
                'dateFrom' => now()->subDays(30)->format('Y-m-d'),
                'dateTo' => now()->format('Y-m-d')
            ]);
        }
    }

    /**
     * Stock Valuation Report
     */
    public function stockValuation(Request $request)
    {
        try {
            $categoryId = $request->get('category_id');
            $locationId = $request->get('location_id');
            $valuationDate = $request->get('valuation_date', now()->format('Y-m-d'));

            // Build query for stock valuation
            $query = StockLocationItem::with(['item.category', 'location'])
                ->where('quantity', '>', 0);

            if ($categoryId) {
                $query->whereHas('item', function ($q) use ($categoryId) {
                    $q->where('category_id', $categoryId);
                });
            }

            if ($locationId) {
                $query->where('location_id', $locationId);
            }

            $stockData = $query->get();

            // Calculate valuation summary
            $totalValue = $stockData->sum('total_value');
            $totalQuantity = $stockData->sum('quantity');

            $valuationByCategory = $stockData->groupBy('item.category.name')
                ->map(function ($items) {
                    return [
                        'total_value' => $items->sum('total_value'),
                        'total_quantity' => $items->sum('quantity'),
                        'percentage' => 0, // Will be calculated in view
                    ];
                });

            $valuationByLocation = $stockData->groupBy('location.name')
                ->map(function ($items) {
                    return [
                        'total_value' => $items->sum('total_value'),
                        'total_quantity' => $items->sum('quantity'),
                        'percentage' => 0, // Will be calculated in view
                    ];
                });

            // Calculate percentages
            foreach ($valuationByCategory as $key => $data) {
                $valuationByCategory[$key]['percentage'] = $totalValue > 0 ? 
                    ($data['total_value'] / $totalValue) * 100 : 0;
            }

            foreach ($valuationByLocation as $key => $data) {
                $valuationByLocation[$key]['percentage'] = $totalValue > 0 ? 
                    ($data['total_value'] / $totalValue) * 100 : 0;
            }

            // Get filter options
            $categories = StockItemCategory::active()->orderBy('name')->get();
            $locations = StockLocation::active()->orderBy('name')->get();

            return view('pages.stock.reports.stock-valuation', compact(
                'stockData',
                'totalValue',
                'totalQuantity',
                'valuationByCategory',
                'valuationByLocation',
                'categories',
                'locations',
                'categoryId',
                'locationId',
                'valuationDate'
            ));

        } catch (\Exception $e) {
            \Log::error('Error in stock valuation report: ' . $e->getMessage());
            return view('pages.stock.reports.stock-valuation-standalone', [
                'stockData' => collect(),
                'totalValue' => 0,
                'totalQuantity' => 0,
                'valuationByCategory' => collect(),
                'valuationByLocation' => collect(),
                'categories' => collect(),
                'locations' => collect(),
                'categoryId' => null,
                'locationId' => null,
                'valuationDate' => now()->format('Y-m-d')
            ]);
        }
    }

    /**
     * Low Stock Report
     */
    public function lowStock(Request $request)
    {
        try {
            $categoryId = $request->get('category_id');
            $locationId = $request->get('location_id');

            // Get items with low stock
            $query = StockItem::with(['category', 'primaryLocation', 'locationItems.location'])
                ->where('is_active', true)
                ->lowStock();

            if ($categoryId) {
                $query->where('category_id', $categoryId);
            }

            if ($locationId) {
                $query->whereHas('locationItems', function ($q) use ($locationId) {
                    $q->where('location_id', $locationId);
                });
            }

            $lowStockItems = $query->get();

            // Get out of stock items
            $outOfStockItems = StockItem::with(['category', 'primaryLocation'])
                ->where('is_active', true)
                ->outOfStock()
                ->get();

            // Get filter options
            $categories = StockItemCategory::active()->orderBy('name')->get();
            $locations = StockLocation::active()->orderBy('name')->get();

            return view('pages.stock.reports.low-stock', compact(
                'lowStockItems',
                'outOfStockItems',
                'categories',
                'locations',
                'categoryId',
                'locationId'
            ));

        } catch (\Exception $e) {
            \Log::error('Error in low stock report: ' . $e->getMessage());
            return view('pages.stock.reports.low-stock-standalone', [
                'lowStockItems' => collect(),
                'outOfStockItems' => collect(),
                'categories' => collect(),
                'locations' => collect(),
                'categoryId' => null,
                'locationId' => null
            ]);
        }
    }

    /**
     * Variance Analysis Report
     */
    public function varianceAnalysis(Request $request)
    {
        try {
            $locationId = $request->get('location_id');
            $dateFrom = $request->get('date_from', now()->subDays(30)->format('Y-m-d'));
            $dateTo = $request->get('date_to', now()->format('Y-m-d'));

            // Get physical counts with variance
            $query = StockPhysicalCount::with(['item', 'location', 'counter'])
                ->whereBetween('count_date', [$dateFrom, $dateTo])
                ->withVariance()
                ->orderBy('count_date', 'desc');

            if ($locationId) {
                $query->where('location_id', $locationId);
            }

            $varianceCounts = $query->get();

            // Calculate summary statistics
            $summary = [
                'total_counts' => $varianceCounts->count(),
                'positive_variances' => $varianceCounts->where('variance', '>', 0)->count(),
                'negative_variances' => $varianceCounts->where('variance', '<', 0)->count(),
                'total_variance_value' => $varianceCounts->sum('variance_value'),
                'average_variance_percentage' => $varianceCounts->avg('variance_percentage'),
            ];

            // Get filter options
            $locations = StockLocation::active()->orderBy('name')->get();

            return view('pages.stock.reports.variance-analysis', compact(
                'varianceCounts',
                'summary',
                'locations',
                'locationId',
                'dateFrom',
                'dateTo'
            ));

        } catch (\Exception $e) {
            \Log::error('Error in variance analysis report: ' . $e->getMessage());
            return view('pages.stock.reports.variance-analysis-standalone', [
                'varianceCounts' => collect(),
                'summary' => [
                    'total_counts' => 0,
                    'positive_variances' => 0,
                    'negative_variances' => 0,
                    'total_variance_value' => 0,
                    'average_variance_percentage' => 0,
                ],
                'locations' => collect(),
                'locationId' => null,
                'dateFrom' => now()->subDays(30)->format('Y-m-d'),
                'dateTo' => now()->format('Y-m-d')
            ]);
        }
    }

    /**
     * ABC Analysis Report
     */
    public function abcAnalysis(Request $request)
    {
        try {
            $dateFrom = $request->get('date_from', now()->subYear()->format('Y-m-d'));
            $dateTo = $request->get('date_to', now()->format('Y-m-d'));
            $categoryId = $request->get('category_id');
            $locationId = $request->get('location_id');

            $categories = StockItemCategory::all();
            $locations = StockLocation::all();

            // Get transaction data for ABC analysis
            $query = StockTransaction::with(['item.category', 'location'])
                ->whereBetween('transaction_date', [$dateFrom, $dateTo])
                ->where('transaction_type', 'out'); // Focus on consumption

            if ($categoryId) {
                $query->whereHas('item', function ($q) use ($categoryId) {
                    $q->where('category_id', $categoryId);
                });
            }

            if ($locationId) {
                $query->where('location_id', $locationId);
            }

            $transactions = $query->get();

            // Calculate consumption by item
            $itemConsumption = $transactions->groupBy('item_id')->map(function ($itemTransactions) {
                $item = $itemTransactions->first()->item;
                $totalQuantity = $itemTransactions->sum('quantity');
                $totalValue = $itemTransactions->sum('total_cost');

                return [
                    'item' => $item,
                    'total_quantity' => $totalQuantity,
                    'total_value' => $totalValue,
                    'transaction_count' => $itemTransactions->count(),
                ];
            })->sortByDesc('total_value');

            // Calculate ABC categories
            $totalValue = $itemConsumption->sum('total_value');
            $cumulativeValue = 0;
            $abcData = [];

            foreach ($itemConsumption as $itemId => $data) {
                $cumulativeValue += $data['total_value'];
                $cumulativePercentage = $totalValue > 0 ? ($cumulativeValue / $totalValue) * 100 : 0;

                // Determine ABC category
                if ($cumulativePercentage <= 80) {
                    $abcCategory = 'A';
                } elseif ($cumulativePercentage <= 95) {
                    $abcCategory = 'B';
                } else {
                    $abcCategory = 'C';
                }

                $abcData[] = array_merge($data, [
                    'percentage_of_total' => $totalValue > 0 ? ($data['total_value'] / $totalValue) * 100 : 0,
                    'cumulative_percentage' => $cumulativePercentage,
                    'abc_category' => $abcCategory,
                ]);
            }

            // Calculate summary statistics
            $summary = [
                'total_items' => count($abcData),
                'category_a_items' => collect($abcData)->where('abc_category', 'A')->count(),
                'category_b_items' => collect($abcData)->where('abc_category', 'B')->count(),
                'category_c_items' => collect($abcData)->where('abc_category', 'C')->count(),
                'total_consumption_value' => $totalValue,
                'category_a_value' => collect($abcData)->where('abc_category', 'A')->sum('total_value'),
                'category_b_value' => collect($abcData)->where('abc_category', 'B')->sum('total_value'),
                'category_c_value' => collect($abcData)->where('abc_category', 'C')->sum('total_value'),
            ];

            return view('pages.stock.reports.abc-analysis', compact(
                'abcData',
                'categories',
                'locations',
                'categoryId',
                'locationId',
                'dateFrom',
                'dateTo',
                'summary'
            ));

        } catch (\Exception $e) {
            \Log::error('Error in ABC analysis report: ' . $e->getMessage());
            return view('pages.stock.reports.abc-analysis-standalone');
        }
    }

    /**
     * Custom Report Builder
     */
    public function customReport(Request $request)
    {
        try {
            $categories = StockItemCategory::all();
            $locations = StockLocation::all();
            $items = StockItem::with('category')->get();

            // If this is a POST request, generate the custom report
            if ($request->isMethod('post')) {
                return $this->generateCustomReport($request);
            }

            return view('pages.stock.reports.custom-report', compact(
                'categories',
                'locations',
                'items'
            ));

        } catch (\Exception $e) {
            \Log::error('Error in custom report: ' . $e->getMessage());
            return view('pages.stock.reports.custom-report-standalone');
        }
    }

    /**
     * Generate custom report based on user criteria
     */
    public function generateCustomReport(Request $request)
    {
        try {
            $reportType = $request->get('report_type');
            $filters = $request->only([
                'category_id', 'location_id', 'item_id', 'date_from', 'date_to',
                'min_quantity', 'max_quantity', 'min_value', 'max_value'
            ]);

            $data = [];
            $title = 'Custom Report';

            switch ($reportType) {
                case 'stock_levels':
                    $data = $this->getCustomStockLevels($filters);
                    $title = 'Custom Stock Levels Report';
                    break;
                case 'transactions':
                    $data = $this->getCustomTransactions($filters);
                    $title = 'Custom Transactions Report';
                    break;
                case 'valuation':
                    $data = $this->getCustomValuation($filters);
                    $title = 'Custom Valuation Report';
                    break;
                default:
                    throw new \Exception('Invalid report type');
            }

            return view('pages.stock.reports.custom-report-results', compact(
                'data',
                'title',
                'filters',
                'reportType'
            ));

        } catch (\Exception $e) {
            \Log::error('Error generating custom report: ' . $e->getMessage());
            return back()->withErrors(['error' => 'Failed to generate custom report: ' . $e->getMessage()]);
        }
    }
}
