<?php

namespace App\Http\Controllers\Stock;

use App\Http\Controllers\Controller;
use App\Models\Stock\StockTransaction;
use App\Models\Stock\StockItem;
use App\Models\Stock\StockLocation;
use App\Models\Stock\StockLocationItem;
use App\Services\Stock\StockMovementService;
use App\Services\Stock\SimpleStockCalculationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class StockTransactionController extends Controller
{
    protected $stockMovementService;
    protected $simpleStockService;

    public function __construct(StockMovementService $stockMovementService, SimpleStockCalculationService $simpleStockService)
    {
        $this->stockMovementService = $stockMovementService;
        $this->simpleStockService = $simpleStockService;
        // Permission middleware removed - accessible to all authenticated users
    }

    /**
     * Display a listing of stock transactions
     */
    public function index(Request $request)
    {
        try {
            // Get filter parameters
            $itemId = $request->get('item_id');
            $locationId = $request->get('location_id');
            $transactionType = $request->get('transaction_type');
            $status = $request->get('status');
            $dateFrom = $request->get('date_from');
            $dateTo = $request->get('date_to');

            // Build query using raw DB queries
            $query = DB::table('stock_transactions')
                ->leftJoin('stock_items', 'stock_transactions.item_id', '=', 'stock_items.id')
                ->leftJoin('stock_locations', 'stock_transactions.location_id', '=', 'stock_locations.id')
                ->select(
                    'stock_transactions.*',
                    'stock_items.name as item_name',
                    'stock_items.sku as item_sku',
                    'stock_items.unit_of_measure',
                    'stock_locations.name as location_name',
                    'stock_locations.code as location_code'
                )
                ->orderBy('stock_transactions.transaction_date', 'desc');

            // Apply filters
            if ($itemId) {
                $query->where('stock_transactions.item_id', $itemId);
            }

            if ($locationId) {
                $query->where('stock_transactions.location_id', $locationId);
            }

            if ($transactionType) {
                $query->where('stock_transactions.transaction_type', $transactionType);
            }

            if ($status) {
                $query->where('stock_transactions.status', $status);
            }

            if ($dateFrom) {
                $query->whereDate('stock_transactions.transaction_date', '>=', $dateFrom);
            }

            if ($dateTo) {
                $query->whereDate('stock_transactions.transaction_date', '<=', $dateTo);
            }

            // Get paginated results
            $transactions = $query->paginate(50);

            // Convert to objects for view compatibility
            $transactions->getCollection()->transform(function ($transaction) {
                $transaction->item = (object) [
                    'name' => $transaction->item_name,
                    'sku' => $transaction->item_sku,
                    'unit_of_measure' => $transaction->unit_of_measure,
                    'image' => null // Add default image property since it doesn't exist in DB
                ];
                $transaction->location = (object) [
                    'name' => $transaction->location_name,
                    'code' => $transaction->location_code
                ];
                $transaction->transaction_date = \Carbon\Carbon::parse($transaction->transaction_date);
                return $transaction;
            });

            // Get filter options
            $items = DB::table('stock_items')
                ->where('is_active', true)
                ->orderBy('name')
                ->get();

            $locations = DB::table('stock_locations')
                ->where('is_active', true)
                ->orderBy('name')
                ->get();

            // Calculate dashboard summary metrics
            $summary = $this->calculateDashboardSummary();

            return view('stock.transactions.index', compact(
                'transactions',
                'items',
                'locations',
                'itemId',
                'locationId',
                'transactionType',
                'status',
                'dateFrom',
                'dateTo',
                'summary'
            ));

        } catch (\Exception $e) {
            \Log::error('Error in stock transactions index: ' . $e->getMessage());

            // Create an empty paginated result instead of a collection
            $emptyTransactions = new \Illuminate\Pagination\LengthAwarePaginator(
                collect(), // items
                0, // total
                50, // per page
                1, // current page
                ['path' => request()->url(), 'pageName' => 'page']
            );

            return view('stock.transactions.index', [
                'transactions' => $emptyTransactions,
                'items' => collect(),
                'locations' => collect(),
                'itemId' => null,
                'locationId' => null,
                'transactionType' => null,
                'status' => null,
                'dateFrom' => null,
                'dateTo' => null,
                'summary' => [
                    'stock_in_today' => 0,
                    'stock_out_today' => 0,
                    'pending_approval' => 0,
                    'total_value_today' => 0,
                ]
            ]);
        }
    }

    /**
     * Show the form for creating a new stock transaction
     */
    public function create(Request $request)
    {
        $transactionType = $request->get('type', 'in');
        $items = StockItem::active()->orderBy('name')->get();
        $locations = StockLocation::active()->orderBy('name')->get();

        return view('stock.transactions.create', compact('transactionType', 'items', 'locations'));
    }

    /**
     * Store a newly created stock transaction
     */
    public function store(Request $request)
    {
        $request->validate([
            'item_id' => 'required|exists:stock_items,id',
            'location_id' => 'required|exists:stock_locations,id',
            'transaction_type' => 'required|in:in,out,adjustment,production_in,production_out,return,damage,loss',
            'quantity' => 'required|numeric|min:0.0001',
            'unit_cost' => 'required|numeric|min:0',
            'batch_number' => 'nullable|string|max:50',
            'lot_number' => 'nullable|string|max:50',
            'expiry_date' => 'nullable|date|after:today',
            'manufacturing_date' => 'nullable|date|before_or_equal:today',
            'reference_type' => 'nullable|in:purchase_order,production_order,sales_order,transfer,adjustment,return,opening_balance',
            'reference_id' => 'nullable|integer',
            'reference_number' => 'nullable|string|max:50',
            'transaction_date' => 'required|date',
            'notes' => 'nullable|string',
            'reason' => 'nullable|string|max:200',
            'quality_grade' => 'nullable|string|max:10',
            'is_damaged' => 'boolean',
            'damage_reason' => 'nullable|string',
        ]);

        try {
            DB::beginTransaction();

            // Validate stock availability for outbound transactions
            if (in_array($request->transaction_type, ['out', 'transfer_out', 'production_out', 'damage', 'loss'])) {
                $this->validateStockAvailability($request->item_id, $request->location_id, $request->quantity);
            }

            // Create the transaction
            $transaction = StockTransaction::create(array_merge($request->all(), [
                'status' => 'pending',
                'created_by' => auth()->id(),
            ]));

            // Auto-approve simple transactions (permission check removed)
            if (in_array($request->transaction_type, ['in', 'out', 'adjustment'])) {
                $transaction->approve(auth()->user());
                $this->stockMovementService->processTransaction($transaction);
            }

            DB::commit();

            return redirect()->route('stock.transactions.show', $transaction)
                ->with('success', 'Stock transaction created successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('Error creating stock transaction: ' . $e->getMessage());
            return back()->withInput()
                ->with('error', 'Failed to create stock transaction. Please try again.');
        }
    }

    /**
     * Display the specified stock transaction
     */
    public function show(StockTransaction $stockTransaction)
    {
        try {
            // Load related data
            $stockTransaction->load(['item', 'location', 'creator', 'approver']);

            return view('stock.transactions.show', compact('stockTransaction'));

        } catch (\Exception $e) {
            \Log::error('Error in transaction show: ' . $e->getMessage());
            return redirect()->route('stock.transactions.index')->with('error', 'Error loading transaction.');
        }
    }

    /**
     * Show the form for editing the specified transaction
     */
    public function edit(StockTransaction $stockTransaction)
    {
        try {
            // Only allow editing of pending transactions
            if ($stockTransaction->status !== 'pending') {
                return redirect()->route('stock.transactions.show', $stockTransaction)
                    ->with('error', 'Only pending transactions can be edited.');
            }

            $items = DB::table('stock_items')
                ->where('is_active', true)
                ->orderBy('name')
                ->get();

            $locations = DB::table('stock_locations')
                ->where('is_active', true)
                ->orderBy('name')
                ->get();

            return view('stock.transactions.edit', compact('stockTransaction', 'items', 'locations'));

        } catch (\Exception $e) {
            \Log::error('Error loading transaction for edit: ' . $e->getMessage());
            return redirect()->route('stock.transactions.index')
                ->with('error', 'Error loading transaction for editing.');
        }
    }

    /**
     * Update the specified transaction
     */
    public function update(Request $request, StockTransaction $stockTransaction)
    {
        // Only allow updating of pending transactions
        if ($stockTransaction->status !== 'pending') {
            return redirect()->route('stock.transactions.show', $stockTransaction)
                ->with('error', 'Only pending transactions can be updated.');
        }

        $request->validate([
            'item_id' => 'required|exists:stock_items,id',
            'location_id' => 'required|exists:stock_locations,id',
            'transaction_type' => 'required|in:in,out,adjustment,transfer_in,transfer_out,production_in,production_out,damage,loss',
            'quantity' => 'required|numeric|min:0.01',
            'unit_cost' => 'required|numeric|min:0',
            'total_cost' => 'required|numeric|min:0',
            'reference_type' => 'nullable|string|max:50',
            'reference_number' => 'nullable|string|max:100',
            'transaction_date' => 'required|date',
            'notes' => 'nullable|string|max:1000',
        ]);

        try {
            // Validate stock availability for outbound transactions
            if (in_array($request->transaction_type, ['out', 'transfer_out', 'production_out', 'damage', 'loss'])) {
                $this->validateStockAvailability($request->item_id, $request->location_id, $request->quantity);
            }

            $stockTransaction->update(array_merge($request->all(), [
                'updated_by' => auth()->id(),
            ]));

            return redirect()->route('stock.transactions.show', $stockTransaction)
                ->with('success', 'Transaction updated successfully.');

        } catch (\Exception $e) {
            \Log::error('Error updating stock transaction: ' . $e->getMessage());
            return back()->withInput()
                ->with('error', 'Failed to update transaction. Please try again.');
        }
    }

    /**
     * Remove the specified transaction
     */
    public function destroy(StockTransaction $stockTransaction)
    {
        try {
            // Only allow deletion of pending transactions
            if ($stockTransaction->status !== 'pending') {
                return redirect()->route('stock.transactions.index')
                    ->with('error', 'Only pending transactions can be deleted.');
            }

            $stockTransaction->delete();

            return redirect()->route('stock.transactions.index')
                ->with('success', 'Transaction deleted successfully.');

        } catch (\Exception $e) {
            \Log::error('Error deleting stock transaction: ' . $e->getMessage());
            return redirect()->route('stock.transactions.index')
                ->with('error', 'Failed to delete transaction. Please try again.');
        }
    }

    /**
     * Approve a stock transaction
     */
    public function approve(StockTransaction $stockTransaction)
    {
        try {
            if (!$stockTransaction->canBeApproved()) {
                return back()->with('error', 'Transaction cannot be approved in its current status.');
            }

            DB::beginTransaction();

            $stockTransaction->approve(auth()->user());
            $this->stockMovementService->processTransaction($stockTransaction);

            DB::commit();

            return back()->with('success', 'Transaction approved and posted successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('Error approving stock transaction: ' . $e->getMessage());
            return back()->with('error', 'Failed to approve transaction. Please try again.');
        }
    }

    /**
     * Reject a stock transaction
     */
    public function reject(Request $request, StockTransaction $stockTransaction)
    {
        $request->validate([
            'reason' => 'required|string|max:500',
        ]);

        try {
            if (!$stockTransaction->canBeRejected()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Transaction cannot be rejected in its current status.'
                ], 400);
            }

            $stockTransaction->reject($request->reason, auth()->user());

            return response()->json([
                'success' => true,
                'message' => 'Transaction rejected successfully.'
            ]);

        } catch (\Exception $e) {
            \Log::error('Error rejecting stock transaction: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to reject transaction. Please try again.'
            ], 500);
        }
    }

    /**
     * Cancel a stock transaction
     */
    public function cancel(StockTransaction $stockTransaction)
    {
        try {
            if (!$stockTransaction->canBeCancelled()) {
                return back()->with('error', 'Transaction cannot be cancelled in its current status.');
            }

            $stockTransaction->cancel();

            return back()->with('success', 'Transaction cancelled successfully.');

        } catch (\Exception $e) {
            \Log::error('Error cancelling stock transaction: ' . $e->getMessage());
            return back()->with('error', 'Failed to cancel transaction. Please try again.');
        }
    }

    /**
     * Show stock adjustment form
     */
    public function adjustment()
    {
        $items = StockItem::with('category')->where('is_active', true)->get();
        $locations = StockLocation::where('is_active', true)->get();

        return view('stock.transactions.adjustment', compact('items', 'locations'));
    }

    /**
     * Process stock adjustment
     */
    public function processAdjustment(Request $request)
    {
        $request->validate([
            'item_id' => 'required|exists:stock_items,id',
            'location_id' => 'required|exists:stock_locations,id',
            'adjustment_type' => 'required|in:increase,decrease',
            'quantity' => 'required|numeric|min:0.0001',
            'unit_cost' => 'required|numeric|min:0',
            'reason' => 'required|string|max:200',
            'notes' => 'nullable|string',
            'batch_number' => 'nullable|string|max:50',
            'lot_number' => 'nullable|string|max:50',
        ]);

        try {
            DB::beginTransaction();

            // Calculate the adjustment quantity (positive for increase, negative for decrease)
            $adjustmentQuantity = $request->adjustment_type === 'increase'
                ? $request->quantity
                : -$request->quantity;

            // Create the adjustment transaction
            $transaction = StockTransaction::create([
                'item_id' => $request->item_id,
                'location_id' => $request->location_id,
                'transaction_type' => 'adjustment',
                'quantity' => abs($request->quantity),
                'effective_quantity' => $adjustmentQuantity,
                'unit_cost' => $request->unit_cost,
                'total_cost' => abs($request->quantity) * $request->unit_cost,
                'batch_number' => $request->batch_number,
                'lot_number' => $request->lot_number,
                'reference_type' => 'adjustment',
                'transaction_date' => now(),
                'status' => 'pending',
                'notes' => $request->notes,
                'reason' => $request->reason,
                'created_by' => auth()->id(),
            ]);

            // Auto-approve transaction (permission check removed)
            $transaction->approve(auth()->user());
            $this->stockMovementService->processTransaction($transaction);

            DB::commit();

            return redirect()->route('stock.transactions.show', $transaction)
                ->with('success', 'Stock adjustment created successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('Error creating stock adjustment: ' . $e->getMessage());
            return back()->withInput()->with('error', 'Failed to create adjustment. Please try again.');
        }
    }

    /**
     * Get stock in form
     */
    public function stockIn()
    {
        $items = DB::table('stock_items')
            ->select([
                'id',
                'name',
                'sku',
                'unit_of_measure',
                'cost_price',
                'average_cost',
                'current_quantity',
                'available_quantity',
                'reorder_level'
            ])
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        $locations = DB::table('stock_locations')
            ->select(['id', 'name', 'code', 'type'])
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        return view('stock.transactions.stock-in', compact('items', 'locations'));
    }

    /**
     * Process stock in
     */
    public function processStockIn(Request $request)
    {
        $request->validate([
            'transactions' => 'required|array|min:1',
            'transactions.*.item_id' => 'required|exists:stock_items,id',
            'transactions.*.location_id' => 'required|exists:stock_locations,id',
            'transactions.*.quantity' => 'required|numeric|min:0.0001',
            'transactions.*.unit_cost' => 'required|numeric|min:0',
            'transactions.*.batch_number' => 'nullable|string|max:50',
            'transactions.*.lot_number' => 'nullable|string|max:50',
            'transactions.*.expiry_date' => 'nullable|date|after:today',
            'transactions.*.notes' => 'nullable|string',
        ]);

        try {
            DB::beginTransaction();

            $createdTransactions = [];

            foreach ($request->transactions as $transactionData) {
                $transaction = StockTransaction::create(array_merge($transactionData, [
                    'transaction_type' => 'in',
                    'reference_type' => $request->reference_type ?? 'manual',
                    'reference_number' => $request->reference_number,
                    'transaction_date' => $request->transaction_date ?? now(),
                    'status' => 'pending',
                    'created_by' => auth()->id(),
                ]));

                // Auto-approve transaction (permission check removed)
                $transaction->approve(auth()->user());
                $this->stockMovementService->processTransaction($transaction);

                $createdTransactions[] = $transaction;
            }

            DB::commit();

            return redirect()->route('stock.transactions.index')
                ->with('success', count($createdTransactions) . ' stock in transactions created successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('Error processing stock in: ' . $e->getMessage());
            return back()->withInput()
                ->with('error', 'Failed to process stock in. Please try again.');
        }
    }

    /**
     * Get stock out form
     */
    public function stockOut(Request $request)
    {
        $items = StockItem::with(['locationItems.location'])
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        $locations = StockLocation::where('is_active', true)
            ->orderBy('name')
            ->get();

        // Get location-specific stock data using transaction-based calculation
        $locationStock = [];
        foreach ($items as $item) {
            // Get all locations that have transactions for this item
            $itemLocations = DB::table('stock_transactions')
                ->where('item_id', $item->id)
                ->where('status', 'posted')
                ->distinct()
                ->pluck('location_id')
                ->filter()
                ->toArray();

            foreach ($itemLocations as $locationId) {
                // Use the simple stock calculation service for accurate stock levels
                $stockData = $this->simpleStockService->getLocationStock($item->id, $locationId);

                // Only include locations with positive stock
                if ($stockData['available_quantity'] > 0) {
                    $locationStock[$item->id][$locationId] = $stockData;
                }
            }
        }

        // If item is pre-selected via query parameter, get its stock info
        $selectedItemStock = null;
        if ($request->has('item')) {
            $selectedItemId = $request->get('item');
            $selectedItem = $items->find($selectedItemId);
            if ($selectedItem && isset($locationStock[$selectedItemId])) {
                $selectedItemStock = $locationStock[$selectedItemId];
            }
        }

        return view('stock.transactions.stock-out', compact('items', 'locations', 'locationStock', 'selectedItemStock'));
    }

    /**
     * Get available stock for item at specific location (AJAX)
     */
    public function getLocationStock(Request $request)
    {
        $itemId = $request->get('item_id');
        $locationId = $request->get('location_id');

        if (!$itemId || !$locationId) {
            return response()->json(['error' => 'Item ID and Location ID are required'], 400);
        }

        // Use simple stock calculation service for accurate stock data
        try {
            $stockData = $this->simpleStockService->getLocationStock($itemId, $locationId);
            return response()->json($stockData);
        } catch (\Exception $e) {
            return response()->json([
                'available_quantity' => 0,
                'total_quantity' => 0,
                'average_cost' => 0,
                'reserved_quantity' => 0,
                'message' => 'No stock available at this location'
            ]);
        }
    }

    /**
     * Validate stock availability for outbound transactions (using simple calculation)
     * Now allows negative stock but logs warnings for business tracking
     */
    private function validateStockAvailability(int $itemId, int $locationId, float $requestedQuantity): void
    {
        $item = StockItem::find($itemId);
        if (!$item) {
            throw new \Exception("Item not found.");
        }

        // Get stock availability info
        $stockInfo = $this->simpleStockService->getStockAvailabilityInfo($itemId, $locationId, $requestedQuantity);

        // Log warnings for business tracking but allow the transaction
        if ($stockInfo['is_overselling']) {
            \Log::warning('Stock overselling transaction', [
                'item_id' => $itemId,
                'item_name' => $item->name,
                'location_id' => $locationId,
                'available' => $stockInfo['available'],
                'requested' => $requestedQuantity,
                'shortage' => $stockInfo['shortage'],
                'resulting_stock' => $stockInfo['resulting_stock'],
            ]);
        }

        // Only block transactions for critical business rules (none in simple system)
        // All transactions are allowed with appropriate warnings
    }

    /**
     * Process stock out
     */
    public function processStockOut(Request $request)
    {
        // Handle both single transaction and bulk transactions
        if ($request->has('transactions')) {
            // Bulk transactions format
            $request->validate([
                'transactions' => 'required|array|min:1',
                'transactions.*.item_id' => 'required|exists:stock_items,id',
                'transactions.*.location_id' => 'required|exists:stock_locations,id',
                'transactions.*.quantity' => 'required|numeric|min:0.0001',
                'transactions.*.unit_cost' => 'required|numeric|min:0',
                'transactions.*.batch_number' => 'nullable|string|max:50',
                'transactions.*.lot_number' => 'nullable|string|max:50',
                'transactions.*.notes' => 'nullable|string',
            ]);
            $transactions = $request->transactions;
        } else {
            // Single transaction format
            $request->validate([
                'item_id' => 'required|exists:stock_items,id',
                'location_id' => 'required|exists:stock_locations,id',
                'quantity' => 'required|numeric|min:0.0001',
                'unit_cost' => 'required|numeric|min:0',
                'transaction_date' => 'required|date',
                'reference_number' => 'nullable|string|max:50',
                'notes' => 'nullable|string',
            ]);
            $transactions = [$request->only([
                'item_id', 'location_id', 'quantity', 'unit_cost', 'notes'
            ])];
        }

        try {
            DB::beginTransaction();

            $createdTransactions = [];

            foreach ($transactions as $transactionData) {
                // Check if sufficient stock is available
                $item = StockItem::find($transactionData['item_id']);
                $locationItem = $item->locationItems()
                    ->where('location_id', $transactionData['location_id'])
                    ->first();

                if (!$locationItem || $locationItem->available_quantity < $transactionData['quantity']) {
                    throw new \Exception("Insufficient stock for item: {$item->name} at selected location.");
                }

                $transaction = StockTransaction::create(array_merge($transactionData, [
                    'transaction_type' => 'out',
                    'reference_type' => $request->reference_type ?? 'adjustment',
                    'reference_number' => $request->reference_number,
                    'transaction_date' => $request->transaction_date ?? now(),
                    'status' => 'pending',
                    'created_by' => auth()->id(),
                ]));

                // Auto-approve transaction (permission check removed)
                $transaction->approve(auth()->user());
                $this->stockMovementService->processTransaction($transaction);

                $createdTransactions[] = $transaction;
            }

            DB::commit();

            return redirect()->route('stock.transactions.index')
                ->with('success', count($createdTransactions) . ' stock out transactions created successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('Error processing stock out: ' . $e->getMessage());
            return back()->withInput()
                ->with('error', $e->getMessage());
        }
    }

    // API Methods

    /**
     * API: Get stock transactions list
     */
    public function apiIndex(Request $request)
    {
        try {
            $query = StockTransaction::with(['item', 'location', 'creator'])
                ->orderBy('transaction_date', 'desc');

            // Apply filters
            if ($request->has('item_id')) {
                $query->where('item_id', $request->item_id);
            }

            if ($request->has('location_id')) {
                $query->where('location_id', $request->location_id);
            }

            if ($request->has('transaction_type')) {
                $query->where('transaction_type', $request->transaction_type);
            }

            if ($request->has('status')) {
                $query->where('status', $request->status);
            }

            if ($request->has('date_from')) {
                $query->whereDate('transaction_date', '>=', $request->date_from);
            }

            if ($request->has('date_to')) {
                $query->whereDate('transaction_date', '<=', $request->date_to);
            }

            $transactions = $query->paginate($request->get('per_page', 50));

            return response()->json([
                'success' => true,
                'data' => $transactions,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve transactions',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * API: Create new stock transaction
     */
    public function apiStore(Request $request)
    {
        $request->validate([
            'item_id' => 'required|exists:stock_items,id',
            'location_id' => 'required|exists:stock_locations,id',
            'transaction_type' => 'required|in:in,out,adjustment,production_in,production_out,return,damage,loss',
            'quantity' => 'required|numeric|min:0.0001',
            'unit_cost' => 'required|numeric|min:0',
            'transaction_date' => 'required|date',
        ]);

        try {
            DB::beginTransaction();

            $transaction = StockTransaction::create(array_merge($request->all(), [
                'status' => 'pending',
                'created_by' => auth()->id(),
            ]));

            // Auto-approve transaction (permission check removed)
            $transaction->approve(auth()->user());
            $this->stockMovementService->processTransaction($transaction);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Transaction created successfully',
                'data' => $transaction->load(['item', 'location']),
            ], 201);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Failed to create transaction',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * API: Get single stock transaction
     */
    public function apiShow(StockTransaction $stockTransaction)
    {
        try {
            $stockTransaction->load(['item', 'location', 'creator', 'approver']);

            return response()->json([
                'success' => true,
                'data' => $stockTransaction,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve transaction',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Calculate dashboard summary metrics for the transactions index page
     */
    private function calculateDashboardSummary(): array
    {
        try {
            $today = now()->format('Y-m-d');

            // Stock In Today - sum of all inbound transactions today
            $stockInToday = DB::table('stock_transactions')
                ->whereDate('transaction_date', $today)
                ->where('status', 'posted')
                ->whereIn('transaction_type', [
                    'in', 'transfer_in', 'production_in', 'adjustment_in', 'opening_balance', 'return_in'
                ])
                ->sum('quantity');

            // Stock Out Today - sum of all outbound transactions today
            $stockOutToday = DB::table('stock_transactions')
                ->whereDate('transaction_date', $today)
                ->where('status', 'posted')
                ->whereIn('transaction_type', [
                    'out', 'transfer_out', 'production_out', 'adjustment_out', 'damage', 'loss', 'return_out'
                ])
                ->sum('quantity');

            // Pending Approval - count of transactions awaiting approval
            $pendingApproval = DB::table('stock_transactions')
                ->where('status', 'pending')
                ->count();

            // Total Value Today - sum of total_cost for all transactions today
            $totalValueToday = DB::table('stock_transactions')
                ->whereDate('transaction_date', $today)
                ->where('status', 'posted')
                ->sum('total_cost');

            return [
                'stock_in_today' => number_format($stockInToday, 0),
                'stock_out_today' => number_format($stockOutToday, 0),
                'pending_approval' => $pendingApproval,
                'total_value_today' => $totalValueToday,
            ];

        } catch (\Exception $e) {
            \Log::error('Error calculating dashboard summary: ' . $e->getMessage());

            return [
                'stock_in_today' => 0,
                'stock_out_today' => 0,
                'pending_approval' => 0,
                'total_value_today' => 0,
            ];
        }
    }
}
