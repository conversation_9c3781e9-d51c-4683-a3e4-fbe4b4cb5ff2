<?php

namespace App\Http\Controllers\Stock;

use App\Http\Controllers\Controller;
use App\Models\Stock\StockTransfer;
use App\Models\Stock\StockItem;
use App\Models\Stock\StockLocation;
use App\Services\Stock\StockMovementService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class StockTransferController extends Controller
{
    protected $stockMovementService;

    public function __construct(StockMovementService $stockMovementService)
    {
        $this->stockMovementService = $stockMovementService;
        // Permission middleware removed - accessible to all authenticated users
    }

    /**
     * Display a listing of stock transfers
     */
    public function index(Request $request)
    {
        try {
            // Get filter parameters
            $fromLocationId = $request->get('from_location_id');
            $toLocationId = $request->get('to_location_id');
            $itemId = $request->get('item_id');
            $status = $request->get('status');
            $dateFrom = $request->get('date_from');
            $dateTo = $request->get('date_to');

            // Build query using raw DB queries
            $query = DB::table('stock_transfers')
                ->leftJoin('stock_items', 'stock_transfers.item_id', '=', 'stock_items.id')
                ->leftJoin('stock_locations as from_loc', 'stock_transfers.from_location_id', '=', 'from_loc.id')
                ->leftJoin('stock_locations as to_loc', 'stock_transfers.to_location_id', '=', 'to_loc.id')
                ->select(
                    'stock_transfers.*',
                    'stock_items.name as item_name',
                    'stock_items.sku as item_sku',
                    'stock_items.unit_of_measure',
                    'from_loc.name as from_location_name',
                    'from_loc.code as from_location_code',
                    'to_loc.name as to_location_name',
                    'to_loc.code as to_location_code'
                )
                ->orderBy('stock_transfers.transfer_date', 'desc');

            // Apply filters
            if ($fromLocationId) {
                $query->where('stock_transfers.from_location_id', $fromLocationId);
            }

            if ($toLocationId) {
                $query->where('stock_transfers.to_location_id', $toLocationId);
            }

            if ($itemId) {
                $query->where('stock_transfers.item_id', $itemId);
            }

            if ($status) {
                $query->where('stock_transfers.status', $status);
            }

            if ($dateFrom) {
                $query->whereDate('stock_transfers.transfer_date', '>=', $dateFrom);
            }

            if ($dateTo) {
                $query->whereDate('stock_transfers.transfer_date', '<=', $dateTo);
            }

            // Get paginated results
            $transfers = $query->paginate(50);

            // Convert to objects for view compatibility
            $transfers->getCollection()->transform(function ($transfer) {
                $transfer->item = (object) [
                    'name' => $transfer->item_name,
                    'sku' => $transfer->item_sku,
                    'unit_of_measure' => $transfer->unit_of_measure
                ];
                $transfer->fromLocation = (object) [
                    'name' => $transfer->from_location_name,
                    'code' => $transfer->from_location_code
                ];
                $transfer->toLocation = (object) [
                    'name' => $transfer->to_location_name,
                    'code' => $transfer->to_location_code
                ];
                $transfer->transfer_date = \Carbon\Carbon::parse($transfer->transfer_date);
                return $transfer;
            });

            // Get filter options
            $items = DB::table('stock_items')
                ->where('is_active', true)
                ->orderBy('name')
                ->get();

            $locations = DB::table('stock_locations')
                ->where('is_active', true)
                ->orderBy('name')
                ->get();

            return view('stock.transfers.index', compact(
                'transfers',
                'items',
                'locations',
                'fromLocationId',
                'toLocationId',
                'itemId',
                'status',
                'dateFrom',
                'dateTo'
            ));

        } catch (\Exception $e) {
            \Log::error('Error in stock transfers index: ' . $e->getMessage());
            return view('pages.stock.transfers.index-standalone', [
                'transfers' => collect(),
                'items' => collect(),
                'locations' => collect(),
                'fromLocationId' => null,
                'toLocationId' => null,
                'itemId' => null,
                'status' => null,
                'dateFrom' => null,
                'dateTo' => null
            ]);
        }
    }

    /**
     * Show the form for creating a new stock transfer
     */
    public function create()
    {
        $items = StockItem::active()->orderBy('name')->get();
        $locations = StockLocation::active()->orderBy('name')->get();

        return view('pages.stock.transfers.create', compact('items', 'locations'));
    }

    /**
     * Store a newly created stock transfer
     */
    public function store(Request $request)
    {
        $request->validate([
            'from_location_id' => 'required|exists:stock_locations,id',
            'to_location_id' => 'required|exists:stock_locations,id|different:from_location_id',
            'item_id' => 'required|exists:stock_items,id',
            'quantity' => 'required|numeric|min:0.0001',
            'unit_cost' => 'required|numeric|min:0',
            'batch_number' => 'nullable|string|max:50',
            'lot_number' => 'nullable|string|max:50',
            'transfer_date' => 'required|date',
            'expected_delivery_date' => 'nullable|date|after_or_equal:transfer_date',
            'notes' => 'nullable|string',
        ]);

        try {
            DB::beginTransaction();

            // Check if sufficient stock is available at source location
            $item = StockItem::find($request->item_id);
            $sourceLocationItem = $item->locationItems()
                ->where('location_id', $request->from_location_id)
                ->first();

            if (!$sourceLocationItem || $sourceLocationItem->available_quantity < $request->quantity) {
                throw new \Exception("Insufficient stock available at source location. Available: " . 
                    ($sourceLocationItem->available_quantity ?? 0) . ", Requested: " . $request->quantity);
            }

            // Create the transfer
            $transfer = StockTransfer::create(array_merge($request->all(), [
                'status' => 'pending',
                'requested_by' => auth()->id(),
                'requested_at' => now(),
            ]));

            // Reserve stock at source location
            $sourceLocationItem->reserveQuantity($request->quantity);

            DB::commit();

            return redirect()->route('stock.transfers.show', $transfer)
                ->with('success', 'Stock transfer created successfully and stock reserved.');

        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('Error creating stock transfer: ' . $e->getMessage());
            return back()->withInput()
                ->with('error', $e->getMessage());
        }
    }

    /**
     * Display the specified stock transfer
     */
    public function show(StockTransfer $stockTransfer)
    {
        $stockTransfer->load([
            'item', 
            'fromLocation', 
            'toLocation', 
            'requester', 
            'approver',
            'dispatcher',
            'receiver'
        ]);

        return view('pages.stock.transfers.show', compact('stockTransfer'));
    }

    /**
     * Approve a stock transfer
     */
    public function approve(StockTransfer $stockTransfer)
    {
        try {
            if (!$stockTransfer->canBeApproved()) {
                return back()->with('error', 'Transfer cannot be approved in its current status.');
            }

            $stockTransfer->approve(auth()->user());

            return back()->with('success', 'Transfer approved successfully.');

        } catch (\Exception $e) {
            \Log::error('Error approving stock transfer: ' . $e->getMessage());
            return back()->with('error', 'Failed to approve transfer. Please try again.');
        }
    }

    /**
     * Dispatch a stock transfer
     */
    public function dispatchTransfer(Request $request, StockTransfer $stockTransfer)
    {
        $request->validate([
            'vehicle_number' => 'nullable|string|max:20',
            'driver_name' => 'nullable|string|max:100',
            'driver_contact' => 'nullable|string|max:20',
        ]);

        try {
            if (!$stockTransfer->canBeDispatched()) {
                return back()->with('error', 'Transfer cannot be dispatched in its current status.');
            }

            DB::beginTransaction();

            // Dispatch the transfer
            $stockTransfer->dispatch(auth()->user(), $request->only([
                'vehicle_number', 'driver_name', 'driver_contact'
            ]));

            // Create stock out transaction at source location
            $this->stockMovementService->processTransferDispatch($stockTransfer);

            DB::commit();

            return back()->with('success', 'Transfer dispatched successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('Error dispatching stock transfer: ' . $e->getMessage());
            return back()->with('error', 'Failed to dispatch transfer. Please try again.');
        }
    }

    /**
     * Receive a stock transfer
     */
    public function receive(Request $request, StockTransfer $stockTransfer)
    {
        $request->validate([
            'received_quantity' => 'required|numeric|min:0',
            'actual_delivery_date' => 'required|date',
            'quality_approved' => 'boolean',
            'quality_notes' => 'nullable|string',
        ]);

        try {
            if (!$stockTransfer->canBeReceived()) {
                return back()->with('error', 'Transfer cannot be received in its current status.');
            }

            DB::beginTransaction();

            // Receive the transfer
            $stockTransfer->receive(auth()->user(), $request->all());

            // Create stock in transaction at destination location
            $this->stockMovementService->processTransferReceipt($stockTransfer);

            DB::commit();

            return back()->with('success', 'Transfer received successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('Error receiving stock transfer: ' . $e->getMessage());
            return back()->with('error', 'Failed to receive transfer. Please try again.');
        }
    }

    /**
     * Cancel a stock transfer
     */
    public function cancel(Request $request, StockTransfer $stockTransfer)
    {
        $request->validate([
            'rejection_reason' => 'required|string|max:500',
        ]);

        try {
            if (!$stockTransfer->canBeCancelled()) {
                return back()->with('error', 'Transfer cannot be cancelled in its current status.');
            }

            DB::beginTransaction();

            // Cancel the transfer
            $stockTransfer->cancel($request->rejection_reason);

            // Release reserved stock at source location
            if ($stockTransfer->status === 'pending') {
                $sourceLocationItem = $stockTransfer->item->locationItems()
                    ->where('location_id', $stockTransfer->from_location_id)
                    ->first();

                if ($sourceLocationItem) {
                    $sourceLocationItem->releaseReservedQuantity($stockTransfer->quantity);
                }
            }

            DB::commit();

            return back()->with('success', 'Transfer cancelled successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('Error cancelling stock transfer: ' . $e->getMessage());
            return back()->with('error', 'Failed to cancel transfer. Please try again.');
        }
    }

    /**
     * Get pending transfers for approval
     */
    public function pendingApprovals()
    {
        $transfers = StockTransfer::with([
            'item', 
            'fromLocation', 
            'toLocation', 
            'requester'
        ])->pending()
          ->orderBy('requested_at')
          ->paginate(20);

        return view('pages.stock.transfers.pending-approvals', compact('transfers'));
    }

    /**
     * Get transfers ready for dispatch
     */
    public function readyForDispatch()
    {
        $transfers = StockTransfer::with([
            'item', 
            'fromLocation', 
            'toLocation', 
            'requester',
            'approver'
        ])->approved()
          ->orderBy('approved_at')
          ->paginate(20);

        return view('pages.stock.transfers.ready-for-dispatch', compact('transfers'));
    }

    /**
     * Get transfers in transit
     */
    public function inTransit()
    {
        $transfers = StockTransfer::with([
            'item', 
            'fromLocation', 
            'toLocation', 
            'requester',
            'approver',
            'dispatcher'
        ])->inTransit()
          ->orderBy('dispatched_at')
          ->paginate(20);

        return view('pages.stock.transfers.in-transit', compact('transfers'));
    }
}
