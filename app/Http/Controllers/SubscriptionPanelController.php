<?php

namespace App\Http\Controllers;

use App\Http\Requests\SubscriptionCodeRequest;
use App\Models\SubscriptionManagement\SubscriptionPlan;
use App\Models\SubscriptionManagement\SubscriptionCode;
use App\Models\SubscriptionManagement\SubscriptionPayment;
use App\Models\SubscriptionManagement\SubscriptionUsage;
use App\Models\SubscriptionManagement\SubscriptionNotification;
use App\Services\SubscriptionService;
use App\Traits\HasSubscriptionFeatures;
use Illuminate\Http\Request;
use PDF;

class SubscriptionPanelController extends Controller
{
    use HasSubscriptionFeatures;

    protected $subscriptionService;

    public function __construct(SubscriptionService $subscriptionService)
    {
        $this->middleware(['auth']);
        $this->subscriptionService = $subscriptionService;
    }

    /**
     * Display the subscription panel dashboard.
     */
    public function index()
    {
        $user = auth()->user();
        $subscription = $this->getCurrentSubscription();
        
        // Get subscription plans
        $plans = SubscriptionPlan::active()->ordered()->get();
        
        // Get usage statistics
        $usageStats = $this->getUsageStatistics($subscription);
        
        // Get recent payments
        $recentPayments = SubscriptionPayment::where('user_id', $user->id)
                                           ->with('userSubscription.subscriptionPlan')
                                           ->latest()
                                           ->limit(5)
                                           ->get();

        // Get subscription status info
        $statusInfo = $this->getSubscriptionStatusInfo($subscription);

        return view('subscription-panel.index', compact(
            'subscription',
            'plans',
            'usageStats',
            'recentPayments',
            'statusInfo'
        ));
    }

    /**
     * Display the upgrade page.
     */
    public function upgrade()
    {
        $user = auth()->user();
        $currentSubscription = $this->getCurrentSubscription();
        $plans = SubscriptionPlan::active()->ordered()->get();

        return view('subscription-panel.upgrade', compact(
            'currentSubscription',
            'plans'
        ));
    }

    /**
     * Process subscription upgrade.
     */
    public function processUpgrade(Request $request)
    {
        try {
            $request->validate([
                'plan_id' => 'required|exists:subscription_plans,id',
                'billing_cycle' => 'required|in:monthly,yearly',
                'payment_method' => 'required|in:manual,code',
            ]);

            $user = auth()->user();
            $newPlan = SubscriptionPlan::findOrFail($request->plan_id);

            // If using subscription code, handle directly
            if ($request->payment_method === 'code') {
                return $this->handleCodeUpgrade($request, $newPlan);
            }

            // Handle manual payment (admin approval required)
            return $this->handleManualUpgrade($request, $newPlan);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed: ' . implode(', ', $e->validator->errors()->all())
            ], 422);
        } catch (\Exception $e) {
            \Log::error('Upgrade process failed: ' . $e->getMessage(), [
                'user_id' => auth()->id(),
                'request_data' => $request->all(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while processing your upgrade request. Please try again.'
            ], 500);
        }
    }

    /**
     * Handle manual payment upgrade.
     */
    protected function handleManualUpgrade(Request $request, SubscriptionPlan $newPlan)
    {
        try {
            $user = auth()->user();
            $subscription = $this->getCurrentSubscription();

        if ($subscription) {
            // Update existing subscription to pending status
            $subscription->update([
                'subscription_plan_id' => $newPlan->id,
                'status' => 'inactive', // Will be activated by admin after payment confirmation
                'billing_cycle' => $request->billing_cycle,
                'amount' => $request->billing_cycle === 'yearly' ? $newPlan->yearly_price : $newPlan->price,
            ]);
        } else {
            // Create new subscription in pending status
            $subscription = $this->subscriptionService->createSubscription($user, $newPlan, [
                'status' => 'inactive', // Will be activated by admin
                'billing_cycle' => $request->billing_cycle,
                'amount' => $request->billing_cycle === 'yearly' ? $newPlan->yearly_price : $newPlan->price,
            ]);
        }

        // Create a pending payment record
        $billingPeriodEnd = $request->billing_cycle === 'yearly' ? now()->addYear() : now()->addMonth();

        SubscriptionPayment::createPayment([
            'user_subscription_id' => $subscription->id,
            'tenant_id' => $subscription->tenant_id,
            'user_id' => $subscription->user_id,
            'amount' => $subscription->amount,
            'currency' => $newPlan->currency,
            'status' => 'pending',
            'payment_method' => 'manual',
            'billing_cycle' => $request->billing_cycle,
            'billing_period_start' => now(),
            'billing_period_end' => $billingPeriodEnd,
            'notes' => 'Manual payment - awaiting admin confirmation',
        ]);

        // Create notification for admin
        SubscriptionNotification::createNotification([
            'user_subscription_id' => $subscription->id,
            'tenant_id' => $subscription->tenant_id,
            'user_id' => $subscription->user_id,
            'type' => 'plan_upgraded',
            'title' => 'Upgrade request submitted',
            'message' => "Your upgrade request to {$newPlan->name} has been submitted. You will be contacted for payment processing.",
            'priority' => 'medium',
        ]);

            return response()->json([
                'success' => true,
                'message' => 'Upgrade request submitted successfully! You will be contacted for payment processing.',
                'redirect' => route('subscription-panel.index')
            ]);

        } catch (\Exception $e) {
            \Log::error('Manual upgrade failed: ' . $e->getMessage(), [
                'user_id' => auth()->id(),
                'plan_id' => $newPlan->id,
                'billing_cycle' => $request->billing_cycle,
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to process upgrade request. Please try again.'
            ], 500);
        }
    }

    /**
     * Handle subscription code upgrade.
     */
    protected function handleCodeUpgrade(Request $request, SubscriptionPlan $newPlan)
    {
        try {
            $request->validate([
                'subscription_code' => 'required|string',
            ]);

            \Log::info('Processing subscription code upgrade', [
                'user_id' => auth()->id(),
                'subscription_code' => $request->subscription_code,
                'plan_id' => $newPlan->id
            ]);

            $result = $this->subscriptionService->applySubscriptionCode(
                auth()->user(),
                $request->subscription_code
            );

            \Log::info('Subscription code upgrade result', [
                'user_id' => auth()->id(),
                'subscription_code' => $request->subscription_code,
                'result' => $result
            ]);

            return response()->json($result);

        } catch (\Exception $e) {
            \Log::error('Subscription code upgrade failed', [
                'user_id' => auth()->id(),
                'subscription_code' => $request->subscription_code,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to apply subscription code: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display subscription code entry form.
     */
    public function subscriptionCode()
    {
        return view('subscription-panel.subscription-code');
    }

    /**
     * Apply subscription code.
     */
    public function applySubscriptionCode(SubscriptionCodeRequest $request)
    {
        $result = $this->subscriptionService->applySubscriptionCode(
            auth()->user(),
            $request->code
        );

        if ($result['success']) {
            return response()->json([
                'success' => true,
                'message' => $result['message'],
                'redirect' => route('subscription-panel.index')
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => $result['message']
        ], 422);
    }

    /**
     * Display payment methods and instructions.
     */
    public function paymentMethods()
    {
        $user = auth()->user();
        $subscription = $this->getCurrentSubscription();

        // Get pending payments
        $pendingPayments = SubscriptionPayment::where('user_id', $user->id)
                                             ->where('status', 'pending')
                                             ->latest()
                                             ->get();

        return view('subscription-panel.payment-methods', compact(
            'subscription',
            'pendingPayments'
        ));
    }

    /**
     * Submit payment confirmation.
     */
    public function submitPaymentConfirmation(Request $request)
    {
        $request->validate([
            'payment_id' => 'required|exists:subscription_payments,id',
            'transaction_reference' => 'required|string|max:255',
            'payment_date' => 'required|date',
            'notes' => 'nullable|string|max:1000',
        ]);

        $payment = SubscriptionPayment::where('id', $request->payment_id)
                                     ->where('user_id', auth()->id())
                                     ->where('status', 'pending')
                                     ->firstOrFail();

        $payment->update([
            'transaction_id' => $request->transaction_reference,
            'payment_date' => $request->payment_date,
            'status' => 'processing',
            'notes' => $request->notes,
        ]);

        // Create notification for admin to review
        SubscriptionNotification::createNotification([
            'user_subscription_id' => $payment->user_subscription_id,
            'tenant_id' => $payment->tenant_id,
            'user_id' => $payment->user_id,
            'type' => 'payment_succeeded',
            'title' => 'Payment confirmation submitted',
            'message' => 'Payment confirmation has been submitted and is pending admin review.',
            'priority' => 'medium',
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Payment confirmation submitted successfully. Your payment will be reviewed by our team.',
        ]);
    }

    /**
     * Display billing history.
     */
    public function billingHistory()
    {
        $user = auth()->user();
        
        $payments = SubscriptionPayment::where('user_id', $user->id)
                                      ->with('userSubscription.subscriptionPlan')
                                      ->latest()
                                      ->paginate(20);

        return view('subscription-panel.billing-history', compact('payments'));
    }

    /**
     * Download invoice.
     */
    public function downloadInvoice(SubscriptionPayment $payment)
    {
        // Ensure user owns this payment
        if ($payment->user_id !== auth()->id()) {
            abort(403);
        }

        // Load necessary relationships
        $payment->load([
            'user',
            'tenant',
            'userSubscription.subscriptionPlan'
        ]);

        // Generate PDF invoice
        $pdf = \PDF::loadView('subscription-panel.invoice-pdf', compact('payment'))
                   ->setPaper('a4', 'portrait');

        // Generate filename
        $filename = 'invoice-' . $payment->invoice_number . '-' . $payment->created_at->format('Y-m-d') . '.pdf';

        return $pdf->download($filename);
    }

    /**
     * Cancel subscription.
     */
    public function cancelSubscription(Request $request)
    {
        $request->validate([
            'reason' => 'required|string|max:255',
            'feedback' => 'nullable|string|max:1000',
        ]);

        $subscription = $this->getCurrentSubscription();
        
        if (!$subscription) {
            return response()->json([
                'success' => false,
                'message' => 'No active subscription found.'
            ], 404);
        }

        $subscription->cancel($request->reason, $request->feedback);

        return response()->json([
            'success' => true,
            'message' => 'Subscription cancelled successfully.',
            'redirect' => route('subscription-panel.index')
        ]);
    }

    /**
     * Get usage statistics for the subscription.
     */
    protected function getUsageStatistics($subscription): array
    {
        if (!$subscription) {
            return [];
        }

        $metrics = ['users', 'orders', 'storage', 'buyers', 'garment_orders', 'stock_items'];
        $stats = [];

        foreach ($metrics as $metric) {
            $usage = SubscriptionUsage::where('user_subscription_id', $subscription->id)
                                    ->where('metric_type', $metric)
                                    ->currentPeriod()
                                    ->first();

            $stats[$metric] = [
                'current' => $usage ? $usage->current_count : 0,
                'limit' => $subscription->subscriptionPlan->getLimit($metric),
                'percentage' => $usage ? $usage->percentage_used : 0,
                'unlimited' => $subscription->subscriptionPlan->isUnlimited($metric),
            ];
        }

        return $stats;
    }

    /**
     * Get subscription status information.
     */
    protected function getSubscriptionStatusInfo($subscription): array
    {
        if (!$subscription) {
            return [
                'status' => 'inactive',
                'message' => 'No active subscription',
                'days_remaining' => 0,
                'show_upgrade' => true,
            ];
        }

        $status = $subscription->getCurrentPeriodType();
        $daysRemaining = $subscription->getDaysRemaining();
        $isPremiumPlan = $subscription->subscriptionPlan->isPremium();

        $statusInfo = [
            'status' => $status,
            'days_remaining' => $daysRemaining,
            'show_upgrade' => false,
        ];

        switch ($status) {
            case 'trial':
                if ($isPremiumPlan) {
                    // Premium user in trial period - don't show upgrade
                    $statusInfo['message'] = "Your premium trial expires in {$daysRemaining} days.";
                    $statusInfo['show_upgrade'] = false;
                } else {
                    // Free tier user in trial period - show upgrade
                    $statusInfo['message'] = "Your free trial expires in {$daysRemaining} days.";
                    $statusInfo['show_upgrade'] = true;
                }
                break;
            case 'grace_period':
                $statusInfo['message'] = "You're in a grace period. {$daysRemaining} days remaining.";
                $statusInfo['show_upgrade'] = true;
                break;
            case 'active':
                if ($subscription->subscriptionPlan->isFree()) {
                    $statusInfo['message'] = "Your free tier expires in {$daysRemaining} days.";
                    $statusInfo['show_upgrade'] = true;
                } else {
                    $statusInfo['message'] = "Your premium subscription is active.";
                    $statusInfo['show_upgrade'] = false;
                }
                break;
            default:
                $statusInfo['message'] = "Your subscription is {$status}.";
                $statusInfo['show_upgrade'] = !$isPremiumPlan;
        }

        return $statusInfo;
    }

    /**
     * Display usage details.
     */
    public function usage()
    {
        $subscription = $this->getCurrentSubscription();

        if (!$subscription) {
            return redirect()->route('subscription-panel.index')
                           ->with('error', 'No active subscription found.');
        }

        $usageStats = $this->getUsageStatistics($subscription);

        return view('subscription-panel.usage', compact('subscription', 'usageStats'));
    }

    /**
     * Get the current user's active subscription.
     */
    protected function getCurrentSubscription()
    {
        return auth()->user()->activeSubscription;
    }
}
