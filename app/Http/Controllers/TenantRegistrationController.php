<?php

namespace App\Http\Controllers;

use App\Models\Tenant;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Illuminate\Validation\Rules\Password;

class TenantRegistrationController extends Controller
{
    /**
     * Show the tenant registration form.
     */
    public function showRegistrationForm()
    {
        return view('tenant.register');
    }

    /**
     * Handle tenant registration.
     */
    public function register(Request $request)
    {
        $validated = $request->validate([
            'company_name' => ['required', 'string', 'max:255'],
            'subdomain' => [
                'required', 
                'string', 
                'max:50', 
                'regex:/^[a-z0-9-]+$/',
                'unique:tenants,name',
                function ($attribute, $value, $fail) {
                    $reserved = ['www', 'admin', 'api', 'app', 'mail', 'ftp', 'localhost', 'system', 'support'];
                    if (in_array(strtolower($value), $reserved)) {
                        $fail('This subdomain is reserved and cannot be used.');
                    }
                }
            ],
            'contact_person' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:tenants,email'],
            'phone' => ['nullable', 'string', 'max:20'],
            'country' => ['required', 'string', 'max:100'],
            'password' => ['required', 'confirmed', Password::defaults()],
            'terms' => ['required', 'accepted'],
        ]);

        DB::beginTransaction();

        try {
            // Create tenant
            $tenant = Tenant::create([
                'name' => strtolower($validated['subdomain']),
                'company_name' => $validated['company_name'],
                'contact_person' => $validated['contact_person'],
                'email' => $validated['email'],
                'phone' => $validated['phone'] ?? null,
                'country' => $validated['country'],
                'database_username' => 'tenant_' . strtolower($validated['subdomain']),
                'database_password' => Str::random(16),
                'plan' => 'trial',
                'trial_ends_at' => now()->addDays(30), // 30-day trial
                'is_active' => true,
            ]);

            // Configure tenant database connection
            $tenant->configure();

            // Create admin user in tenant database
            $adminUser = User::on('tenant')->create([
                'name' => $validated['contact_person'],
                'email' => $validated['email'],
                'password' => Hash::make($validated['password']),
                'role' => 'admin',
                'email_verified_at' => now(),
            ]);

            // Assign admin role using Spatie permissions
            $adminUser->assignRole('admin');

            DB::commit();

            // Send welcome email (implement as needed)
            // $this->sendWelcomeEmail($tenant, $adminUser);

            return redirect()
                ->route('tenant.registration.success')
                ->with('tenant', $tenant);

        } catch (\Exception $e) {
            DB::rollBack();
            
            // Clean up tenant if creation failed
            if (isset($tenant)) {
                $tenant->forceDelete();
            }

            \Log::error('Tenant registration failed: ' . $e->getMessage());

            return back()
                ->withInput()
                ->withErrors(['error' => 'Registration failed. Please try again.']);
        }
    }

    /**
     * Show registration success page.
     */
    public function registrationSuccess()
    {
        $tenant = session('tenant');
        
        if (!$tenant) {
            return redirect()->route('tenant.register');
        }

        return view('tenant.registration-success', compact('tenant'));
    }

    /**
     * Check subdomain availability.
     */
    public function checkSubdomain(Request $request)
    {
        $subdomain = $request->input('subdomain');
        
        if (!$subdomain) {
            return response()->json(['available' => false, 'message' => 'Subdomain is required']);
        }

        // Check format
        if (!preg_match('/^[a-z0-9-]+$/', $subdomain)) {
            return response()->json(['available' => false, 'message' => 'Subdomain can only contain lowercase letters, numbers, and hyphens']);
        }

        // Check reserved names
        $reserved = ['www', 'admin', 'api', 'app', 'mail', 'ftp', 'localhost', 'system', 'support'];
        if (in_array(strtolower($subdomain), $reserved)) {
            return response()->json(['available' => false, 'message' => 'This subdomain is reserved']);
        }

        // Check availability
        $exists = Tenant::where('name', strtolower($subdomain))->exists();
        
        return response()->json([
            'available' => !$exists,
            'message' => $exists ? 'This subdomain is already taken' : 'Subdomain is available'
        ]);
    }

    /**
     * Show tenant not found page.
     */
    public function tenantNotFound()
    {
        return view('tenant.not-found');
    }

    /**
     * Send welcome email to new tenant.
     */
    protected function sendWelcomeEmail(Tenant $tenant, User $user)
    {
        // Implement email sending logic
        // You can use Laravel's Mail facade or notification system
    }
}
