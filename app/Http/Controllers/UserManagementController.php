<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Rules\StrongPassword;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;
use Spatie\Permission\Models\Role;

class UserManagementController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('tenant');
    }

    /**
     * Display a listing of users.
     */
    public function index(Request $request)
    {
        $user = auth()->user();
        
        // Get users based on hierarchy
        if ($user->isMasterAccount()) {
            $query = User::query();
        } else {
            $userIds = $user->getHierarchyUsers()->pluck('id');
            $query = User::whereIn('id', $userIds);
        }

        // Apply filters
        if ($request->filled('role')) {
            $query->where('role', $request->role);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status === 'active');
        }

        if ($request->filled('department')) {
            $query->where('department', $request->department);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('employee_id', 'like', "%{$search}%");
            });
        }

        $users = $query->with(['creator', 'roles'])
                      ->orderBy('created_at', 'desc')
                      ->paginate(15);

        // Get filter options
        $roles = $user->getAssignableRoles();
        $departments = User::distinct()->pluck('department')->filter()->sort();

        return view('user-management.index', compact('users', 'roles', 'departments'));
    }

    /**
     * Show the form for creating a new user.
     */
    public function create()
    {
        $user = auth()->user();
        $roles = $user->getAssignableRoles();
        
        if (empty($roles)) {
            abort(403, 'You do not have permission to create users.');
        }

        $departments = User::distinct()->pluck('department')->filter()->sort();

        return view('user-management.create', compact('roles', 'departments'));
    }

    /**
     * Store a newly created user.
     */
    public function store(Request $request)
    {
        $user = auth()->user();
        
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users'],
            'phone' => ['nullable', 'string', 'max:20'],
            'role' => ['required', 'string', Rule::in(array_keys($user->getAssignableRoles()))],
            'department' => ['nullable', 'string', 'max:100'],
            'employee_id' => ['nullable', 'string', 'max:50', 'unique:users'],
            'country' => ['nullable', 'string', 'max:100'],
            'timezone' => ['nullable', 'string', 'max:50'],
            'language' => ['nullable', 'string', 'max:10'],
            'password' => ['required', 'confirmed', new StrongPassword()],
            'status' => ['boolean'],
        ]);

        if (!$user->canCreateRole($validated['role'])) {
            return back()->withErrors(['role' => 'You do not have permission to create users with this role.']);
        }

        $validated['password'] = Hash::make($validated['password']);
        $validated['status'] = $request->boolean('status', true);
        
        $newUser = $user->createSubUser($validated);

        return redirect()
            ->route('user-management.show', $newUser)
            ->with('success', 'User created successfully.');
    }

    /**
     * Display the specified user.
     */
    public function show(User $user)
    {
        $currentUser = auth()->user();
        
        if (!$currentUser->canManageUser($user)) {
            abort(403, 'You do not have permission to view this user.');
        }

        $user->load(['creator', 'createdUsers', 'roles', 'permissions']);

        return view('user-management.show', compact('user'));
    }

    /**
     * Show the form for editing the specified user.
     */
    public function edit(User $user)
    {
        $currentUser = auth()->user();
        
        if (!$currentUser->canManageUser($user)) {
            abort(403, 'You do not have permission to edit this user.');
        }

        $roles = $currentUser->getAssignableRoles();
        $departments = User::distinct()->pluck('department')->filter()->sort();

        return view('user-management.edit', compact('user', 'roles', 'departments'));
    }

    /**
     * Update the specified user.
     */
    public function update(Request $request, User $user)
    {
        $currentUser = auth()->user();
        
        if (!$currentUser->canManageUser($user)) {
            abort(403, 'You do not have permission to edit this user.');
        }

        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', Rule::unique('users')->ignore($user->id)],
            'phone' => ['nullable', 'string', 'max:20'],
            'role' => ['required', 'string', Rule::in(array_keys($currentUser->getAssignableRoles()))],
            'department' => ['nullable', 'string', 'max:100'],
            'employee_id' => ['nullable', 'string', 'max:50', Rule::unique('users')->ignore($user->id)],
            'country' => ['nullable', 'string', 'max:100'],
            'timezone' => ['nullable', 'string', 'max:50'],
            'language' => ['nullable', 'string', 'max:10'],
            'status' => ['boolean'],
        ]);

        if (!$currentUser->canCreateRole($validated['role'])) {
            return back()->withErrors(['role' => 'You do not have permission to assign this role.']);
        }

        $validated['status'] = $request->boolean('status', true);
        
        $user->update($validated);
        
        // Update role if changed
        if ($user->role !== $validated['role']) {
            $user->syncRoles([$validated['role']]);
            $user->update(['role' => $validated['role']]);
        }

        return redirect()
            ->route('user-management.show', $user)
            ->with('success', 'User updated successfully.');
    }

    /**
     * Remove the specified user.
     */
    public function destroy(User $user)
    {
        $currentUser = auth()->user();
        
        if (!$currentUser->canManageUser($user)) {
            abort(403, 'You do not have permission to delete this user.');
        }

        if ($user->id === $currentUser->id) {
            return back()->withErrors(['error' => 'You cannot delete your own account.']);
        }

        // Check if user has created other users
        if ($user->createdUsers()->count() > 0) {
            return back()->withErrors(['error' => 'Cannot delete user who has created other users. Please reassign or delete their created users first.']);
        }

        $user->delete();

        return redirect()
            ->route('user-management.index')
            ->with('success', 'User deleted successfully.');
    }

    /**
     * Toggle user status (activate/deactivate).
     */
    public function toggleStatus(User $user)
    {
        $currentUser = auth()->user();
        
        if (!$currentUser->canManageUser($user)) {
            abort(403, 'You do not have permission to change this user\'s status.');
        }

        if ($user->id === $currentUser->id) {
            return back()->withErrors(['error' => 'You cannot deactivate your own account.']);
        }

        if ($user->status) {
            $user->deactivate();
            $message = 'User deactivated successfully.';
        } else {
            $user->activate();
            $message = 'User activated successfully.';
        }

        return back()->with('success', $message);
    }

    /**
     * Reset user password.
     */
    public function resetPassword(Request $request, User $user)
    {
        $currentUser = auth()->user();
        
        if (!$currentUser->canManageUser($user)) {
            abort(403, 'You do not have permission to reset this user\'s password.');
        }

        $validated = $request->validate([
            'password' => ['required', 'confirmed', new StrongPassword()],
        ]);

        $user->update([
            'password' => Hash::make($validated['password']),
        ]);

        return back()->with('success', 'Password reset successfully.');
    }

    /**
     * Get user hierarchy data for API.
     */
    public function getHierarchy()
    {
        $user = auth()->user();
        $users = $user->getHierarchyUsers();

        $hierarchy = $this->buildHierarchyTree($users, null);

        return response()->json($hierarchy);
    }

    /**
     * Build hierarchy tree structure.
     */
    private function buildHierarchyTree($users, $parentId)
    {
        $tree = [];
        
        foreach ($users->where('created_by', $parentId) as $user) {
            $node = [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'role' => $user->getRoleDisplayName(),
                'status' => $user->status,
                'children' => $this->buildHierarchyTree($users, $user->id)
            ];
            
            $tree[] = $node;
        }
        
        return $tree;
    }
}
