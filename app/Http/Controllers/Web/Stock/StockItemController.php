<?php

namespace App\Http\Controllers\Web\Stock;

use App\Http\Controllers\Controller;
use App\Http\Requests\Stock\StockItemRequest;
use App\Services\Stock\Enhanced\StockItemService;
use App\Exceptions\Stock\StockItemException;
use App\Traits\HasSubscriptionFeatures;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Log;

class StockItemController extends Controller
{
    use HasSubscriptionFeatures;

    protected $stockItemService;

    public function __construct(StockItemService $stockItemService)
    {
        $this->stockItemService = $stockItemService;

        // Apply permissions
        $this->middleware('permission:stock.items.view')->only(['index', 'show']);
        $this->middleware('permission:stock.items.create')->only(['create', 'store']);
        $this->middleware('permission:stock.items.update')->only(['edit', 'update']);
        $this->middleware('permission:stock.items.delete')->only(['destroy']);

        // Apply subscription middleware
        $this->middleware('subscription');
        $this->middleware('feature.gate:stock_management');
        $this->middleware('usage.limit:stock_items')->only(['store']);
    }

    /**
     * Display a listing of stock items
     */
    public function index(Request $request): View
    {
        try {
            $filters = $request->only([
                'category_id', 'item_type', 'is_active', 'search', 
                'location_id', 'low_stock', 'out_of_stock', 'overstock'
            ]);

            $perPage = $request->get('per_page', 15);
            $with = ['category', 'primaryLocation', 'alerts'];

            $items = $this->stockItemService->getPaginatedItems($filters, $perPage, $with);
            $summary = $this->stockItemService->getStockSummary();

            return view('stock.items.index', compact('items', 'summary', 'filters'));

        } catch (StockItemException $e) {
            return redirect()->back()
                           ->with('error', $e->getMessage());

        } catch (\Exception $e) {
            Log::error('Error in stock items index', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->back()
                           ->with('error', 'An error occurred while retrieving stock items');
        }
    }

    /**
     * Show the form for creating a new stock item
     */
    public function create(): View
    {
        return view('stock.items.create');
    }

    /**
     * Store a newly created stock item
     */
    public function store(StockItemRequest $request): RedirectResponse
    {
        try {
            $item = $this->stockItemService->createItem($request->validated());

            // Update usage tracking
            $this->incrementUsage('stock_items');

            return redirect()->route('stock.items.show', $item->id)
                           ->with('success', 'Stock item created successfully');

        } catch (StockItemException $e) {
            return redirect()->back()
                           ->withInput()
                           ->with('error', $e->getMessage());

        } catch (\Exception $e) {
            Log::error('Error creating stock item', [
                'data' => $request->validated(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->back()
                           ->withInput()
                           ->with('error', 'An error occurred while creating the stock item');
        }
    }

    /**
     * Display the specified stock item
     */
    public function show(Request $request, int $id): View
    {
        try {
            $with = [
                'category', 'primaryLocation', 'locationItems.location', 
                'batches', 'alerts', 'stockMovements.location', 'stockMovements.creator'
            ];

            $item = $this->stockItemService->findItem($id, $with);

            if (!$item) {
                abort(404, 'Stock item not found');
            }

            return view('stock.items.show', compact('item'));

        } catch (StockItemException $e) {
            return redirect()->route('stock.items.index')
                           ->with('error', $e->getMessage());

        } catch (\Exception $e) {
            Log::error('Error showing stock item', [
                'id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->route('stock.items.index')
                           ->with('error', 'An error occurred while retrieving the stock item');
        }
    }

    /**
     * Show the form for editing the specified stock item
     */
    public function edit(int $id): View
    {
        try {
            $item = $this->stockItemService->findItem($id, ['category', 'primaryLocation']);

            if (!$item) {
                abort(404, 'Stock item not found');
            }

            return view('stock.items.edit', compact('item'));

        } catch (StockItemException $e) {
            return redirect()->route('stock.items.index')
                           ->with('error', $e->getMessage());

        } catch (\Exception $e) {
            Log::error('Error editing stock item', [
                'id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->route('stock.items.index')
                           ->with('error', 'An error occurred while retrieving the stock item');
        }
    }

    /**
     * Update the specified stock item
     */
    public function update(StockItemRequest $request, int $id): RedirectResponse
    {
        try {
            $updated = $this->stockItemService->updateItem($id, $request->validated());

            if (!$updated) {
                return redirect()->back()
                               ->withInput()
                               ->with('error', 'Stock item not found or could not be updated');
            }

            return redirect()->route('stock.items.show', $id)
                           ->with('success', 'Stock item updated successfully');

        } catch (StockItemException $e) {
            return redirect()->back()
                           ->withInput()
                           ->with('error', $e->getMessage());

        } catch (\Exception $e) {
            Log::error('Error updating stock item', [
                'id' => $id,
                'data' => $request->validated(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->back()
                           ->withInput()
                           ->with('error', 'An error occurred while updating the stock item');
        }
    }

    /**
     * Remove the specified stock item
     */
    public function destroy(int $id): RedirectResponse
    {
        try {
            $deleted = $this->stockItemService->deleteItem($id);

            if (!$deleted) {
                return redirect()->back()
                               ->with('error', 'Stock item not found or could not be deleted');
            }

            // Update usage tracking
            $this->decrementUsage('stock_items');

            return redirect()->route('stock.items.index')
                           ->with('success', 'Stock item deleted successfully');

        } catch (StockItemException $e) {
            return redirect()->back()
                           ->with('error', $e->getMessage());

        } catch (\Exception $e) {
            Log::error('Error deleting stock item', [
                'id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->back()
                           ->with('error', 'An error occurred while deleting the stock item');
        }
    }

    /**
     * Search stock items (AJAX)
     */
    public function search(Request $request)
    {
        try {
            $request->validate([
                'query' => 'required|string|min:2|max:100'
            ]);

            $query = $request->get('query');
            $items = $this->stockItemService->searchItems($query, ['category']);

            return response()->json([
                'success' => true,
                'data' => $items->map(function ($item) {
                    return [
                        'id' => $item->id,
                        'sku' => $item->sku,
                        'name' => $item->name,
                        'category' => $item->category->name ?? null,
                        'current_quantity' => $item->current_quantity,
                        'available_quantity' => $item->available_quantity,
                        'unit_of_measure' => $item->unit_of_measure,
                        'stock_status' => $item->stock_status,
                    ];
                })
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);

        } catch (StockItemException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);

        } catch (\Exception $e) {
            Log::error('Error searching stock items', [
                'query' => $request->get('query'),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while searching stock items'
            ], 500);
        }
    }

    /**
     * Get stock summary (AJAX)
     */
    public function summary()
    {
        try {
            $summary = $this->stockItemService->getStockSummary();

            return response()->json([
                'success' => true,
                'data' => $summary
            ]);

        } catch (StockItemException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);

        } catch (\Exception $e) {
            Log::error('Error getting stock summary', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while retrieving stock summary'
            ], 500);
        }
    }

    /**
     * Reserve stock quantity (AJAX)
     */
    public function reserve(Request $request, int $id)
    {
        try {
            $request->validate([
                'quantity' => 'required|numeric|min:0.0001',
                'reason' => 'nullable|string|max:255'
            ]);

            $reserved = $this->stockItemService->reserveStock(
                $id,
                $request->get('quantity'),
                $request->get('reason')
            );

            if (!$reserved) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to reserve stock'
                ], 400);
            }

            return response()->json([
                'success' => true,
                'message' => 'Stock reserved successfully'
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);

        } catch (StockItemException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);

        } catch (\Exception $e) {
            Log::error('Error reserving stock', [
                'id' => $id,
                'quantity' => $request->get('quantity'),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred while reserving stock'
            ], 500);
        }
    }
}
