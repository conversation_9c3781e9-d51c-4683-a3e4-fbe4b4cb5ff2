<?php

namespace App\Http\Controllers;

use App\Services\ZKTecoService;
use App\Models\Employee;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class ZKTecoSettingsController extends Controller
{
    protected $zktecoService;

    public function __construct(ZKTecoService $zktecoService)
    {
        $this->middleware('permission:employees-read');
        $this->zktecoService = $zktecoService;
    }

    /**
     * Show ZKTeco settings page
     */
    public function index()
    {
        $config = config('zkteco');
        $machines = $config['machines'];

        // Test connection status for each machine
        $machineStatus = [];
        foreach ($machines as $key => $machine) {
            $cacheKey = "zkteco_status_{$key}";
            $status = Cache::remember($cacheKey, 300, function() use ($machine) {
                return $this->zktecoService->testConnection($machine['ip'], $machine['port']);
            });
            $machineStatus[$key] = $status;
        }

        return view('hr.settings.zkteco', compact('config', 'machines', 'machineStatus'));
    }

    /**
     * Test connection to a specific machine
     */
    public function testConnection(Request $request)
    {
        $request->validate([
            'ip' => 'required|ip',
            'port' => 'required|integer|min:1|max:65535',
        ]);

        $result = $this->zktecoService->testConnection($request->ip, $request->port);

        return response()->json($result);
    }

    /**
     * Sync attendance data manually
     */
    public function syncAttendance(Request $request)
    {
        $request->validate([
            'date' => 'required|date|before_or_equal:today',
            'machine_ip' => 'required|ip',
            'dry_run' => 'boolean',
        ]);

        $date = $request->date;
        $machineIp = $request->machine_ip;
        $dryRun = $request->boolean('dry_run', false);

        try {
            // Fetch attendance logs
            $logsResult = $this->zktecoService->fetchAttendanceLogs($date, $machineIp);

            if (!$logsResult['success']) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to fetch logs: ' . $logsResult['message']
                ]);
            }

            // Process logs
            $processResult = $this->zktecoService->processAttendanceLogs(
                $logsResult['data'],
                $date,
                $dryRun
            );

            return response()->json([
                'success' => $processResult['success'],
                'message' => $dryRun ? 'Dry run completed successfully' : 'Attendance synced successfully',
                'data' => $processResult
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Sync failed: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Show employee mapping page
     */
    public function employeeMapping()
    {
        $config = config('zkteco');
        $machines = $config['machines'];

        // Get employees with and without machine_user_id
        $mappedEmployees = Employee::whereNotNull('machine_user_id')
            ->where('status', 1)
            ->orderBy('name')
            ->get();

        $unmappedEmployees = Employee::whereNull('machine_user_id')
            ->where('status', 1)
            ->orderBy('name')
            ->get();

        return view('hr.settings.zkteco-employee-mapping', compact(
            'config',
            'machines',
            'mappedEmployees',
            'unmappedEmployees'
        ));
    }

    /**
     * Fetch device users from a specific machine
     */
    public function fetchDeviceUsers(Request $request)
    {
        // Add debug logging
        \Log::info('fetchDeviceUsers called', [
            'request_data' => $request->all(),
            'machine_ip' => $request->machine_ip,
            'timestamp' => now()
        ]);

        $request->validate([
            'machine_ip' => 'required|ip',
        ]);

        try {
            $machineIp = $request->machine_ip;

            // Get device users using the ZKTeco service
            $connectionTest = $this->zktecoService->testConnection($machineIp);

            if (!$connectionTest['success']) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot connect to device: ' . $connectionTest['message']
                ]);
            }

            // For now, we'll use the adapter to get users
            $adapter = $this->zktecoService->getAdapter();
            $connected = $adapter->connect($machineIp);

            if (!$connected) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to connect to device: ' . $adapter->getLastError()
                ]);
            }

            $deviceUsers = $adapter->getUsers();
            $adapter->disconnect();

            return response()->json([
                'success' => true,
                'users' => $deviceUsers
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error fetching device users: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Save employee-device mappings
     */
    public function saveMappings(Request $request)
    {
        try {
            // Handle both JSON and FormData formats
            $mappings = $request->input('mappings');

            // If mappings is a string (from FormData), decode it
            if (is_string($mappings)) {
                $mappings = json_decode($mappings, true);
            }

            if (!is_array($mappings) || empty($mappings)) {
                return response()->json([
                    'success' => false,
                    'message' => 'No valid mappings provided'
                ]);
            }

            // Validate each mapping
            foreach ($mappings as $mapping) {
                if (!isset($mapping['employee_id']) || !isset($mapping['machine_user_id'])) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Invalid mapping format'
                    ]);
                }
            }

            DB::transaction(function() use ($mappings) {
                foreach ($mappings as $mapping) {
                    $employee = Employee::find($mapping['employee_id']);

                    if ($employee) {
                        $employee->update([
                            'machine_user_id' => $mapping['machine_user_id']
                        ]);
                    }
                }
            });

            return response()->json([
                'success' => true,
                'message' => 'Employee mappings saved successfully',
                'count' => count($mappings)
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error saving mappings: ' . $e->getMessage()
            ]);
        }
    }
}
