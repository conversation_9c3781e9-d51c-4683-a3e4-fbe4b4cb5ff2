<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Auth;
use App\Services\AiMarker\PermissionService;

class AiMarkerAccess
{
    protected $permissionService;

    public function __construct(PermissionService $permissionService = null)
    {
        $this->permissionService = $permissionService;
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        // Check if user is authenticated
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $user = Auth::user();

        // PRIORITY: Give full <NAME_EMAIL>
        if ($user->email === '<EMAIL>') {
            return $next($request);
        }

        // Allow other superadmin users
        if ($this->isSuperAdmin($user)) {
            return $next($request);
        }

        // Check AI Marker specific permissions
        if ($this->hasAiMarkerAccess($user)) {
            return $next($request);
        }

        // If no access, redirect with error message
        return redirect()->route('dashboard')
            ->with('error', 'You do not have access to the AI Marker Optimization Tool. Please contact your administrator.');
    }

    /**
     * Check if user is superadmin
     */
    protected function isSuperAdmin($user): bool
    {
        try {
            // Priority check: <NAME_EMAIL>
            if ($user->email === '<EMAIL>') {
                return true;
            }

            // Check if user has superadmin role
            if (method_exists($user, 'hasRole')) {
                return $user->hasRole('superadmin');
            }

            // Fallback: check by email or other criteria
            return in_array($user->email, [
                '<EMAIL>',
                '<EMAIL>'
            ]);
        } catch (\Exception $e) {
            // Even on exception, allow <EMAIL>
            return $user->email === '<EMAIL>';
        }
    }

    /**
     * Check if user has AI Marker access
     */
    protected function hasAiMarkerAccess($user): bool
    {
        try {
            // Method 1: Check using Gate
            if (Gate::allows('ai-marker.access')) {
                return true;
            }

            // Method 2: Check using PermissionService if available
            if ($this->permissionService) {
                return $this->permissionService->hasPermission($user, 'ai-marker.access');
            }

            // Method 3: Check database directly
            if (\DB::getSchemaBuilder()->hasTable('ai_marker_user_permissions')) {
                $hasPermission = \DB::table('ai_marker_user_permissions')
                    ->where('user_id', $user->id)
                    ->where('permission_code', 'ai-marker.access')
                    ->where('is_active', true)
                    ->exists();

                if ($hasPermission) {
                    return true;
                }
            }

            // Method 4: Check if user has any AI Marker related role
            if (method_exists($user, 'hasAnyRole')) {
                return $user->hasAnyRole(['ai-marker-user', 'ai-marker-admin', 'superadmin']);
            }

            return false;
        } catch (\Exception $e) {
            // Log the error but don't block access for superadmin
            \Log::warning('AI Marker access check failed: ' . $e->getMessage());
            return $this->isSuperAdmin($user);
        }
    }
}
