<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\RateLimiter;
use Symfony\Component\HttpFoundation\Response;

class AuthRateLimit
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        $key = $this->resolveRequestSignature($request);
        
        // Check if IP is temporarily blocked
        if (Cache::has("blocked_ip_{$request->ip()}")) {
            return response()->json([
                'message' => 'Too many failed attempts. Please try again later.',
                'retry_after' => Cache::get("blocked_ip_{$request->ip()}")
            ], 429);
        }

        // Rate limit: 5 attempts per minute per IP
        if (RateLimiter::tooManyAttempts($key, 5)) {
            $seconds = RateLimiter::availableIn($key);
            
            // Block IP for 15 minutes after exceeding rate limit
            Cache::put("blocked_ip_{$request->ip()}", now()->addMinutes(15), 900);
            
            return response()->json([
                'message' => 'Too many login attempts. Please try again later.',
                'retry_after' => $seconds
            ], 429);
        }

        $response = $next($request);

        // If login failed, increment the rate limiter
        if ($response->getStatusCode() === 422 || $response->getStatusCode() === 401) {
            RateLimiter::hit($key, 60); // 1 minute decay
            
            // Track failed attempts for this IP
            $failedAttempts = Cache::get("failed_attempts_{$request->ip()}", 0) + 1;
            Cache::put("failed_attempts_{$request->ip()}", $failedAttempts, 3600); // 1 hour
            
            // Block IP after 10 failed attempts in an hour
            if ($failedAttempts >= 10) {
                Cache::put("blocked_ip_{$request->ip()}", now()->addHours(1), 3600);
            }
        } else {
            // Clear rate limiter on successful login
            RateLimiter::clear($key);
            Cache::forget("failed_attempts_{$request->ip()}");
        }

        return $response;
    }

    /**
     * Resolve the request signature for rate limiting.
     */
    protected function resolveRequestSignature(Request $request): string
    {
        return sha1(
            $request->method() .
            '|' . $request->server('SERVER_NAME') .
            '|' . $request->path() .
            '|' . $request->ip()
        );
    }
}
