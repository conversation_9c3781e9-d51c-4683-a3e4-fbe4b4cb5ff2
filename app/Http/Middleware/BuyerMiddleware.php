<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Models\Party;

class BuyerMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = auth()->user();
        
        if (!$user) {
            return redirect()->route('login');
        }

        // Check if the user is associated with a buyer party
        $party = Party::where('user_id', $user->id)->where('type', 'buyer')->first();
        
        if (!$party) {
            abort(403, 'Access denied. You are not authorized to access the buyer portal.');
        }

        // Add the party to the request for easy access in controllers
        $request->merge(['buyer_party' => $party]);

        return $next($request);
    }
}
