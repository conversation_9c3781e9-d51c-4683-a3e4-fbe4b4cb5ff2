<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class FeatureGateMiddleware
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next, string ...$features): Response
    {
        $user = auth()->user();
        
        if (!$user) {
            return redirect()->route('login');
        }

        // Super admins bypass all feature gates
        if ($user->isSuperAdmin()) {
            return $next($request);
        }

        // Check each required feature
        foreach ($features as $feature) {
            if (!$user->canAccessFeature($feature)) {
                return $this->handleFeatureRestricted($request, $feature);
            }
        }

        return $next($request);
    }

    /**
     * Handle feature restricted access.
     */
    protected function handleFeatureRestricted(Request $request, string $feature): Response
    {
        $featureLabels = [
            'buyer_crm' => 'Buyer & Marketing CRM',
            'garment_orders' => 'Garments Orders Management',
            'stock_management' => 'Stock Management System',
            'financial_reports' => 'Financial Reports',
            'advanced_analytics' => 'Advanced Analytics',
            'api_access' => 'API Access',
            'custom_branding' => 'Custom Branding',
            'priority_support' => 'Priority Support',
            'data_export' => 'Data Export',
            'multi_location' => 'Multi-Location Management',
        ];

        $featureLabel = $featureLabels[$feature] ?? ucfirst(str_replace('_', ' ', $feature));
        $message = "{$featureLabel} is not available in your current plan. Upgrade to Premium to access this feature.";
        
        if ($request->expectsJson()) {
            return response()->json([
                'error' => 'Feature not available',
                'message' => $message,
                'feature' => $feature,
                'feature_label' => $featureLabel,
                'redirect' => route('subscription-panel.upgrade')
            ], 403);
        }

        // For web requests, show upgrade prompt
        return redirect()->route('subscription-panel.upgrade')
                        ->with('error', $message)
                        ->with('restricted_feature', $feature);
    }
}
