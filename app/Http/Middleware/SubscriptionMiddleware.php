<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Models\SubscriptionManagement\UserSubscription;
use App\Models\SubscriptionManagement\SubscriptionUsage;

class SubscriptionMiddleware
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next, string $feature = null): Response
    {
        $user = auth()->user();

        if (!$user) {
            return redirect()->route('login');
        }

        // Super admins bypass all subscription checks
        if ($user->isSuperAdmin()) {
            return $next($request);
        }

        // Allow access to subscription panel routes even without subscription
        // This prevents redirect loops when users need to manage their subscriptions
        if ($this->isSubscriptionPanelRoute($request)) {
            return $next($request);
        }

        // Get user's active subscription
        $subscription = $user->activeSubscription;

        if (!$subscription) {
            return $this->handleNoSubscription($request);
        }

        // Check subscription status
        if (!$this->isSubscriptionValid($subscription)) {
            return $this->handleInvalidSubscription($request, $subscription);
        }

        // Check feature access if specified
        if ($feature && !$subscription->canAccessFeature($feature)) {
            return $this->handleFeatureRestricted($request, $feature);
        }

        // Update user's last subscription activity
        $user->updateSubscriptionActivity();

        return $next($request);
    }

    /**
     * Check if subscription is valid for access.
     */
    protected function isSubscriptionValid(UserSubscription $subscription): bool
    {
        return $subscription->isActive() || 
               $subscription->isInTrial() || 
               $subscription->isInGracePeriod();
    }

    /**
     * Handle user with no subscription.
     */
    protected function handleNoSubscription(Request $request): Response
    {
        if ($request->expectsJson()) {
            return response()->json([
                'error' => 'No active subscription',
                'message' => 'You need an active subscription to access this feature.',
                'redirect' => route('subscription-panel.index')
            ], 402);
        }

        return redirect()->route('subscription-panel.index')
                        ->with('error', 'You need an active subscription to access this feature.');
    }

    /**
     * Handle invalid subscription.
     */
    protected function handleInvalidSubscription(Request $request, UserSubscription $subscription): Response
    {
        $message = $this->getSubscriptionStatusMessage($subscription);
        
        if ($request->expectsJson()) {
            return response()->json([
                'error' => 'Subscription inactive',
                'message' => $message,
                'redirect' => route('subscription-panel.index')
            ], 402);
        }

        return redirect()->route('subscription-panel.index')
                        ->with('error', $message);
    }

    /**
     * Handle feature restricted access.
     */
    protected function handleFeatureRestricted(Request $request, string $feature): Response
    {
        $message = "This feature is not available in your current plan. Upgrade to Premium to access {$feature}.";
        
        if ($request->expectsJson()) {
            return response()->json([
                'error' => 'Feature restricted',
                'message' => $message,
                'feature' => $feature,
                'redirect' => route('subscription-panel.upgrade')
            ], 403);
        }

        return redirect()->route('subscription-panel.upgrade')
                        ->with('error', $message);
    }

    /**
     * Check if the current request is for a subscription panel route.
     */
    protected function isSubscriptionPanelRoute(Request $request): bool
    {
        $routeName = $request->route()?->getName();

        return $routeName && str_starts_with($routeName, 'subscription-panel.');
    }

    /**
     * Get subscription status message.
     */
    protected function getSubscriptionStatusMessage(UserSubscription $subscription): string
    {
        if ($subscription->isExpired()) {
            return 'Your subscription has expired. Please renew to continue using the service.';
        }

        if ($subscription->isSuspended()) {
            return 'Your subscription is currently suspended. Please contact support for assistance.';
        }

        if ($subscription->isCancelled()) {
            return 'Your subscription has been cancelled. Reactivate to continue using the service.';
        }

        return 'Your subscription is not active. Please check your subscription status.';
    }
}
