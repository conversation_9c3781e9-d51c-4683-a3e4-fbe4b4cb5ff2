<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Services\UsageTrackingService;

class SubscriptionStatusMiddleware
{
    protected $usageTrackingService;

    public function __construct(UsageTrackingService $usageTrackingService)
    {
        $this->usageTrackingService = $usageTrackingService;
    }

    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        try {
            $user = auth()->user();

            if (!$user) {
                return $next($request);
            }

            // Check if user has the isSuperAdmin method (defensive programming)
            if (method_exists($user, 'isSuperAdmin') && $user->isSuperAdmin()) {
                return $next($request);
            }

            // Check if subscription system is available
            if (!method_exists($user, 'activeSubscription')) {
                return $next($request);
            }

            $subscription = $user->activeSubscription;

            if (!$subscription) {
                return $next($request);
            }

            // Get usage warnings (with error handling)
            $approachingLimits = [];
            $exceededLimits = [];

            try {
                $approachingLimits = $this->usageTrackingService->getApproachingLimits($subscription);
                $exceededLimits = $this->usageTrackingService->getExceededLimits($subscription);
            } catch (\Exception $e) {
                // If usage tracking fails, continue without warnings
                \Log::warning('Usage tracking failed in SubscriptionStatusMiddleware: ' . $e->getMessage());
            }

            // Share data with views
            view()->share('subscriptionWarnings', [
                'subscription' => $subscription,
                'approaching_limits' => $approachingLimits,
                'exceeded_limits' => $exceededLimits,
                'days_remaining' => method_exists($subscription, 'getDaysRemaining') ? $subscription->getDaysRemaining() : 0,
                'status' => method_exists($subscription, 'getCurrentPeriodType') ? $subscription->getCurrentPeriodType() : 'unknown',
                'show_upgrade' => $this->shouldShowUpgrade($subscription, $exceededLimits),
            ]);

        } catch (\Exception $e) {
            // If anything fails, log it and continue without subscription warnings
            \Log::warning('SubscriptionStatusMiddleware failed: ' . $e->getMessage());
        }

        return $next($request);
    }

    /**
     * Determine if upgrade should be shown.
     */
    protected function shouldShowUpgrade($subscription, array $exceededLimits): bool
    {
        try {
            if (!$subscription || !method_exists($subscription, 'subscriptionPlan')) {
                return false;
            }

            $plan = $subscription->subscriptionPlan;
            if (!$plan || !method_exists($plan, 'isFree')) {
                return false;
            }

            return $plan->isFree() ||
                   (method_exists($subscription, 'isInGracePeriod') && $subscription->isInGracePeriod()) ||
                   !empty($exceededLimits);
        } catch (\Exception $e) {
            return false;
        }
    }
}
