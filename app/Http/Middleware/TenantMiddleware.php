<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Models\Tenant;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Facades\Config;

class TenantMiddleware
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if tenants table exists (for development/transition mode)
        try {
            \DB::connection()->getPdo();
            if (!\Schema::hasTable('tenants')) {
                // Running in single-tenant mode, skip tenant resolution
                return $next($request);
            }
        } catch (\Exception $e) {
            // Database connection issues, skip tenant resolution
            return $next($request);
        }

        $host = $request->getHost();

        // Skip tenant resolution for main domain admin routes
        if ($this->isMainDomain($host) || $this->isAdminRoute($request)) {
            return $next($request);
        }

        // For localhost (development), use user-based tenant resolution
        if ($this->isLocalhost($host)) {
            return $this->handleLocalhostTenantResolution($request, $next);
        }

        // Find tenant by domain
        try {
            $tenant = Tenant::findByDomain($host);
        } catch (\Exception $e) {
            // If tenant model fails, continue without tenant resolution
            return $next($request);
        }

        if (!$tenant) {
            // For localhost or development, continue without tenant
            if ($this->isLocalhost($host)) {
                return $next($request);
            }
            // Redirect to main domain or show tenant not found page
            return $this->handleTenantNotFound($request);
        }

        if (!$tenant->is_active) {
            return $this->handleInactiveTenant($tenant);
        }

        if (!$tenant->hasActiveSubscription()) {
            return $this->handleExpiredSubscription($tenant);
        }

        // Set current tenant
        app()->instance('tenant', $tenant);
        $request->attributes->set('tenant', $tenant);

        // Configure tenant database
        $tenant->configure();

        // Set tenant-specific configurations
        $this->configureTenant($tenant);

        // Share tenant data with views
        View::share('tenant', $tenant);

        return $next($request);
    }

    /**
     * Handle tenant resolution for localhost (development mode).
     */
    protected function handleLocalhostTenantResolution(Request $request, Closure $next): Response
    {
        // Only resolve tenant for authenticated users
        if (!auth()->check()) {
            return $next($request);
        }

        $user = auth()->user();
        $tenant = Tenant::where('email', $user->email)->first();

        if ($tenant && $tenant->is_active) {
            // Set current tenant
            app()->instance('tenant', $tenant);
            $request->attributes->set('tenant', $tenant);

            // Configure tenant database (if needed)
            if (app()->environment('production')) {
                $tenant->configure();
            }

            // Set tenant-specific configurations
            $this->configureTenant($tenant);

            // Share tenant data with views
            View::share('tenant', $tenant);
        }

        return $next($request);
    }

    /**
     * Check if the host is the main domain.
     */
    protected function isMainDomain(string $host): bool
    {
        $mainDomain = config('app.domain', 'localhost');
        return $host === $mainDomain || $host === "www.{$mainDomain}";
    }

    /**
     * Check if the host is localhost (development mode).
     */
    protected function isLocalhost(string $host): bool
    {
        return in_array($host, ['localhost', '127.0.0.1', '::1']) ||
               str_contains($host, 'localhost') ||
               str_contains($host, '127.0.0.1');
    }

    /**
     * Check if the route is an admin route.
     */
    protected function isAdminRoute(Request $request): bool
    {
        return $request->is('admin/*') || 
               $request->is('system/*') || 
               $request->is('super-admin/*');
    }

    /**
     * Handle tenant not found.
     */
    protected function handleTenantNotFound(Request $request): Response
    {
        if ($request->expectsJson()) {
            return response()->json([
                'error' => 'Tenant not found',
                'message' => 'The requested tenant does not exist or is not active.'
            ], 404);
        }

        // Redirect to main domain with error
        $mainDomain = config('app.url');
        return redirect($mainDomain . '/tenant-not-found');
    }

    /**
     * Handle inactive tenant.
     */
    protected function handleInactiveTenant(Tenant $tenant): Response
    {
        return response()->view('tenant.inactive', compact('tenant'), 403);
    }

    /**
     * Handle expired subscription.
     */
    protected function handleExpiredSubscription(Tenant $tenant): Response
    {
        return response()->view('tenant.expired', compact('tenant'), 402);
    }

    /**
     * Configure tenant-specific settings.
     */
    protected function configureTenant(Tenant $tenant): void
    {
        // Set timezone
        if ($tenant->timezone) {
            Config::set('app.timezone', $tenant->timezone);
            date_default_timezone_set($tenant->timezone);
        }

        // Set currency
        if ($tenant->currency) {
            Config::set('app.currency', $tenant->currency);
        }

        // Set branding
        if ($tenant->custom_branding) {
            Config::set('app.tenant_logo', $tenant->logo_path);
            Config::set('app.primary_color', $tenant->primary_color);
            Config::set('app.secondary_color', $tenant->secondary_color);
        }

        // Set tenant-specific settings
        if ($tenant->settings) {
            foreach ($tenant->settings as $key => $value) {
                Config::set("tenant.{$key}", $value);
            }
        }

        // Set application name to company name
        Config::set('app.name', $tenant->company_name);
    }
}
