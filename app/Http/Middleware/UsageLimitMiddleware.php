<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Models\SubscriptionManagement\UserSubscription;
use App\Models\SubscriptionManagement\SubscriptionUsage;

class UsageLimitMiddleware
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next, string $metric = null): Response
    {
        $user = auth()->user();
        
        if (!$user) {
            return redirect()->route('login');
        }

        // Super admins bypass all usage limits
        if ($user->isSuperAdmin()) {
            return $next($request);
        }

        // Get user's active subscription
        $subscription = $user->activeSubscription;
        
        if (!$subscription) {
            return $this->handleNoSubscription($request);
        }

        // Check usage limits if metric is specified
        if ($metric && $this->hasReachedLimit($subscription, $metric)) {
            return $this->handleLimitReached($request, $metric, $subscription);
        }

        return $next($request);
    }

    /**
     * Check if user has reached the limit for a specific metric.
     */
    protected function hasReachedLimit(UserSubscription $subscription, string $metric): bool
    {
        // Premium users have unlimited access
        if ($subscription->subscriptionPlan->isPremium()) {
            return false;
        }

        return $subscription->hasReachedLimit($metric);
    }

    /**
     * Handle user with no subscription.
     */
    protected function handleNoSubscription(Request $request): Response
    {
        if ($request->expectsJson()) {
            return response()->json([
                'error' => 'No active subscription',
                'message' => 'You need an active subscription to perform this action.',
                'redirect' => route('subscription-panel.index')
            ], 402);
        }

        return redirect()->route('subscription-panel.index')
                        ->with('error', 'You need an active subscription to perform this action.');
    }

    /**
     * Handle limit reached.
     */
    protected function handleLimitReached(Request $request, string $metric, UserSubscription $subscription): Response
    {
        $limit = $subscription->subscriptionPlan->getLimit($metric);
        $current = $subscription->{'current_' . $metric} ?? 0;
        
        $message = $this->getLimitMessage($metric, $current, $limit);
        
        if ($request->expectsJson()) {
            return response()->json([
                'error' => 'Usage limit reached',
                'message' => $message,
                'metric' => $metric,
                'current' => $current,
                'limit' => $limit,
                'redirect' => route('subscription-panel.upgrade')
            ], 403);
        }

        return redirect()->back()
                        ->with('error', $message)
                        ->with('upgrade_prompt', true);
    }

    /**
     * Get limit message for specific metric.
     */
    protected function getLimitMessage(string $metric, int $current, int $limit): string
    {
        $metricLabels = [
            'users' => 'users',
            'orders' => 'orders',
            'storage' => 'storage space',
            'buyers' => 'buyers',
            'garment_orders' => 'garment orders',
            'stock_items' => 'stock items',
        ];

        $label = $metricLabels[$metric] ?? $metric;
        
        return "You've reached your limit of {$limit} {$label}. Upgrade to Premium for unlimited access.";
    }
}
