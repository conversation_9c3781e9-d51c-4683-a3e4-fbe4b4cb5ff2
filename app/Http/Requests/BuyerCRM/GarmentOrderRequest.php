<?php

namespace App\Http\Requests\BuyerCRM;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use App\Models\BuyerCRM\GarmentOrder;

class GarmentOrderRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Authorization handled by middleware
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        $orderId = $this->route('garment_order') ? $this->route('garment_order')->id : null;
        
        return [
            // Required basic information
            'buyer_id' => ['required', 'integer', 'exists:buyer_profiles,id'],
            'merchandiser_id' => ['nullable', 'integer', 'exists:users,id'],
            'style_no' => ['required', 'string', 'max:100'],
            'season' => ['required', Rule::in(GarmentOrder::$seasons)],
            'product_category' => ['required', Rule::in(GarmentOrder::$productCategories)],
            'order_type' => ['required', Rule::in(GarmentOrder::$orderTypes)],
            'garment_type' => ['required', 'string', 'max:255'],
            
            // Fabric specifications
            'fabric_type' => ['required', 'string', 'max:255'],
            'fabric_color' => ['required', 'string', 'max:100'],
            'wash_type' => ['required', Rule::in(GarmentOrder::$washTypes)],
            
            // Technical specifications (optional)
            'construction_type' => ['nullable', 'string', 'max:255'],
            'gsm' => ['nullable', 'numeric', 'min:0', 'max:9999.99'],
            'shrinkage' => ['nullable', 'numeric', 'min:0', 'max:100'],
            'stitch_type' => ['nullable', 'string', 'max:255'],
            
            // Quantities and pricing
            'total_quantity' => ['required', 'integer', 'min:1', 'max:999999'],
            'unit_price' => ['nullable', 'numeric', 'min:0', 'max:99999.99'],
            'final_price' => ['nullable', 'numeric', 'min:0', 'max:99999.99'],
            
            // Dates
            'order_date' => ['required', 'date', 'before_or_equal:today'],
            'delivery_date' => ['nullable', 'date', 'after:order_date'],
            'confirmed_date' => ['nullable', 'date', 'after_or_equal:order_date'],
            
            // Status
            'status' => ['required', Rule::in(GarmentOrder::$statuses)],
            
            // Additional information
            'special_instructions' => ['nullable', 'string', 'max:2000'],
            'meta' => ['nullable', 'array'],
            
            // Size ratios (if provided)
            'size_ratios' => ['nullable', 'array'],
            'size_ratios.*.size_label' => ['required_with:size_ratios', 'string', 'max:10'],
            'size_ratios.*.size_type' => ['required_with:size_ratios', 'in:text,numeric'],
            'size_ratios.*.ratio' => ['required_with:size_ratios', 'integer', 'min:1', 'max:999'],
            'size_ratios.*.manual_quantity' => ['nullable', 'integer', 'min:0'],
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'buyer_id.required' => 'Please select a buyer.',
            'buyer_id.exists' => 'The selected buyer is invalid.',
            'style_no.required' => 'Style number is required.',
            'style_no.max' => 'Style number cannot exceed 100 characters.',
            'season.required' => 'Please select a season.',
            'season.in' => 'Please select a valid season.',
            'product_category.required' => 'Please select a product category.',
            'product_category.in' => 'Please select a valid product category.',
            'order_type.required' => 'Please select an order type.',
            'order_type.in' => 'Please select a valid order type.',
            'garment_type.required' => 'Garment type is required.',
            'fabric_type.required' => 'Fabric type is required.',
            'fabric_color.required' => 'Fabric color is required.',
            'wash_type.required' => 'Please select a wash type.',
            'wash_type.in' => 'Please select a valid wash type.',
            'gsm.numeric' => 'GSM must be a valid number.',
            'gsm.min' => 'GSM cannot be negative.',
            'gsm.max' => 'GSM cannot exceed 9999.99.',
            'shrinkage.numeric' => 'Shrinkage must be a valid number.',
            'shrinkage.min' => 'Shrinkage cannot be negative.',
            'shrinkage.max' => 'Shrinkage cannot exceed 100%.',
            'total_quantity.required' => 'Total quantity is required.',
            'total_quantity.integer' => 'Total quantity must be a whole number.',
            'total_quantity.min' => 'Total quantity must be at least 1.',
            'total_quantity.max' => 'Total quantity cannot exceed 999,999.',
            'unit_price.numeric' => 'Unit price must be a valid number.',
            'unit_price.min' => 'Unit price cannot be negative.',
            'final_price.numeric' => 'Final price must be a valid number.',
            'final_price.min' => 'Final price cannot be negative.',
            'order_date.required' => 'Order date is required.',
            'order_date.date' => 'Order date must be a valid date.',
            'order_date.before_or_equal' => 'Order date cannot be in the future.',
            'delivery_date.date' => 'Delivery date must be a valid date.',
            'delivery_date.after' => 'Delivery date must be after order date.',
            'confirmed_date.date' => 'Confirmed date must be a valid date.',
            'confirmed_date.after_or_equal' => 'Confirmed date cannot be before order date.',
            'status.required' => 'Please select a status.',
            'status.in' => 'Please select a valid status.',
            'special_instructions.max' => 'Special instructions cannot exceed 2000 characters.',
            'size_ratios.*.size_label.required_with' => 'Size label is required.',
            'size_ratios.*.size_type.required_with' => 'Size type is required.',
            'size_ratios.*.size_type.in' => 'Size type must be either text or numeric.',
            'size_ratios.*.ratio.required_with' => 'Ratio is required.',
            'size_ratios.*.ratio.integer' => 'Ratio must be a whole number.',
            'size_ratios.*.ratio.min' => 'Ratio must be at least 1.',
            'size_ratios.*.manual_quantity.integer' => 'Manual quantity must be a whole number.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'buyer_id' => 'buyer',
            'merchandiser_id' => 'merchandiser',
            'style_no' => 'style number',
            'product_category' => 'product category',
            'order_type' => 'order type',
            'garment_type' => 'garment type',
            'fabric_type' => 'fabric type',
            'fabric_color' => 'fabric color',
            'wash_type' => 'wash type',
            'construction_type' => 'construction type',
            'stitch_type' => 'stitch type',
            'total_quantity' => 'total quantity',
            'unit_price' => 'unit price',
            'final_price' => 'final price',
            'order_date' => 'order date',
            'delivery_date' => 'delivery date',
            'confirmed_date' => 'confirmed date',
            'special_instructions' => 'special instructions',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Custom validation for delivery date based on order type
            if ($this->order_type === 'Sample' && $this->delivery_date) {
                $orderDate = \Carbon\Carbon::parse($this->order_date);
                $deliveryDate = \Carbon\Carbon::parse($this->delivery_date);
                
                if ($deliveryDate->diffInDays($orderDate) > 30) {
                    $validator->errors()->add('delivery_date', 'Sample orders should have delivery within 30 days.');
                }
            }
            
            // Validate size ratios total matches order quantity
            if ($this->size_ratios && $this->total_quantity) {
                $totalRatioQuantity = 0;
                $totalRatio = 0;
                
                foreach ($this->size_ratios as $sizeRatio) {
                    $totalRatio += $sizeRatio['ratio'] ?? 0;
                }
                
                if ($totalRatio > 0) {
                    foreach ($this->size_ratios as $sizeRatio) {
                        $calculatedQty = (int) round(($sizeRatio['ratio'] / $totalRatio) * $this->total_quantity);
                        $totalRatioQuantity += $calculatedQty;
                    }
                    
                    // Allow small variance due to rounding
                    if (abs($totalRatioQuantity - $this->total_quantity) > 5) {
                        $validator->errors()->add('size_ratios', 'Size ratio quantities do not match total order quantity.');
                    }
                }
            }
            
            // Validate pricing logic
            if ($this->unit_price && $this->final_price) {
                if ($this->final_price < ($this->unit_price * 0.5)) {
                    $validator->errors()->add('final_price', 'Final price seems too low compared to unit price.');
                }
            }
            
            // Validate status transitions for existing orders
            if ($this->isMethod('PUT') || $this->isMethod('PATCH')) {
                $currentOrder = $this->route('garment_order');
                if ($currentOrder && $currentOrder->status === 'Completed' && $this->status !== 'Completed') {
                    $validator->errors()->add('status', 'Cannot change status from Completed to another status.');
                }
            }
        });
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Auto-generate order number for new orders
        if ($this->isMethod('POST') && !$this->order_no) {
            $this->merge([
                'order_no' => GarmentOrder::generateOrderNumber(),
                'created_by' => auth()->id(),
            ]);
        }
        
        // Set default order date if not provided
        if (!$this->order_date) {
            $this->merge([
                'order_date' => now()->format('Y-m-d'),
            ]);
        }
        
        // Calculate total value if both price and quantity are provided
        if ($this->final_price && $this->total_quantity) {
            $this->merge([
                'total_value' => $this->final_price * $this->total_quantity,
            ]);
        }
    }
}
