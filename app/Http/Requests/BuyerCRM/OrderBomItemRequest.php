<?php

namespace App\Http\Requests\BuyerCRM;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use App\Models\BuyerCRM\OrderBomItem;

class OrderBomItemRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            // Required fields
            'garment_order_id' => ['required', 'integer', 'exists:garment_orders,id'],
            'item_name' => ['required', 'string', 'max:255'],
            'item_category' => ['required', Rule::in(OrderBomItem::$itemCategories)],
            'unit' => ['required', 'string', 'max:50'],
            'consumption_per_piece' => ['required', 'numeric', 'min:0.0001', 'max:9999.9999'],
            'rate_per_unit' => ['required', 'numeric', 'min:0.0001', 'max:9999.9999'],
            
            // Optional fields
            'supplier_id' => ['nullable', 'integer', 'exists:parties,id'],
            'description' => ['nullable', 'string', 'max:1000'],
            'item_code' => ['nullable', 'string', 'max:100'],
            'color' => ['nullable', 'string', 'max:100'],
            'size' => ['nullable', 'string', 'max:50'],
            'specifications' => ['nullable', 'string', 'max:2000'],
            
            // Costing fields
            'wastage_percentage' => ['nullable', 'numeric', 'min:0', 'max:100'],
            
            // Status and workflow
            'status' => ['required', Rule::in(OrderBomItem::$statuses)],
            'is_critical' => ['boolean'],
            'required_date' => ['nullable', 'date', 'after:today'],
            
            // Version control
            'version' => ['nullable', 'integer', 'min:1'],
            'is_current_version' => ['boolean'],
            
            // Ordering information
            'order_quantity' => ['nullable', 'numeric', 'min:0', 'max:999999.99'],
            'received_quantity' => ['nullable', 'numeric', 'min:0', 'max:999999.99'],
            'order_date' => ['nullable', 'date', 'before_or_equal:today'],
            'delivery_date' => ['nullable', 'date', 'after_or_equal:order_date'],
            
            // Bulk operations
            'items' => ['sometimes', 'array', 'min:1', 'max:50'],
            'items.*.item_name' => ['required_with:items', 'string', 'max:255'],
            'items.*.item_category' => ['required_with:items', Rule::in(OrderBomItem::$itemCategories)],
            'items.*.unit' => ['required_with:items', 'string', 'max:50'],
            'items.*.consumption_per_piece' => ['required_with:items', 'numeric', 'min:0.0001', 'max:9999.9999'],
            'items.*.rate_per_unit' => ['required_with:items', 'numeric', 'min:0.0001', 'max:9999.9999'],
            'items.*.supplier_id' => ['nullable', 'integer', 'exists:parties,id'],
            'items.*.description' => ['nullable', 'string', 'max:1000'],
            'items.*.wastage_percentage' => ['nullable', 'numeric', 'min:0', 'max:100'],
            'items.*.is_critical' => ['boolean'],
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'garment_order_id.required' => 'Garment order is required.',
            'garment_order_id.exists' => 'The selected garment order is invalid.',
            'item_name.required' => 'Item name is required.',
            'item_name.max' => 'Item name cannot exceed 255 characters.',
            'item_category.required' => 'Item category is required.',
            'item_category.in' => 'Please select a valid item category.',
            'unit.required' => 'Unit of measurement is required.',
            'unit.max' => 'Unit cannot exceed 50 characters.',
            'consumption_per_piece.required' => 'Consumption per piece is required.',
            'consumption_per_piece.numeric' => 'Consumption per piece must be a valid number.',
            'consumption_per_piece.min' => 'Consumption per piece must be greater than 0.',
            'consumption_per_piece.max' => 'Consumption per piece cannot exceed 9999.9999.',
            'rate_per_unit.required' => 'Rate per unit is required.',
            'rate_per_unit.numeric' => 'Rate per unit must be a valid number.',
            'rate_per_unit.min' => 'Rate per unit must be greater than 0.',
            'rate_per_unit.max' => 'Rate per unit cannot exceed 9999.9999.',
            'supplier_id.exists' => 'The selected supplier is invalid.',
            'description.max' => 'Description cannot exceed 1000 characters.',
            'item_code.max' => 'Item code cannot exceed 100 characters.',
            'color.max' => 'Color cannot exceed 100 characters.',
            'size.max' => 'Size cannot exceed 50 characters.',
            'specifications.max' => 'Specifications cannot exceed 2000 characters.',
            'wastage_percentage.numeric' => 'Wastage percentage must be a valid number.',
            'wastage_percentage.min' => 'Wastage percentage cannot be negative.',
            'wastage_percentage.max' => 'Wastage percentage cannot exceed 100%.',
            'status.required' => 'Status is required.',
            'status.in' => 'Please select a valid status.',
            'required_date.date' => 'Required date must be a valid date.',
            'required_date.after' => 'Required date must be in the future.',
            'version.integer' => 'Version must be a number.',
            'version.min' => 'Version must be at least 1.',
            'order_quantity.numeric' => 'Order quantity must be a valid number.',
            'order_quantity.min' => 'Order quantity cannot be negative.',
            'order_quantity.max' => 'Order quantity cannot exceed 999,999.99.',
            'received_quantity.numeric' => 'Received quantity must be a valid number.',
            'received_quantity.min' => 'Received quantity cannot be negative.',
            'received_quantity.max' => 'Received quantity cannot exceed 999,999.99.',
            'order_date.date' => 'Order date must be a valid date.',
            'order_date.before_or_equal' => 'Order date cannot be in the future.',
            'delivery_date.date' => 'Delivery date must be a valid date.',
            'delivery_date.after_or_equal' => 'Delivery date must be after or equal to order date.',
            'items.array' => 'Items must be an array.',
            'items.min' => 'At least one item is required.',
            'items.max' => 'Cannot add more than 50 items at once.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'garment_order_id' => 'garment order',
            'item_name' => 'item name',
            'item_category' => 'item category',
            'consumption_per_piece' => 'consumption per piece',
            'rate_per_unit' => 'rate per unit',
            'supplier_id' => 'supplier',
            'item_code' => 'item code',
            'wastage_percentage' => 'wastage percentage',
            'is_critical' => 'critical item',
            'required_date' => 'required date',
            'is_current_version' => 'current version',
            'order_quantity' => 'order quantity',
            'received_quantity' => 'received quantity',
            'order_date' => 'order date',
            'delivery_date' => 'delivery date',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Validate received quantity doesn't exceed order quantity
            if ($this->order_quantity && $this->received_quantity) {
                if ($this->received_quantity > $this->order_quantity) {
                    $validator->errors()->add('received_quantity', 'Received quantity cannot exceed order quantity.');
                }
            }
            
            // Validate status transitions
            if ($this->status === 'received' && !$this->received_quantity) {
                $validator->errors()->add('received_quantity', 'Received quantity is required when status is received.');
            }
            
            if ($this->status === 'ordered' && !$this->order_quantity) {
                $validator->errors()->add('order_quantity', 'Order quantity is required when status is ordered.');
            }
            
            // Validate supplier for certain categories
            if (in_array($this->item_category, ['fabric', 'trim', 'accessory']) && $this->status !== 'draft' && !$this->supplier_id) {
                $validator->errors()->add('supplier_id', 'Supplier is required for this item category when not in draft status.');
            }
            
            // Validate critical items have required date
            if ($this->is_critical && !$this->required_date) {
                $validator->errors()->add('required_date', 'Required date is mandatory for critical items.');
            }
            
            // Validate wastage percentage for different categories
            $maxWastage = [
                'fabric' => 15,
                'trim' => 10,
                'thread' => 5,
                'button' => 5,
                'zipper' => 5,
                'label' => 10,
                'packaging' => 5,
                'accessory' => 10,
                'other' => 20,
            ];
            
            if ($this->wastage_percentage && isset($maxWastage[$this->item_category])) {
                if ($this->wastage_percentage > $maxWastage[$this->item_category]) {
                    $validator->errors()->add('wastage_percentage', "Wastage percentage for {$this->item_category} should not exceed {$maxWastage[$this->item_category]}%.");
                }
            }
            
            // Validate bulk items
            if ($this->items) {
                foreach ($this->items as $index => $item) {
                    // Check for duplicate item names within the same category
                    $duplicates = collect($this->items)->where('item_name', $item['item_name'] ?? '')
                        ->where('item_category', $item['item_category'] ?? '');
                    
                    if ($duplicates->count() > 1) {
                        $validator->errors()->add("items.{$index}.item_name", 'Duplicate item names within the same category are not allowed.');
                    }
                    
                    // Validate consumption and rate combination
                    if (isset($item['consumption_per_piece']) && isset($item['rate_per_unit'])) {
                        $totalCost = $item['consumption_per_piece'] * $item['rate_per_unit'];
                        if ($totalCost > 1000) {
                            $validator->errors()->add("items.{$index}.rate_per_unit", 'Total cost per piece seems too high. Please verify consumption and rate.');
                        }
                    }
                }
            }
            
            // Validate version for updates
            if ($this->isMethod('PUT') || $this->isMethod('PATCH')) {
                $currentBomItem = $this->route('bom_item');
                if ($currentBomItem && $this->version && $this->version <= $currentBomItem->version) {
                    $validator->errors()->add('version', 'New version must be greater than current version.');
                }
            }
        });
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Set default values
        $defaults = [
            'created_by' => auth()->id(),
            'wastage_percentage' => 0,
            'is_critical' => false,
            'status' => 'draft',
            'version' => 1,
            'is_current_version' => true,
        ];
        
        // Only set defaults for new items
        if ($this->isMethod('POST')) {
            foreach ($defaults as $key => $value) {
                if (!$this->has($key)) {
                    $this->merge([$key => $value]);
                }
            }
        }
        
        // Normalize item name and code
        if ($this->item_name) {
            $this->merge(['item_name' => trim($this->item_name)]);
        }
        
        if ($this->item_code) {
            $this->merge(['item_code' => strtoupper(trim($this->item_code))]);
        }
        
        // Set order date if status is ordered but no date provided
        if ($this->status === 'ordered' && !$this->order_date) {
            $this->merge(['order_date' => now()->format('Y-m-d')]);
        }
    }

    /**
     * Get validated data with calculated costs.
     */
    public function validated($key = null, $default = null)
    {
        $validated = parent::validated($key, $default);
        
        // Calculate costs
        if (isset($validated['consumption_per_piece']) && isset($validated['rate_per_unit'])) {
            $validated['total_cost_per_piece'] = $validated['consumption_per_piece'] * $validated['rate_per_unit'];
            
            $wastageMultiplier = 1 + (($validated['wastage_percentage'] ?? 0) / 100);
            $validated['final_cost_per_piece'] = $validated['total_cost_per_piece'] * $wastageMultiplier;
        }
        
        return $validated;
    }
}
