<?php

namespace App\Http\Requests\BuyerCRM;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use App\Models\BuyerCRM\OrderCosting;

class OrderCostingRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            // Required fields
            'garment_order_id' => ['required', 'integer', 'exists:garment_orders,id'],
            
            // Fabric costing
            'fabric_consumption' => ['nullable', 'numeric', 'min:0', 'max:9999.9999'],
            'fabric_rate' => ['nullable', 'numeric', 'min:0', 'max:9999.9999'],
            'fabric_wastage_percentage' => ['nullable', 'numeric', 'min:0', 'max:50'],
            
            // Manufacturing costs
            'cm_cost' => ['nullable', 'numeric', 'min:0', 'max:9999.9999'],
            'washing_cost' => ['nullable', 'numeric', 'min:0', 'max:9999.9999'],
            'finishing_cost' => ['nullable', 'numeric', 'min:0', 'max:9999.9999'],
            'packing_cost' => ['nullable', 'numeric', 'min:0', 'max:9999.9999'],
            
            // Additional costs
            'testing_cost' => ['nullable', 'numeric', 'min:0', 'max:9999.9999'],
            'inspection_cost' => ['nullable', 'numeric', 'min:0', 'max:9999.9999'],
            'freight_cost' => ['nullable', 'numeric', 'min:0', 'max:9999.9999'],
            'other_costs' => ['nullable', 'numeric', 'min:0', 'max:9999.9999'],
            
            // Profit and pricing
            'profit_margin_percentage' => ['nullable', 'numeric', 'min:0', 'max:100'],
            'quoted_price' => ['nullable', 'numeric', 'min:0', 'max:99999.99'],
            'final_negotiated_price' => ['nullable', 'numeric', 'min:0', 'max:99999.99'],
            
            // Currency and exchange
            'currency' => ['required', 'string', 'size:3', Rule::in(OrderCosting::$currencies)],
            'exchange_rate' => ['nullable', 'numeric', 'min:0.0001', 'max:9999.9999'],
            
            // Status and approval
            'status' => ['required', Rule::in(OrderCosting::$statuses)],
            'approval_comments' => ['nullable', 'string', 'max:2000'],
            
            // Version control
            'version' => ['nullable', 'integer', 'min:1'],
            'is_current_version' => ['boolean'],
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'garment_order_id.required' => 'Garment order is required.',
            'garment_order_id.exists' => 'The selected garment order is invalid.',
            'fabric_consumption.numeric' => 'Fabric consumption must be a valid number.',
            'fabric_consumption.min' => 'Fabric consumption cannot be negative.',
            'fabric_consumption.max' => 'Fabric consumption cannot exceed 9999.9999.',
            'fabric_rate.numeric' => 'Fabric rate must be a valid number.',
            'fabric_rate.min' => 'Fabric rate cannot be negative.',
            'fabric_rate.max' => 'Fabric rate cannot exceed 9999.9999.',
            'fabric_wastage_percentage.numeric' => 'Fabric wastage percentage must be a valid number.',
            'fabric_wastage_percentage.min' => 'Fabric wastage percentage cannot be negative.',
            'fabric_wastage_percentage.max' => 'Fabric wastage percentage cannot exceed 50%.',
            'cm_cost.numeric' => 'CM cost must be a valid number.',
            'cm_cost.min' => 'CM cost cannot be negative.',
            'cm_cost.max' => 'CM cost cannot exceed 9999.9999.',
            'washing_cost.numeric' => 'Washing cost must be a valid number.',
            'washing_cost.min' => 'Washing cost cannot be negative.',
            'washing_cost.max' => 'Washing cost cannot exceed 9999.9999.',
            'finishing_cost.numeric' => 'Finishing cost must be a valid number.',
            'finishing_cost.min' => 'Finishing cost cannot be negative.',
            'finishing_cost.max' => 'Finishing cost cannot exceed 9999.9999.',
            'packing_cost.numeric' => 'Packing cost must be a valid number.',
            'packing_cost.min' => 'Packing cost cannot be negative.',
            'packing_cost.max' => 'Packing cost cannot exceed 9999.9999.',
            'testing_cost.numeric' => 'Testing cost must be a valid number.',
            'testing_cost.min' => 'Testing cost cannot be negative.',
            'testing_cost.max' => 'Testing cost cannot exceed 9999.9999.',
            'inspection_cost.numeric' => 'Inspection cost must be a valid number.',
            'inspection_cost.min' => 'Inspection cost cannot be negative.',
            'inspection_cost.max' => 'Inspection cost cannot exceed 9999.9999.',
            'freight_cost.numeric' => 'Freight cost must be a valid number.',
            'freight_cost.min' => 'Freight cost cannot be negative.',
            'freight_cost.max' => 'Freight cost cannot exceed 9999.9999.',
            'other_costs.numeric' => 'Other costs must be a valid number.',
            'other_costs.min' => 'Other costs cannot be negative.',
            'other_costs.max' => 'Other costs cannot exceed 9999.9999.',
            'profit_margin_percentage.numeric' => 'Profit margin percentage must be a valid number.',
            'profit_margin_percentage.min' => 'Profit margin percentage cannot be negative.',
            'profit_margin_percentage.max' => 'Profit margin percentage cannot exceed 100%.',
            'quoted_price.numeric' => 'Quoted price must be a valid number.',
            'quoted_price.min' => 'Quoted price cannot be negative.',
            'quoted_price.max' => 'Quoted price cannot exceed 99999.99.',
            'final_negotiated_price.numeric' => 'Final negotiated price must be a valid number.',
            'final_negotiated_price.min' => 'Final negotiated price cannot be negative.',
            'final_negotiated_price.max' => 'Final negotiated price cannot exceed 99999.99.',
            'currency.required' => 'Currency is required.',
            'currency.size' => 'Currency must be a 3-letter code.',
            'currency.in' => 'Please select a valid currency.',
            'exchange_rate.numeric' => 'Exchange rate must be a valid number.',
            'exchange_rate.min' => 'Exchange rate must be greater than 0.',
            'exchange_rate.max' => 'Exchange rate cannot exceed 9999.9999.',
            'status.required' => 'Status is required.',
            'status.in' => 'Please select a valid status.',
            'approval_comments.max' => 'Approval comments cannot exceed 2000 characters.',
            'version.integer' => 'Version must be a number.',
            'version.min' => 'Version must be at least 1.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'garment_order_id' => 'garment order',
            'fabric_consumption' => 'fabric consumption',
            'fabric_rate' => 'fabric rate',
            'fabric_wastage_percentage' => 'fabric wastage percentage',
            'cm_cost' => 'CM cost',
            'washing_cost' => 'washing cost',
            'finishing_cost' => 'finishing cost',
            'packing_cost' => 'packing cost',
            'testing_cost' => 'testing cost',
            'inspection_cost' => 'inspection cost',
            'freight_cost' => 'freight cost',
            'other_costs' => 'other costs',
            'profit_margin_percentage' => 'profit margin percentage',
            'quoted_price' => 'quoted price',
            'final_negotiated_price' => 'final negotiated price',
            'exchange_rate' => 'exchange rate',
            'approval_comments' => 'approval comments',
            'is_current_version' => 'current version',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Validate fabric cost calculation
            if ($this->fabric_consumption && $this->fabric_rate) {
                $fabricCost = $this->fabric_consumption * $this->fabric_rate;
                if ($fabricCost > 100) {
                    $validator->errors()->add('fabric_rate', 'Fabric cost per piece seems too high. Please verify consumption and rate.');
                }
            }
            
            // Validate profit margin logic
            if ($this->profit_margin_percentage && $this->profit_margin_percentage > 50) {
                $validator->errors()->add('profit_margin_percentage', 'Profit margin over 50% requires special approval.');
            }
            
            // Validate final price vs quoted price
            if ($this->quoted_price && $this->final_negotiated_price) {
                $difference = abs($this->final_negotiated_price - $this->quoted_price);
                $percentageDiff = ($difference / $this->quoted_price) * 100;
                
                if ($percentageDiff > 20) {
                    $validator->errors()->add('final_negotiated_price', 'Final price differs significantly from quoted price (>20%). Please verify.');
                }
            }
            
            // Validate status transitions
            if ($this->status === 'approved' && !auth()->user()->can('approve_costing')) {
                $validator->errors()->add('status', 'You do not have permission to approve costing.');
            }
            
            // Validate required fields for approval
            if ($this->status === 'pending_approval') {
                $requiredFields = ['fabric_consumption', 'fabric_rate', 'cm_cost'];
                foreach ($requiredFields as $field) {
                    if (!$this->$field) {
                        $validator->errors()->add($field, ucfirst(str_replace('_', ' ', $field)) . ' is required for approval submission.');
                    }
                }
            }
            
            // Validate currency and exchange rate
            if ($this->currency !== 'USD' && !$this->exchange_rate) {
                $validator->errors()->add('exchange_rate', 'Exchange rate is required for non-USD currencies.');
            }
            
            // Validate total cost reasonableness
            $totalCost = ($this->fabric_consumption ?? 0) * ($this->fabric_rate ?? 0) +
                        ($this->cm_cost ?? 0) +
                        ($this->washing_cost ?? 0) +
                        ($this->finishing_cost ?? 0) +
                        ($this->packing_cost ?? 0) +
                        ($this->testing_cost ?? 0) +
                        ($this->inspection_cost ?? 0) +
                        ($this->freight_cost ?? 0) +
                        ($this->other_costs ?? 0);
            
            if ($totalCost > 500) {
                $validator->errors()->add('other_costs', 'Total cost per piece seems very high. Please review all cost components.');
            }
            
            // Validate version for updates
            if ($this->isMethod('PUT') || $this->isMethod('PATCH')) {
                $currentCosting = $this->route('costing');
                if ($currentCosting && $this->version && $this->version <= $currentCosting->version) {
                    $validator->errors()->add('version', 'New version must be greater than current version.');
                }
                
                // Prevent editing approved costing
                if ($currentCosting && $currentCosting->status === 'approved' && $this->status !== 'approved') {
                    $validator->errors()->add('status', 'Cannot modify approved costing. Create a new version instead.');
                }
            }
        });
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Set default values
        $defaults = [
            'created_by' => auth()->id(),
            'currency' => 'USD',
            'exchange_rate' => 1.0000,
            'fabric_wastage_percentage' => 5.00,
            'status' => 'draft',
            'version' => 1,
            'is_current_version' => true,
        ];
        
        // Only set defaults for new costing
        if ($this->isMethod('POST')) {
            foreach ($defaults as $key => $value) {
                if (!$this->has($key)) {
                    $this->merge([$key => $value]);
                }
            }
        }
        
        // Set updated_by for updates
        if ($this->isMethod('PUT') || $this->isMethod('PATCH')) {
            $this->merge(['updated_by' => auth()->id()]);
        }
        
        // Set approval fields if status is being approved
        if ($this->status === 'approved' && !$this->approved_by) {
            $this->merge([
                'approved_by' => auth()->id(),
                'approved_date' => now()->format('Y-m-d'),
            ]);
        }
    }

    /**
     * Get validated data with calculated values.
     */
    public function validated($key = null, $default = null)
    {
        $validated = parent::validated($key, $default);
        
        // Calculate fabric costs
        if (isset($validated['fabric_consumption']) && isset($validated['fabric_rate'])) {
            $validated['fabric_cost'] = $validated['fabric_consumption'] * $validated['fabric_rate'];
            
            $wastageMultiplier = 1 + (($validated['fabric_wastage_percentage'] ?? 0) / 100);
            $validated['fabric_final_cost'] = $validated['fabric_cost'] * $wastageMultiplier;
        }
        
        // Calculate total manufacturing cost
        $validated['total_manufacturing_cost'] = 
            ($validated['cm_cost'] ?? 0) +
            ($validated['washing_cost'] ?? 0) +
            ($validated['finishing_cost'] ?? 0) +
            ($validated['packing_cost'] ?? 0);
        
        // Calculate total additional costs
        $validated['total_additional_costs'] = 
            ($validated['testing_cost'] ?? 0) +
            ($validated['inspection_cost'] ?? 0) +
            ($validated['freight_cost'] ?? 0) +
            ($validated['other_costs'] ?? 0);
        
        // Calculate total cost per piece
        $validated['total_cost_per_piece'] = 
            ($validated['fabric_final_cost'] ?? 0) +
            $validated['total_manufacturing_cost'] +
            $validated['total_additional_costs'];
        
        // Calculate profit and unit price
        if (isset($validated['profit_margin_percentage']) && $validated['total_cost_per_piece']) {
            $validated['profit_amount'] = ($validated['total_cost_per_piece'] * $validated['profit_margin_percentage']) / 100;
            $validated['calculated_unit_price'] = $validated['total_cost_per_piece'] + $validated['profit_amount'];
        }
        
        // Calculate actual margin if final price is set
        if (isset($validated['final_negotiated_price']) && $validated['total_cost_per_piece']) {
            $validated['actual_margin_amount'] = $validated['final_negotiated_price'] - $validated['total_cost_per_piece'];
            $validated['actual_margin_percentage'] = ($validated['actual_margin_amount'] / $validated['total_cost_per_piece']) * 100;
        }
        
        return $validated;
    }
}
