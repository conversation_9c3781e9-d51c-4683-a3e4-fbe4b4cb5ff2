<?php

namespace App\Http\Requests\BuyerCRM;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use App\Models\BuyerCRM\OrderDeliveryBatch;

class OrderDeliveryBatchRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        $garmentOrderId = $this->route('garment_order_id') ?? $this->garment_order_id;
        $batchId = $this->route('delivery_batch') ? $this->route('delivery_batch')->id : null;
        
        return [
            // Required fields
            'garment_order_id' => ['required', 'integer', 'exists:garment_orders,id'],
            'batch_no' => [
                'required',
                'string',
                'max:50',
                Rule::unique('order_delivery_batches')
                    ->where('garment_order_id', $garmentOrderId)
                    ->ignore($batchId)
            ],
            'batch_sequence' => ['required', 'integer', 'min:1', 'max:999'],
            'delivery_date' => ['required', 'date', 'after:today'],
            'quantity' => ['required', 'integer', 'min:1', 'max:999999'],
            'shipping_mode' => ['required', Rule::in(OrderDeliveryBatch::$shippingModes)],
            
            // Optional shipping information
            'port_of_loading' => ['nullable', 'string', 'max:255'],
            'port_of_discharge' => ['nullable', 'string', 'max:255'],
            'shipping_line' => ['nullable', 'string', 'max:255'],
            'vessel_name' => ['nullable', 'string', 'max:255'],
            'etd' => ['nullable', 'date', 'before_or_equal:delivery_date'],
            'eta' => ['nullable', 'date', 'after_or_equal:etd', 'before_or_equal:delivery_date'],
            
            // Documentation
            'invoice_no' => ['nullable', 'string', 'max:100'],
            'packing_list_no' => ['nullable', 'string', 'max:100'],
            'bl_no' => ['nullable', 'string', 'max:100'],
            'container_no' => ['nullable', 'string', 'max:100'],
            'seal_no' => ['nullable', 'string', 'max:100'],
            
            // Status fields
            'production_status' => ['required', Rule::in(OrderDeliveryBatch::$productionStatuses)],
            'shipment_status' => ['required', Rule::in(OrderDeliveryBatch::$shipmentStatuses)],
            
            // Dates
            'production_start_date' => ['nullable', 'date', 'before_or_equal:today'],
            'production_completion_date' => ['nullable', 'date', 'after_or_equal:production_start_date'],
            'actual_shipment_date' => ['nullable', 'date', 'before_or_equal:today'],
            'actual_delivery_date' => ['nullable', 'date', 'after_or_equal:actual_shipment_date'],
            
            // Quality approval
            'quality_approved' => ['boolean'],
            'quality_approval_date' => ['nullable', 'date', 'before_or_equal:today'],
            'quality_approved_by' => ['nullable', 'integer', 'exists:users,id'],
            'quality_comments' => ['nullable', 'string', 'max:1000'],
            
            // Instructions and notes
            'packing_instructions' => ['nullable', 'string', 'max:2000'],
            'shipping_instructions' => ['nullable', 'string', 'max:2000'],
            'delivery_instructions' => ['nullable', 'string', 'max:2000'],
            'remarks' => ['nullable', 'string', 'max:2000'],
            
            // Delay tracking
            'has_delay' => ['boolean'],
            'delay_reason' => ['nullable', 'string', 'max:1000'],
            'revised_delivery_date' => ['nullable', 'date', 'after:delivery_date'],
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'garment_order_id.required' => 'Garment order is required.',
            'garment_order_id.exists' => 'The selected garment order is invalid.',
            'batch_no.required' => 'Batch number is required.',
            'batch_no.unique' => 'This batch number already exists for this order.',
            'batch_no.max' => 'Batch number cannot exceed 50 characters.',
            'batch_sequence.required' => 'Batch sequence is required.',
            'batch_sequence.integer' => 'Batch sequence must be a number.',
            'batch_sequence.min' => 'Batch sequence must be at least 1.',
            'batch_sequence.max' => 'Batch sequence cannot exceed 999.',
            'delivery_date.required' => 'Delivery date is required.',
            'delivery_date.date' => 'Delivery date must be a valid date.',
            'delivery_date.after' => 'Delivery date must be in the future.',
            'quantity.required' => 'Quantity is required.',
            'quantity.integer' => 'Quantity must be a whole number.',
            'quantity.min' => 'Quantity must be at least 1.',
            'quantity.max' => 'Quantity cannot exceed 999,999.',
            'shipping_mode.required' => 'Shipping mode is required.',
            'shipping_mode.in' => 'Please select a valid shipping mode.',
            'port_of_loading.max' => 'Port of loading cannot exceed 255 characters.',
            'port_of_discharge.max' => 'Port of discharge cannot exceed 255 characters.',
            'shipping_line.max' => 'Shipping line cannot exceed 255 characters.',
            'vessel_name.max' => 'Vessel name cannot exceed 255 characters.',
            'etd.date' => 'ETD must be a valid date.',
            'etd.before_or_equal' => 'ETD must be before or equal to delivery date.',
            'eta.date' => 'ETA must be a valid date.',
            'eta.after_or_equal' => 'ETA must be after or equal to ETD.',
            'eta.before_or_equal' => 'ETA must be before or equal to delivery date.',
            'invoice_no.max' => 'Invoice number cannot exceed 100 characters.',
            'packing_list_no.max' => 'Packing list number cannot exceed 100 characters.',
            'bl_no.max' => 'Bill of lading number cannot exceed 100 characters.',
            'container_no.max' => 'Container number cannot exceed 100 characters.',
            'seal_no.max' => 'Seal number cannot exceed 100 characters.',
            'production_status.required' => 'Production status is required.',
            'production_status.in' => 'Please select a valid production status.',
            'shipment_status.required' => 'Shipment status is required.',
            'shipment_status.in' => 'Please select a valid shipment status.',
            'production_start_date.date' => 'Production start date must be a valid date.',
            'production_start_date.before_or_equal' => 'Production start date cannot be in the future.',
            'production_completion_date.date' => 'Production completion date must be a valid date.',
            'production_completion_date.after_or_equal' => 'Production completion date must be after or equal to start date.',
            'actual_shipment_date.date' => 'Actual shipment date must be a valid date.',
            'actual_shipment_date.before_or_equal' => 'Actual shipment date cannot be in the future.',
            'actual_delivery_date.date' => 'Actual delivery date must be a valid date.',
            'actual_delivery_date.after_or_equal' => 'Actual delivery date must be after or equal to shipment date.',
            'quality_approval_date.date' => 'Quality approval date must be a valid date.',
            'quality_approval_date.before_or_equal' => 'Quality approval date cannot be in the future.',
            'quality_approved_by.exists' => 'The selected quality approver is invalid.',
            'quality_comments.max' => 'Quality comments cannot exceed 1000 characters.',
            'packing_instructions.max' => 'Packing instructions cannot exceed 2000 characters.',
            'shipping_instructions.max' => 'Shipping instructions cannot exceed 2000 characters.',
            'delivery_instructions.max' => 'Delivery instructions cannot exceed 2000 characters.',
            'remarks.max' => 'Remarks cannot exceed 2000 characters.',
            'delay_reason.max' => 'Delay reason cannot exceed 1000 characters.',
            'revised_delivery_date.date' => 'Revised delivery date must be a valid date.',
            'revised_delivery_date.after' => 'Revised delivery date must be after original delivery date.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'garment_order_id' => 'garment order',
            'batch_no' => 'batch number',
            'batch_sequence' => 'batch sequence',
            'delivery_date' => 'delivery date',
            'shipping_mode' => 'shipping mode',
            'port_of_loading' => 'port of loading',
            'port_of_discharge' => 'port of discharge',
            'shipping_line' => 'shipping line',
            'vessel_name' => 'vessel name',
            'invoice_no' => 'invoice number',
            'packing_list_no' => 'packing list number',
            'bl_no' => 'bill of lading number',
            'container_no' => 'container number',
            'seal_no' => 'seal number',
            'production_status' => 'production status',
            'shipment_status' => 'shipment status',
            'production_start_date' => 'production start date',
            'production_completion_date' => 'production completion date',
            'actual_shipment_date' => 'actual shipment date',
            'actual_delivery_date' => 'actual delivery date',
            'quality_approved' => 'quality approved',
            'quality_approval_date' => 'quality approval date',
            'quality_approved_by' => 'quality approved by',
            'quality_comments' => 'quality comments',
            'packing_instructions' => 'packing instructions',
            'shipping_instructions' => 'shipping instructions',
            'delivery_instructions' => 'delivery instructions',
            'has_delay' => 'has delay',
            'delay_reason' => 'delay reason',
            'revised_delivery_date' => 'revised delivery date',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Validate quantity against order total
            if ($this->garment_order_id && $this->quantity) {
                $order = \App\Models\BuyerCRM\GarmentOrder::find($this->garment_order_id);
                if ($order) {
                    $existingBatches = OrderDeliveryBatch::where('garment_order_id', $this->garment_order_id)
                        ->when($this->route('delivery_batch'), function ($query) {
                            return $query->where('id', '!=', $this->route('delivery_batch')->id);
                        })
                        ->sum('quantity');
                    
                    $totalQuantity = $existingBatches + $this->quantity;
                    if ($totalQuantity > $order->total_quantity) {
                        $validator->errors()->add('quantity', 'Total batch quantities cannot exceed order quantity.');
                    }
                }
            }
            
            // Validate status transitions
            if ($this->production_status === 'ready' && !$this->production_completion_date) {
                $validator->errors()->add('production_completion_date', 'Production completion date is required when status is ready.');
            }
            
            if ($this->shipment_status === 'shipped' && !$this->actual_shipment_date) {
                $validator->errors()->add('actual_shipment_date', 'Actual shipment date is required when status is shipped.');
            }
            
            if ($this->shipment_status === 'delivered' && !$this->actual_delivery_date) {
                $validator->errors()->add('actual_delivery_date', 'Actual delivery date is required when status is delivered.');
            }
            
            // Validate quality approval
            if ($this->quality_approved && !$this->quality_approved_by) {
                $validator->errors()->add('quality_approved_by', 'Quality approver is required when quality is approved.');
            }
            
            if ($this->quality_approved && !$this->quality_approval_date) {
                $validator->errors()->add('quality_approval_date', 'Quality approval date is required when quality is approved.');
            }
            
            // Validate delay logic
            if ($this->has_delay && !$this->delay_reason) {
                $validator->errors()->add('delay_reason', 'Delay reason is required when batch has delay.');
            }
            
            if ($this->has_delay && !$this->revised_delivery_date) {
                $validator->errors()->add('revised_delivery_date', 'Revised delivery date is required when batch has delay.');
            }
            
            // Validate shipping information based on mode
            if ($this->shipping_mode === 'Sea') {
                if (!$this->port_of_loading) {
                    $validator->errors()->add('port_of_loading', 'Port of loading is required for sea shipments.');
                }
                if (!$this->port_of_discharge) {
                    $validator->errors()->add('port_of_discharge', 'Port of discharge is required for sea shipments.');
                }
            }
            
            // Validate production status progression
            $statusOrder = ['not_started', 'cutting', 'sewing', 'finishing', 'packing', 'ready'];
            if ($this->isMethod('PUT') || $this->isMethod('PATCH')) {
                $currentBatch = $this->route('delivery_batch');
                if ($currentBatch) {
                    $currentIndex = array_search($currentBatch->production_status, $statusOrder);
                    $newIndex = array_search($this->production_status, $statusOrder);
                    
                    if ($newIndex !== false && $currentIndex !== false && $newIndex < $currentIndex) {
                        $validator->errors()->add('production_status', 'Cannot move production status backwards.');
                    }
                }
            }
        });
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Set default values
        $defaults = [
            'created_by' => auth()->id(),
            'production_status' => 'not_started',
            'shipment_status' => 'pending',
            'quality_approved' => false,
            'has_delay' => false,
        ];
        
        // Only set defaults for new batches
        if ($this->isMethod('POST')) {
            foreach ($defaults as $key => $value) {
                if (!$this->has($key)) {
                    $this->merge([$key => $value]);
                }
            }
            
            // Auto-generate batch number if not provided
            if (!$this->batch_no && $this->garment_order_id) {
                $this->merge([
                    'batch_no' => OrderDeliveryBatch::generateBatchNumber($this->garment_order_id)
                ]);
            }
            
            // Auto-set batch sequence
            if (!$this->batch_sequence && $this->garment_order_id) {
                $lastSequence = OrderDeliveryBatch::where('garment_order_id', $this->garment_order_id)
                    ->max('batch_sequence') ?? 0;
                $this->merge(['batch_sequence' => $lastSequence + 1]);
            }
        }
        
        // Auto-set production start date when status changes from not_started
        if ($this->production_status !== 'not_started' && !$this->production_start_date) {
            $this->merge(['production_start_date' => now()->format('Y-m-d')]);
        }
        
        // Auto-set completion date when status is ready
        if ($this->production_status === 'ready' && !$this->production_completion_date) {
            $this->merge(['production_completion_date' => now()->format('Y-m-d')]);
        }
        
        // Auto-set shipment date when status is shipped
        if ($this->shipment_status === 'shipped' && !$this->actual_shipment_date) {
            $this->merge(['actual_shipment_date' => now()->format('Y-m-d')]);
        }
        
        // Auto-set delivery date when status is delivered
        if ($this->shipment_status === 'delivered' && !$this->actual_delivery_date) {
            $this->merge(['actual_delivery_date' => now()->format('Y-m-d')]);
        }
    }

    /**
     * Get validated data with calculated batch value.
     */
    public function validated($key = null, $default = null)
    {
        $validated = parent::validated($key, $default);
        
        // Calculate batch value if order has pricing
        if (isset($validated['garment_order_id']) && isset($validated['quantity'])) {
            $order = \App\Models\BuyerCRM\GarmentOrder::find($validated['garment_order_id']);
            if ($order && $order->final_price) {
                $validated['batch_value'] = $order->final_price * $validated['quantity'];
            }
        }
        
        return $validated;
    }
}
