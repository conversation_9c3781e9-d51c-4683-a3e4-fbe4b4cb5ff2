<?php

namespace App\Http\Requests\BuyerCRM;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use App\Models\BuyerCRM\OrderSizeRatio;

class OrderSizeRatioRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        $garmentOrderId = $this->route('garment_order_id') ?? $this->garment_order_id;
        $sizeRatioId = $this->route('size_ratio') ? $this->route('size_ratio')->id : null;
        
        return [
            'garment_order_id' => ['required', 'integer', 'exists:garment_orders,id'],
            'size_label' => [
                'required',
                'string',
                'max:10',
                Rule::unique('order_size_ratios')
                    ->where('garment_order_id', $garmentOrderId)
                    ->ignore($sizeRatioId)
            ],
            'size_type' => ['required', Rule::in(OrderSizeRatio::$sizeTypes)],
            'sort_order' => ['required', 'integer', 'min:1', 'max:999'],
            'ratio' => ['required', 'integer', 'min:1', 'max:999'],
            'manual_quantity' => ['nullable', 'integer', 'min:0', 'max:999999'],
            'is_manual_override' => ['boolean'],
            'notes' => ['nullable', 'string', 'max:500'],
            
            // Bulk operations
            'sizes' => ['sometimes', 'array', 'min:1', 'max:20'],
            'sizes.*.size_label' => ['required_with:sizes', 'string', 'max:10'],
            'sizes.*.size_type' => ['required_with:sizes', Rule::in(OrderSizeRatio::$sizeTypes)],
            'sizes.*.ratio' => ['required_with:sizes', 'integer', 'min:1', 'max:999'],
            'sizes.*.manual_quantity' => ['nullable', 'integer', 'min:0'],
            'sizes.*.sort_order' => ['nullable', 'integer', 'min:1'],
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'garment_order_id.required' => 'Garment order is required.',
            'garment_order_id.exists' => 'The selected garment order is invalid.',
            'size_label.required' => 'Size label is required.',
            'size_label.max' => 'Size label cannot exceed 10 characters.',
            'size_label.unique' => 'This size already exists for this order.',
            'size_type.required' => 'Size type is required.',
            'size_type.in' => 'Size type must be either text or numeric.',
            'sort_order.required' => 'Sort order is required.',
            'sort_order.integer' => 'Sort order must be a number.',
            'sort_order.min' => 'Sort order must be at least 1.',
            'sort_order.max' => 'Sort order cannot exceed 999.',
            'ratio.required' => 'Ratio is required.',
            'ratio.integer' => 'Ratio must be a whole number.',
            'ratio.min' => 'Ratio must be at least 1.',
            'ratio.max' => 'Ratio cannot exceed 999.',
            'manual_quantity.integer' => 'Manual quantity must be a whole number.',
            'manual_quantity.min' => 'Manual quantity cannot be negative.',
            'manual_quantity.max' => 'Manual quantity cannot exceed 999,999.',
            'notes.max' => 'Notes cannot exceed 500 characters.',
            'sizes.array' => 'Sizes must be an array.',
            'sizes.min' => 'At least one size is required.',
            'sizes.max' => 'Cannot add more than 20 sizes at once.',
            'sizes.*.size_label.required_with' => 'Size label is required for each size.',
            'sizes.*.size_type.required_with' => 'Size type is required for each size.',
            'sizes.*.ratio.required_with' => 'Ratio is required for each size.',
            'sizes.*.ratio.integer' => 'Ratio must be a whole number.',
            'sizes.*.ratio.min' => 'Ratio must be at least 1.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'garment_order_id' => 'garment order',
            'size_label' => 'size label',
            'size_type' => 'size type',
            'sort_order' => 'sort order',
            'manual_quantity' => 'manual quantity',
            'is_manual_override' => 'manual override',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Validate size label format based on size type
            if ($this->size_type && $this->size_label) {
                if ($this->size_type === 'numeric' && !is_numeric($this->size_label)) {
                    $validator->errors()->add('size_label', 'Size label must be numeric when size type is numeric.');
                }
                
                if ($this->size_type === 'text' && is_numeric($this->size_label)) {
                    $validator->errors()->add('size_label', 'Size label should be text when size type is text.');
                }
            }
            
            // Validate manual override logic
            if ($this->is_manual_override && !$this->manual_quantity) {
                $validator->errors()->add('manual_quantity', 'Manual quantity is required when manual override is enabled.');
            }
            
            // Validate bulk sizes for duplicates
            if ($this->sizes) {
                $sizeLabels = collect($this->sizes)->pluck('size_label');
                if ($sizeLabels->count() !== $sizeLabels->unique()->count()) {
                    $validator->errors()->add('sizes', 'Duplicate size labels are not allowed.');
                }
                
                // Check for consistent size types
                $sizeTypes = collect($this->sizes)->pluck('size_type')->unique();
                if ($sizeTypes->count() > 1) {
                    $validator->errors()->add('sizes', 'All sizes must have the same size type.');
                }
                
                // Validate size label format for bulk operations
                foreach ($this->sizes as $index => $size) {
                    if (isset($size['size_type']) && isset($size['size_label'])) {
                        if ($size['size_type'] === 'numeric' && !is_numeric($size['size_label'])) {
                            $validator->errors()->add("sizes.{$index}.size_label", 'Size label must be numeric when size type is numeric.');
                        }
                        
                        if ($size['size_type'] === 'text' && is_numeric($size['size_label'])) {
                            $validator->errors()->add("sizes.{$index}.size_label", 'Size label should be text when size type is text.');
                        }
                    }
                }
            }
            
            // Validate against existing sizes for the order
            if ($this->garment_order_id && ($this->size_label || $this->sizes)) {
                $existingSizes = OrderSizeRatio::where('garment_order_id', $this->garment_order_id)
                    ->when($this->route('size_ratio'), function ($query) {
                        return $query->where('id', '!=', $this->route('size_ratio')->id);
                    })
                    ->pluck('size_label')
                    ->toArray();
                
                if ($this->size_label && in_array($this->size_label, $existingSizes)) {
                    $validator->errors()->add('size_label', 'This size already exists for this order.');
                }
                
                if ($this->sizes) {
                    foreach ($this->sizes as $index => $size) {
                        if (isset($size['size_label']) && in_array($size['size_label'], $existingSizes)) {
                            $validator->errors()->add("sizes.{$index}.size_label", 'This size already exists for this order.');
                        }
                    }
                }
            }
        });
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Set default values
        if ($this->is_manual_override === null) {
            $this->merge(['is_manual_override' => false]);
        }
        
        // Auto-assign sort order for bulk operations
        if ($this->sizes) {
            $sizes = $this->sizes;
            foreach ($sizes as $index => &$size) {
                if (!isset($size['sort_order'])) {
                    $size['sort_order'] = $index + 1;
                }
            }
            $this->merge(['sizes' => $sizes]);
        }
        
        // Normalize size label
        if ($this->size_label) {
            $this->merge(['size_label' => trim(strtoupper($this->size_label))]);
        }
        
        if ($this->sizes) {
            $sizes = $this->sizes;
            foreach ($sizes as &$size) {
                if (isset($size['size_label'])) {
                    $size['size_label'] = trim(strtoupper($size['size_label']));
                }
            }
            $this->merge(['sizes' => $sizes]);
        }
    }

    /**
     * Get validated data with additional processing.
     */
    public function validated($key = null, $default = null)
    {
        $validated = parent::validated($key, $default);
        
        // Calculate final quantity based on manual override
        if (isset($validated['is_manual_override']) && $validated['is_manual_override'] && isset($validated['manual_quantity'])) {
            $validated['final_quantity'] = $validated['manual_quantity'];
        }
        
        return $validated;
    }
}
