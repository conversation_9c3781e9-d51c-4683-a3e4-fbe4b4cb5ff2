<?php

namespace App\Http\Requests\BuyerCRM;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use App\Models\BuyerCRM\OrderSopCompliance;

class OrderSopComplianceRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            // Required fields
            'garment_order_id' => ['required', 'integer', 'exists:garment_orders,id'],
            'buyer_id' => ['required', 'integer', 'exists:buyer_profiles,id'],
            'sop_category' => ['required', 'string', 'max:100'],
            'sop_title' => ['required', 'string', 'max:255'],
            'sop_description' => ['required', 'string', 'max:2000'],
            'priority' => ['required', Rule::in(OrderSopCompliance::$priorities)],
            
            // Optional assignment
            'assigned_to' => ['nullable', 'integer', 'exists:users,id'],
            
            // Status and progress
            'compliance_status' => ['required', Rule::in(OrderSopCompliance::$complianceStatuses)],
            'completion_percentage' => ['nullable', 'numeric', 'min:0', 'max:100'],
            
            // Requirements and instructions
            'requirements' => ['nullable', 'array'],
            'buyer_instructions' => ['nullable', 'string', 'max:2000'],
            'internal_notes' => ['nullable', 'string', 'max:2000'],
            
            // Document requirements
            'required_documents' => ['nullable', 'array'],
            'uploaded_documents' => ['nullable', 'array'],
            'documents_verified' => ['boolean'],
            
            // Testing requirements
            'testing_required' => ['boolean'],
            'testing_type' => ['nullable', 'string', 'max:255'],
            'testing_standard' => ['nullable', 'string', 'max:255'],
            'testing_status' => ['required', Rule::in(OrderSopCompliance::$testingStatuses)],
            'testing_completion_date' => ['nullable', 'date', 'before_or_equal:today'],
            'testing_comments' => ['nullable', 'string', 'max:1000'],
            
            // Labeling requirements
            'labeling_required' => ['boolean'],
            'label_specifications' => ['nullable', 'array'],
            'labeling_status' => ['required', Rule::in(OrderSopCompliance::$approvalStatuses)],
            
            // Packing requirements
            'special_packing_required' => ['boolean'],
            'packing_specifications' => ['nullable', 'array'],
            'packing_status' => ['required', Rule::in(OrderSopCompliance::$approvalStatuses)],
            
            // Dates and deadlines
            'due_date' => ['nullable', 'date', 'after:today'],
            'started_date' => ['nullable', 'date', 'before_or_equal:today'],
            'completed_date' => ['nullable', 'date', 'after_or_equal:started_date'],
            'verified_date' => ['nullable', 'date', 'after_or_equal:completed_date'],
            
            // Verification and approval
            'verified_by' => ['nullable', 'integer', 'exists:users,id'],
            'verification_comments' => ['nullable', 'string', 'max:1000'],
            'buyer_approved' => ['boolean'],
            'buyer_approval_date' => ['nullable', 'date', 'before_or_equal:today'],
            'buyer_feedback' => ['nullable', 'string', 'max:1000'],
            
            // Alert settings
            'alert_enabled' => ['boolean'],
            'alert_days_before' => ['nullable', 'integer', 'min:1', 'max:30'],
            
            // Non-compliance tracking
            'is_non_compliant' => ['boolean'],
            'non_compliance_reason' => ['nullable', 'string', 'max:1000'],
            'corrective_action' => ['nullable', 'string', 'max:2000'],
            'corrective_action_due_date' => ['nullable', 'date', 'after:today'],
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'garment_order_id.required' => 'Garment order is required.',
            'garment_order_id.exists' => 'The selected garment order is invalid.',
            'buyer_id.required' => 'Buyer is required.',
            'buyer_id.exists' => 'The selected buyer is invalid.',
            'sop_category.required' => 'SOP category is required.',
            'sop_category.max' => 'SOP category cannot exceed 100 characters.',
            'sop_title.required' => 'SOP title is required.',
            'sop_title.max' => 'SOP title cannot exceed 255 characters.',
            'sop_description.required' => 'SOP description is required.',
            'sop_description.max' => 'SOP description cannot exceed 2000 characters.',
            'priority.required' => 'Priority is required.',
            'priority.in' => 'Please select a valid priority level.',
            'assigned_to.exists' => 'The selected assignee is invalid.',
            'compliance_status.required' => 'Compliance status is required.',
            'compliance_status.in' => 'Please select a valid compliance status.',
            'completion_percentage.numeric' => 'Completion percentage must be a valid number.',
            'completion_percentage.min' => 'Completion percentage cannot be negative.',
            'completion_percentage.max' => 'Completion percentage cannot exceed 100%.',
            'buyer_instructions.max' => 'Buyer instructions cannot exceed 2000 characters.',
            'internal_notes.max' => 'Internal notes cannot exceed 2000 characters.',
            'testing_type.max' => 'Testing type cannot exceed 255 characters.',
            'testing_standard.max' => 'Testing standard cannot exceed 255 characters.',
            'testing_status.required' => 'Testing status is required.',
            'testing_status.in' => 'Please select a valid testing status.',
            'testing_completion_date.date' => 'Testing completion date must be a valid date.',
            'testing_completion_date.before_or_equal' => 'Testing completion date cannot be in the future.',
            'testing_comments.max' => 'Testing comments cannot exceed 1000 characters.',
            'labeling_status.required' => 'Labeling status is required.',
            'labeling_status.in' => 'Please select a valid labeling status.',
            'packing_status.required' => 'Packing status is required.',
            'packing_status.in' => 'Please select a valid packing status.',
            'due_date.date' => 'Due date must be a valid date.',
            'due_date.after' => 'Due date must be in the future.',
            'started_date.date' => 'Started date must be a valid date.',
            'started_date.before_or_equal' => 'Started date cannot be in the future.',
            'completed_date.date' => 'Completed date must be a valid date.',
            'completed_date.after_or_equal' => 'Completed date must be after or equal to started date.',
            'verified_date.date' => 'Verified date must be a valid date.',
            'verified_date.after_or_equal' => 'Verified date must be after or equal to completed date.',
            'verified_by.exists' => 'The selected verifier is invalid.',
            'verification_comments.max' => 'Verification comments cannot exceed 1000 characters.',
            'buyer_approval_date.date' => 'Buyer approval date must be a valid date.',
            'buyer_approval_date.before_or_equal' => 'Buyer approval date cannot be in the future.',
            'buyer_feedback.max' => 'Buyer feedback cannot exceed 1000 characters.',
            'alert_days_before.integer' => 'Alert days before must be a number.',
            'alert_days_before.min' => 'Alert days before must be at least 1.',
            'alert_days_before.max' => 'Alert days before cannot exceed 30.',
            'non_compliance_reason.max' => 'Non-compliance reason cannot exceed 1000 characters.',
            'corrective_action.max' => 'Corrective action cannot exceed 2000 characters.',
            'corrective_action_due_date.date' => 'Corrective action due date must be a valid date.',
            'corrective_action_due_date.after' => 'Corrective action due date must be in the future.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'garment_order_id' => 'garment order',
            'buyer_id' => 'buyer',
            'sop_category' => 'SOP category',
            'sop_title' => 'SOP title',
            'sop_description' => 'SOP description',
            'assigned_to' => 'assigned to',
            'compliance_status' => 'compliance status',
            'completion_percentage' => 'completion percentage',
            'buyer_instructions' => 'buyer instructions',
            'internal_notes' => 'internal notes',
            'required_documents' => 'required documents',
            'uploaded_documents' => 'uploaded documents',
            'documents_verified' => 'documents verified',
            'testing_required' => 'testing required',
            'testing_type' => 'testing type',
            'testing_standard' => 'testing standard',
            'testing_status' => 'testing status',
            'testing_completion_date' => 'testing completion date',
            'testing_comments' => 'testing comments',
            'labeling_required' => 'labeling required',
            'label_specifications' => 'label specifications',
            'labeling_status' => 'labeling status',
            'special_packing_required' => 'special packing required',
            'packing_specifications' => 'packing specifications',
            'packing_status' => 'packing status',
            'due_date' => 'due date',
            'started_date' => 'started date',
            'completed_date' => 'completed date',
            'verified_date' => 'verified date',
            'verified_by' => 'verified by',
            'verification_comments' => 'verification comments',
            'buyer_approved' => 'buyer approved',
            'buyer_approval_date' => 'buyer approval date',
            'buyer_feedback' => 'buyer feedback',
            'alert_enabled' => 'alert enabled',
            'alert_days_before' => 'alert days before',
            'is_non_compliant' => 'non-compliant',
            'non_compliance_reason' => 'non-compliance reason',
            'corrective_action' => 'corrective action',
            'corrective_action_due_date' => 'corrective action due date',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Validate testing requirements
            if ($this->testing_required) {
                if (!$this->testing_type) {
                    $validator->errors()->add('testing_type', 'Testing type is required when testing is required.');
                }
                if (!$this->testing_standard) {
                    $validator->errors()->add('testing_standard', 'Testing standard is required when testing is required.');
                }
            }
            
            // Validate testing status logic
            if ($this->testing_status === 'passed' || $this->testing_status === 'failed') {
                if (!$this->testing_completion_date) {
                    $validator->errors()->add('testing_completion_date', 'Testing completion date is required when testing is completed.');
                }
            }
            
            // Validate labeling requirements
            if ($this->labeling_required && empty($this->label_specifications)) {
                $validator->errors()->add('label_specifications', 'Label specifications are required when labeling is required.');
            }
            
            // Validate packing requirements
            if ($this->special_packing_required && empty($this->packing_specifications)) {
                $validator->errors()->add('packing_specifications', 'Packing specifications are required when special packing is required.');
            }
            
            // Validate completion status logic
            if ($this->compliance_status === 'completed' && !$this->completed_date) {
                $validator->errors()->add('completed_date', 'Completed date is required when status is completed.');
            }
            
            if ($this->compliance_status === 'verified' && !$this->verified_by) {
                $validator->errors()->add('verified_by', 'Verifier is required when status is verified.');
            }
            
            // Validate non-compliance logic
            if ($this->is_non_compliant) {
                if (!$this->non_compliance_reason) {
                    $validator->errors()->add('non_compliance_reason', 'Non-compliance reason is required when marked as non-compliant.');
                }
                if (!$this->corrective_action) {
                    $validator->errors()->add('corrective_action', 'Corrective action is required when marked as non-compliant.');
                }
                if (!$this->corrective_action_due_date) {
                    $validator->errors()->add('corrective_action_due_date', 'Corrective action due date is required when marked as non-compliant.');
                }
            }
            
            // Validate buyer approval logic
            if ($this->buyer_approved && !$this->buyer_approval_date) {
                $validator->errors()->add('buyer_approval_date', 'Buyer approval date is required when buyer approved is true.');
            }
            
            // Validate alert settings
            if ($this->alert_enabled && !$this->alert_days_before) {
                $validator->errors()->add('alert_days_before', 'Alert days before is required when alerts are enabled.');
            }
            
            // Validate completion percentage consistency
            if ($this->completion_percentage !== null) {
                if ($this->compliance_status === 'not_started' && $this->completion_percentage > 0) {
                    $validator->errors()->add('completion_percentage', 'Completion percentage should be 0 when status is not started.');
                }
                
                if ($this->compliance_status === 'completed' && $this->completion_percentage < 100) {
                    $validator->errors()->add('completion_percentage', 'Completion percentage should be 100 when status is completed.');
                }
            }
            
            // Validate priority and due date relationship
            if ($this->priority === 'critical' && !$this->due_date) {
                $validator->errors()->add('due_date', 'Due date is required for critical priority items.');
            }
            
            // Validate document verification
            if ($this->documents_verified && empty($this->uploaded_documents)) {
                $validator->errors()->add('documents_verified', 'Cannot verify documents when no documents are uploaded.');
            }
            
            // Validate status progression
            $statusOrder = ['not_started', 'in_progress', 'completed', 'verified'];
            if ($this->isMethod('PUT') || $this->isMethod('PATCH')) {
                $currentCompliance = $this->route('sop_compliance');
                if ($currentCompliance && $currentCompliance->compliance_status === 'verified' && $this->compliance_status !== 'verified') {
                    $validator->errors()->add('compliance_status', 'Cannot change status from verified to another status.');
                }
            }
        });
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Set default values
        $defaults = [
            'compliance_status' => 'not_started',
            'completion_percentage' => 0,
            'testing_required' => false,
            'testing_status' => 'not_required',
            'labeling_required' => false,
            'labeling_status' => 'not_required',
            'special_packing_required' => false,
            'packing_status' => 'not_required',
            'documents_verified' => false,
            'buyer_approved' => false,
            'alert_enabled' => true,
            'alert_days_before' => 7,
            'is_non_compliant' => false,
        ];
        
        // Only set defaults for new compliance items
        if ($this->isMethod('POST')) {
            foreach ($defaults as $key => $value) {
                if (!$this->has($key)) {
                    $this->merge([$key => $value]);
                }
            }
        }
        
        // Auto-set started date when status changes from not_started
        if ($this->compliance_status !== 'not_started' && !$this->started_date) {
            $this->merge(['started_date' => now()->format('Y-m-d')]);
        }
        
        // Auto-set completed date when status is completed
        if ($this->compliance_status === 'completed' && !$this->completed_date) {
            $this->merge(['completed_date' => now()->format('Y-m-d')]);
        }
        
        // Auto-set verified date and user when status is verified
        if ($this->compliance_status === 'verified') {
            if (!$this->verified_date) {
                $this->merge(['verified_date' => now()->format('Y-m-d')]);
            }
            if (!$this->verified_by) {
                $this->merge(['verified_by' => auth()->id()]);
            }
        }
        
        // Auto-set buyer approval date when buyer approved
        if ($this->buyer_approved && !$this->buyer_approval_date) {
            $this->merge(['buyer_approval_date' => now()->format('Y-m-d')]);
        }
        
        // Auto-set testing completion date when testing status is passed/failed
        if (in_array($this->testing_status, ['passed', 'failed']) && !$this->testing_completion_date) {
            $this->merge(['testing_completion_date' => now()->format('Y-m-d')]);
        }
    }

    /**
     * Get validated data with auto-calculated completion percentage.
     */
    public function validated($key = null, $default = null)
    {
        $validated = parent::validated($key, $default);
        
        // Auto-calculate completion percentage based on status if not provided
        if (!isset($validated['completion_percentage']) || $validated['completion_percentage'] === null) {
            $validated['completion_percentage'] = match($validated['compliance_status']) {
                'not_started' => 0,
                'in_progress' => 50,
                'completed' => 100,
                'verified' => 100,
                'non_compliant' => 0,
                default => 0
            };
        }
        
        return $validated;
    }
}
