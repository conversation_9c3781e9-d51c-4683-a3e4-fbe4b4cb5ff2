<?php

namespace App\Http\Requests\BuyerCRM;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use App\Models\BuyerCRM\OrderTechPack;

class OrderTechPackRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        $rules = [
            'garment_order_id' => ['required', 'integer', 'exists:garment_orders,id'],
            'file_category' => ['required', Rule::in(OrderTechPack::$fileCategories)],
            'description' => ['nullable', 'string', 'max:500'],
            'sample_type' => ['nullable', Rule::in(OrderTechPack::$sampleTypes)],
            'sample_status' => ['nullable', Rule::in(OrderTechPack::$sampleStatuses)],
            'sample_comments' => ['nullable', 'string', 'max:1000'],
            'version' => ['nullable', 'integer', 'min:1'],
            'is_current_version' => ['boolean'],
            'is_active' => ['boolean'],
        ];

        // File upload validation
        if ($this->hasFile('file')) {
            $rules['file'] = [
                'required',
                'file',
                'max:10240', // 10MB max
            ];

            // Category-specific file type validation
            switch ($this->file_category) {
                case 'tech_pack':
                    $rules['file'][] = 'mimes:pdf,doc,docx';
                    break;
                case 'image':
                case 'sample_image':
                    $rules['file'][] = 'mimes:jpg,jpeg,png,gif,webp';
                    $rules['file'][] = 'dimensions:min_width=100,min_height=100,max_width=5000,max_height=5000';
                    break;
                case 'reference':
                    $rules['file'][] = 'mimes:pdf,doc,docx,jpg,jpeg,png,gif,webp';
                    break;
                default:
                    $rules['file'][] = 'mimes:pdf,doc,docx,jpg,jpeg,png,gif,webp,xls,xlsx';
            }
        } elseif ($this->isMethod('POST')) {
            $rules['file'] = ['required'];
        }

        // Sample approval validation
        if ($this->sample_status === 'approved' || $this->sample_status === 'rejected') {
            $rules['approved_by'] = ['required', 'integer', 'exists:users,id'];
            $rules['sample_approval_date'] = ['required', 'date', 'before_or_equal:today'];
        }

        return $rules;
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'garment_order_id.required' => 'Garment order is required.',
            'garment_order_id.exists' => 'The selected garment order is invalid.',
            'file.required' => 'Please select a file to upload.',
            'file.file' => 'The uploaded file is invalid.',
            'file.max' => 'File size cannot exceed 10MB.',
            'file.mimes' => 'Invalid file type. Please check allowed file types for this category.',
            'file.dimensions' => 'Image dimensions must be between 100x100 and 5000x5000 pixels.',
            'file_category.required' => 'File category is required.',
            'file_category.in' => 'Please select a valid file category.',
            'description.max' => 'Description cannot exceed 500 characters.',
            'sample_type.in' => 'Please select a valid sample type.',
            'sample_status.in' => 'Please select a valid sample status.',
            'sample_comments.max' => 'Sample comments cannot exceed 1000 characters.',
            'approved_by.required' => 'Approver is required when setting approval status.',
            'approved_by.exists' => 'The selected approver is invalid.',
            'sample_approval_date.required' => 'Approval date is required when setting approval status.',
            'sample_approval_date.date' => 'Approval date must be a valid date.',
            'sample_approval_date.before_or_equal' => 'Approval date cannot be in the future.',
            'version.integer' => 'Version must be a number.',
            'version.min' => 'Version must be at least 1.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'garment_order_id' => 'garment order',
            'file_category' => 'file category',
            'sample_type' => 'sample type',
            'sample_status' => 'sample status',
            'sample_comments' => 'sample comments',
            'approved_by' => 'approver',
            'sample_approval_date' => 'approval date',
            'is_current_version' => 'current version',
            'is_active' => 'active status',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Validate sample type is required for sample-related categories
            if (in_array($this->file_category, ['sample_image']) && !$this->sample_type) {
                $validator->errors()->add('sample_type', 'Sample type is required for sample images.');
            }
            
            // Validate sample status logic
            if ($this->sample_type && !$this->sample_status) {
                $this->merge(['sample_status' => 'pending']);
            }
            
            // Validate file size based on category
            if ($this->hasFile('file')) {
                $file = $this->file('file');
                $maxSizes = [
                    'tech_pack' => 10240, // 10MB
                    'image' => 5120,      // 5MB
                    'sample_image' => 5120, // 5MB
                    'reference' => 10240,   // 10MB
                    'other' => 10240,       // 10MB
                ];
                
                $maxSize = $maxSizes[$this->file_category] ?? 10240;
                if ($file->getSize() > ($maxSize * 1024)) {
                    $validator->errors()->add('file', "File size cannot exceed {$maxSize}KB for this category.");
                }
            }
            
            // Validate version logic for updates
            if ($this->isMethod('PUT') || $this->isMethod('PATCH')) {
                $currentTechPack = $this->route('tech_pack');
                if ($currentTechPack && $this->version && $this->version <= $currentTechPack->version) {
                    $validator->errors()->add('version', 'New version must be greater than current version.');
                }
            }
            
            // Validate approval logic
            if ($this->sample_status === 'approved' && $this->sample_type) {
                // Check if user has permission to approve samples
                $user = auth()->user();
                if (!$user->can('approve_samples')) {
                    $validator->errors()->add('sample_status', 'You do not have permission to approve samples.');
                }
            }
            
            // Validate file naming conventions
            if ($this->hasFile('file')) {
                $file = $this->file('file');
                $fileName = $file->getClientOriginalName();
                
                // Check for potentially dangerous file names
                if (preg_match('/[<>:"|?*]/', $fileName)) {
                    $validator->errors()->add('file', 'File name contains invalid characters.');
                }
                
                // Check file name length
                if (strlen($fileName) > 255) {
                    $validator->errors()->add('file', 'File name is too long (maximum 255 characters).');
                }
            }
        });
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Set default values
        $defaults = [
            'uploaded_by' => auth()->id(),
            'is_current_version' => true,
            'is_active' => true,
            'version' => 1,
        ];
        
        // Only set defaults for new uploads
        if ($this->isMethod('POST')) {
            foreach ($defaults as $key => $value) {
                if (!$this->has($key)) {
                    $this->merge([$key => $value]);
                }
            }
        }
        
        // Set sample approval date if status is being set
        if (in_array($this->sample_status, ['approved', 'rejected']) && !$this->sample_approval_date) {
            $this->merge(['sample_approval_date' => now()->format('Y-m-d')]);
        }
        
        // Set approved_by if not provided but status requires it
        if (in_array($this->sample_status, ['approved', 'rejected']) && !$this->approved_by) {
            $this->merge(['approved_by' => auth()->id()]);
        }
    }

    /**
     * Get validated data with file information.
     */
    public function validated($key = null, $default = null)
    {
        $validated = parent::validated($key, $default);
        
        // Add file information if file was uploaded
        if ($this->hasFile('file')) {
            $file = $this->file('file');
            $validated['file_name'] = $file->getClientOriginalName();
            $validated['file_size'] = $file->getSize();
            $validated['mime_type'] = $file->getMimeType();
            $validated['file_type'] = $file->getClientOriginalExtension();
        }
        
        return $validated;
    }

    /**
     * Get the allowed file types for a category.
     */
    public static function getAllowedFileTypes(string $category): array
    {
        return match($category) {
            'tech_pack' => ['pdf', 'doc', 'docx'],
            'image', 'sample_image' => ['jpg', 'jpeg', 'png', 'gif', 'webp'],
            'reference' => ['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png', 'gif', 'webp'],
            default => ['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png', 'gif', 'webp', 'xls', 'xlsx']
        };
    }

    /**
     * Get the maximum file size for a category (in KB).
     */
    public static function getMaxFileSize(string $category): int
    {
        return match($category) {
            'tech_pack', 'reference', 'other' => 10240, // 10MB
            'image', 'sample_image' => 5120,            // 5MB
            default => 10240
        };
    }
}
