<?php

namespace App\Http\Requests\BuyerCRM;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

/**
 * Store Buyer Profile Request
 * 
 * Handles validation for creating a new buyer profile
 */
class StoreBuyerProfileRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'company' => ['required', 'string', 'max:255'],
            'country' => ['required', 'string', 'max:100'],
            'contact_person' => ['required', 'string', 'max:255'],
            'phone' => ['required', 'string', 'max:20'],
            'email' => ['required', 'email', 'unique:buyer_profiles,email'],
            'interest' => ['nullable', 'array'],
            'interest.*' => ['string', 'max:255'],
            'type' => ['required', Rule::in(['Brand', 'Retailer', 'Wholesaler', 'Agent'])],
            'priority' => ['required', Rule::in(['Hot', 'Warm', 'Cold'])],
            'status' => ['required', Rule::in(['Active', 'Inactive', 'Blocked'])],
            'assigned_to' => ['nullable', 'exists:users,id'],
            'rating' => ['nullable', 'integer', 'min:1', 'max:10'],
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'name' => 'buyer name',
            'company' => 'company name',
            'contact_person' => 'contact person',
            'phone' => 'phone number',
            'email' => 'email address',
            'interest' => 'product interests',
            'type' => 'buyer type',
            'priority' => 'priority level',
            'status' => 'status',
            'assigned_to' => 'assigned user',
            'rating' => 'rating',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'email.unique' => 'A buyer with this email address already exists.',
            'rating.min' => 'Rating must be between 1 and 10.',
            'rating.max' => 'Rating must be between 1 and 10.',
            'assigned_to.exists' => 'The selected user does not exist.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Clean and format phone number
        if ($this->has('phone')) {
            $this->merge([
                'phone' => preg_replace('/[^0-9+\-\s]/', '', $this->phone),
            ]);
        }

        // Ensure interest is an array
        if ($this->has('interest') && !is_array($this->interest)) {
            $this->merge([
                'interest' => explode(',', $this->interest),
            ]);
        }

        // Clean interest array
        if ($this->has('interest') && is_array($this->interest)) {
            $this->merge([
                'interest' => array_filter(array_map('trim', $this->interest)),
            ]);
        }
    }
}
