<?php

namespace App\Http\Requests\Stock;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StockItemRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user()->can('stock.items.create') || $this->user()->can('stock.items.update');
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $itemId = $this->route('stock_item') ?? $this->route('id');
        $isUpdate = $this->isMethod('PUT') || $this->isMethod('PATCH');

        $rules = [
            'category_id' => 'required|exists:stock_item_categories,id',
            'name' => 'required|string|max:200',
            'sku' => [
                'nullable',
                'string',
                'max:50',
                Rule::unique('stock_items', 'sku')->ignore($itemId)
            ],
            'item_type' => 'required|in:fabric,accessory,wip,finished_goods',
            'description' => 'nullable|string|max:1000',
            'color' => 'nullable|string|max:50',
            'size' => 'nullable|string|max:20',
            'style_number' => 'nullable|string|max:50',
            'season' => 'nullable|string|max:20',
            'fabric_composition' => 'nullable|array',
            'fabric_composition.*' => 'string|max:100',
            'quality_grade' => 'nullable|string|max:10',
            'unit_of_measure' => 'required|string|max:20',
            'current_quantity' => 'required|numeric|min:0|max:999999999.9999',
            'reserved_quantity' => 'nullable|numeric|min:0|max:999999999.9999',
            'reorder_level' => 'required|numeric|min:0|max:999999999.9999',
            'max_level' => 'nullable|numeric|min:0|max:999999999.9999',
            'cost_price' => 'required|numeric|min:0|max:999999999.9999',
            'average_cost' => 'nullable|numeric|min:0|max:999999999.9999',
            'costing_method' => 'required|in:fifo,lifo,average',
            'primary_location_id' => 'nullable|exists:stock_locations,id',
            'track_batches' => 'boolean',
            'track_expiry' => 'boolean',
            'is_active' => 'boolean',
            
            // Custom attributes
            'custom_attributes' => 'nullable|array',
            'custom_attributes.*' => 'nullable|string|max:255',
            
            // Supplier information
            'supplier_name' => 'nullable|string|max:100',
            'supplier_sku' => 'nullable|string|max:50',
            
            // Additional fields for specific item types
            'weight' => 'nullable|numeric|min:0',
            'dimensions' => 'nullable|array',
            'dimensions.length' => 'nullable|numeric|min:0',
            'dimensions.width' => 'nullable|numeric|min:0',
            'dimensions.height' => 'nullable|numeric|min:0',
            
            // Barcode information
            'barcode' => 'nullable|string|max:50',
            'barcode_type' => 'nullable|in:ean13,ean8,upc,code128,code39',
            
            // Images
            'images' => 'nullable|array|max:5',
            'images.*' => 'image|mimes:jpeg,png,jpg,gif|max:2048',
            
            // Tags
            'tags' => 'nullable|array',
            'tags.*' => 'string|max:50',
        ];

        // Additional validation for updates
        if ($isUpdate) {
            // Make some fields optional for updates
            $rules['category_id'] = 'sometimes|required|exists:stock_item_categories,id';
            $rules['name'] = 'sometimes|required|string|max:200';
            $rules['item_type'] = 'sometimes|required|in:fabric,accessory,wip,finished_goods';
            $rules['unit_of_measure'] = 'sometimes|required|string|max:20';
            $rules['current_quantity'] = 'sometimes|required|numeric|min:0|max:999999999.9999';
            $rules['reorder_level'] = 'sometimes|required|numeric|min:0|max:999999999.9999';
            $rules['cost_price'] = 'sometimes|required|numeric|min:0|max:999999999.9999';
            $rules['costing_method'] = 'sometimes|required|in:fifo,lifo,average';
        }

        return $rules;
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'category_id.required' => 'Please select a category for the stock item.',
            'category_id.exists' => 'The selected category does not exist.',
            'name.required' => 'Stock item name is required.',
            'name.max' => 'Stock item name cannot exceed 200 characters.',
            'sku.unique' => 'This SKU is already in use. Please choose a different SKU.',
            'sku.max' => 'SKU cannot exceed 50 characters.',
            'item_type.required' => 'Please select an item type.',
            'item_type.in' => 'Invalid item type selected.',
            'unit_of_measure.required' => 'Unit of measure is required.',
            'current_quantity.required' => 'Current quantity is required.',
            'current_quantity.numeric' => 'Current quantity must be a valid number.',
            'current_quantity.min' => 'Current quantity cannot be negative.',
            'current_quantity.max' => 'Current quantity is too large.',
            'reorder_level.required' => 'Reorder level is required.',
            'reorder_level.numeric' => 'Reorder level must be a valid number.',
            'reorder_level.min' => 'Reorder level cannot be negative.',
            'cost_price.required' => 'Cost price is required.',
            'cost_price.numeric' => 'Cost price must be a valid number.',
            'cost_price.min' => 'Cost price cannot be negative.',
            'costing_method.required' => 'Please select a costing method.',
            'costing_method.in' => 'Invalid costing method selected.',
            'primary_location_id.exists' => 'The selected primary location does not exist.',
            'images.*.image' => 'Each file must be an image.',
            'images.*.mimes' => 'Images must be in JPEG, PNG, JPG, or GIF format.',
            'images.*.max' => 'Each image must not exceed 2MB.',
            'fabric_composition.array' => 'Fabric composition must be an array.',
            'custom_attributes.array' => 'Custom attributes must be an array.',
            'dimensions.array' => 'Dimensions must be an array.',
            'tags.array' => 'Tags must be an array.',
            'tags.*.max' => 'Each tag cannot exceed 50 characters.',
        ];
    }

    /**
     * Get custom attribute names for validation errors.
     */
    public function attributes(): array
    {
        return [
            'category_id' => 'category',
            'item_type' => 'item type',
            'unit_of_measure' => 'unit of measure',
            'current_quantity' => 'current quantity',
            'reserved_quantity' => 'reserved quantity',
            'reorder_level' => 'reorder level',
            'max_level' => 'maximum level',
            'cost_price' => 'cost price',
            'average_cost' => 'average cost',
            'costing_method' => 'costing method',
            'primary_location_id' => 'primary location',
            'track_batches' => 'track batches',
            'track_expiry' => 'track expiry',
            'is_active' => 'active status',
            'fabric_composition' => 'fabric composition',
            'quality_grade' => 'quality grade',
            'style_number' => 'style number',
            'supplier_name' => 'supplier name',
            'supplier_sku' => 'supplier SKU',
            'barcode_type' => 'barcode type',
            'custom_attributes' => 'custom attributes',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Custom validation logic
            $this->validateMaxLevelGreaterThanReorderLevel($validator);
            $this->validateReservedQuantityNotGreaterThanCurrent($validator);
            $this->validateFabricComposition($validator);
            $this->validateDimensions($validator);
        });
    }

    /**
     * Validate that max level is greater than reorder level
     */
    protected function validateMaxLevelGreaterThanReorderLevel($validator): void
    {
        $maxLevel = $this->input('max_level');
        $reorderLevel = $this->input('reorder_level');

        if ($maxLevel && $reorderLevel && $maxLevel <= $reorderLevel) {
            $validator->errors()->add(
                'max_level',
                'Maximum level must be greater than reorder level.'
            );
        }
    }

    /**
     * Validate that reserved quantity is not greater than current quantity
     */
    protected function validateReservedQuantityNotGreaterThanCurrent($validator): void
    {
        $currentQuantity = $this->input('current_quantity');
        $reservedQuantity = $this->input('reserved_quantity');

        if ($reservedQuantity && $currentQuantity && $reservedQuantity > $currentQuantity) {
            $validator->errors()->add(
                'reserved_quantity',
                'Reserved quantity cannot be greater than current quantity.'
            );
        }
    }

    /**
     * Validate fabric composition for fabric items
     */
    protected function validateFabricComposition($validator): void
    {
        $itemType = $this->input('item_type');
        $fabricComposition = $this->input('fabric_composition');

        if ($itemType === 'fabric' && empty($fabricComposition)) {
            $validator->errors()->add(
                'fabric_composition',
                'Fabric composition is required for fabric items.'
            );
        }
    }

    /**
     * Validate dimensions if provided
     */
    protected function validateDimensions($validator): void
    {
        $dimensions = $this->input('dimensions');

        if ($dimensions && is_array($dimensions)) {
            $hasAnyDimension = !empty($dimensions['length']) || 
                              !empty($dimensions['width']) || 
                              !empty($dimensions['height']);

            if ($hasAnyDimension) {
                foreach (['length', 'width', 'height'] as $dimension) {
                    if (isset($dimensions[$dimension]) && $dimensions[$dimension] <= 0) {
                        $validator->errors()->add(
                            "dimensions.{$dimension}",
                            "The {$dimension} must be greater than 0."
                        );
                    }
                }
            }
        }
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Convert string booleans to actual booleans
        $booleanFields = ['track_batches', 'track_expiry', 'is_active'];
        
        foreach ($booleanFields as $field) {
            if ($this->has($field)) {
                $this->merge([
                    $field => filter_var($this->input($field), FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE)
                ]);
            }
        }

        // Clean up numeric fields
        $numericFields = [
            'current_quantity', 'reserved_quantity', 'reorder_level', 
            'max_level', 'cost_price', 'average_cost', 'weight'
        ];
        
        foreach ($numericFields as $field) {
            if ($this->has($field) && $this->input($field) !== null) {
                $value = str_replace(',', '', $this->input($field));
                $this->merge([$field => $value]);
            }
        }

        // Trim string fields
        $stringFields = [
            'name', 'sku', 'description', 'color', 'size', 
            'style_number', 'season', 'quality_grade', 'unit_of_measure',
            'supplier_name', 'supplier_sku', 'barcode'
        ];
        
        foreach ($stringFields as $field) {
            if ($this->has($field)) {
                $this->merge([$field => trim($this->input($field))]);
            }
        }
    }
}
