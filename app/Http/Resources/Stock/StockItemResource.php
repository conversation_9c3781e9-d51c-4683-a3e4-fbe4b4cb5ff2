<?php

namespace App\Http\Resources\Stock;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class StockItemResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'sku' => $this->sku,
            'name' => $this->name,
            'description' => $this->description,
            'item_type' => $this->item_type,
            'item_type_label' => $this->getItemTypeLabel(),
            
            // Basic attributes
            'color' => $this->color,
            'size' => $this->size,
            'style_number' => $this->style_number,
            'season' => $this->season,
            'quality_grade' => $this->quality_grade,
            'fabric_composition' => $this->fabric_composition,
            
            // Inventory information
            'unit_of_measure' => $this->unit_of_measure,
            'current_quantity' => $this->current_quantity,
            'reserved_quantity' => $this->reserved_quantity,
            'available_quantity' => $this->available_quantity,
            'reorder_level' => $this->reorder_level,
            'max_level' => $this->max_level,
            
            // Costing information
            'cost_price' => $this->cost_price,
            'average_cost' => $this->average_cost,
            'costing_method' => $this->costing_method,
            'costing_method_label' => $this->getCostingMethodLabel(),
            
            // Stock status indicators
            'stock_status' => $this->getStockStatus(),
            'stock_status_label' => $this->getStockStatusLabel(),
            'is_low_stock' => $this->current_quantity <= $this->reorder_level,
            'is_out_of_stock' => $this->current_quantity <= 0,
            'is_overstock' => $this->max_level > 0 && $this->current_quantity > $this->max_level,
            
            // Tracking settings
            'track_batches' => $this->track_batches,
            'track_expiry' => $this->track_expiry,
            'is_active' => $this->is_active,
            
            // Calculated values
            'total_value' => $this->current_quantity * $this->average_cost,
            'available_value' => $this->available_quantity * $this->average_cost,
            'reserved_value' => $this->reserved_quantity * $this->average_cost,
            
            // Relationships
            'category' => $this->whenLoaded('category', function () {
                return [
                    'id' => $this->category->id,
                    'name' => $this->category->name,
                    'code' => $this->category->code ?? null,
                ];
            }),
            
            'primary_location' => $this->whenLoaded('primaryLocation', function () {
                return [
                    'id' => $this->primaryLocation->id,
                    'name' => $this->primaryLocation->name,
                    'code' => $this->primaryLocation->code,
                    'type' => $this->primaryLocation->type,
                ];
            }),
            
            'location_items' => $this->whenLoaded('locationItems', function () {
                return $this->locationItems->map(function ($locationItem) {
                    return [
                        'location_id' => $locationItem->location_id,
                        'location_name' => $locationItem->location->name ?? null,
                        'location_code' => $locationItem->location->code ?? null,
                        'quantity' => $locationItem->quantity,
                        'reserved_quantity' => $locationItem->reserved_quantity,
                        'available_quantity' => $locationItem->available_quantity,
                        'damaged_quantity' => $locationItem->damaged_quantity,
                        'min_quantity' => $locationItem->min_quantity,
                        'max_quantity' => $locationItem->max_quantity,
                        'average_cost' => $locationItem->average_cost,
                        'total_value' => $locationItem->total_value,
                        'last_transaction_date' => $locationItem->last_transaction_date,
                        'last_transaction_type' => $locationItem->last_transaction_type,
                    ];
                });
            }),
            
            'batches' => $this->whenLoaded('batches', function () {
                return $this->batches->map(function ($batch) {
                    return [
                        'id' => $batch->id,
                        'batch_number' => $batch->batch_number,
                        'lot_number' => $batch->lot_number,
                        'manufacturing_date' => $batch->manufacturing_date,
                        'expiry_date' => $batch->expiry_date,
                        'supplier_name' => $batch->supplier_name,
                        'initial_quantity' => $batch->initial_quantity,
                        'current_quantity' => $batch->current_quantity,
                        'reserved_quantity' => $batch->reserved_quantity,
                        'unit_cost' => $batch->unit_cost,
                        'status' => $batch->status,
                        'quality_notes' => $batch->quality_notes,
                        'is_expired' => $batch->expiry_date && $batch->expiry_date < now(),
                        'days_to_expiry' => $batch->expiry_date ? now()->diffInDays($batch->expiry_date, false) : null,
                    ];
                });
            }),
            
            'recent_movements' => $this->whenLoaded('stockMovements', function () {
                return $this->stockMovements->take(10)->map(function ($movement) {
                    return [
                        'id' => $movement->id,
                        'movement_number' => $movement->movement_number,
                        'movement_type' => $movement->movement_type,
                        'quantity' => $movement->quantity,
                        'unit_cost' => $movement->unit_cost,
                        'total_cost' => $movement->total_cost,
                        'movement_date' => $movement->movement_date,
                        'status' => $movement->status,
                        'location_name' => $movement->location->name ?? null,
                        'created_by_name' => $movement->creator->name ?? null,
                        'notes' => $movement->notes,
                    ];
                });
            }),
            
            'alerts' => $this->whenLoaded('alerts', function () {
                return $this->alerts->where('status', 'active')->map(function ($alert) {
                    return [
                        'id' => $alert->id,
                        'alert_type' => $alert->alert_type,
                        'title' => $alert->title,
                        'message' => $alert->message,
                        'priority' => $alert->priority,
                        'alert_date' => $alert->alert_date,
                        'status' => $alert->status,
                    ];
                });
            }),
            
            // Audit information
            'created_by' => $this->whenLoaded('creator', function () {
                return [
                    'id' => $this->creator->id,
                    'name' => $this->creator->name,
                ];
            }),
            
            'updated_by' => $this->whenLoaded('updater', function () {
                return [
                    'id' => $this->updater->id,
                    'name' => $this->updater->name,
                ];
            }),
            
            // Timestamps
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'deleted_at' => $this->when($this->deleted_at, $this->deleted_at),
            
            // Additional computed fields
            'turnover_rate' => $this->when(
                $request->has('include_analytics'),
                function () {
                    return $this->calculateTurnoverRate();
                }
            ),
            
            'abc_classification' => $this->when(
                $request->has('include_analytics'),
                function () {
                    return $this->getAbcClassification();
                }
            ),
            
            'stock_days_remaining' => $this->when(
                $request->has('include_analytics'),
                function () {
                    return $this->calculateStockDaysRemaining();
                }
            ),
        ];
    }

    /**
     * Get item type label
     */
    protected function getItemTypeLabel(): string
    {
        $labels = [
            'fabric' => 'Fabric',
            'accessory' => 'Accessory',
            'wip' => 'Work in Progress',
            'finished_goods' => 'Finished Goods',
        ];

        return $labels[$this->item_type] ?? $this->item_type;
    }

    /**
     * Get costing method label
     */
    protected function getCostingMethodLabel(): string
    {
        $labels = [
            'fifo' => 'First In, First Out (FIFO)',
            'lifo' => 'Last In, First Out (LIFO)',
            'average' => 'Weighted Average',
        ];

        return $labels[$this->costing_method] ?? $this->costing_method;
    }

    /**
     * Get stock status
     */
    protected function getStockStatus(): string
    {
        if ($this->current_quantity <= 0) {
            return 'out_of_stock';
        }

        if ($this->current_quantity <= $this->reorder_level) {
            return 'low_stock';
        }

        if ($this->max_level > 0 && $this->current_quantity > $this->max_level) {
            return 'overstock';
        }

        return 'normal';
    }

    /**
     * Get stock status label
     */
    protected function getStockStatusLabel(): string
    {
        $labels = [
            'out_of_stock' => 'Out of Stock',
            'low_stock' => 'Low Stock',
            'overstock' => 'Overstock',
            'normal' => 'Normal',
        ];

        return $labels[$this->getStockStatus()] ?? 'Unknown';
    }

    /**
     * Calculate turnover rate (placeholder)
     */
    protected function calculateTurnoverRate(): ?float
    {
        // This would be implemented based on business logic
        // For now, return null as placeholder
        return null;
    }

    /**
     * Get ABC classification (placeholder)
     */
    protected function getAbcClassification(): ?string
    {
        // This would be implemented based on business logic
        // For now, return null as placeholder
        return null;
    }

    /**
     * Calculate stock days remaining (placeholder)
     */
    protected function calculateStockDaysRemaining(): ?int
    {
        // This would be implemented based on consumption patterns
        // For now, return null as placeholder
        return null;
    }
}
