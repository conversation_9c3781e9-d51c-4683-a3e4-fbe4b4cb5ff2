<?php

namespace App\Imports;

use App\Models\Attendance;
use App\Models\Employee;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;
use Illuminate\Support\Collection;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class AttendanceImport implements ToCollection, WithHeadingRow, WithValidation
{
    protected $results = [
        'total_rows' => 0,
        'processed_employees' => 0,
        'inserted_records' => 0,
        'updated_records' => 0,
        'errors' => [],
        'attendance_data' => []
    ];

    /**
     * Process the collection of rows
     */
    public function collection(Collection $rows)
    {
        $this->results['total_rows'] = $rows->count();
        $attendanceData = [];

        // Group rows by employee and date
        foreach ($rows as $index => $row) {
            try {
                $this->processRow($row, $index + 2, $attendanceData); // +2 because of header row and 0-based index
            } catch (\Exception $e) {
                $this->results['errors'][] = "Row " . ($index + 2) . ": " . $e->getMessage();
                Log::error("Attendance import error on row " . ($index + 2), [
                    'error' => $e->getMessage(),
                    'row_data' => $row->toArray()
                ]);
            }
        }

        // Process grouped attendance data
        $this->processAttendanceData($attendanceData);
    }

    /**
     * Process individual row and group by employee/date
     */
    protected function processRow($row, $rowNumber, &$attendanceData)
    {
        // Expected columns: user_id, date, time, type
        $userId = $this->cleanValue($row['user_id'] ?? $row['userid'] ?? null);
        $date = $this->parseDate($row['date'] ?? null);
        $time = $this->parseTime($row['time'] ?? null);
        $type = strtolower(trim($row['type'] ?? $row['check_type'] ?? ''));

        if (!$userId || !$date || !$time) {
            throw new \Exception("Missing required data: User ID, Date, or Time");
        }

        // Find employee by machine_user_id
        $employee = Employee::where('machine_user_id', $userId)->first();
        if (!$employee) {
            throw new \Exception("Employee not found for User ID: {$userId}");
        }

        $dateKey = $date->format('Y-m-d');
        $employeeKey = $employee->id;

        // Initialize employee data if not exists
        if (!isset($attendanceData[$employeeKey])) {
            $attendanceData[$employeeKey] = [];
        }

        if (!isset($attendanceData[$employeeKey][$dateKey])) {
            $attendanceData[$employeeKey][$dateKey] = [
                'employee' => $employee,
                'date' => $date,
                'check_ins' => [],
                'check_outs' => [],
                'all_times' => []
            ];
        }

        // Categorize the time entry
        $timeEntry = [
            'time' => $time,
            'type' => $type,
            'raw_data' => $row->toArray()
        ];

        $attendanceData[$employeeKey][$dateKey]['all_times'][] = $timeEntry;

        if (in_array($type, ['in', 'check in', 'checkin', 'entry', '0'])) {
            $attendanceData[$employeeKey][$dateKey]['check_ins'][] = $timeEntry;
        } elseif (in_array($type, ['out', 'check out', 'checkout', 'exit', '1'])) {
            $attendanceData[$employeeKey][$dateKey]['check_outs'][] = $timeEntry;
        }
    }

    /**
     * Process grouped attendance data and save to database
     */
    protected function processAttendanceData($attendanceData)
    {
        foreach ($attendanceData as $employeeId => $employeeDates) {
            foreach ($employeeDates as $dateKey => $dayData) {
                try {
                    $this->processEmployeeDay($dayData);
                } catch (\Exception $e) {
                    $this->results['errors'][] = "Employee {$dayData['employee']->name} on {$dateKey}: " . $e->getMessage();
                }
            }
        }
    }

    /**
     * Process a single employee's day attendance
     */
    protected function processEmployeeDay($dayData)
    {
        $employee = $dayData['employee'];
        $date = $dayData['date'];
        $checkIns = $dayData['check_ins'];
        $checkOuts = $dayData['check_outs'];
        $allTimes = $dayData['all_times'];

        // Determine in_time (earliest check-in or earliest time if no specific check-ins)
        $inTime = null;
        if (!empty($checkIns)) {
            $inTime = collect($checkIns)->min('time');
        } elseif (!empty($allTimes)) {
            $inTime = collect($allTimes)->min('time');
        }

        // Determine out_time (latest check-out or latest time if no specific check-outs)
        $outTime = null;
        if (!empty($checkOuts)) {
            $outTime = collect($checkOuts)->max('time');
        } elseif (!empty($allTimes) && count($allTimes) > 1) {
            $outTime = collect($allTimes)->max('time');
        }

        // Calculate working hours with enhanced logic
        $workingHours = null;
        $totalBreakTime = 0; // Default break time in hours

        if ($inTime && $outTime && $outTime > $inTime) {
            // Calculate total time difference
            $totalHours = $inTime->diffInHours($outTime, true);

            // Subtract break time if working more than 6 hours (standard lunch break)
            if ($totalHours > 6) {
                $totalBreakTime = 1; // 1 hour lunch break
            }

            $workingHours = max(0, $totalHours - $totalBreakTime);

            // Round to 2 decimal places
            $workingHours = round($workingHours, 2);
        }

        // Determine status with enhanced logic
        $status = 'absent';
        if ($inTime) {
            if ($workingHours >= 8) {
                $status = 'present'; // Full day
            } elseif ($workingHours >= 4) {
                $status = 'present'; // Still present but less than full day
            } elseif ($workingHours > 0) {
                $status = 'half_day'; // Half day
            } else {
                $status = 'present'; // Present but no out time yet
            }
        }

        // Calculate daily cost based on working hours and employee salary
        $dailyCost = null;
        if ($employee->salary && $workingHours) {
            // Assuming 22 working days per month and 8 hours per day
            $dailyRate = $employee->salary / 22;
            $hourlyRate = $dailyRate / 8;
            $dailyCost = $hourlyRate * $workingHours;
        }

        // Create or update attendance record
        $attendance = Attendance::updateOrCreate(
            [
                'employee_id' => $employee->id,
                'date' => $date->format('Y-m-d'),
            ],
            [
                'status' => $status,
                'in_time' => $inTime?->format('H:i:s'),
                'out_time' => $outTime?->format('H:i:s'),
                'working_hours' => $workingHours,
                'monthly_salary' => $employee->salary,
                'daily_cost' => $dailyCost,
                'is_imported' => true,
                'source' => 'imported',
                'machine_logs' => $allTimes,
                'notes' => $this->generateAttendanceNotes($allTimes, $workingHours),
            ]
        );

        // Track results
        if ($attendance->wasRecentlyCreated) {
            $this->results['inserted_records']++;
        } else {
            $this->results['updated_records']++;
        }

        // Add to results for display
        $this->results['attendance_data'][] = [
            'employee_name' => $employee->name,
            'employee_id' => $employee->employee_id,
            'date' => $date->format('Y-m-d'),
            'in_time' => $inTime?->format('H:i:s'),
            'out_time' => $outTime?->format('H:i:s'),
            'working_hours' => $workingHours ? number_format($workingHours, 2) : null,
            'status' => $status,
            'action' => $attendance->wasRecentlyCreated ? 'Inserted' : 'Updated'
        ];

        $this->results['processed_employees']++;
    }

    /**
     * Clean and trim values
     */
    protected function cleanValue($value)
    {
        return $value ? trim($value) : null;
    }

    /**
     * Parse date from various formats
     */
    protected function parseDate($dateValue)
    {
        if (!$dateValue) {
            return null;
        }

        try {
            // Handle Excel date serial numbers
            if (is_numeric($dateValue)) {
                return Carbon::createFromFormat('Y-m-d', '1900-01-01')->addDays($dateValue - 2);
            }

            // Try common date formats
            $formats = ['Y-m-d', 'd/m/Y', 'm/d/Y', 'd-m-Y', 'm-d-Y', 'Y/m/d'];

            foreach ($formats as $format) {
                try {
                    return Carbon::createFromFormat($format, $dateValue);
                } catch (\Exception $e) {
                    continue;
                }
            }

            // Try Carbon's flexible parsing
            return Carbon::parse($dateValue);
        } catch (\Exception $e) {
            throw new \Exception("Invalid date format: {$dateValue}");
        }
    }

    /**
     * Parse time from various formats
     */
    protected function parseTime($timeValue)
    {
        if (!$timeValue) {
            return null;
        }

        try {
            // Handle Excel time serial numbers (decimal part of date-time)
            if (is_numeric($timeValue) && $timeValue < 1) {
                $totalSeconds = $timeValue * 24 * 60 * 60;
                $hours = floor($totalSeconds / 3600);
                $minutes = floor(($totalSeconds % 3600) / 60);
                $seconds = $totalSeconds % 60;
                return Carbon::createFromTime($hours, $minutes, $seconds);
            }

            // Try common time formats
            $formats = ['H:i:s', 'H:i', 'g:i A', 'g:i:s A', 'h:i A', 'h:i:s A'];

            foreach ($formats as $format) {
                try {
                    return Carbon::createFromFormat($format, $timeValue);
                } catch (\Exception $e) {
                    continue;
                }
            }

            // Try Carbon's flexible parsing
            return Carbon::parse($timeValue);
        } catch (\Exception $e) {
            throw new \Exception("Invalid time format: {$timeValue}");
        }
    }

    /**
     * Get import results
     */
    public function getResults()
    {
        return $this->results;
    }

    /**
     * Validation rules for the import
     */
    public function rules(): array
    {
        return [
            '*.user_id' => 'required',
            '*.date' => 'required',
            '*.time' => 'required',
            '*.type' => 'required',
        ];
    }

    /**
     * Custom validation messages
     */
    public function customValidationMessages()
    {
        return [
            '*.user_id.required' => 'User ID is required',
            '*.date.required' => 'Date is required',
            '*.time.required' => 'Time is required',
            '*.type.required' => 'Type is required',
        ];
    }

    /**
     * Generate attendance notes based on machine logs and working hours
     */
    private function generateAttendanceNotes($allTimes, $workingHours)
    {
        $notes = [];

        if (count($allTimes) > 2) {
            $notes[] = "Multiple entries detected (" . count($allTimes) . " records)";
        }

        if ($workingHours && $workingHours > 10) {
            $notes[] = "Overtime: " . round($workingHours - 8, 2) . " hours";
        } elseif ($workingHours && $workingHours < 4) {
            $notes[] = "Short day: " . $workingHours . " hours";
        }

        if (empty($allTimes)) {
            $notes[] = "No machine records found";
        }

        return implode('; ', $notes);
    }
}
