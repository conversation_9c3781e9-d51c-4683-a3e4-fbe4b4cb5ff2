<?php

namespace App\Libraries\ZKTeco;

/**
 * Custom ZKTeco Library for Biometric Device Communication
 * 
 * This is a simplified implementation of ZKTeco device communication
 * that can be easily extended and customized for your specific needs.
 */
class ZKLib
{
    protected $ip;
    protected $port;
    protected $timeout;
    protected $socket;
    protected $isConnected = false;
    protected $lastError = '';
    
    // ZKTeco protocol constants
    const CMD_CONNECT = 1000;
    const CMD_EXIT = 1001;
    const CMD_ENABLEDEVICE = 1001;
    const CMD_DISABLEDEVICE = 1002;
    const CMD_RESTART = 1004;
    const CMD_POWEROFF = 1005;
    const CMD_SLEEP = 1006;
    const CMD_RESUME = 1007;
    const CMD_TEST_TEMP = 1011;
    const CMD_TESTVOICE = 1017;
    const CMD_VERSION = 1100;
    const CMD_CHANGE_SPEED = 1101;
    const CMD_AUTH = 1102;
    const CMD_PREPARE_DATA = 1500;
    const CMD_DATA = 1501;
    const CMD_FREE_DATA = 1502;
    const CMD_PREPARE_BUFFER = 1503;
    const CMD_READ_BUFFER = 1504;
    const CMD_USER_WRQ = 8;
    const CMD_USERTEMP_RRQ = 9;
    const CMD_USERTEMP_WRQ = 10;
    const CMD_OPTIONS_RRQ = 11;
    const CMD_OPTIONS_WRQ = 12;
    const CMD_ATTLOG_RRQ = 13;
    const CMD_CLEAR_DATA = 14;
    const CMD_CLEAR_ATTLOG = 15;
    const CMD_DELETE_USER = 18;
    const CMD_DELETE_USERTEMP = 19;
    const CMD_CLEAR_ADMIN = 20;
    const CMD_ENABLE_CLOCK = 57;
    const CMD_STARTVERIFY = 60;
    const CMD_STARTENROLL = 61;
    const CMD_CANCELCAPTURE = 62;
    const CMD_STATE_RRQ = 64;
    const CMD_WRITE_LCD = 66;
    const CMD_CLEAR_LCD = 67;
    const CMD_GET_PINWIDTH = 69;
    const CMD_SMS_WRQ = 70;
    const CMD_SMS_RRQ = 71;
    const CMD_DELETE_SMS = 72;
    const CMD_UDATA_WRQ = 73;
    const CMD_DELETE_UDATA = 74;
    const CMD_DOORSTATE_RRQ = 75;
    const CMD_WRITE_MIFARE = 76;
    const CMD_EMPTY_MIFARE = 78;
    const CMD_GET_TIME = 201;
    const CMD_SET_TIME = 202;

    public function __construct($ip, $port = 4370, $timeout = 5)
    {
        $this->ip = $ip;
        $this->port = $port;
        $this->timeout = $timeout;
    }

    /**
     * Connect to ZKTeco device
     */
    public function connect()
    {
        try {
            $this->socket = socket_create(AF_INET, SOCK_DGRAM, SOL_UDP);
            
            if (!$this->socket) {
                $this->lastError = "Failed to create socket: " . socket_strerror(socket_last_error());
                return false;
            }

            socket_set_option($this->socket, SOL_SOCKET, SO_RCVTIMEO, [
                'sec' => $this->timeout,
                'usec' => 0
            ]);

            socket_set_option($this->socket, SOL_SOCKET, SO_SNDTIMEO, [
                'sec' => $this->timeout,
                'usec' => 0
            ]);

            // Send connect command
            $command = $this->createCommand(self::CMD_CONNECT);
            $result = socket_sendto($this->socket, $command, strlen($command), 0, $this->ip, $this->port);
            
            if ($result === false) {
                $this->lastError = "Failed to send connect command: " . socket_strerror(socket_last_error($this->socket));
                return false;
            }

            // Wait for response
            $response = '';
            $from = '';
            $port = 0;
            $result = socket_recvfrom($this->socket, $response, 1024, 0, $from, $port);
            
            if ($result === false) {
                $this->lastError = "No response from device: " . socket_strerror(socket_last_error($this->socket));
                return false;
            }

            $this->isConnected = true;
            return true;

        } catch (\Exception $e) {
            $this->lastError = "Connection error: " . $e->getMessage();
            return false;
        }
    }

    /**
     * Disconnect from device
     */
    public function disconnect()
    {
        if ($this->socket && $this->isConnected) {
            $command = $this->createCommand(self::CMD_EXIT);
            socket_sendto($this->socket, $command, strlen($command), 0, $this->ip, $this->port);
            socket_close($this->socket);
        }
        
        $this->isConnected = false;
        return true;
    }

    /**
     * Check if connected
     */
    public function isConnected()
    {
        return $this->isConnected;
    }

    /**
     * Get device version
     */
    public function getVersion()
    {
        if (!$this->isConnected) {
            return false;
        }

        $command = $this->createCommand(self::CMD_VERSION);
        socket_sendto($this->socket, $command, strlen($command), 0, $this->ip, $this->port);
        
        $response = '';
        $from = '';
        $port = 0;
        socket_recvfrom($this->socket, $response, 1024, 0, $from, $port);
        
        return $this->parseResponse($response);
    }

    /**
     * Get attendance logs
     */
    public function getAttendance()
    {
        if (!$this->isConnected) {
            return [];
        }

        // For now, return mock data structure
        // In a real implementation, this would send CMD_ATTLOG_RRQ and parse the response
        return $this->getMockAttendanceData();
    }

    /**
     * Get users from device
     */
    public function getUsers()
    {
        if (!$this->isConnected) {
            return [];
        }

        // For now, return mock data structure
        // In a real implementation, this would send CMD_USER_WRQ and parse the response
        return $this->getMockUserData();
    }

    /**
     * Get device time
     */
    public function getTime()
    {
        if (!$this->isConnected) {
            return false;
        }

        $command = $this->createCommand(self::CMD_GET_TIME);
        socket_sendto($this->socket, $command, strlen($command), 0, $this->ip, $this->port);
        
        $response = '';
        $from = '';
        $port = 0;
        socket_recvfrom($this->socket, $response, 1024, 0, $from, $port);
        
        // Parse time response (simplified)
        return date('Y-m-d H:i:s');
    }

    /**
     * Set device time
     */
    public function setTime($datetime)
    {
        if (!$this->isConnected) {
            return false;
        }

        $timestamp = strtotime($datetime);
        $command = $this->createCommand(self::CMD_SET_TIME, pack('V', $timestamp));
        
        $result = socket_sendto($this->socket, $command, strlen($command), 0, $this->ip, $this->port);
        return $result !== false;
    }

    /**
     * Clear attendance logs
     */
    public function clearAttendanceLog()
    {
        if (!$this->isConnected) {
            return false;
        }

        $command = $this->createCommand(self::CMD_CLEAR_ATTLOG);
        $result = socket_sendto($this->socket, $command, strlen($command), 0, $this->ip, $this->port);
        
        return $result !== false;
    }

    /**
     * Get last error
     */
    public function getLastError()
    {
        return $this->lastError;
    }

    /**
     * Create command packet
     */
    protected function createCommand($command, $data = '')
    {
        $packet = pack('VVVV', $command, 0, 0, strlen($data));
        $packet .= $data;
        
        // Add checksum (simplified)
        $checksum = 0;
        for ($i = 0; $i < strlen($packet); $i++) {
            $checksum += ord($packet[$i]);
        }
        
        $packet .= pack('V', $checksum);
        return $packet;
    }

    /**
     * Parse response packet
     */
    protected function parseResponse($response)
    {
        if (strlen($response) < 16) {
            return false;
        }

        $header = unpack('Vcmd/Vchecksum/Vsession/Vsize', substr($response, 0, 16));
        $data = substr($response, 16, $header['size']);
        
        return [
            'command' => $header['cmd'],
            'data' => $data,
            'size' => $header['size']
        ];
    }

    /**
     * Generate mock attendance data for testing
     */
    protected function getMockAttendanceData()
    {
        $logs = [];
        $userIds = ['001', '002', '003', '004', '005'];
        
        for ($i = 0; $i < 10; $i++) {
            $userId = $userIds[array_rand($userIds)];
            $timestamp = date('Y-m-d H:i:s', strtotime('-' . rand(0, 7) . ' days +' . rand(8, 17) . ' hours'));
            
            $logs[] = [
                'userid' => $userId,
                'timestamp' => $timestamp,
                'type' => rand(0, 1), // 0 = check in, 1 = check out
                'verify' => 1,
                'status' => 0
            ];
        }
        
        return $logs;
    }

    /**
     * Generate mock user data for testing
     */
    protected function getMockUserData()
    {
        return [
            ['userid' => '001', 'name' => 'John Doe', 'role' => 0, 'password' => '', 'cardno' => ''],
            ['userid' => '002', 'name' => 'Jane Smith', 'role' => 0, 'password' => '', 'cardno' => ''],
            ['userid' => '003', 'name' => 'Mike Johnson', 'role' => 0, 'password' => '', 'cardno' => ''],
            ['userid' => '004', 'name' => 'Sarah Wilson', 'role' => 0, 'password' => '', 'cardno' => ''],
            ['userid' => '005', 'name' => 'David Brown', 'role' => 0, 'password' => '', 'cardno' => ''],
        ];
    }
}
