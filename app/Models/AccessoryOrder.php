<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Traits\TenantScoped;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AccessoryOrder extends Model
{
    use HasFactory;

    protected $fillable = [
        'invoice_no',
        'user_id',
        'party_id',
        'qty_unit',
        'unit_price',
        'ttl_amount',
        'accessory_id',
        'type',
        'balance',
        'qty_balance'
    ];

    public function accessory(): BelongsTo
    {
        return $this->belongsTo(Accessory::class,'accessory_id');
    }
    public function party(): BelongsTo
    {
        return $this->belongsTo(Party::class,'party_id');
    }

    /**
     * Calculate balance for a specific accessory
     * Balance = Total IN amount - Total OUT amount
     */
    public static function calculateAccessoryBalance($accessoryId)
    {
        $inTotal = self::where('accessory_id', $accessoryId)
            ->where('type', 'in')
            ->sum('ttl_amount');

        $outTotal = self::where('accessory_id', $accessoryId)
            ->where('type', 'out')
            ->sum('ttl_amount');

        return $inTotal - $outTotal;
    }

    /**
     * Calculate quantity balance for a specific accessory
     * QTY Balance = Total IN quantity - Total OUT quantity
     */
    public static function calculateAccessoryQtyBalance($accessoryId)
    {
        $inQty = self::where('accessory_id', $accessoryId)
            ->where('type', 'in')
            ->sum('qty_unit');

        $outQty = self::where('accessory_id', $accessoryId)
            ->where('type', 'out')
            ->sum('qty_unit');

        return $inQty - $outQty;
    }

    /**
     * Get the calculated balance attribute
     */
    public function getCalculatedBalanceAttribute()
    {
        return self::calculateAccessoryBalance($this->accessory_id);
    }

    /**
     * Get the calculated quantity balance attribute
     */
    public function getCalculatedQtyBalanceAttribute()
    {
        return self::calculateAccessoryQtyBalance($this->accessory_id);
    }
}
