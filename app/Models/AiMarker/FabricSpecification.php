<?php

namespace App\Models\AiMarker;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Builder;

/**
 * AI Marker Fabric Specification Model
 * 
 * Represents fabric types and their properties for marker optimization
 * 
 * @property int $id
 * @property int $company_id
 * @property int $created_by
 * @property string $fabric_code
 * @property string $name
 * @property string|null $description
 * @property string $fabric_type
 * @property string|null $weave_type
 * @property float|null $weight_gsm
 * @property float|null $thickness_mm
 * @property array $standard_widths
 * @property float $default_width
 * @property float|null $max_width
 * @property float|null $min_width
 * @property float $stretch_percentage
 * @property string $stretch_direction
 * @property float $shrinkage_percentage
 * @property bool $directional_print
 * @property bool $one_way_design
 * @property string $grain_stability
 * @property float $grain_angle_tolerance
 * @property float $minimum_spacing
 * @property float $seam_allowance
 * @property bool $requires_pattern_matching
 * @property float|null $pattern_repeat_length
 * @property float|null $pattern_repeat_width
 * @property float|null $cost_per_yard
 * @property string $currency
 * @property float $waste_factor
 * @property string|null $supplier_name
 * @property string|null $supplier_code
 * @property string|null $supplier_notes
 * @property array|null $available_colors
 * @property string|null $finish_type
 * @property bool $color_fastness
 * @property array|null $care_instructions
 * @property string|null $fabric_content
 * @property int $usage_count
 * @property \Carbon\Carbon|null $last_used_at
 * @property string $status
 * @property bool $approved
 * @property int|null $approved_by
 * @property \Carbon\Carbon|null $approved_at
 */
class FabricSpecification extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'ai_marker_fabric_specifications';

    protected $fillable = [
        'company_id',
        'created_by',
        'fabric_code',
        'name',
        'description',
        'fabric_type',
        'weave_type',
        'weight_gsm',
        'thickness_mm',
        'standard_widths',
        'default_width',
        'max_width',
        'min_width',
        'stretch_percentage',
        'stretch_direction',
        'shrinkage_percentage',
        'directional_print',
        'one_way_design',
        'grain_stability',
        'grain_angle_tolerance',
        'minimum_spacing',
        'seam_allowance',
        'requires_pattern_matching',
        'pattern_repeat_length',
        'pattern_repeat_width',
        'cost_per_yard',
        'currency',
        'waste_factor',
        'supplier_name',
        'supplier_code',
        'supplier_notes',
        'available_colors',
        'finish_type',
        'color_fastness',
        'care_instructions',
        'fabric_content',
        'usage_count',
        'last_used_at',
        'status',
        'approved',
        'approved_by',
        'approved_at'
    ];

    protected $casts = [
        'standard_widths' => 'array',
        'available_colors' => 'array',
        'care_instructions' => 'array',
        'weight_gsm' => 'decimal:2',
        'thickness_mm' => 'decimal:3',
        'default_width' => 'decimal:2',
        'max_width' => 'decimal:2',
        'min_width' => 'decimal:2',
        'stretch_percentage' => 'decimal:2',
        'shrinkage_percentage' => 'decimal:2',
        'grain_angle_tolerance' => 'decimal:2',
        'minimum_spacing' => 'decimal:3',
        'seam_allowance' => 'decimal:3',
        'pattern_repeat_length' => 'decimal:2',
        'pattern_repeat_width' => 'decimal:2',
        'cost_per_yard' => 'decimal:2',
        'waste_factor' => 'decimal:3',
        'directional_print' => 'boolean',
        'one_way_design' => 'boolean',
        'requires_pattern_matching' => 'boolean',
        'color_fastness' => 'boolean',
        'approved' => 'boolean',
        'usage_count' => 'integer',
        'last_used_at' => 'datetime',
        'approved_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime'
    ];

    /**
     * Get the company that owns the fabric specification
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(\App\Models\Company::class);
    }

    /**
     * Get the user who created the fabric specification
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'created_by');
    }

    /**
     * Get the user who approved the fabric specification
     */
    public function approver(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'approved_by');
    }

    /**
     * Get optimization templates using this fabric
     */
    public function optimizationTemplates(): HasMany
    {
        return $this->hasMany(OptimizationTemplate::class);
    }

    /**
     * Scope for filtering by company
     */
    public function scopeForCompany(Builder $query, int $companyId): Builder
    {
        return $query->where('company_id', $companyId);
    }

    /**
     * Scope for active fabrics
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope for approved fabrics
     */
    public function scopeApproved(Builder $query): Builder
    {
        return $query->where('approved', true);
    }

    /**
     * Scope for filtering by fabric type
     */
    public function scopeByType(Builder $query, string $fabricType): Builder
    {
        return $query->where('fabric_type', $fabricType);
    }

    /**
     * Scope for filtering by width range
     */
    public function scopeByWidthRange(Builder $query, float $minWidth, float $maxWidth): Builder
    {
        return $query->where('default_width', '>=', $minWidth)
                    ->where('default_width', '<=', $maxWidth);
    }

    /**
     * Scope for searching fabrics
     */
    public function scopeSearch(Builder $query, string $search): Builder
    {
        return $query->where(function ($q) use ($search) {
            $q->where('name', 'like', "%{$search}%")
              ->orWhere('description', 'like', "%{$search}%")
              ->orWhere('fabric_code', 'like', "%{$search}%")
              ->orWhere('fabric_content', 'like', "%{$search}%");
        });
    }

    /**
     * Check if fabric supports a specific width
     */
    public function supportsWidth(float $width): bool
    {
        return in_array($width, $this->standard_widths) ||
               ($width >= ($this->min_width ?? 0) && $width <= ($this->max_width ?? PHP_FLOAT_MAX));
    }

    /**
     * Get the closest standard width to a given width
     */
    public function getClosestStandardWidth(float $targetWidth): float
    {
        $standardWidths = $this->standard_widths;
        
        if (empty($standardWidths)) {
            return $this->default_width;
        }

        $closest = $standardWidths[0];
        $minDifference = abs($targetWidth - $closest);

        foreach ($standardWidths as $width) {
            $difference = abs($targetWidth - $width);
            if ($difference < $minDifference) {
                $minDifference = $difference;
                $closest = $width;
            }
        }

        return $closest;
    }

    /**
     * Calculate fabric cost for given area
     */
    public function calculateCost(float $areaSquareInches, float $efficiency = 0.85): ?float
    {
        if (!$this->cost_per_yard) {
            return null;
        }

        // Convert square inches to yards (36 inches = 1 yard)
        $areaSquareYards = $areaSquareInches / (36 * 36);
        
        // Account for efficiency and waste factor
        $actualAreaNeeded = $areaSquareYards / $efficiency * (1 + $this->waste_factor);
        
        // Calculate fabric yards needed based on width
        $fabricYardsNeeded = $actualAreaNeeded / ($this->default_width / 36);
        
        return $fabricYardsNeeded * $this->cost_per_yard;
    }

    /**
     * Get fabric efficiency constraints
     */
    public function getEfficiencyConstraints(): array
    {
        return [
            'minimum_spacing' => $this->minimum_spacing,
            'seam_allowance' => $this->seam_allowance,
            'grain_direction_required' => $this->grain_stability !== 'unstable',
            'grain_angle_tolerance' => $this->grain_angle_tolerance,
            'directional_constraints' => $this->directional_print || $this->one_way_design,
            'pattern_matching_required' => $this->requires_pattern_matching,
            'pattern_repeat' => [
                'length' => $this->pattern_repeat_length,
                'width' => $this->pattern_repeat_width
            ],
            'stretch_properties' => [
                'percentage' => $this->stretch_percentage,
                'direction' => $this->stretch_direction
            ]
        ];
    }

    /**
     * Update usage statistics
     */
    public function recordUsage(): void
    {
        $this->increment('usage_count');
        $this->update(['last_used_at' => now()]);
    }

    /**
     * Approve fabric specification
     */
    public function approve(int $approvedBy, string $notes = null): void
    {
        $this->update([
            'approved' => true,
            'approved_by' => $approvedBy,
            'approved_at' => now(),
            'status' => 'active'
        ]);
    }

    /**
     * Get formatted fabric content
     */
    public function getFormattedContentAttribute(): string
    {
        return $this->fabric_content ?? 'Content not specified';
    }

    /**
     * Get formatted cost
     */
    public function getFormattedCostAttribute(): string
    {
        if (!$this->cost_per_yard) {
            return 'Price not available';
        }

        return $this->currency . ' ' . number_format($this->cost_per_yard, 2) . '/yard';
    }

    /**
     * Export fabric specification for API
     */
    public function toApiArray(): array
    {
        return [
            'fabric_code' => $this->fabric_code,
            'name' => $this->name,
            'description' => $this->description,
            'fabric_type' => $this->fabric_type,
            'weave_type' => $this->weave_type,
            'properties' => [
                'weight_gsm' => $this->weight_gsm,
                'thickness_mm' => $this->thickness_mm,
                'stretch_percentage' => $this->stretch_percentage,
                'stretch_direction' => $this->stretch_direction,
                'shrinkage_percentage' => $this->shrinkage_percentage,
                'grain_stability' => $this->grain_stability
            ],
            'dimensions' => [
                'standard_widths' => $this->standard_widths,
                'default_width' => $this->default_width,
                'max_width' => $this->max_width,
                'min_width' => $this->min_width
            ],
            'constraints' => $this->getEfficiencyConstraints(),
            'cost_info' => [
                'cost_per_yard' => $this->cost_per_yard,
                'currency' => $this->currency,
                'waste_factor' => $this->waste_factor
            ],
            'supplier' => [
                'name' => $this->supplier_name,
                'code' => $this->supplier_code
            ],
            'status' => $this->status,
            'approved' => $this->approved,
            'usage_count' => $this->usage_count
        ];
    }
}
