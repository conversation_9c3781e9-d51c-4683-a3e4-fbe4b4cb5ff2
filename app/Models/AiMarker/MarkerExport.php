<?php

namespace App\Models\AiMarker;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * AI Marker Export Model
 * 
 * Represents exported marker files and their metadata
 */
class MarkerExport extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'ai_marker_exports';

    protected $fillable = [
        'company_id',
        'user_id',
        'optimization_id',
        'export_id',
        'export_name',
        'description',
        'format',
        'file_path',
        'file_name',
        'file_size',
        'mime_type',
        'export_settings',
        'scale_factor',
        'units',
        'include_annotations',
        'include_measurements',
        'include_grain_lines',
        'dpi',
        'quality',
        'color_settings',
        'marker_data',
        'efficiency_data',
        'pattern_pieces_count',
        'fabric_utilization',
        'download_count',
        'last_downloaded_at',
        'download_log',
        'visibility',
        'share_token',
        'share_expires_at',
        'shared_with_users',
        'storage_type',
        'storage_path',
        'archived',
        'archived_at',
        'archive_location',
        'status',
        'error_message',
        'processing_time_ms',
        'completed_at',
        'expires_at',
        'auto_delete',
        'retention_days'
    ];

    protected $casts = [
        'export_settings' => 'array',
        'color_settings' => 'array',
        'marker_data' => 'array',
        'efficiency_data' => 'array',
        'download_log' => 'array',
        'shared_with_users' => 'array',
        'include_annotations' => 'boolean',
        'include_measurements' => 'boolean',
        'include_grain_lines' => 'boolean',
        'archived' => 'boolean',
        'auto_delete' => 'boolean',
        'scale_factor' => 'decimal:4',
        'fabric_utilization' => 'decimal:2',
        'file_size' => 'integer',
        'download_count' => 'integer',
        'pattern_pieces_count' => 'integer',
        'dpi' => 'integer',
        'processing_time_ms' => 'integer',
        'retention_days' => 'integer',
        'last_downloaded_at' => 'datetime',
        'share_expires_at' => 'datetime',
        'archived_at' => 'datetime',
        'completed_at' => 'datetime',
        'expires_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime'
    ];

    /**
     * Get the company that owns the export
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(\App\Models\Company::class);
    }

    /**
     * Get the user who created the export
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class);
    }

    /**
     * Get the associated optimization
     */
    public function optimization(): BelongsTo
    {
        return $this->belongsTo(MarkerOptimization::class);
    }

    /**
     * Scope for filtering by company
     */
    public function scopeForCompany($query, int $companyId)
    {
        return $query->where('company_id', $companyId);
    }

    /**
     * Get formatted file size
     */
    public function getFormattedFileSizeAttribute(): string
    {
        $bytes = $this->file_size;
        
        if ($bytes >= 1073741824) {
            return number_format($bytes / 1073741824, 2) . ' GB';
        } elseif ($bytes >= 1048576) {
            return number_format($bytes / 1048576, 2) . ' MB';
        } elseif ($bytes >= 1024) {
            return number_format($bytes / 1024, 2) . ' KB';
        } else {
            return $bytes . ' bytes';
        }
    }
}
