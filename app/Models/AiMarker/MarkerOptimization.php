<?php

namespace App\Models\AiMarker;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * AI Marker Optimization Model
 * 
 * Represents optimization results and metadata
 */
class MarkerOptimization extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'marker_optimizations';

    protected $fillable = [
        'company_id',
        'user_id',
        'order_id',
        'optimization_id',
        'algorithm_used',
        'status',
        'fabric_dimensions',
        'efficiency_metrics',
        'execution_time_ms',
        'optimization_data',
        'notes',
        'tags'
    ];

    protected $casts = [
        'fabric_dimensions' => 'array',
        'efficiency_metrics' => 'array',
        'optimization_data' => 'array',
        'tags' => 'array',
        'execution_time_ms' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime'
    ];

    /**
     * Get the company that owns the optimization
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(\App\Models\Company::class);
    }

    /**
     * Get the user who created the optimization
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class);
    }

    /**
     * Get the associated order
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(\App\Models\Order::class);
    }

    /**
     * Scope for filtering by company
     */
    public function scopeForCompany($query, int $companyId)
    {
        return $query->where('company_id', $companyId);
    }

    /**
     * Get formatted efficiency percentage
     */
    public function getEfficiencyPercentageAttribute(): float
    {
        return round($this->efficiency_metrics['yield_percentage'] ?? 0, 1);
    }

    /**
     * Get formatted execution time
     */
    public function getFormattedExecutionTimeAttribute(): string
    {
        $seconds = $this->execution_time_ms / 1000;
        
        if ($seconds < 1) {
            return $this->execution_time_ms . 'ms';
        } elseif ($seconds < 60) {
            return number_format($seconds, 1) . 's';
        } else {
            $minutes = floor($seconds / 60);
            $remainingSeconds = $seconds % 60;
            return $minutes . 'm ' . number_format($remainingSeconds, 0) . 's';
        }
    }
}
