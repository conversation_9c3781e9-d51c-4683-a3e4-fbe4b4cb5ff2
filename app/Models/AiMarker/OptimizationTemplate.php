<?php

namespace App\Models\AiMarker;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * AI Marker Optimization Template Model
 * 
 * Represents reusable optimization configurations
 */
class OptimizationTemplate extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'ai_marker_optimization_templates';

    protected $fillable = [
        'company_id',
        'created_by',
        'template_code',
        'name',
        'description',
        'category',
        'garment_type',
        'production_type',
        'fabric_specification_id',
        'fabric_width',
        'fabric_length',
        'pattern_pieces',
        'size_ratios',
        'algorithm_type',
        'max_iterations',
        'rotation_angles',
        'allow_rotation',
        'minimum_spacing',
        'seam_allowance',
        'algorithm_parameters',
        'target_efficiency',
        'acceptable_efficiency',
        'max_optimization_time',
        'visibility',
        'is_default',
        'status',
        'approved',
        'approved_by',
        'approved_at',
        'version',
        'parent_template_id',
        'tags',
        'metadata'
    ];

    protected $casts = [
        'pattern_pieces' => 'array',
        'size_ratios' => 'array',
        'rotation_angles' => 'array',
        'algorithm_parameters' => 'array',
        'tags' => 'array',
        'metadata' => 'array',
        'allow_rotation' => 'boolean',
        'is_default' => 'boolean',
        'approved' => 'boolean',
        'fabric_width' => 'decimal:2',
        'fabric_length' => 'decimal:2',
        'minimum_spacing' => 'decimal:3',
        'seam_allowance' => 'decimal:3',
        'target_efficiency' => 'decimal:2',
        'acceptable_efficiency' => 'decimal:2',
        'approved_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime'
    ];

    /**
     * Get the company that owns the template
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(\App\Models\Company::class);
    }

    /**
     * Get the user who created the template
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'created_by');
    }

    /**
     * Get the fabric specification
     */
    public function fabricSpecification(): BelongsTo
    {
        return $this->belongsTo(FabricSpecification::class);
    }

    /**
     * Scope for filtering by company
     */
    public function scopeForCompany($query, int $companyId)
    {
        return $query->where('company_id', $companyId);
    }

    /**
     * Scope for active templates
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }
}
