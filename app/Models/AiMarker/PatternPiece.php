<?php

namespace App\Models\AiMarker;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Builder;

/**
 * AI Marker Pattern Piece Model
 * 
 * Represents individual pattern pieces used in marker optimization
 * 
 * @property int $id
 * @property int $company_id
 * @property int $created_by
 * @property string $piece_code
 * @property string $name
 * @property string|null $description
 * @property string $garment_type
 * @property string $piece_type
 * @property array $sizes_available
 * @property array $geometry_data
 * @property string $geometry_type
 * @property float $width
 * @property float $height
 * @property float|null $area
 * @property bool $rotation_allowed
 * @property array|null $rotation_constraints
 * @property string $grain_direction
 * @property bool $fold_line
 * @property float $seam_allowance
 * @property string|null $category
 * @property array|null $tags
 * @property string|null $season
 * @property string|null $style
 * @property int $usage_count
 * @property \Carbon\Carbon|null $last_used_at
 * @property string|null $image_path
 * @property string|null $file_path
 * @property string|null $file_type
 * @property string $status
 * @property string $version
 * @property int|null $parent_id
 */
class PatternPiece extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'ai_marker_pattern_pieces';

    protected $fillable = [
        'company_id',
        'created_by',
        'piece_code',
        'name',
        'description',
        'garment_type',
        'piece_type',
        'sizes_available',
        'geometry_data',
        'geometry_type',
        'width',
        'height',
        'area',
        'rotation_allowed',
        'rotation_constraints',
        'grain_direction',
        'fold_line',
        'seam_allowance',
        'category',
        'tags',
        'season',
        'style',
        'usage_count',
        'last_used_at',
        'image_path',
        'file_path',
        'file_type',
        'status',
        'version',
        'parent_id'
    ];

    protected $casts = [
        'sizes_available' => 'array',
        'geometry_data' => 'array',
        'rotation_constraints' => 'array',
        'tags' => 'array',
        'rotation_allowed' => 'boolean',
        'fold_line' => 'boolean',
        'width' => 'decimal:3',
        'height' => 'decimal:3',
        'area' => 'decimal:3',
        'seam_allowance' => 'decimal:3',
        'usage_count' => 'integer',
        'last_used_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime'
    ];

    protected $hidden = [
        'deleted_at'
    ];

    /**
     * Get the company that owns the pattern piece
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(\App\Models\Company::class);
    }

    /**
     * Get the user who created the pattern piece
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'created_by');
    }

    /**
     * Get the parent pattern piece (for versioning)
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(self::class, 'parent_id');
    }

    /**
     * Get child pattern pieces (versions)
     */
    public function children(): HasMany
    {
        return $this->hasMany(self::class, 'parent_id');
    }

    /**
     * Get optimizations that use this pattern piece
     */
    public function optimizations(): HasMany
    {
        return $this->hasMany(MarkerOptimization::class);
    }

    /**
     * Scope for filtering by company
     */
    public function scopeForCompany(Builder $query, int $companyId): Builder
    {
        return $query->where('company_id', $companyId);
    }

    /**
     * Scope for active pattern pieces
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope for filtering by garment type
     */
    public function scopeByGarmentType(Builder $query, string $garmentType): Builder
    {
        return $query->where('garment_type', $garmentType);
    }

    /**
     * Scope for filtering by piece type
     */
    public function scopeByPieceType(Builder $query, string $pieceType): Builder
    {
        return $query->where('piece_type', $pieceType);
    }

    /**
     * Scope for filtering by category
     */
    public function scopeByCategory(Builder $query, string $category): Builder
    {
        return $query->where('category', $category);
    }

    /**
     * Scope for filtering by size availability
     */
    public function scopeWithSize(Builder $query, string $size): Builder
    {
        return $query->whereJsonContains('sizes_available', $size);
    }

    /**
     * Scope for searching by name or description
     */
    public function scopeSearch(Builder $query, string $search): Builder
    {
        return $query->where(function ($q) use ($search) {
            $q->where('name', 'like', "%{$search}%")
              ->orWhere('description', 'like', "%{$search}%")
              ->orWhere('piece_code', 'like', "%{$search}%");
        });
    }

    /**
     * Get formatted dimensions
     */
    public function getFormattedDimensionsAttribute(): string
    {
        return "{$this->width}\" × {$this->height}\"";
    }

    /**
     * Get calculated area if not stored
     */
    public function getCalculatedAreaAttribute(): float
    {
        return $this->area ?? ($this->width * $this->height);
    }

    /**
     * Check if pattern piece supports a specific size
     */
    public function supportsSize(string $size): bool
    {
        return in_array(strtoupper($size), array_map('strtoupper', $this->sizes_available));
    }

    /**
     * Get available rotation angles
     */
    public function getRotationAngles(): array
    {
        if (!$this->rotation_allowed) {
            return [0];
        }

        return $this->rotation_constraints ?? [0, 90, 180, 270];
    }

    /**
     * Check if piece can be rotated to specific angle
     */
    public function canRotateTo(float $angle): bool
    {
        if (!$this->rotation_allowed) {
            return $angle == 0;
        }

        $allowedAngles = $this->getRotationAngles();
        return in_array($angle, $allowedAngles);
    }

    /**
     * Get geometry as polygon coordinates
     */
    public function getPolygonCoordinates(): array
    {
        if ($this->geometry_type === 'polygon') {
            return $this->geometry_data['coordinates'] ?? [];
        }

        // For SVG or other formats, return bounding rectangle
        return [
            [0, 0],
            [$this->width, 0],
            [$this->width, $this->height],
            [0, $this->height],
            [0, 0]
        ];
    }

    /**
     * Update usage statistics
     */
    public function recordUsage(): void
    {
        $this->increment('usage_count');
        $this->update(['last_used_at' => now()]);
    }

    /**
     * Create a new version of this pattern piece
     */
    public function createVersion(array $changes): self
    {
        $versionNumber = $this->getNextVersionNumber();
        
        $newVersion = $this->replicate();
        $newVersion->parent_id = $this->id;
        $newVersion->version = $versionNumber;
        $newVersion->piece_code = $this->piece_code . '_v' . str_replace('.', '_', $versionNumber);
        
        foreach ($changes as $key => $value) {
            $newVersion->$key = $value;
        }
        
        $newVersion->save();
        
        return $newVersion;
    }

    /**
     * Get next version number
     */
    protected function getNextVersionNumber(): string
    {
        $latestVersion = self::where('parent_id', $this->id)
            ->orWhere('id', $this->id)
            ->orderByRaw('CAST(SUBSTRING_INDEX(version, ".", 1) AS UNSIGNED) DESC')
            ->orderByRaw('CAST(SUBSTRING_INDEX(version, ".", -1) AS UNSIGNED) DESC')
            ->first();

        if (!$latestVersion) {
            return '1.0';
        }

        $parts = explode('.', $latestVersion->version);
        $major = (int)$parts[0];
        $minor = isset($parts[1]) ? (int)$parts[1] : 0;

        return $major . '.' . ($minor + 1);
    }

    /**
     * Export pattern piece data for API
     */
    public function toApiArray(): array
    {
        return [
            'piece_id' => $this->piece_code,
            'name' => $this->name,
            'description' => $this->description,
            'garment_type' => $this->garment_type,
            'piece_type' => $this->piece_type,
            'sizes_available' => $this->sizes_available,
            'geometry' => [
                'type' => $this->geometry_type,
                'data' => $this->geometry_data,
                'width' => $this->width,
                'height' => $this->height,
                'area' => $this->calculated_area
            ],
            'rotation_allowed' => $this->rotation_allowed,
            'rotation_angles' => $this->getRotationAngles(),
            'grain_direction' => $this->grain_direction,
            'fold_line' => $this->fold_line,
            'seam_allowance' => $this->seam_allowance,
            'category' => $this->category,
            'tags' => $this->tags,
            'usage_count' => $this->usage_count,
            'status' => $this->status,
            'version' => $this->version,
            'created_at' => $this->created_at->toISOString(),
            'updated_at' => $this->updated_at->toISOString()
        ];
    }
}
