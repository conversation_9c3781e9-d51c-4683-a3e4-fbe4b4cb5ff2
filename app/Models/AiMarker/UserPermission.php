<?php

namespace App\Models\AiMarker;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * AI Marker User Permission Model
 * 
 * Manages role-based access control for the AI Marker Optimization Tool
 */
class UserPermission extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'ai_marker_user_permissions';

    protected $fillable = [
        'user_id',
        'company_id',
        'granted_by',
        'permission_code',
        'permission_category',
        'permission_level',
        'permission_name',
        'permission_description',
        'scope',
        'scope_restrictions',
        'resource_filters',
        'is_active',
        'valid_from',
        'valid_until',
        'time_restrictions',
        'usage_count',
        'last_used_at',
        'usage_log',
        'requires_approval',
        'approval_status',
        'approved_by',
        'approved_at',
        'approval_notes',
        'grant_reason',
        'conditions',
        'can_delegate'
    ];

    protected $casts = [
        'scope_restrictions' => 'array',
        'resource_filters' => 'array',
        'time_restrictions' => 'array',
        'usage_log' => 'array',
        'conditions' => 'array',
        'is_active' => 'boolean',
        'requires_approval' => 'boolean',
        'can_delegate' => 'boolean',
        'usage_count' => 'integer',
        'valid_from' => 'datetime',
        'valid_until' => 'datetime',
        'last_used_at' => 'datetime',
        'approved_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime'
    ];

    /**
     * Get the user that owns the permission
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class);
    }

    /**
     * Get the company that owns the permission
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(\App\Models\Company::class);
    }

    /**
     * Get the user who granted the permission
     */
    public function grantedBy(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'granted_by');
    }

    /**
     * Get the user who approved the permission
     */
    public function approvedBy(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'approved_by');
    }

    /**
     * Scope for active permissions
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true)
                    ->where(function ($q) {
                        $q->whereNull('valid_until')
                          ->orWhere('valid_until', '>', now());
                    });
    }

    /**
     * Scope for filtering by permission category
     */
    public function scopeByCategory($query, string $category)
    {
        return $query->where('permission_category', $category);
    }

    /**
     * Check if permission is currently valid
     */
    public function isValid(): bool
    {
        if (!$this->is_active) {
            return false;
        }

        if ($this->valid_from && $this->valid_from->isFuture()) {
            return false;
        }

        if ($this->valid_until && $this->valid_until->isPast()) {
            return false;
        }

        return true;
    }

    /**
     * Record permission usage
     */
    public function recordUsage(): void
    {
        $this->increment('usage_count');
        $this->update(['last_used_at' => now()]);
    }
}
