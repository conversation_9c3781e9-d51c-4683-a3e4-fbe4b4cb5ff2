<?php

namespace App\Models;

use App\Traits\TenantScoped;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Attendance extends Model
{
    use HasFactory;

    protected $fillable = [
        'employee_id',
        'date',
        'status',
        'monthly_salary',
        'daily_cost',
        'notes',
        'in_time',
        'out_time',
        'working_hours',
        'is_imported',
        'machine_logs',
    ];

    protected $casts = [
        'date' => 'date',
        'monthly_salary' => 'decimal:2',
        'daily_cost' => 'decimal:2',
        'working_hours' => 'decimal:2',
        'is_imported' => 'boolean',
        'machine_logs' => 'json',
    ];

    /**
     * Get the employee that owns the attendance.
     */
    public function employee(): BelongsTo
    {
        return $this->belongsTo(Employee::class);
    }

    /**
     * Boot method to automatically calculate daily cost
     */
    protected static function boot()
    {
        parent::boot();

        static::saving(function ($attendance) {
            if ($attendance->employee && $attendance->monthly_salary) {
                $attendance->daily_cost = self::calculateDailyCost($attendance->monthly_salary, $attendance->status);
            }
        });
    }

    /**
     * Calculate daily cost based on monthly salary and status
     */
    public static function calculateDailyCost($monthlySalary, $status)
    {
        $dailyRate = $monthlySalary / 30; // Assuming 30 days per month

        return match($status) {
            'present' => $dailyRate,
            'half_day' => $dailyRate * 0.5,
            'absent' => 0,
            default => 0,
        };
    }

    /**
     * Scope for specific date
     */
    public function scopeForDate($query, $date)
    {
        return $query->whereDate('date', $date);
    }

    /**
     * Scope for specific month
     */
    public function scopeForMonth($query, $year, $month)
    {
        return $query->whereYear('date', $year)->whereMonth('date', $month);
    }

    /**
     * Scope for present attendances
     */
    public function scopePresent($query)
    {
        return $query->where('status', 'present');
    }

    /**
     * Scope for absent attendances
     */
    public function scopeAbsent($query)
    {
        return $query->where('status', 'absent');
    }

    /**
     * Scope for half day attendances
     */
    public function scopeHalfDay($query)
    {
        return $query->where('status', 'half_day');
    }

    /**
     * Get attendance status badge color
     */
    public function getStatusBadgeAttribute()
    {
        return match($this->status) {
            'present' => 'success',
            'half_day' => 'warning',
            'absent' => 'danger',
            default => 'secondary',
        };
    }

    /**
     * Check if employee was late
     */
    public function getIsLateAttribute()
    {
        return $this->in_time && $this->in_time > '09:00:00';
    }

    /**
     * Get status symbol for display
     */
    public function getStatusSymbolAttribute()
    {
        if ($this->status === 'present') {
            return $this->is_late ? 'L' : 'P';
        } elseif ($this->status === 'absent') {
            return 'A';
        } elseif ($this->status === 'half_day') {
            return 'H';
        }
        return '-';
    }

    /**
     * Scope for employees with attendance in a specific month
     */
    public function scopeForEmployeeMonth($query, $employeeId, $year, $month)
    {
        return $query->where('employee_id', $employeeId)
                    ->whereYear('date', $year)
                    ->whereMonth('date', $month);
    }

    /**
     * Get monthly attendance summary for an employee
     */
    public static function getMonthlyAttendanceSummary($employeeId, $year, $month)
    {
        $attendances = self::forEmployeeMonth($employeeId, $year, $month)->get();

        return [
            'total_days' => $attendances->count(),
            'present_days' => $attendances->where('status', 'present')->count(),
            'absent_days' => $attendances->where('status', 'absent')->count(),
            'half_days' => $attendances->where('status', 'half_day')->count(),
            'late_days' => $attendances->filter(function($attendance) {
                return $attendance->is_late;
            })->count(),
            'total_cost' => $attendances->sum('daily_cost'),
            'total_hours' => $attendances->sum('working_hours'),
        ];
    }

    /**
     * Get formatted daily cost
     */
    public function getFormattedDailyCostAttribute()
    {
        return '৳' . number_format($this->daily_cost, 2);
    }
}
