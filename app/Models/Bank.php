<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use App\Traits\TenantScoped;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Bank extends Model
{
    use HasFactory, SoftDeletes, TenantScoped;

    protected $fillable = ['user_id','holder_name','bank_name','account_number','branch_name','routing_number','balance', 'status'];
}
