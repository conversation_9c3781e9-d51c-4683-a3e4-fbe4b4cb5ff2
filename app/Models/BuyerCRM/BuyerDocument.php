<?php

namespace App\Models\BuyerCRM;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Storage;
use App\Models\User;

/**
 * BuyerDocument Model
 * 
 * Represents documents associated with buyers
 * 
 * @property int $id
 * @property int $buyer_id
 * @property string $document_type
 * @property string $file_name
 * @property string $file_path
 * @property int $file_size
 * @property string $mime_type
 * @property int $uploaded_by
 * @property string $version
 * @property string|null $description
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class BuyerDocument extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'buyer_documents';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'buyer_id',
        'document_type',
        'file_name',
        'file_path',
        'file_size',
        'mime_type',
        'uploaded_by',
        'version',
        'description',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'file_size' => 'integer',
    ];

    /**
     * Available document types.
     *
     * @var array<string>
     */
    public static array $documentTypes = [
        'Contract',
        'Sample',
        'Quotation',
        'Certificate',
        'Other'
    ];

    /**
     * Allowed file types and their MIME types.
     *
     * @var array<string, array>
     */
    public static array $allowedTypes = [
        'pdf' => ['application/pdf'],
        'doc' => ['application/msword'],
        'docx' => ['application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
        'xls' => ['application/vnd.ms-excel'],
        'xlsx' => ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'],
        'jpg' => ['image/jpeg'],
        'jpeg' => ['image/jpeg'],
        'png' => ['image/png'],
        'gif' => ['image/gif'],
    ];

    /**
     * Get the buyer that owns this document.
     */
    public function buyer(): BelongsTo
    {
        return $this->belongsTo(BuyerProfile::class, 'buyer_id');
    }

    /**
     * Get the user who uploaded this document.
     */
    public function uploadedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'uploaded_by');
    }

    /**
     * Scope a query to only include documents for a specific buyer.
     */
    public function scopeForBuyer($query, int $buyerId)
    {
        return $query->where('buyer_id', $buyerId);
    }

    /**
     * Scope a query to filter by document type.
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('document_type', $type);
    }

    /**
     * Scope a query to filter by uploaded user.
     */
    public function scopeUploadedBy($query, int $userId)
    {
        return $query->where('uploaded_by', $userId);
    }

    /**
     * Scope a query to only include recent documents.
     */
    public function scopeRecent($query, int $days = 30)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }

    /**
     * Get the document type icon for UI.
     */
    public function getDocumentTypeIcon(): string
    {
        return match ($this->document_type) {
            'Contract' => 'fas fa-file-contract',
            'Sample' => 'fas fa-box',
            'Quotation' => 'fas fa-file-invoice-dollar',
            'Certificate' => 'fas fa-certificate',
            'Other' => 'fas fa-file',
            default => 'fas fa-file',
        };
    }

    /**
     * Get the document type color for UI.
     */
    public function getDocumentTypeColor(): string
    {
        return match ($this->document_type) {
            'Contract' => 'bg-green-100 text-green-800',
            'Sample' => 'bg-blue-100 text-blue-800',
            'Quotation' => 'bg-yellow-100 text-yellow-800',
            'Certificate' => 'bg-purple-100 text-purple-800',
            'Other' => 'bg-gray-100 text-gray-800',
            default => 'bg-gray-100 text-gray-800',
        };
    }

    /**
     * Get the file type icon based on MIME type.
     */
    public function getFileTypeIcon(): string
    {
        return match (true) {
            str_contains($this->mime_type, 'pdf') => 'fas fa-file-pdf text-red-600',
            str_contains($this->mime_type, 'word') || str_contains($this->mime_type, 'document') => 'fas fa-file-word text-blue-600',
            str_contains($this->mime_type, 'excel') || str_contains($this->mime_type, 'spreadsheet') => 'fas fa-file-excel text-green-600',
            str_contains($this->mime_type, 'image') => 'fas fa-file-image text-purple-600',
            default => 'fas fa-file text-gray-600',
        };
    }

    /**
     * Get formatted file size.
     */
    public function getFormattedFileSize(): string
    {
        $bytes = $this->file_size;
        
        if ($bytes >= 1073741824) {
            return number_format($bytes / 1073741824, 2) . ' GB';
        } elseif ($bytes >= 1048576) {
            return number_format($bytes / 1048576, 2) . ' MB';
        } elseif ($bytes >= 1024) {
            return number_format($bytes / 1024, 2) . ' KB';
        } else {
            return $bytes . ' bytes';
        }
    }

    /**
     * Get the file extension.
     */
    public function getFileExtension(): string
    {
        return pathinfo($this->file_name, PATHINFO_EXTENSION);
    }

    /**
     * Check if the document is an image.
     */
    public function isImage(): bool
    {
        return str_contains($this->mime_type, 'image');
    }

    /**
     * Check if the document is a PDF.
     */
    public function isPdf(): bool
    {
        return $this->mime_type === 'application/pdf';
    }

    /**
     * Check if the document exists in storage.
     */
    public function exists(): bool
    {
        return Storage::disk('public')->exists($this->file_path);
    }

    /**
     * Get the full storage path.
     */
    public function getFullPath(): string
    {
        return Storage::disk('public')->path($this->file_path);
    }

    /**
     * Get the download URL.
     */
    public function getDownloadUrl(): string
    {
        return route('buyer-crm.documents.download', $this);
    }

    /**
     * Get the public URL for images.
     */
    public function getPublicUrl(): ?string
    {
        if ($this->isImage() && $this->exists()) {
            return Storage::disk('public')->url($this->file_path);
        }
        
        return null;
    }

    /**
     * Delete the document file from storage.
     */
    public function deleteFile(): bool
    {
        if ($this->exists()) {
            return Storage::disk('public')->delete($this->file_path);
        }
        
        return true;
    }

    /**
     * Get the next version number for this document type and buyer.
     */
    public static function getNextVersion(int $buyerId, string $documentType): string
    {
        $latestDocument = static::forBuyer($buyerId)
            ->byType($documentType)
            ->orderBy('version', 'desc')
            ->first();

        if (!$latestDocument) {
            return '1.0';
        }

        $version = floatval($latestDocument->version);
        return number_format($version + 0.1, 1);
    }

    /**
     * Validate file type.
     */
    public static function isValidFileType(string $mimeType): bool
    {
        $allowedMimeTypes = array_merge(...array_values(static::$allowedTypes));
        return in_array($mimeType, $allowedMimeTypes);
    }

    /**
     * Get allowed file extensions.
     */
    public static function getAllowedExtensions(): array
    {
        return array_keys(static::$allowedTypes);
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        // Delete file when model is deleted
        static::deleting(function ($document) {
            $document->deleteFile();
        });
    }

    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory()
    {
        return \Database\Factories\BuyerCRM\BuyerDocumentFactory::new();
    }
}
