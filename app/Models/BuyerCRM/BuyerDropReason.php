<?php

namespace App\Models\BuyerCRM;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Models\User;
use Carbon\Carbon;

/**
 * BuyerDropReason Model
 * 
 * Represents reasons why buyers were dropped and tracks re-engagement efforts
 * 
 * @property int $id
 * @property int $buyer_id
 * @property string $reason
 * @property string|null $detailed_reason
 * @property string $impact_level
 * @property int $dropped_by
 * @property \Carbon\Carbon $dropped_at
 * @property \Carbon\Carbon|null $next_review_date
 * @property bool $re_engagement_attempted
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class BuyerDropReason extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'buyer_drop_reasons';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'buyer_id',
        'reason',
        'detailed_reason',
        'impact_level',
        'dropped_by',
        'dropped_at',
        'next_review_date',
        're_engagement_attempted',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'dropped_at' => 'datetime',
        'next_review_date' => 'date',
        're_engagement_attempted' => 'boolean',
    ];

    /**
     * Available drop reasons.
     *
     * @var array<string>
     */
    public static array $reasons = [
        'Pricing Issue',
        'No Response',
        'Product Mismatch',
        'Sample Rejected',
        'Quality Concerns',
        'Timeline Issues',
        'Other'
    ];

    /**
     * Available impact levels.
     *
     * @var array<string>
     */
    public static array $impactLevels = [
        'Low',
        'Medium',
        'High'
    ];

    /**
     * Get the buyer that was dropped.
     */
    public function buyer(): BelongsTo
    {
        return $this->belongsTo(BuyerProfile::class, 'buyer_id');
    }

    /**
     * Get the user who marked the buyer as dropped.
     */
    public function droppedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'dropped_by');
    }

    /**
     * Scope a query to filter by reason.
     */
    public function scopeByReason($query, string $reason)
    {
        return $query->where('reason', $reason);
    }

    /**
     * Scope a query to filter by impact level.
     */
    public function scopeByImpactLevel($query, string $impactLevel)
    {
        return $query->where('impact_level', $impactLevel);
    }





    /**
     * Scope a query to only include high impact drops.
     */
    public function scopeHighImpact($query)
    {
        return $query->where('impact_level', 'High');
    }

    /**
     * Scope a query to only include medium impact drops.
     */
    public function scopeMediumImpact($query)
    {
        return $query->where('impact_level', 'Medium');
    }

    /**
     * Scope a query to only include low impact drops.
     */
    public function scopeLowImpact($query)
    {
        return $query->where('impact_level', 'Low');
    }

    /**
     * Scope a query to filter by dropped user.
     */
    public function scopeDroppedBy($query, int $userId)
    {
        return $query->where('dropped_by', $userId);
    }

    /**
     * Scope a query to only include recent drops.
     */
    public function scopeRecent($query, int $days = 30)
    {
        return $query->where('dropped_at', '>=', now()->subDays($days));
    }

    /**
     * Scope a query to only include drops due for review.
     */
    public function scopeDueForReview($query)
    {
        return $query->whereNotNull('next_review_date')
                    ->where('next_review_date', '<=', today());
    }

    /**
     * Scope a query to only include drops where re-engagement was attempted.
     */
    public function scopeReEngagementAttempted($query)
    {
        return $query->where('re_engagement_attempted', true);
    }

    /**
     * Scope a query to only include drops where re-engagement was not attempted.
     */
    public function scopeReEngagementNotAttempted($query)
    {
        return $query->where('re_engagement_attempted', false);
    }

    /**
     * Scope a query to filter by date range.
     */
    public function scopeDateRange($query, Carbon $startDate, Carbon $endDate)
    {
        return $query->whereBetween('dropped_at', [$startDate, $endDate]);
    }

    /**
     * Get the reason color for UI display.
     */
    public function getReasonColor(): string
    {
        return match ($this->reason) {
            'Pricing Issue' => 'bg-yellow-100 text-yellow-800',
            'No Response' => 'bg-gray-100 text-gray-800',
            'Product Mismatch' => 'bg-blue-100 text-blue-800',
            'Sample Rejected' => 'bg-orange-100 text-orange-800',
            'Quality Concerns' => 'bg-red-100 text-red-800',
            'Timeline Issues' => 'bg-purple-100 text-purple-800',
            'Other' => 'bg-gray-100 text-gray-800',
            default => 'bg-gray-100 text-gray-800',
        };
    }

    /**
     * Get the reason icon for UI display.
     */
    public function getReasonIcon(): string
    {
        return match ($this->reason) {
            'Pricing Issue' => 'fas fa-dollar-sign',
            'No Response' => 'fas fa-phone-slash',
            'Product Mismatch' => 'fas fa-times-circle',
            'Sample Rejected' => 'fas fa-thumbs-down',
            'Quality Concerns' => 'fas fa-exclamation-triangle',
            'Timeline Issues' => 'fas fa-clock',
            'Other' => 'fas fa-question-circle',
            default => 'fas fa-question-circle',
        };
    }

    /**
     * Get the impact level color for UI display.
     */
    public function getImpactLevelColor(): string
    {
        return match ($this->impact_level) {
            'High' => 'bg-red-100 text-red-800',
            'Medium' => 'bg-yellow-100 text-yellow-800',
            'Low' => 'bg-green-100 text-green-800',
            default => 'bg-gray-100 text-gray-800',
        };
    }

    /**
     * Get the impact level icon for UI display.
     */
    public function getImpactLevelIcon(): string
    {
        return match ($this->impact_level) {
            'High' => 'fas fa-arrow-up',
            'Medium' => 'fas fa-minus',
            'Low' => 'fas fa-arrow-down',
            default => 'fas fa-question',
        };
    }

    /**
     * Check if this is a high impact drop.
     */
    public function isHighImpact(): bool
    {
        return $this->impact_level === 'High';
    }

    /**
     * Check if this is a medium impact drop.
     */
    public function isMediumImpact(): bool
    {
        return $this->impact_level === 'Medium';
    }

    /**
     * Check if this is a low impact drop.
     */
    public function isLowImpact(): bool
    {
        return $this->impact_level === 'Low';
    }

    /**
     * Check if the drop is due for review.
     */
    public function isDueForReview(): bool
    {
        return $this->next_review_date && $this->next_review_date->isPast();
    }

    /**
     * Check if re-engagement has been attempted.
     */
    public function hasReEngagementAttempted(): bool
    {
        return $this->re_engagement_attempted;
    }

    /**
     * Get the days since the buyer was dropped.
     */
    public function getDaysSinceDropped(): int
    {
        return $this->dropped_at->diffInDays(now());
    }

    /**
     * Get the days until next review.
     */
    public function getDaysUntilReview(): ?int
    {
        if (!$this->next_review_date) {
            return null;
        }

        return today()->diffInDays($this->next_review_date, false);
    }

    /**
     * Check if the reason is addressable (can potentially be resolved).
     */
    public function isAddressable(): bool
    {
        return in_array($this->reason, [
            'Pricing Issue',
            'Product Mismatch',
            'Timeline Issues',
            'Quality Concerns'
        ]);
    }

    /**
     * Get re-engagement priority based on impact and reason.
     */
    public function getReEngagementPriority(): string
    {
        if ($this->impact_level === 'High' && $this->isAddressable()) {
            return 'High';
        }

        if ($this->impact_level === 'Medium' && $this->isAddressable()) {
            return 'Medium';
        }

        if ($this->reason === 'No Response' && $this->impact_level !== 'Low') {
            return 'Medium';
        }

        return 'Low';
    }

    /**
     * Schedule next review.
     */
    public function scheduleReview(Carbon $reviewDate): bool
    {
        return $this->update([
            'next_review_date' => $reviewDate,
        ]);
    }

    /**
     * Mark re-engagement as attempted.
     */
    public function markReEngagementAttempted(): bool
    {
        return $this->update([
            're_engagement_attempted' => true,
        ]);
    }

    /**
     * Get suggested re-engagement strategy based on drop reason.
     */
    public function getSuggestedReEngagementStrategy(): string
    {
        return match ($this->reason) {
            'Pricing Issue' => 'Review pricing strategy, offer volume discounts, or negotiate terms',
            'No Response' => 'Try different communication channels, send personalized message, or call directly',
            'Product Mismatch' => 'Present alternative products, understand specific requirements better',
            'Sample Rejected' => 'Improve sample quality, offer different samples, or address specific concerns',
            'Quality Concerns' => 'Address quality issues, provide quality certifications, or arrange factory visit',
            'Timeline Issues' => 'Improve delivery schedules, offer expedited options, or better planning',
            'Other' => 'Analyze specific concerns and develop targeted approach',
            default => 'Contact buyer to understand current needs and situation',
        };
    }

    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory()
    {
        return \Database\Factories\BuyerCRM\BuyerDropReasonFactory::new();
    }
}
