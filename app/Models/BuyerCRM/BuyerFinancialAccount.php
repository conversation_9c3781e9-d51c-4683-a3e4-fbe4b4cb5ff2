<?php

namespace App\Models\BuyerCRM;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * BuyerFinancialAccount Model
 *
 * Manages financial accounts for buyers including balances and credit limits
 *
 * @property int $id
 * @property int $buyer_id
 * @property float $current_balance
 * @property float|null $credit_limit
 * @property float $total_orders_value
 * @property float $total_payments_received
 * @property float $total_adjustments
 * @property string $account_status
 * @property string $payment_terms
 * @property string $currency
 * @property \Carbon\Carbon|null $last_transaction_date
 * @property string|null $notes
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class BuyerFinancialAccount extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'buyer_financial_accounts';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'buyer_id',
        'current_balance',
        'credit_limit',
        'total_orders_value',
        'total_payments_received',
        'total_adjustments',
        'account_status',
        'payment_terms',
        'currency',
        'last_transaction_date',
        'notes',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'current_balance' => 'decimal:2',
        'credit_limit' => 'decimal:2',
        'total_orders_value' => 'decimal:2',
        'total_payments_received' => 'decimal:2',
        'total_adjustments' => 'decimal:2',
        'last_transaction_date' => 'date',
    ];

    /**
     * Available account statuses.
     *
     * @var array<string>
     */
    public static array $accountStatuses = [
        'Active',
        'Suspended',
        'Closed'
    ];

    /**
     * Available payment terms.
     *
     * @var array<string>
     */
    public static array $paymentTerms = [
        'Net 30',
        'Net 60',
        'Net 90',
        'COD',
        'Advance',
        'Custom'
    ];

    /**
     * Get the buyer profile.
     */
    public function buyer(): BelongsTo
    {
        return $this->belongsTo(BuyerProfile::class, 'buyer_id');
    }

    /**
     * Get the financial transactions.
     */
    public function transactions(): HasMany
    {
        return $this->hasMany(BuyerFinancialTransaction::class, 'buyer_id', 'buyer_id');
    }

    /**
     * Get the invoices.
     */
    public function invoices(): HasMany
    {
        return $this->hasMany(BuyerInvoice::class, 'buyer_id', 'buyer_id');
    }

    /**
     * Add an order transaction and update balance.
     */
    public function addOrderTransaction(GarmentOrder $order): BuyerFinancialTransaction
    {
        $transaction = BuyerFinancialTransaction::create([
            'buyer_id' => $this->buyer_id,
            'garment_order_id' => $order->id,
            'transaction_type' => 'order',
            'reference_number' => 'ORD-' . $order->order_no,
            'amount' => $order->total_value,
            'currency' => $this->currency,
            'status' => 'Completed',
            'transaction_date' => $order->order_date,
            'due_date' => $this->calculateDueDate($order->order_date),
            'description' => "Order #{$order->order_no} - {$order->style_no}",
            'created_by' => $order->created_by,
        ]);

        $this->updateBalance();
        return $transaction;
    }

    /**
     * Add a payment transaction and update balance.
     */
    public function addPaymentTransaction(float $amount, array $data = []): BuyerFinancialTransaction
    {
        $transaction = BuyerFinancialTransaction::create(array_merge([
            'buyer_id' => $this->buyer_id,
            'transaction_type' => 'payment',
            'reference_number' => 'PAY-' . date('Ymd') . '-' . rand(1000, 9999),
            'amount' => -abs($amount), // Payments are negative
            'currency' => $this->currency,
            'status' => 'Completed',
            'transaction_date' => now()->toDateString(),
            'description' => 'Payment received',
            'created_by' => auth()->id(),
        ], $data));

        $this->updateBalance();
        return $transaction;
    }

    /**
     * Update account balance based on transactions.
     */
    public function updateBalance(): void
    {
        $totals = $this->transactions()
            ->where('status', 'Completed')
            ->selectRaw('
                SUM(CASE WHEN transaction_type = "order" THEN amount ELSE 0 END) as orders_total,
                SUM(CASE WHEN transaction_type = "payment" THEN ABS(amount) ELSE 0 END) as payments_total,
                SUM(CASE WHEN transaction_type = "adjustment" THEN amount ELSE 0 END) as adjustments_total,
                SUM(amount) as balance
            ')
            ->first();

        $this->update([
            'total_orders_value' => $totals->orders_total ?? 0,
            'total_payments_received' => $totals->payments_total ?? 0,
            'total_adjustments' => $totals->adjustments_total ?? 0,
            'current_balance' => $totals->balance ?? 0,
            'last_transaction_date' => now()->toDateString(),
        ]);
    }

    /**
     * Calculate due date based on payment terms.
     */
    protected function calculateDueDate(string $orderDate): string
    {
        $date = \Carbon\Carbon::parse($orderDate);

        switch ($this->payment_terms) {
            case 'Net 30':
                return $date->addDays(30)->toDateString();
            case 'Net 60':
                return $date->addDays(60)->toDateString();
            case 'Net 90':
                return $date->addDays(90)->toDateString();
            case 'COD':
                return $date->toDateString();
            case 'Advance':
                return $date->subDays(7)->toDateString();
            default:
                return $date->addDays(30)->toDateString();
        }
    }

    /**
     * Check if account is over credit limit.
     */
    public function isOverCreditLimit(): bool
    {
        if (!$this->credit_limit) {
            return false;
        }

        return $this->current_balance > $this->credit_limit;
    }

    /**
     * Get available credit.
     */
    public function getAvailableCreditAttribute(): float
    {
        if (!$this->credit_limit) {
            return 0;
        }

        return max(0, $this->credit_limit - $this->current_balance);
    }

    /**
     * Get overdue invoices.
     */
    public function getOverdueInvoices()
    {
        return $this->invoices()
            ->where('status', '!=', 'Paid')
            ->where('due_date', '<', now()->toDateString())
            ->get();
    }

    /**
     * Create or get financial account for buyer.
     */
    public static function getOrCreateForBuyer(int $buyerId): self
    {
        return self::firstOrCreate(
            ['buyer_id' => $buyerId],
            [
                'current_balance' => 0,
                'total_orders_value' => 0,
                'total_payments_received' => 0,
                'total_adjustments' => 0,
                'account_status' => 'Active',
                'payment_terms' => 'Net 30',
                'currency' => 'USD',
            ]
        );
    }
}
