<?php

namespace App\Models\BuyerCRM;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Models\User;

/**
 * BuyerFinancialTransaction Model
 *
 * Records all financial transactions for buyers
 *
 * @property int $id
 * @property int $buyer_id
 * @property int|null $garment_order_id
 * @property string $transaction_type
 * @property string $reference_number
 * @property float $amount
 * @property string $currency
 * @property string $status
 * @property \Carbon\Carbon $transaction_date
 * @property \Carbon\Carbon|null $due_date
 * @property string|null $payment_method
 * @property string|null $payment_reference
 * @property string $description
 * @property string|null $notes
 * @property array|null $metadata
 * @property int $created_by
 * @property int|null $approved_by
 * @property \Carbon\Carbon|null $approved_at
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class BuyerFinancialTransaction extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'buyer_financial_transactions';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'buyer_id',
        'garment_order_id',
        'transaction_type',
        'reference_number',
        'amount',
        'currency',
        'status',
        'transaction_date',
        'due_date',
        'payment_method',
        'payment_reference',
        'description',
        'notes',
        'metadata',
        'created_by',
        'approved_by',
        'approved_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'amount' => 'decimal:2',
        'transaction_date' => 'date',
        'due_date' => 'date',
        'approved_at' => 'datetime',
        'metadata' => 'array',
    ];

    /**
     * Available transaction types.
     *
     * @var array<string>
     */
    public static array $transactionTypes = [
        'order',
        'payment',
        'adjustment',
        'refund',
        'discount',
        'late_fee',
        'credit_note'
    ];

    /**
     * Available transaction statuses.
     *
     * @var array<string>
     */
    public static array $statuses = [
        'Pending',
        'Completed',
        'Cancelled',
        'Failed'
    ];

    /**
     * Get the buyer profile.
     */
    public function buyer(): BelongsTo
    {
        return $this->belongsTo(BuyerProfile::class, 'buyer_id');
    }

    /**
     * Get the related garment order.
     */
    public function garmentOrder(): BelongsTo
    {
        return $this->belongsTo(GarmentOrder::class, 'garment_order_id');
    }

    /**
     * Get the user who created the transaction.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who approved the transaction.
     */
    public function approver(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Scope for order transactions.
     */
    public function scopeOrders($query)
    {
        return $query->where('transaction_type', 'order');
    }

    /**
     * Scope for payment transactions.
     */
    public function scopePayments($query)
    {
        return $query->where('transaction_type', 'payment');
    }

    /**
     * Scope for completed transactions.
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'Completed');
    }

    /**
     * Scope for pending transactions.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'Pending');
    }

    /**
     * Check if transaction is a debit (increases balance).
     */
    public function isDebit(): bool
    {
        return $this->amount > 0;
    }

    /**
     * Check if transaction is a credit (decreases balance).
     */
    public function isCredit(): bool
    {
        return $this->amount < 0;
    }

    /**
     * Get formatted amount with currency.
     */
    public function getFormattedAmountAttribute(): string
    {
        $symbol = $this->currency === 'USD' ? '$' : $this->currency . ' ';
        return $symbol . number_format(abs($this->amount), 2);
    }

    /**
     * Get transaction type label.
     */
    public function getTypeLabel(): string
    {
        return match($this->transaction_type) {
            'order' => 'Order Charge',
            'payment' => 'Payment Received',
            'adjustment' => 'Balance Adjustment',
            'refund' => 'Refund Issued',
            'discount' => 'Discount Applied',
            'late_fee' => 'Late Fee',
            'credit_note' => 'Credit Note',
            default => ucfirst($this->transaction_type)
        };
    }

    /**
     * Approve the transaction.
     */
    public function approve(int $approvedBy): bool
    {
        if ($this->status !== 'Pending') {
            return false;
        }

        $this->update([
            'status' => 'Completed',
            'approved_by' => $approvedBy,
            'approved_at' => now(),
        ]);

        // Update buyer's financial account balance
        $account = BuyerFinancialAccount::getOrCreateForBuyer($this->buyer_id);
        $account->updateBalance();

        return true;
    }

    /**
     * Cancel the transaction.
     */
    public function cancel(): bool
    {
        if ($this->status === 'Completed') {
            return false;
        }

        $this->update(['status' => 'Cancelled']);
        return true;
    }

    /**
     * Generate unique reference number.
     */
    public static function generateReferenceNumber(string $type): string
    {
        $prefix = match($type) {
            'order' => 'ORD',
            'payment' => 'PAY',
            'adjustment' => 'ADJ',
            'refund' => 'REF',
            default => 'TXN'
        };

        do {
            $reference = $prefix . '-' . date('Ymd') . '-' . rand(1000, 9999);
        } while (self::where('reference_number', $reference)->exists());

        return $reference;
    }
}
