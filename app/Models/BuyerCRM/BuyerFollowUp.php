<?php

namespace App\Models\BuyerCRM;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Models\User;
use Carbon\Carbon;

/**
 * BuyerFollowUp Model
 * 
 * Represents follow-up tasks and reminders for buyers
 * 
 * @property int $id
 * @property int $buyer_id
 * @property string $task_type
 * @property \Carbon\Carbon $task_date
 * @property string $status
 * @property int $assigned_to
 * @property string|null $note
 * @property \Carbon\Carbon|null $completed_at
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class BuyerFollowUp extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'buyer_follow_ups';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'buyer_id',
        'task_type',
        'task_date',
        'status',
        'assigned_to',
        'note',
        'completed_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'task_date' => 'datetime',
        'completed_at' => 'datetime',
    ];

    /**
     * Available task types.
     *
     * @var array<string>
     */
    public static array $taskTypes = [
        'Call',
        'Meeting',
        'Reminder',
        'Sample Send',
        'Email'
    ];

    /**
     * Available task statuses.
     *
     * @var array<string>
     */
    public static array $statuses = [
        'Pending',
        'Completed',
        'Delayed',
        'Cancelled'
    ];

    /**
     * Get the buyer that owns this follow-up.
     */
    public function buyer(): BelongsTo
    {
        return $this->belongsTo(BuyerProfile::class, 'buyer_id');
    }

    /**
     * Get the user assigned to this follow-up.
     */
    public function assignedUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    /**
     * Scope a query to only include pending tasks.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'Pending');
    }

    /**
     * Scope a query to only include completed tasks.
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'Completed');
    }



    /**
     * Scope a query to only include overdue tasks.
     */
    public function scopeOverdue($query)
    {
        return $query->where('status', 'Pending')
                    ->where('task_date', '<', now());
    }

    /**
     * Scope a query to only include today's tasks.
     */
    public function scopeToday($query)
    {
        return $query->whereDate('task_date', today());
    }

    /**
     * Scope a query to only include upcoming tasks.
     */
    public function scopeUpcoming($query, int $days = 7)
    {
        return $query->where('status', 'Pending')
                    ->whereBetween('task_date', [now(), now()->addDays($days)]);
    }

    /**
     * Scope a query to filter by task type.
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('task_type', $type);
    }

    /**
     * Scope a query to filter by assigned user.
     */
    public function scopeAssignedTo($query, int $userId)
    {
        return $query->where('assigned_to', $userId);
    }

    /**
     * Scope a query to filter by buyer.
     */
    public function scopeForBuyer($query, int $buyerId)
    {
        return $query->where('buyer_id', $buyerId);
    }

    /**
     * Scope a query to filter by date range.
     */
    public function scopeDateRange($query, Carbon $startDate, Carbon $endDate)
    {
        return $query->whereBetween('task_date', [$startDate, $endDate]);
    }

    /**
     * Check if the task is overdue.
     */
    public function isOverdue(): bool
    {
        return $this->status === 'Pending' && $this->task_date->isPast();
    }

    /**
     * Check if the task is due today.
     */
    public function isDueToday(): bool
    {
        return $this->task_date->isToday();
    }

    /**
     * Check if the task is due this week.
     */
    public function isDueThisWeek(): bool
    {
        return $this->task_date->isBetween(now()->startOfWeek(), now()->endOfWeek());
    }

    /**
     * Get the task priority based on due date and type.
     */
    public function getPriority(): string
    {
        if ($this->isOverdue()) {
            return 'high';
        }

        if ($this->isDueToday()) {
            return 'high';
        }

        if ($this->task_date->isBetween(now(), now()->addDays(3))) {
            return 'medium';
        }

        return 'low';
    }

    /**
     * Get the task priority color for UI.
     */
    public function getPriorityColor(): string
    {
        return match ($this->getPriority()) {
            'high' => 'text-red-600',
            'medium' => 'text-yellow-600',
            'low' => 'text-green-600',
            default => 'text-gray-600',
        };
    }

    /**
     * Get the task type icon for UI.
     */
    public function getTaskTypeIcon(): string
    {
        return match ($this->task_type) {
            'Call' => 'fas fa-phone',
            'Meeting' => 'fas fa-calendar-alt',
            'Reminder' => 'fas fa-bell',
            'Sample Send' => 'fas fa-box',
            'Email' => 'fas fa-envelope',
            default => 'fas fa-tasks',
        };
    }

    /**
     * Get the status color for UI.
     */
    public function getStatusColor(): string
    {
        return match ($this->status) {
            'Pending' => $this->isOverdue() ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800',
            'Completed' => 'bg-green-100 text-green-800',
            'Delayed' => 'bg-orange-100 text-orange-800',
            'Cancelled' => 'bg-gray-100 text-gray-800',
            default => 'bg-gray-100 text-gray-800',
        };
    }

    /**
     * Mark the task as completed.
     */
    public function markAsCompleted(): bool
    {
        return $this->update([
            'status' => 'Completed',
            'completed_at' => now(),
        ]);
    }

    /**
     * Mark the task as delayed.
     */
    public function markAsDelayed(): bool
    {
        return $this->update([
            'status' => 'Delayed',
        ]);
    }

    /**
     * Mark the task as cancelled.
     */
    public function markAsCancelled(): bool
    {
        return $this->update([
            'status' => 'Cancelled',
        ]);
    }

    /**
     * Reschedule the task to a new date.
     */
    public function reschedule(Carbon $newDate): bool
    {
        return $this->update([
            'task_date' => $newDate,
            'status' => 'Pending',
        ]);
    }

    /**
     * Get the time until the task is due.
     */
    public function getTimeUntilDue(): string
    {
        if ($this->isOverdue()) {
            return 'Overdue by ' . $this->task_date->diffForHumans(null, true);
        }

        return 'Due ' . $this->task_date->diffForHumans();
    }

    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory()
    {
        return \Database\Factories\BuyerCRM\BuyerFollowUpFactory::new();
    }
}
