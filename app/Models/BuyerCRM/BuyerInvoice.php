<?php

namespace App\Models\BuyerCRM;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Models\User;

/**
 * BuyerInvoice Model
 *
 * Manages invoices generated from garment orders
 *
 * @property int $id
 * @property int $buyer_id
 * @property int $garment_order_id
 * @property int|null $financial_transaction_id
 * @property string $invoice_number
 * @property \Carbon\Carbon $invoice_date
 * @property \Carbon\Carbon $due_date
 * @property float $subtotal
 * @property float $tax_rate
 * @property float $tax_amount
 * @property float $discount_rate
 * @property float $discount_amount
 * @property float $total_amount
 * @property float $paid_amount
 * @property float $balance_due
 * @property string $currency
 * @property string $status
 * @property string $payment_terms
 * @property string|null $notes
 * @property array $line_items
 * @property string|null $pdf_path
 * @property \Carbon\Carbon|null $sent_at
 * @property \Carbon\Carbon|null $viewed_at
 * @property \Carbon\Carbon|null $paid_at
 * @property int $created_by
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class BuyerInvoice extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'buyer_invoices';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'buyer_id',
        'garment_order_id',
        'financial_transaction_id',
        'invoice_number',
        'invoice_date',
        'due_date',
        'subtotal',
        'tax_rate',
        'tax_amount',
        'discount_rate',
        'discount_amount',
        'total_amount',
        'paid_amount',
        'balance_due',
        'currency',
        'status',
        'payment_terms',
        'notes',
        'line_items',
        'pdf_path',
        'sent_at',
        'viewed_at',
        'paid_at',
        'created_by',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'invoice_date' => 'date',
        'due_date' => 'date',
        'subtotal' => 'decimal:2',
        'tax_rate' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'discount_rate' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'paid_amount' => 'decimal:2',
        'balance_due' => 'decimal:2',
        'line_items' => 'array',
        'sent_at' => 'datetime',
        'viewed_at' => 'datetime',
        'paid_at' => 'datetime',
    ];

    /**
     * Available invoice statuses.
     *
     * @var array<string>
     */
    public static array $statuses = [
        'Draft',
        'Sent',
        'Viewed',
        'Paid',
        'Overdue',
        'Cancelled'
    ];

    /**
     * Get the buyer profile.
     */
    public function buyer(): BelongsTo
    {
        return $this->belongsTo(BuyerProfile::class, 'buyer_id');
    }

    /**
     * Get the related garment order.
     */
    public function garmentOrder(): BelongsTo
    {
        return $this->belongsTo(GarmentOrder::class, 'garment_order_id');
    }

    /**
     * Get the related financial transaction.
     */
    public function financialTransaction(): BelongsTo
    {
        return $this->belongsTo(BuyerFinancialTransaction::class, 'financial_transaction_id');
    }

    /**
     * Get the user who created the invoice.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Scope for paid invoices.
     */
    public function scopePaid($query)
    {
        return $query->where('status', 'Paid');
    }

    /**
     * Scope for overdue invoices.
     */
    public function scopeOverdue($query)
    {
        return $query->where('status', 'Overdue')
                    ->orWhere(function($q) {
                        $q->whereIn('status', ['Sent', 'Viewed'])
                          ->where('due_date', '<', now()->toDateString());
                    });
    }

    /**
     * Scope for unpaid invoices.
     */
    public function scopeUnpaid($query)
    {
        return $query->whereIn('status', ['Draft', 'Sent', 'Viewed', 'Overdue']);
    }

    /**
     * Check if invoice is overdue.
     */
    public function isOverdue(): bool
    {
        return $this->status !== 'Paid' &&
               $this->status !== 'Cancelled' &&
               $this->due_date < now()->toDateString();
    }

    /**
     * Check if invoice is paid.
     */
    public function isPaid(): bool
    {
        return $this->status === 'Paid';
    }

    /**
     * Get formatted total amount with currency.
     */
    public function getFormattedTotalAttribute(): string
    {
        $symbol = $this->currency === 'USD' ? '$' : $this->currency . ' ';
        return $symbol . number_format($this->total_amount, 2);
    }

    /**
     * Get status badge class.
     */
    public function getStatusBadgeClass(): string
    {
        return match($this->status) {
            'Draft' => 'bg-secondary',
            'Sent' => 'bg-info',
            'Viewed' => 'bg-warning',
            'Paid' => 'bg-success',
            'Overdue' => 'bg-danger',
            'Cancelled' => 'bg-dark',
            default => 'bg-secondary'
        };
    }

    /**
     * Mark invoice as sent.
     */
    public function markAsSent(): void
    {
        $this->update([
            'status' => 'Sent',
            'sent_at' => now(),
        ]);
    }

    /**
     * Mark invoice as viewed.
     */
    public function markAsViewed(): void
    {
        if ($this->status === 'Sent') {
            $this->update([
                'status' => 'Viewed',
                'viewed_at' => now(),
            ]);
        }
    }

    /**
     * Mark invoice as paid.
     */
    public function markAsPaid(float $amount = null): void
    {
        $paidAmount = $amount ?? $this->total_amount;

        $this->update([
            'status' => 'Paid',
            'paid_amount' => $paidAmount,
            'balance_due' => $this->total_amount - $paidAmount,
            'paid_at' => now(),
        ]);
    }

    /**
     * Update overdue status.
     */
    public function updateOverdueStatus(): void
    {
        if ($this->isOverdue() && $this->status !== 'Paid') {
            $this->update(['status' => 'Overdue']);
        }
    }

    /**
     * Generate unique invoice number.
     */
    public static function generateInvoiceNumber(): string
    {
        $prefix = 'INV';
        $year = date('Y');
        $month = date('m');

        // Get the last invoice number for this month
        $lastInvoice = self::where('invoice_number', 'like', "{$prefix}-{$year}{$month}%")
                          ->orderBy('invoice_number', 'desc')
                          ->first();

        if ($lastInvoice) {
            $lastNumber = (int) substr($lastInvoice->invoice_number, -4);
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return sprintf('%s-%s%s-%04d', $prefix, $year, $month, $newNumber);
    }

    /**
     * Create invoice from garment order.
     */
    public static function createFromOrder(GarmentOrder $order, array $options = []): self
    {
        $buyer = $order->buyer;
        $account = BuyerFinancialAccount::getOrCreateForBuyer($buyer->id);

        // Calculate line items from order variants
        $lineItems = [];
        $subtotal = 0;

        if ($order->variants->count() > 0) {
            foreach ($order->variants as $variant) {
                $lineItems[] = [
                    'description' => "{$variant->size_label} / {$variant->color_name}",
                    'quantity' => $variant->quantity,
                    'unit_price' => $variant->unit_price,
                    'total_price' => $variant->total_price,
                ];
                $subtotal += $variant->total_price;
            }
        } else {
            $lineItems[] = [
                'description' => $order->garment_type,
                'quantity' => $order->total_quantity,
                'unit_price' => $order->final_price ?? $order->unit_price ?? 0,
                'total_price' => $order->total_value ?? 0,
            ];
            $subtotal = $order->total_value ?? 0;
        }

        // Calculate tax and discount
        $taxRate = $options['tax_rate'] ?? 0;
        $discountRate = $options['discount_rate'] ?? 0;

        $taxAmount = $subtotal * ($taxRate / 100);
        $discountAmount = $subtotal * ($discountRate / 100);
        $totalAmount = $subtotal + $taxAmount - $discountAmount;

        // Calculate due date
        $dueDate = match($account->payment_terms) {
            'Net 30' => now()->addDays(30),
            'Net 60' => now()->addDays(60),
            'Net 90' => now()->addDays(90),
            'COD' => now(),
            'Advance' => now()->subDays(7),
            default => now()->addDays(30)
        };

        return self::create([
            'buyer_id' => $buyer->id,
            'garment_order_id' => $order->id,
            'invoice_number' => self::generateInvoiceNumber(),
            'invoice_date' => now()->toDateString(),
            'due_date' => $dueDate->toDateString(),
            'subtotal' => $subtotal,
            'tax_rate' => $taxRate,
            'tax_amount' => $taxAmount,
            'discount_rate' => $discountRate,
            'discount_amount' => $discountAmount,
            'total_amount' => $totalAmount,
            'paid_amount' => 0,
            'balance_due' => $totalAmount,
            'currency' => $account->currency,
            'status' => 'Draft',
            'payment_terms' => $account->payment_terms,
            'notes' => $options['notes'] ?? null,
            'line_items' => $lineItems,
            'created_by' => auth()->id(),
        ]);
    }
}
