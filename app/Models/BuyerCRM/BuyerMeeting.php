<?php

namespace App\Models\BuyerCRM;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Models\User;
use Carbon\Carbon;

/**
 * BuyerMeeting Model
 * 
 * Represents meetings and interactions with buyers
 * 
 * @property int $id
 * @property int $buyer_id
 * @property string $interaction_type
 * @property string $summary
 * @property \Carbon\Carbon $meeting_date
 * @property int|null $duration
 * @property int $added_by
 * @property string|null $action_points
 * @property \Carbon\Carbon|null $next_meeting_date
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class BuyerMeeting extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'buyer_meetings';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'buyer_id',
        'interaction_type',
        'summary',
        'meeting_date',
        'duration',
        'added_by',
        'action_points',
        'next_meeting_date',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'meeting_date' => 'datetime',
        'next_meeting_date' => 'datetime',
        'duration' => 'integer',
    ];

    /**
     * Available interaction types.
     *
     * @var array<string>
     */
    public static array $interactionTypes = [
        'In-person',
        'Phone',
        'Video Call',
        'Email',
        'Trade Fair',
        'Exhibition'
    ];

    /**
     * Get the buyer that owns this meeting.
     */
    public function buyer(): BelongsTo
    {
        return $this->belongsTo(BuyerProfile::class, 'buyer_id');
    }

    /**
     * Get the user who added this meeting.
     */
    public function addedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'added_by');
    }

    /**
     * Scope a query to only include meetings for a specific buyer.
     */
    public function scopeForBuyer($query, int $buyerId)
    {
        return $query->where('buyer_id', $buyerId);
    }

    /**
     * Scope a query to filter by interaction type.
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('interaction_type', $type);
    }

    /**
     * Scope a query to filter by date range.
     */
    public function scopeDateRange($query, Carbon $startDate, Carbon $endDate)
    {
        return $query->whereBetween('meeting_date', [$startDate, $endDate]);
    }

    /**
     * Scope a query to only include recent meetings.
     */
    public function scopeRecent($query, int $days = 30)
    {
        return $query->where('meeting_date', '>=', now()->subDays($days));
    }

    /**
     * Scope a query to only include meetings this month.
     */
    public function scopeThisMonth($query)
    {
        return $query->whereBetween('meeting_date', [
            now()->startOfMonth(),
            now()->endOfMonth()
        ]);
    }

    /**
     * Scope a query to only include meetings this week.
     */
    public function scopeThisWeek($query)
    {
        return $query->whereBetween('meeting_date', [
            now()->startOfWeek(),
            now()->endOfWeek()
        ]);
    }

    /**
     * Scope a query to only include meetings with follow-up scheduled.
     */
    public function scopeWithFollowUp($query)
    {
        return $query->whereNotNull('next_meeting_date');
    }

    /**
     * Scope a query to only include meetings added by a specific user.
     */
    public function scopeAddedBy($query, int $userId)
    {
        return $query->where('added_by', $userId);
    }

    /**
     * Get the interaction type icon for UI.
     */
    public function getInteractionTypeIcon(): string
    {
        return match ($this->interaction_type) {
            'In-person' => 'fas fa-handshake',
            'Phone' => 'fas fa-phone',
            'Video Call' => 'fas fa-video',
            'Email' => 'fas fa-envelope',
            'Trade Fair' => 'fas fa-store',
            'Exhibition' => 'fas fa-building',
            default => 'fas fa-calendar',
        };
    }

    /**
     * Get the interaction type color for UI.
     */
    public function getInteractionTypeColor(): string
    {
        return match ($this->interaction_type) {
            'In-person' => 'bg-green-100 text-green-800',
            'Phone' => 'bg-blue-100 text-blue-800',
            'Video Call' => 'bg-purple-100 text-purple-800',
            'Email' => 'bg-gray-100 text-gray-800',
            'Trade Fair' => 'bg-yellow-100 text-yellow-800',
            'Exhibition' => 'bg-orange-100 text-orange-800',
            default => 'bg-gray-100 text-gray-800',
        };
    }

    /**
     * Get formatted duration.
     */
    public function getFormattedDuration(): string
    {
        if (!$this->duration) {
            return 'Not specified';
        }

        $hours = intval($this->duration / 60);
        $minutes = $this->duration % 60;

        if ($hours > 0) {
            return $hours . 'h ' . ($minutes > 0 ? $minutes . 'm' : '');
        }

        return $minutes . 'm';
    }

    /**
     * Check if meeting has action points.
     */
    public function hasActionPoints(): bool
    {
        return !empty($this->action_points);
    }

    /**
     * Check if meeting has follow-up scheduled.
     */
    public function hasFollowUp(): bool
    {
        return $this->next_meeting_date !== null;
    }

    /**
     * Check if follow-up is overdue.
     */
    public function isFollowUpOverdue(): bool
    {
        return $this->hasFollowUp() && $this->next_meeting_date->isPast();
    }

    /**
     * Get the meeting age in days.
     */
    public function getAgeInDays(): int
    {
        return $this->meeting_date->diffInDays(now());
    }

    /**
     * Check if this is a recent meeting.
     */
    public function isRecent(int $days = 7): bool
    {
        return $this->getAgeInDays() <= $days;
    }

    /**
     * Get meeting effectiveness score based on duration and follow-up.
     */
    public function getEffectivenessScore(): int
    {
        $score = 0;

        // Base score for having a meeting
        $score += 30;

        // Duration score (longer meetings generally more effective)
        if ($this->duration) {
            if ($this->duration >= 60) {
                $score += 30;
            } elseif ($this->duration >= 30) {
                $score += 20;
            } elseif ($this->duration >= 15) {
                $score += 10;
            }
        }

        // Action points score
        if ($this->hasActionPoints()) {
            $score += 20;
        }

        // Follow-up score
        if ($this->hasFollowUp()) {
            $score += 20;
        }

        return min($score, 100);
    }

    /**
     * Get action points as array.
     */
    public function getActionPointsArray(): array
    {
        if (!$this->action_points) {
            return [];
        }

        return array_filter(array_map('trim', explode("\n", $this->action_points)));
    }

    /**
     * Get summary excerpt for display.
     */
    public function getSummaryExcerpt(int $length = 100): string
    {
        return strlen($this->summary) > $length 
            ? substr($this->summary, 0, $length) . '...'
            : $this->summary;
    }

    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory()
    {
        return \Database\Factories\BuyerCRM\BuyerMeetingFactory::new();
    }
}
