<?php

namespace App\Models\BuyerCRM;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Models\User;

/**
 * BuyerPipeline Model
 * 
 * Represents a buyer's journey through the sales pipeline
 * 
 * @property int $id
 * @property int $buyer_id
 * @property string $current_stage
 * @property \Carbon\Carbon $moved_at
 * @property string|null $note
 * @property int $moved_by
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class BuyerPipeline extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'buyer_pipelines';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'buyer_id',
        'current_stage',
        'moved_at',
        'note',
        'moved_by',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'moved_at' => 'datetime',
    ];

    /**
     * Available pipeline stages.
     *
     * @var array<string>
     */
    public static array $stages = [
        'Lead Captured',
        'Contacted',
        'Sample Sent',
        'Quotation Sent',
        'Negotiation',
        'Converted',
        'Dropped'
    ];

    /**
     * Get the buyer that owns this pipeline record.
     */
    public function buyer(): BelongsTo
    {
        return $this->belongsTo(BuyerProfile::class, 'buyer_id');
    }

    /**
     * Get the user who moved this stage.
     */
    public function movedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'moved_by');
    }

    /**
     * Scope a query to only include records for a specific buyer.
     */
    public function scopeForBuyer($query, $buyerId)
    {
        return $query->where('buyer_id', $buyerId);
    }

    /**
     * Scope a query to only include records for a specific stage.
     */
    public function scopeInStage($query, $stage)
    {
        return $query->where('current_stage', $stage);
    }

    /**
     * Scope a query to get the latest pipeline record for each buyer.
     */
    public function scopeLatestForEachBuyer($query)
    {
        return $query->whereIn('id', function ($subQuery) {
            $subQuery->selectRaw('MAX(id)')
                ->from('buyer_pipelines')
                ->groupBy('buyer_id');
        });
    }

    /**
     * Get the next stage in the pipeline.
     */
    public function getNextStage(): ?string
    {
        $currentIndex = array_search($this->current_stage, self::$stages);
        
        if ($currentIndex === false || $currentIndex >= count(self::$stages) - 1) {
            return null;
        }
        
        return self::$stages[$currentIndex + 1];
    }

    /**
     * Get the previous stage in the pipeline.
     */
    public function getPreviousStage(): ?string
    {
        $currentIndex = array_search($this->current_stage, self::$stages);
        
        if ($currentIndex === false || $currentIndex <= 0) {
            return null;
        }
        
        return self::$stages[$currentIndex - 1];
    }

    /**
     * Check if this is a terminal stage (Converted or Dropped).
     */
    public function isTerminalStage(): bool
    {
        return in_array($this->current_stage, ['Converted', 'Dropped']);
    }

    /**
     * Check if this is a positive terminal stage (Converted).
     */
    public function isConverted(): bool
    {
        return $this->current_stage === 'Converted';
    }

    /**
     * Check if this is a negative terminal stage (Dropped).
     */
    public function isDropped(): bool
    {
        return $this->current_stage === 'Dropped';
    }

    /**
     * Get the stage progress percentage.
     */
    public function getProgressPercentage(): int
    {
        $currentIndex = array_search($this->current_stage, self::$stages);
        
        if ($currentIndex === false) {
            return 0;
        }
        
        // Calculate percentage based on stage position
        return (int) round(($currentIndex / (count(self::$stages) - 1)) * 100);
    }

    /**
     * Get the stage color for UI display.
     */
    public function getStageColor(): string
    {
        return match ($this->current_stage) {
            'Lead Captured' => 'bg-gray-500',
            'Contacted' => 'bg-blue-500',
            'Sample Sent' => 'bg-yellow-500',
            'Quotation Sent' => 'bg-orange-500',
            'Negotiation' => 'bg-purple-500',
            'Converted' => 'bg-green-500',
            'Dropped' => 'bg-red-500',
            default => 'bg-gray-500',
        };
    }

    /**
     * Get the stage icon for UI display.
     */
    public function getStageIcon(): string
    {
        return match ($this->current_stage) {
            'Lead Captured' => 'fas fa-user-plus',
            'Contacted' => 'fas fa-phone',
            'Sample Sent' => 'fas fa-box',
            'Quotation Sent' => 'fas fa-file-invoice-dollar',
            'Negotiation' => 'fas fa-handshake',
            'Converted' => 'fas fa-check-circle',
            'Dropped' => 'fas fa-times-circle',
            default => 'fas fa-circle',
        };
    }

    /**
     * Validate stage transition.
     */
    public static function isValidTransition(string $fromStage, string $toStage): bool
    {
        // Allow any transition to Dropped
        if ($toStage === 'Dropped') {
            return true;
        }
        
        // Don't allow transitions from terminal stages (except Dropped to any stage for re-engagement)
        if ($fromStage === 'Converted') {
            return false;
        }
        
        // Allow re-engagement from Dropped
        if ($fromStage === 'Dropped') {
            return true;
        }
        
        $fromIndex = array_search($fromStage, self::$stages);
        $toIndex = array_search($toStage, self::$stages);
        
        // Allow forward movement or one step backward
        return $toIndex >= $fromIndex - 1;
    }

    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory()
    {
        return \Database\Factories\BuyerCRM\BuyerPipelineFactory::new();
    }
}
