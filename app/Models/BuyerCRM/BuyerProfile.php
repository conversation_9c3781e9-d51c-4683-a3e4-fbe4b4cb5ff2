<?php

namespace App\Models\BuyerCRM;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use App\Models\User;

/**
 * BuyerProfile Model
 * 
 * Represents a buyer profile in the CRM system
 * 
 * @property int $id
 * @property string $name
 * @property string $company
 * @property string $country
 * @property string $contact_person
 * @property string $phone
 * @property string $email
 * @property array $interest
 * @property string $type
 * @property string $priority
 * @property string $status
 * @property int|null $assigned_to
 * @property int|null $rating
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class BuyerProfile extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'buyer_profiles';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'company',
        'country',
        'contact_person',
        'phone',
        'email',
        'interest',
        'type',
        'priority',
        'status',
        'assigned_to',
        'rating',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'interest' => 'array',
        'rating' => 'integer',
    ];

    /**
     * Get the user assigned to this buyer.
     */
    public function assignedUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    /**
     * Get the buyer's pipeline records.
     */
    public function pipelines(): HasMany
    {
        return $this->hasMany(BuyerPipeline::class, 'buyer_id');
    }

    /**
     * Get the buyer's latest score.
     */
    public function latestScore(): HasOne
    {
        return $this->hasOne(BuyerScore::class, 'buyer_id')->latest();
    }

    /**
     * Get the buyer's current pipeline stage.
     */
    public function currentPipeline(): HasOne
    {
        return $this->hasOne(BuyerPipeline::class, 'buyer_id')->latest();
    }



    /**
     * Get the buyer's follow-ups.
     */
    public function followUps(): HasMany
    {
        return $this->hasMany(BuyerFollowUp::class, 'buyer_id');
    }

    /**
     * Get the buyer's meetings.
     */
    public function meetings(): HasMany
    {
        return $this->hasMany(BuyerMeeting::class, 'buyer_id');
    }

    /**
     * Get the buyer's documents.
     */
    public function documents(): HasMany
    {
        return $this->hasMany(BuyerDocument::class, 'buyer_id');
    }

    /**
     * Get the buyer's score.
     */
    public function score(): HasOne
    {
        return $this->hasOne(BuyerScore::class, 'buyer_id');
    }

    /**
     * Get the buyer's drop reasons.
     */
    public function dropReasons(): HasMany
    {
        return $this->hasMany(BuyerDropReason::class, 'buyer_id');
    }

    /**
     * Get the buyer's marketing campaigns.
     */
    public function marketingCampaigns(): HasMany
    {
        return $this->hasMany(MarketingCampaignBuyer::class, 'buyer_id');
    }

    /**
     * Get the buyer's financial account.
     */
    public function financialAccount(): HasOne
    {
        return $this->hasOne(BuyerFinancialAccount::class, 'buyer_id');
    }

    /**
     * Get the buyer's financial transactions.
     */
    public function financialTransactions(): HasMany
    {
        return $this->hasMany(BuyerFinancialTransaction::class, 'buyer_id');
    }

    /**
     * Get the buyer's invoices.
     */
    public function invoices(): HasMany
    {
        return $this->hasMany(BuyerInvoice::class, 'buyer_id');
    }

    /**
     * Get the buyer's garment orders.
     */
    public function garmentOrders(): HasMany
    {
        return $this->hasMany(GarmentOrder::class, 'buyer_id');
    }

    /**
     * Scope a query to only include active buyers.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'Active');
    }

    /**
     * Scope a query to only include hot priority buyers.
     */
    public function scopeHot($query)
    {
        return $query->where('priority', 'Hot');
    }

    /**
     * Scope a query to filter by country.
     */
    public function scopeByCountry($query, $country)
    {
        return $query->where('country', $country);
    }

    /**
     * Scope a query to filter by type.
     */
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope a query to filter by assigned user.
     */
    public function scopeAssignedTo($query, $userId)
    {
        return $query->where('assigned_to', $userId);
    }

    /**
     * Scope a query to filter by priority.
     */
    public function scopeByPriority($query, $priority)
    {
        return $query->where('priority', $priority);
    }

    /**
     * Scope a query to search by name, company, or email.
     */
    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('name', 'like', "%{$search}%")
              ->orWhere('company', 'like', "%{$search}%")
              ->orWhere('email', 'like', "%{$search}%")
              ->orWhere('contact_person', 'like', "%{$search}%");
        });
    }

    /**
     * Scope a query to filter by region.
     */
    public function scopeByRegion($query, $region)
    {
        $regions = [
            'Asia' => ['Bangladesh', 'India', 'Pakistan', 'China', 'Japan', 'South Korea', 'Thailand', 'Vietnam', 'Indonesia', 'Malaysia', 'Singapore', 'Philippines'],
            'Europe' => ['Germany', 'France', 'Italy', 'Spain', 'United Kingdom', 'Netherlands', 'Belgium', 'Sweden', 'Denmark', 'Norway', 'Finland'],
            'North America' => ['United States', 'Canada', 'Mexico'],
            'South America' => ['Brazil', 'Argentina', 'Chile', 'Colombia', 'Peru'],
            'Africa' => ['South Africa', 'Egypt', 'Nigeria', 'Kenya', 'Morocco'],
            'Oceania' => ['Australia', 'New Zealand'],
        ];

        $countries = $regions[$region] ?? [];
        return $query->whereIn('country', $countries);
    }

    /**
     * Scope a query to filter by product interest.
     */
    public function scopeByProductInterest($query, $product)
    {
        return $query->whereJsonContains('interest', $product);
    }

    /**
     * Scope a query to filter by rating range.
     */
    public function scopeByRatingRange($query, $minRating, $maxRating = null)
    {
        $query->where('rating', '>=', $minRating);

        if ($maxRating !== null) {
            $query->where('rating', '<=', $maxRating);
        }

        return $query;
    }

    /**
     * Scope a query to filter by last contact date.
     */
    public function scopeLastContactedWithin($query, $days)
    {
        return $query->whereHas('meetings', function ($q) use ($days) {
            $q->where('meeting_date', '>=', now()->subDays($days));
        });
    }

    /**
     * Scope a query to filter buyers never contacted.
     */
    public function scopeNeverContacted($query)
    {
        return $query->whereDoesntHave('meetings');
    }

    /**
     * Scope a query to filter by pipeline stage.
     */
    public function scopeInPipelineStage($query, $stage)
    {
        return $query->whereHas('currentPipeline', function ($q) use ($stage) {
            $q->where('current_stage', $stage);
        });
    }

    /**
     * Scope a query to filter high-value buyers (rating >= 8).
     */
    public function scopeHighValue($query)
    {
        return $query->where('rating', '>=', 8);
    }

    /**
     * Scope a query to filter buyers needing attention (hot priority, not contacted recently).
     */
    public function scopeNeedsAttention($query, $days = 15)
    {
        return $query->where('priority', 'Hot')
            ->where(function ($q) use ($days) {
                $q->whereDoesntHave('meetings')
                  ->orWhereDoesntHave('meetings', function ($subQ) use ($days) {
                      $subQ->where('meeting_date', '>=', now()->subDays($days));
                  });
            });
    }

    /**
     * Scope a query to filter converted buyers.
     */
    public function scopeConverted($query)
    {
        return $query->whereHas('currentPipeline', function ($q) {
            $q->where('current_stage', 'Converted');
        });
    }

    /**
     * Scope a query to filter dropped buyers.
     */
    public function scopeDropped($query)
    {
        return $query->whereHas('currentPipeline', function ($q) {
            $q->where('current_stage', 'Dropped');
        });
    }

    /**
     * Get the buyer's region based on country.
     */
    public function getRegionAttribute(): string
    {
        $regions = [
            'Asia' => ['Bangladesh', 'India', 'Pakistan', 'China', 'Japan', 'South Korea', 'Thailand', 'Vietnam', 'Indonesia', 'Malaysia', 'Singapore', 'Philippines'],
            'Europe' => ['Germany', 'France', 'Italy', 'Spain', 'United Kingdom', 'Netherlands', 'Belgium', 'Sweden', 'Denmark', 'Norway', 'Finland'],
            'North America' => ['United States', 'Canada', 'Mexico'],
            'South America' => ['Brazil', 'Argentina', 'Chile', 'Colombia', 'Peru'],
            'Africa' => ['South Africa', 'Egypt', 'Nigeria', 'Kenya', 'Morocco'],
            'Oceania' => ['Australia', 'New Zealand'],
        ];

        foreach ($regions as $region => $countries) {
            if (in_array($this->country, $countries)) {
                return $region;
            }
        }

        return 'Other';
    }

    /**
     * Get the buyer's full display name.
     */
    public function getFullNameAttribute(): string
    {
        return "{$this->name} ({$this->company})";
    }

    /**
     * Check if buyer has been contacted recently.
     */
    public function hasRecentContact(int $days = 15): bool
    {
        return $this->meetings()
            ->where('meeting_date', '>=', now()->subDays($days))
            ->exists();
    }

    /**
     * Get the buyer's last contact date.
     */
    public function getLastContactDateAttribute()
    {
        $lastMeeting = $this->meetings()->latest('meeting_date')->first();
        return $lastMeeting ? $lastMeeting->meeting_date : null;
    }

    /**
     * Get the buyer's current pipeline stage.
     */
    public function getCurrentStageAttribute(): ?string
    {
        $currentPipeline = $this->currentPipeline;
        return $currentPipeline ? $currentPipeline->current_stage : 'Lead Captured';
    }

    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory()
    {
        return \Database\Factories\BuyerCRM\BuyerProfileFactory::new();
    }
}
