<?php

namespace App\Models\BuyerCRM;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Models\User;

/**
 * BuyerScore Model
 * 
 * Represents the calculated engagement score for buyers
 * 
 * @property int $id
 * @property int $buyer_id
 * @property int $score
 * @property string $engagement_level
 * @property \Carbon\Carbon $last_calculated_at
 * @property array $factors
 * @property int $updated_by
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class BuyerScore extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'buyer_scores';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'buyer_id',
        'score',
        'engagement_level',
        'last_calculated_at',
        'factors',
        'updated_by',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'score' => 'integer',
        'last_calculated_at' => 'datetime',
        'factors' => 'array',
    ];

    /**
     * Available engagement levels.
     *
     * @var array<string>
     */
    public static array $engagementLevels = [
        'Low',
        'Medium',
        'High'
    ];

    /**
     * Get the buyer that owns this score.
     */
    public function buyer(): BelongsTo
    {
        return $this->belongsTo(BuyerProfile::class, 'buyer_id');
    }

    /**
     * Get the user who last updated this score.
     */
    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Scope a query to only include high engagement scores.
     */
    public function scopeHighEngagement($query)
    {
        return $query->where('engagement_level', 'High');
    }

    /**
     * Scope a query to only include medium engagement scores.
     */
    public function scopeMediumEngagement($query)
    {
        return $query->where('engagement_level', 'Medium');
    }

    /**
     * Scope a query to only include low engagement scores.
     */
    public function scopeLowEngagement($query)
    {
        return $query->where('engagement_level', 'Low');
    }

    /**
     * Scope a query to filter by minimum score.
     */
    public function scopeMinScore($query, int $minScore)
    {
        return $query->where('score', '>=', $minScore);
    }

    /**
     * Scope a query to filter by score range.
     */
    public function scopeScoreRange($query, int $minScore, int $maxScore)
    {
        return $query->whereBetween('score', [$minScore, $maxScore]);
    }

    /**
     * Scope a query to only include recently calculated scores.
     */
    public function scopeRecentlyCalculated($query, int $hours = 24)
    {
        return $query->where('last_calculated_at', '>=', now()->subHours($hours));
    }

    /**
     * Scope a query to only include stale scores that need recalculation.
     */
    public function scopeStale($query, int $hours = 24)
    {
        return $query->where('last_calculated_at', '<', now()->subHours($hours));
    }

    /**
     * Get the score color for UI display.
     */
    public function getScoreColor(): string
    {
        return match ($this->engagement_level) {
            'High' => 'text-green-600',
            'Medium' => 'text-yellow-600',
            'Low' => 'text-red-600',
            default => 'text-gray-600',
        };
    }

    /**
     * Get the score background color for UI display.
     */
    public function getScoreBackgroundColor(): string
    {
        return match ($this->engagement_level) {
            'High' => 'bg-green-100',
            'Medium' => 'bg-yellow-100',
            'Low' => 'bg-red-100',
            default => 'bg-gray-100',
        };
    }

    /**
     * Get the score badge color for UI display.
     */
    public function getScoreBadgeColor(): string
    {
        return match ($this->engagement_level) {
            'High' => 'bg-green-500',
            'Medium' => 'bg-yellow-500',
            'Low' => 'bg-red-500',
            default => 'bg-gray-500',
        };
    }

    /**
     * Get the score icon for UI display.
     */
    public function getScoreIcon(): string
    {
        return match ($this->engagement_level) {
            'High' => 'fas fa-arrow-up',
            'Medium' => 'fas fa-minus',
            'Low' => 'fas fa-arrow-down',
            default => 'fas fa-question',
        };
    }

    /**
     * Get the score percentage.
     */
    public function getScorePercentage(): int
    {
        return min(100, max(0, $this->score));
    }

    /**
     * Check if the score is considered high.
     */
    public function isHighEngagement(): bool
    {
        return $this->engagement_level === 'High';
    }

    /**
     * Check if the score is considered medium.
     */
    public function isMediumEngagement(): bool
    {
        return $this->engagement_level === 'Medium';
    }

    /**
     * Check if the score is considered low.
     */
    public function isLowEngagement(): bool
    {
        return $this->engagement_level === 'Low';
    }

    /**
     * Check if the score needs recalculation.
     */
    public function needsRecalculation(int $hours = 24): bool
    {
        return $this->last_calculated_at->addHours($hours)->isPast();
    }

    /**
     * Get the time since last calculation.
     */
    public function getTimeSinceCalculation(): string
    {
        return $this->last_calculated_at->diffForHumans();
    }

    /**
     * Get a specific factor value.
     */
    public function getFactor(string $factorName): mixed
    {
        return $this->factors[$factorName] ?? null;
    }

    /**
     * Get all factor names.
     */
    public function getFactorNames(): array
    {
        return array_keys($this->factors ?? []);
    }

    /**
     * Get factors formatted for display.
     */
    public function getFormattedFactors(): array
    {
        $factors = $this->factors ?? [];
        $formatted = [];

        foreach ($factors as $key => $value) {
            $formatted[] = [
                'name' => ucwords(str_replace('_', ' ', $key)),
                'value' => $value,
                'formatted_value' => is_numeric($value) ? number_format($value, 2) : $value,
            ];
        }

        return $formatted;
    }

    /**
     * Determine engagement level based on score.
     */
    public static function determineEngagementLevel(int $score): string
    {
        $thresholds = config('buyer-crm.scoring.thresholds', [
            'high_engagement' => 80,
            'medium_engagement' => 50,
        ]);

        if ($score >= $thresholds['high_engagement']) {
            return 'High';
        } elseif ($score >= $thresholds['medium_engagement']) {
            return 'Medium';
        } else {
            return 'Low';
        }
    }

    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory()
    {
        return \Database\Factories\BuyerCRM\BuyerScoreFactory::new();
    }
}
