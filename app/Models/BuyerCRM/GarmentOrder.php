<?php

namespace App\Models\BuyerCRM;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use App\Models\User;
use Carbon\Carbon;

/**
 * GarmentOrder Model
 * 
 * Represents a garment order in the order management system
 * 
 * @property int $id
 * @property int $buyer_id
 * @property int $created_by
 * @property int|null $merchandiser_id
 * @property string $order_no
 * @property string $style_no
 * @property string $season
 * @property string $product_category
 * @property string $order_type
 * @property string $garment_type
 * @property string $fabric_type
 * @property string $fabric_color
 * @property string $wash_type
 * @property string|null $construction_type
 * @property float|null $gsm
 * @property float|null $shrinkage
 * @property string|null $stitch_type
 * @property int $total_quantity
 * @property float|null $unit_price
 * @property float|null $final_price
 * @property float|null $total_value
 * @property string $status
 * @property bool $production_ready
 * @property \Carbon\Carbon $order_date
 * @property \Carbon\Carbon|null $delivery_date
 * @property \Carbon\Carbon|null $confirmed_date
 * @property string|null $special_instructions
 * @property array|null $meta
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class GarmentOrder extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'garment_orders';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'buyer_id',
        'created_by',
        'merchandiser_id',
        'order_no',
        'style_no',
        'season',
        'product_category',
        'order_type',
        'garment_type',
        'fabric_type',
        'fabric_color',
        'wash_type',
        'construction_type',
        'gsm',
        'shrinkage',
        'stitch_type',
        'total_quantity',
        'unit_price',
        'final_price',
        'total_value',
        'status',
        'production_ready',
        'order_date',
        'delivery_date',
        'confirmed_date',
        'special_instructions',
        'meta',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'gsm' => 'decimal:2',
        'shrinkage' => 'decimal:2',
        'total_quantity' => 'integer',
        'unit_price' => 'decimal:2',
        'final_price' => 'decimal:2',
        'total_value' => 'decimal:2',
        'production_ready' => 'boolean',
        'order_date' => 'date',
        'delivery_date' => 'date',
        'confirmed_date' => 'date',
        'meta' => 'array',
    ];

    /**
     * Available seasons.
     *
     * @var array<string>
     */
    public static array $seasons = [
        'Spring',
        'Summer',
        'Fall',
        'Winter',
        'All Season'
    ];

    /**
     * Available product categories.
     *
     * @var array<string>
     */
    public static array $productCategories = [
        'Shirt',
        'Pant',
        'Denim',
        'Jacket',
        'T-Shirt',
        'Polo',
        'Hoodie',
        'Other'
    ];

    /**
     * Available order types.
     *
     * @var array<string>
     */
    public static array $orderTypes = [
        'Regular',
        'Sample',
        'Repeat'
    ];

    /**
     * Available wash types.
     *
     * @var array<string>
     */
    public static array $washTypes = [
        'Stone',
        'Enzyme',
        'Bleach',
        'Raw',
        'Acid',
        'Silicon',
        'Other'
    ];

    /**
     * Available statuses.
     *
     * @var array<string>
     */
    public static array $statuses = [
        'Draft',
        'Confirmed',
        'In Production',
        'Completed',
        'Cancelled'
    ];

    /**
     * Get the buyer profile.
     */
    public function buyer(): BelongsTo
    {
        return $this->belongsTo(BuyerProfile::class, 'buyer_id');
    }

    /**
     * Get the user who created the order.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the assigned merchandiser.
     */
    public function merchandiser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'merchandiser_id');
    }

    /**
     * Get the order's size ratios.
     */
    public function sizeRatios(): HasMany
    {
        return $this->hasMany(OrderSizeRatio::class, 'garment_order_id');
    }

    /**
     * Get the order variants (size-color combinations).
     */
    public function variants(): HasMany
    {
        return $this->hasMany(GarmentOrderVariant::class, 'garment_order_id');
    }

    /**
     * Get the order's available sizes.
     */
    public function sizes(): HasMany
    {
        return $this->hasMany(GarmentOrderSize::class, 'garment_order_id');
    }

    /**
     * Get the order's available colors.
     */
    public function colors(): HasMany
    {
        return $this->hasMany(GarmentOrderColor::class, 'garment_order_id');
    }

    /**
     * Get the order's tech packs and files.
     */
    public function techPacks(): HasMany
    {
        return $this->hasMany(OrderTechPack::class, 'garment_order_id');
    }

    /**
     * Get the order's BOM items.
     */
    public function bomItems(): HasMany
    {
        return $this->hasMany(OrderBomItem::class, 'garment_order_id');
    }

    /**
     * Get the order's costing.
     */
    public function costing(): HasOne
    {
        return $this->hasOne(OrderCosting::class, 'garment_order_id');
    }

    /**
     * Get the order's delivery batches.
     */
    public function deliveryBatches(): HasMany
    {
        return $this->hasMany(OrderDeliveryBatch::class, 'garment_order_id');
    }

    /**
     * Get the order's SOP compliance items.
     */
    public function sopCompliance(): HasMany
    {
        return $this->hasMany(OrderSopCompliance::class, 'garment_order_id');
    }

    /**
     * Get the order's production checklist.
     */
    public function productionChecklist(): HasOne
    {
        return $this->hasOne(OrderProductionChecklist::class, 'garment_order_id');
    }

    /**
     * Get the order's financial transactions.
     */
    public function financialTransactions(): HasMany
    {
        return $this->hasMany(BuyerFinancialTransaction::class, 'garment_order_id');
    }

    /**
     * Get the order's invoices.
     */
    public function invoices(): HasMany
    {
        return $this->hasMany(BuyerInvoice::class, 'garment_order_id');
    }

    /**
     * Generate unique order number.
     */
    public static function generateOrderNumber(): string
    {
        $prefix = 'GO';
        $year = date('Y');
        $month = date('m');
        
        $lastOrder = static::whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->orderBy('id', 'desc')
            ->first();
        
        $sequence = $lastOrder ? (int) substr($lastOrder->order_no, -4) + 1 : 1;
        
        return $prefix . $year . $month . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Calculate total value based on final price and quantity.
     */
    public function calculateTotalValue(): void
    {
        if ($this->final_price && $this->total_quantity) {
            $this->total_value = $this->final_price * $this->total_quantity;
            $this->save();
        }
    }

    /**
     * Check if order is overdue.
     */
    public function isOverdue(): bool
    {
        return $this->delivery_date && $this->delivery_date->isPast() && $this->status !== 'Completed';
    }

    /**
     * Get days until delivery.
     */
    public function daysUntilDelivery(): ?int
    {
        return $this->delivery_date ? now()->diffInDays($this->delivery_date, false) : null;
    }

    /**
     * Get status badge color.
     */
    public function getStatusBadgeColorAttribute(): string
    {
        return match($this->status) {
            'Draft' => 'secondary',
            'Confirmed' => 'primary',
            'In Production' => 'warning',
            'Completed' => 'success',
            'Cancelled' => 'danger',
            default => 'secondary'
        };
    }

    /**
     * Get formatted total value.
     */
    public function getFormattedTotalValueAttribute(): string
    {
        return $this->total_value ? '$' . number_format($this->total_value, 2) : 'N/A';
    }

    /**
     * Scope for filtering by status.
     */
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope for filtering by buyer.
     */
    public function scopeByBuyer($query, int $buyerId)
    {
        return $query->where('buyer_id', $buyerId);
    }

    /**
     * Scope for filtering by product category.
     */
    public function scopeByProductCategory($query, string $category)
    {
        return $query->where('product_category', $category);
    }

    /**
     * Scope for overdue orders.
     */
    public function scopeOverdue($query)
    {
        return $query->where('delivery_date', '<', now())
            ->whereNotIn('status', ['Completed', 'Cancelled']);
    }

    /**
     * Scope for production ready orders.
     */
    public function scopeProductionReady($query)
    {
        return $query->where('production_ready', true);
    }

    /**
     * Get variant summary for the order.
     */
    public function getVariantSummary(): array
    {
        return GarmentOrderVariant::getSummaryForOrder($this->id);
    }

    /**
     * Check if order has variants.
     */
    public function hasVariants(): bool
    {
        return $this->variants()->exists();
    }

    /**
     * Get total quantity from variants.
     */
    public function getTotalVariantQuantity(): int
    {
        return $this->variants()->sum('quantity');
    }

    /**
     * Get total value from variants.
     */
    public function getTotalVariantValue(): float
    {
        return $this->variants()->sum('total_price') ?? 0;
    }

    /**
     * Update order totals from variants.
     */
    public function updateTotalsFromVariants(): void
    {
        $this->total_quantity = $this->getTotalVariantQuantity();
        $this->total_value = $this->getTotalVariantValue();
        $this->save();
    }

    /**
     * Get unique sizes from variants.
     */
    public function getUniqueSizes(): array
    {
        return $this->variants()
                   ->distinct()
                   ->orderBy('size_sort_order')
                   ->pluck('size_label')
                   ->toArray();
    }

    /**
     * Get unique colors from variants.
     */
    public function getUniqueColors(): array
    {
        return $this->variants()
                   ->distinct()
                   ->orderBy('color_sort_order')
                   ->pluck('color_name')
                   ->toArray();
    }
}
