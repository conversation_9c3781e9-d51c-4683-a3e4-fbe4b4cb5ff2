<?php

namespace App\Models\BuyerCRM;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * GarmentOrderColor Model
 * 
 * Manages available colors for garment orders
 * 
 * @property int $id
 * @property int $garment_order_id
 * @property string $color_name
 * @property string|null $color_code
 * @property string|null $hex_value
 * @property int $sort_order
 * @property string $color_type
 * @property string|null $pantone_code
 * @property string|null $supplier_color_ref
 * @property bool $is_active
 * @property array|null $meta
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class GarmentOrderColor extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'garment_order_colors';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'garment_order_id',
        'color_name',
        'color_code',
        'hex_value',
        'sort_order',
        'color_type',
        'pantone_code',
        'supplier_color_ref',
        'is_active',
        'meta',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_active' => 'boolean',
        'meta' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the garment order that owns this color.
     */
    public function garmentOrder(): BelongsTo
    {
        return $this->belongsTo(GarmentOrder::class, 'garment_order_id');
    }

    /**
     * Get the variants that use this color.
     */
    public function variants(): HasMany
    {
        return $this->hasMany(GarmentOrderVariant::class, 'color_id');
    }

    /**
     * Scope to get only active colors.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to order by sort order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('color_name');
    }

    /**
     * Get the display color with swatch if hex value exists.
     */
    public function getDisplayColorAttribute(): string
    {
        if ($this->hex_value) {
            return '<span class="color-swatch" style="background-color: ' . $this->hex_value . ';"></span> ' . $this->color_name;
        }
        
        return $this->color_name;
    }

    /**
     * Generate standard colors.
     */
    public static function generateStandardColors(int $orderId, array $selectedColors = null): array
    {
        $standardColors = [
            'Black' => ['hex' => '#000000', 'sort_order' => 1],
            'White' => ['hex' => '#FFFFFF', 'sort_order' => 2],
            'Navy Blue' => ['hex' => '#000080', 'sort_order' => 3],
            'Gray' => ['hex' => '#808080', 'sort_order' => 4],
            'Charcoal' => ['hex' => '#36454F', 'sort_order' => 5],
            'Brown' => ['hex' => '#964B00', 'sort_order' => 6],
            'Khaki' => ['hex' => '#C3B091', 'sort_order' => 7],
            'Olive' => ['hex' => '#808000', 'sort_order' => 8],
            'Burgundy' => ['hex' => '#800020', 'sort_order' => 9],
            'Royal Blue' => ['hex' => '#4169E1', 'sort_order' => 10],
            'Forest Green' => ['hex' => '#228B22', 'sort_order' => 11],
            'Maroon' => ['hex' => '#800000', 'sort_order' => 12],
        ];

        $colors = [];
        $colorsToUse = $selectedColors ?: array_keys($standardColors);

        foreach ($colorsToUse as $colorName) {
            if (isset($standardColors[$colorName])) {
                $colors[] = [
                    'garment_order_id' => $orderId,
                    'color_name' => $colorName,
                    'hex_value' => $standardColors[$colorName]['hex'],
                    'sort_order' => $standardColors[$colorName]['sort_order'],
                    'color_type' => 'standard',
                    'is_active' => true,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
            }
        }

        return $colors;
    }

    /**
     * Generate custom colors.
     */
    public static function generateCustomColors(int $orderId, array $customColors): array
    {
        $colors = [];
        $sortOrder = 100; // Start custom colors after standard ones

        foreach ($customColors as $colorData) {
            $colors[] = [
                'garment_order_id' => $orderId,
                'color_name' => $colorData['name'],
                'color_code' => $colorData['code'] ?? null,
                'hex_value' => $colorData['hex'] ?? null,
                'pantone_code' => $colorData['pantone'] ?? null,
                'supplier_color_ref' => $colorData['supplier_ref'] ?? null,
                'sort_order' => $sortOrder++,
                'color_type' => 'custom',
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        return $colors;
    }

    /**
     * Get all standard color options.
     */
    public static function getStandardColorOptions(): array
    {
        return [
            'Black' => '#000000',
            'White' => '#FFFFFF',
            'Navy Blue' => '#000080',
            'Gray' => '#808080',
            'Charcoal' => '#36454F',
            'Brown' => '#964B00',
            'Khaki' => '#C3B091',
            'Olive' => '#808000',
            'Burgundy' => '#800020',
            'Royal Blue' => '#4169E1',
            'Forest Green' => '#228B22',
            'Maroon' => '#800000',
            'Beige' => '#F5F5DC',
            'Cream' => '#FFFDD0',
            'Light Gray' => '#D3D3D3',
            'Dark Gray' => '#A9A9A9',
            'Red' => '#FF0000',
            'Blue' => '#0000FF',
            'Green' => '#008000',
            'Yellow' => '#FFFF00',
            'Orange' => '#FFA500',
            'Purple' => '#800080',
            'Pink' => '#FFC0CB',
            'Turquoise' => '#40E0D0',
            'Coral' => '#FF7F50',
            'Mint' => '#98FB98',
            'Lavender' => '#E6E6FA',
            'Peach' => '#FFCBA4',
            'Sage' => '#9CAF88',
            'Mustard' => '#FFDB58',
        ];
    }
}
