<?php

namespace App\Models\BuyerCRM;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * GarmentOrderSize Model
 * 
 * Manages available sizes for garment orders
 * 
 * @property int $id
 * @property int $garment_order_id
 * @property string $size_value
 * @property string $size_type
 * @property int $sort_order
 * @property float|null $numeric_value
 * @property string|null $display_label
 * @property bool $is_active
 * @property array|null $meta
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class GarmentOrderSize extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'garment_order_sizes';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'garment_order_id',
        'size_value',
        'size_type',
        'sort_order',
        'numeric_value',
        'display_label',
        'is_active',
        'meta',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_active' => 'boolean',
        'numeric_value' => 'decimal:2',
        'meta' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the garment order that owns this size.
     */
    public function garmentOrder(): BelongsTo
    {
        return $this->belongsTo(GarmentOrder::class, 'garment_order_id');
    }

    /**
     * Get the variants that use this size.
     */
    public function variants(): HasMany
    {
        return $this->hasMany(GarmentOrderVariant::class, 'size_id');
    }

    /**
     * Scope to get only active sizes.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to order by sort order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('numeric_value')->orderBy('size_value');
    }

    /**
     * Get the display name for the size.
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->display_label ?: $this->size_value;
    }

    /**
     * Generate standard numeric sizes.
     */
    public static function generateNumericSizes(int $orderId, int $startSize, int $endSize, int $increment = 2): array
    {
        $sizes = [];
        $sortOrder = 1;
        
        for ($size = $startSize; $size <= $endSize; $size += $increment) {
            $sizes[] = [
                'garment_order_id' => $orderId,
                'size_value' => (string) $size,
                'size_type' => 'numeric',
                'sort_order' => $sortOrder++,
                'numeric_value' => $size,
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }
        
        return $sizes;
    }

    /**
     * Generate standard alpha sizes.
     */
    public static function generateAlphaSizes(int $orderId, array $selectedSizes = null): array
    {
        $standardSizes = [
            'XS' => ['sort_order' => 1, 'numeric_value' => 1],
            'S' => ['sort_order' => 2, 'numeric_value' => 2],
            'M' => ['sort_order' => 3, 'numeric_value' => 3],
            'L' => ['sort_order' => 4, 'numeric_value' => 4],
            'XL' => ['sort_order' => 5, 'numeric_value' => 5],
            'XXL' => ['sort_order' => 6, 'numeric_value' => 6],
            'XXXL' => ['sort_order' => 7, 'numeric_value' => 7],
        ];

        $sizes = [];
        $sizesToUse = $selectedSizes ?: array_keys($standardSizes);

        foreach ($sizesToUse as $sizeValue) {
            if (isset($standardSizes[$sizeValue])) {
                $sizes[] = [
                    'garment_order_id' => $orderId,
                    'size_value' => $sizeValue,
                    'size_type' => 'alpha',
                    'sort_order' => $standardSizes[$sizeValue]['sort_order'],
                    'numeric_value' => $standardSizes[$sizeValue]['numeric_value'],
                    'is_active' => true,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
            }
        }

        return $sizes;
    }

    /**
     * Generate custom sizes.
     */
    public static function generateCustomSizes(int $orderId, array $customSizes): array
    {
        $sizes = [];
        $sortOrder = 1;

        foreach ($customSizes as $sizeValue) {
            $sizes[] = [
                'garment_order_id' => $orderId,
                'size_value' => $sizeValue,
                'size_type' => 'custom',
                'sort_order' => $sortOrder++,
                'numeric_value' => is_numeric($sizeValue) ? (float) $sizeValue : null,
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        return $sizes;
    }
}
