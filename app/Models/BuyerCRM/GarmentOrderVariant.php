<?php

namespace App\Models\BuyerCRM;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Models\User;

/**
 * GarmentOrderVariant Model
 * 
 * Represents size-color-quantity combinations for garment orders
 * 
 * @property int $id
 * @property int $garment_order_id
 * @property string $size_label
 * @property string $size_type
 * @property int $size_sort_order
 * @property string $color_name
 * @property string|null $color_code
 * @property int $color_sort_order
 * @property int $quantity
 * @property float|null $unit_price
 * @property float|null $total_price
 * @property \Carbon\Carbon|null $delivery_date
 * @property \Carbon\Carbon|null $production_start_date
 * @property \Carbon\Carbon|null $production_end_date
 * @property string $status
 * @property bool $is_active
 * @property string|null $notes
 * @property array|null $specifications
 * @property int $created_by
 * @property int|null $updated_by
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class GarmentOrderVariant extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'garment_order_variants';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'garment_order_id',
        'size_label',
        'size_type',
        'size_sort_order',
        'color_name',
        'color_code',
        'color_sort_order',
        'quantity',
        'unit_price',
        'total_price',
        'delivery_date',
        'production_start_date',
        'production_end_date',
        'status',
        'is_active',
        'notes',
        'specifications',
        'created_by',
        'updated_by',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'size_sort_order' => 'integer',
        'color_sort_order' => 'integer',
        'quantity' => 'integer',
        'unit_price' => 'decimal:2',
        'total_price' => 'decimal:2',
        'delivery_date' => 'date',
        'production_start_date' => 'date',
        'production_end_date' => 'date',
        'is_active' => 'boolean',
        'specifications' => 'array',
    ];

    /**
     * Available size types.
     *
     * @var array<string>
     */
    public static array $sizeTypes = [
        'text' => 'Text Sizes (XS, S, M, L, XL)',
        'numeric' => 'Numeric Sizes (28, 30, 32, etc.)'
    ];

    /**
     * Available statuses.
     *
     * @var array<string>
     */
    public static array $statuses = [
        'pending' => 'Pending',
        'confirmed' => 'Confirmed',
        'in_production' => 'In Production',
        'completed' => 'Completed',
        'cancelled' => 'Cancelled'
    ];

    /**
     * Common text sizes.
     *
     * @var array<string>
     */
    public static array $textSizes = [
        'XXS', 'XS', 'S', 'M', 'L', 'XL', 'XXL', 'XXXL'
    ];

    /**
     * Common numeric sizes for different categories.
     *
     * @var array<string, array>
     */
    public static array $numericSizes = [
        'pants' => ['28', '30', '32', '34', '36', '38', '40', '42', '44', '46', '48', '50'],
        'shirts' => ['14', '15', '16', '17', '18', '19', '20'],
        'shoes' => ['6', '7', '8', '9', '10', '11', '12'],
        'general' => ['28', '30', '32', '34', '36', '38', '40', '42', '44', '46', '48', '50']
    ];

    /**
     * Common colors for garments.
     *
     * @var array<string>
     */
    public static array $commonColors = [
        'Black', 'White', 'Gray', 'Navy', 'Blue', 'Red', 'Green', 'Yellow', 
        'Orange', 'Purple', 'Pink', 'Brown', 'Beige', 'Khaki', 'Maroon'
    ];

    /**
     * Get the garment order.
     */
    public function garmentOrder(): BelongsTo
    {
        return $this->belongsTo(GarmentOrder::class, 'garment_order_id');
    }

    /**
     * Get the user who created this variant.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who last updated this variant.
     */
    public function updater(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Scope to get active variants.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get variants by status.
     */
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to order by size and color.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('size_sort_order')->orderBy('color_sort_order');
    }

    /**
     * Calculate total price based on quantity and unit price.
     */
    public function calculateTotalPrice(): void
    {
        if ($this->quantity && $this->unit_price) {
            $this->total_price = $this->quantity * $this->unit_price;
        }
    }

    /**
     * Get formatted size-color combination.
     */
    public function getSizeColorAttribute(): string
    {
        return "{$this->size_label} - {$this->color_name}";
    }

    /**
     * Get status badge class.
     */
    public function getStatusBadgeClassAttribute(): string
    {
        return match($this->status) {
            'pending' => 'badge-warning',
            'confirmed' => 'badge-info',
            'in_production' => 'badge-primary',
            'completed' => 'badge-success',
            'cancelled' => 'badge-danger',
            default => 'badge-secondary'
        };
    }

    /**
     * Static method to create variants from matrix data.
     */
    public static function createFromMatrix(int $garmentOrderId, array $matrixData, int $createdBy): void
    {
        foreach ($matrixData as $variantData) {
            $variant = static::create([
                'garment_order_id' => $garmentOrderId,
                'size_label' => $variantData['size_label'],
                'size_type' => $variantData['size_type'],
                'size_sort_order' => $variantData['size_sort_order'],
                'color_name' => $variantData['color_name'],
                'color_code' => $variantData['color_code'] ?? null,
                'color_sort_order' => $variantData['color_sort_order'],
                'quantity' => $variantData['quantity'] ?? 0,
                'unit_price' => $variantData['unit_price'] ?? null,
                'delivery_date' => $variantData['delivery_date'] ?? null,
                'status' => 'pending',
                'is_active' => true,
                'created_by' => $createdBy,
            ]);
            
            $variant->calculateTotalPrice();
            $variant->save();
        }
    }

    /**
     * Get order summary by size.
     */
    public static function getOrderSummaryBySize(int $garmentOrderId): array
    {
        $variants = static::where('garment_order_id', $garmentOrderId)
            ->active()
            ->get()
            ->groupBy('size_label');

        $summary = [];
        foreach ($variants as $size => $sizeVariants) {
            $summary[$size] = [
                'total_quantity' => $sizeVariants->sum('quantity'),
                'total_value' => $sizeVariants->sum('total_price'),
                'color_count' => $sizeVariants->count(),
                'colors' => $sizeVariants->pluck('color_name')->toArray()
            ];
        }

        return $summary;
    }

    /**
     * Get order summary by color.
     */
    public static function getOrderSummaryByColor(int $garmentOrderId): array
    {
        $variants = static::where('garment_order_id', $garmentOrderId)
            ->active()
            ->get()
            ->groupBy('color_name');

        $summary = [];
        foreach ($variants as $color => $colorVariants) {
            $summary[$color] = [
                'total_quantity' => $colorVariants->sum('quantity'),
                'total_value' => $colorVariants->sum('total_price'),
                'size_count' => $colorVariants->count(),
                'sizes' => $colorVariants->pluck('size_label')->toArray()
            ];
        }

        return $summary;
    }

    /**
     * Get complete order summary.
     */
    public static function getOrderSummary(int $garmentOrderId): array
    {
        $variants = static::where('garment_order_id', $garmentOrderId)->active()->get();

        return [
            'total_variants' => $variants->count(),
            'total_quantity' => $variants->sum('quantity'),
            'total_value' => $variants->sum('total_price'),
            'unique_sizes' => $variants->pluck('size_label')->unique()->count(),
            'unique_colors' => $variants->pluck('color_name')->unique()->count(),
            'by_size' => static::getOrderSummaryBySize($garmentOrderId),
            'by_color' => static::getOrderSummaryByColor($garmentOrderId),
        ];
    }

    /**
     * Update garment order totals based on variants.
     */
    public static function updateOrderTotals(int $garmentOrderId): void
    {
        $summary = static::getOrderSummary($garmentOrderId);

        $garmentOrder = GarmentOrder::find($garmentOrderId);
        if ($garmentOrder) {
            $garmentOrder->update([
                'total_quantity' => $summary['total_quantity'],
                'total_value' => $summary['total_value'],
            ]);
        }
    }

    /**
     * Get variant summary for a specific order.
     */
    public static function getSummaryForOrder(int $orderId): array
    {
        $variants = self::where('garment_order_id', $orderId)->get();

        if ($variants->isEmpty()) {
            return [
                'total_variants' => 0,
                'total_quantity' => 0,
                'total_value' => 0,
                'unique_sizes' => 0,
                'unique_colors' => 0,
                'size_breakdown' => [],
                'color_breakdown' => []
            ];
        }

        $uniqueSizes = $variants->pluck('size_label')->unique()->count();
        $uniqueColors = $variants->pluck('color_name')->unique()->count();
        $totalQuantity = $variants->sum('quantity');
        $totalValue = $variants->sum('total_price');

        // Size breakdown
        $sizeBreakdown = $variants->groupBy('size_label')->map(function ($sizeVariants) {
            return [
                'quantity' => $sizeVariants->sum('quantity'),
                'value' => $sizeVariants->sum('total_price'),
                'colors' => $sizeVariants->count()
            ];
        })->toArray();

        // Color breakdown
        $colorBreakdown = $variants->groupBy('color_name')->map(function ($colorVariants) {
            return [
                'quantity' => $colorVariants->sum('quantity'),
                'value' => $colorVariants->sum('total_price'),
                'sizes' => $colorVariants->count(),
                'color_code' => $colorVariants->first()->color_code
            ];
        })->toArray();

        return [
            'total_variants' => $variants->count(),
            'total_quantity' => $totalQuantity,
            'total_value' => $totalValue,
            'unique_sizes' => $uniqueSizes,
            'unique_colors' => $uniqueColors,
            'size_breakdown' => $sizeBreakdown,
            'color_breakdown' => $colorBreakdown
        ];
    }
}
