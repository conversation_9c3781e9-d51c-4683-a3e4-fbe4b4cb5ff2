<?php

namespace App\Models\BuyerCRM;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use App\Models\User;
use Carbon\Carbon;

/**
 * MarketingCampaign Model
 * 
 * Represents marketing campaigns targeting buyers
 * 
 * @property int $id
 * @property string $title
 * @property string|null $description
 * @property string $target_market
 * @property string $product_focus
 * @property string $goal
 * @property float|null $budget
 * @property \Carbon\Carbon $start_date
 * @property \Carbon\Carbon $end_date
 * @property int $assigned_to
 * @property string $status
 * @property string|null $notes
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class MarketingCampaign extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'marketing_campaigns';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'title',
        'description',
        'target_market',
        'product_focus',
        'goal',
        'budget',
        'start_date',
        'end_date',
        'assigned_to',
        'status',
        'notes',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'budget' => 'decimal:2',
    ];

    /**
     * Available campaign statuses.
     *
     * @var array<string>
     */
    public static array $statuses = [
        'Planned',
        'Running',
        'Paused',
        'Completed',
        'Cancelled'
    ];

    /**
     * Get the user assigned to this campaign.
     */
    public function assignedUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    /**
     * Get the campaign buyers (pivot records).
     */
    public function campaignBuyers(): HasMany
    {
        return $this->hasMany(MarketingCampaignBuyer::class, 'campaign_id');
    }

    /**
     * Get the buyers associated with this campaign.
     */
    public function buyers(): BelongsToMany
    {
        return $this->belongsToMany(BuyerProfile::class, 'marketing_campaign_buyers', 'campaign_id', 'buyer_id')
            ->withPivot(['stage', 'contacted_at', 'response_received_at', 'notes'])
            ->withTimestamps();
    }

    /**
     * Scope a query to only include active campaigns.
     */
    public function scopeActive($query)
    {
        return $query->whereIn('status', ['Running', 'Planned']);
    }

    /**
     * Scope a query to only include running campaigns.
     */
    public function scopeRunning($query)
    {
        return $query->where('status', 'Running');
    }

    /**
     * Scope a query to only include completed campaigns.
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'Completed');
    }

    /**
     * Scope a query to filter by assigned user.
     */
    public function scopeAssignedTo($query, int $userId)
    {
        return $query->where('assigned_to', $userId);
    }

    /**
     * Scope a query to filter by target market.
     */
    public function scopeByTargetMarket($query, string $market)
    {
        return $query->where('target_market', 'like', "%{$market}%");
    }

    /**
     * Scope a query to filter by date range.
     */
    public function scopeDateRange($query, Carbon $startDate, Carbon $endDate)
    {
        return $query->where(function ($q) use ($startDate, $endDate) {
            $q->whereBetween('start_date', [$startDate, $endDate])
              ->orWhereBetween('end_date', [$startDate, $endDate])
              ->orWhere(function ($subQ) use ($startDate, $endDate) {
                  $subQ->where('start_date', '<=', $startDate)
                       ->where('end_date', '>=', $endDate);
              });
        });
    }

    /**
     * Scope a query to only include current campaigns.
     */
    public function scopeCurrent($query)
    {
        $today = today();
        return $query->where('start_date', '<=', $today)
                    ->where('end_date', '>=', $today);
    }

    /**
     * Scope a query to only include upcoming campaigns.
     */
    public function scopeUpcoming($query)
    {
        return $query->where('start_date', '>', today());
    }

    /**
     * Scope a query to only include past campaigns.
     */
    public function scopePast($query)
    {
        return $query->where('end_date', '<', today());
    }

    /**
     * Get the campaign status color for UI.
     */
    public function getStatusColor(): string
    {
        return match ($this->status) {
            'Planned' => 'bg-blue-100 text-blue-800',
            'Running' => 'bg-green-100 text-green-800',
            'Paused' => 'bg-yellow-100 text-yellow-800',
            'Completed' => 'bg-gray-100 text-gray-800',
            'Cancelled' => 'bg-red-100 text-red-800',
            default => 'bg-gray-100 text-gray-800',
        };
    }

    /**
     * Get the campaign status icon for UI.
     */
    public function getStatusIcon(): string
    {
        return match ($this->status) {
            'Planned' => 'fas fa-calendar-plus',
            'Running' => 'fas fa-play-circle',
            'Paused' => 'fas fa-pause-circle',
            'Completed' => 'fas fa-check-circle',
            'Cancelled' => 'fas fa-times-circle',
            default => 'fas fa-question-circle',
        };
    }

    /**
     * Check if the campaign is currently active.
     */
    public function isActive(): bool
    {
        return in_array($this->status, ['Running', 'Planned']) && 
               $this->start_date <= today() && 
               $this->end_date >= today();
    }

    /**
     * Check if the campaign is running.
     */
    public function isRunning(): bool
    {
        return $this->status === 'Running';
    }

    /**
     * Check if the campaign is completed.
     */
    public function isCompleted(): bool
    {
        return $this->status === 'Completed';
    }

    /**
     * Check if the campaign is overdue.
     */
    public function isOverdue(): bool
    {
        return $this->end_date->isPast() && !in_array($this->status, ['Completed', 'Cancelled']);
    }

    /**
     * Get the campaign duration in days.
     */
    public function getDurationInDays(): int
    {
        return $this->start_date->diffInDays($this->end_date) + 1;
    }

    /**
     * Get the days remaining in the campaign.
     */
    public function getDaysRemaining(): int
    {
        if ($this->end_date->isPast()) {
            return 0;
        }

        return today()->diffInDays($this->end_date);
    }

    /**
     * Get the campaign progress percentage.
     */
    public function getProgressPercentage(): int
    {
        if ($this->start_date->isFuture()) {
            return 0;
        }

        if ($this->end_date->isPast()) {
            return 100;
        }

        $totalDays = $this->getDurationInDays();
        $daysPassed = $this->start_date->diffInDays(today()) + 1;

        return (int) round(($daysPassed / $totalDays) * 100);
    }

    /**
     * Get campaign statistics.
     */
    public function getStatistics(): array
    {
        $totalBuyers = $this->campaignBuyers()->count();
        
        if ($totalBuyers === 0) {
            return [
                'total_buyers' => 0,
                'contacted' => 0,
                'responded' => 0,
                'success' => 0,
                'contact_rate' => 0,
                'response_rate' => 0,
                'success_rate' => 0,
            ];
        }

        $contacted = $this->campaignBuyers()->whereNotNull('contacted_at')->count();
        $responded = $this->campaignBuyers()->whereNotNull('response_received_at')->count();
        $success = $this->campaignBuyers()->where('stage', 'Success')->count();

        return [
            'total_buyers' => $totalBuyers,
            'contacted' => $contacted,
            'responded' => $responded,
            'success' => $success,
            'contact_rate' => $totalBuyers > 0 ? round(($contacted / $totalBuyers) * 100, 2) : 0,
            'response_rate' => $contacted > 0 ? round(($responded / $contacted) * 100, 2) : 0,
            'success_rate' => $totalBuyers > 0 ? round(($success / $totalBuyers) * 100, 2) : 0,
        ];
    }

    /**
     * Get formatted budget.
     */
    public function getFormattedBudget(): string
    {
        if (!$this->budget) {
            return 'Not specified';
        }

        return '$' . number_format($this->budget, 2);
    }

    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory()
    {
        return \Database\Factories\BuyerCRM\MarketingCampaignFactory::new();
    }
}
