<?php

namespace App\Models\BuyerCRM;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * MarketingCampaignBuyer Model
 * 
 * Represents the relationship between marketing campaigns and buyers
 * 
 * @property int $id
 * @property int $campaign_id
 * @property int $buyer_id
 * @property string $stage
 * @property \Carbon\Carbon|null $contacted_at
 * @property \Carbon\Carbon|null $response_received_at
 * @property string|null $notes
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class MarketingCampaignBuyer extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'marketing_campaign_buyers';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'campaign_id',
        'buyer_id',
        'stage',
        'contacted_at',
        'response_received_at',
        'notes',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'contacted_at' => 'datetime',
        'response_received_at' => 'datetime',
    ];

    /**
     * Available campaign stages.
     *
     * @var array<string>
     */
    public static array $stages = [
        'Targeted',
        'Contacted',
        'Followed-up',
        'Interested',
        'Success',
        'No Response',
        'Declined'
    ];

    /**
     * Get the marketing campaign.
     */
    public function campaign(): BelongsTo
    {
        return $this->belongsTo(MarketingCampaign::class, 'campaign_id');
    }

    /**
     * Get the buyer.
     */
    public function buyer(): BelongsTo
    {
        return $this->belongsTo(BuyerProfile::class, 'buyer_id');
    }

    /**
     * Scope a query to filter by campaign.
     */
    public function scopeForCampaign($query, int $campaignId)
    {
        return $query->where('campaign_id', $campaignId);
    }

    /**
     * Scope a query to filter by buyer.
     */
    public function scopeForBuyer($query, int $buyerId)
    {
        return $query->where('buyer_id', $buyerId);
    }

    /**
     * Scope a query to filter by stage.
     */
    public function scopeInStage($query, string $stage)
    {
        return $query->where('stage', $stage);
    }

    /**
     * Scope a query to only include contacted buyers.
     */
    public function scopeContacted($query)
    {
        return $query->whereNotNull('contacted_at');
    }

    /**
     * Scope a query to only include buyers who responded.
     */
    public function scopeResponded($query)
    {
        return $query->whereNotNull('response_received_at');
    }

    /**
     * Scope a query to only include successful conversions.
     */
    public function scopeSuccess($query)
    {
        return $query->where('stage', 'Success');
    }

    /**
     * Scope a query to only include buyers with no response.
     */
    public function scopeNoResponse($query)
    {
        return $query->where('stage', 'No Response');
    }

    /**
     * Scope a query to only include declined buyers.
     */
    public function scopeDeclined($query)
    {
        return $query->where('stage', 'Declined');
    }

    /**
     * Get the stage color for UI display.
     */
    public function getStageColor(): string
    {
        return match ($this->stage) {
            'Targeted' => 'bg-gray-100 text-gray-800',
            'Contacted' => 'bg-blue-100 text-blue-800',
            'Followed-up' => 'bg-yellow-100 text-yellow-800',
            'Interested' => 'bg-purple-100 text-purple-800',
            'Success' => 'bg-green-100 text-green-800',
            'No Response' => 'bg-orange-100 text-orange-800',
            'Declined' => 'bg-red-100 text-red-800',
            default => 'bg-gray-100 text-gray-800',
        };
    }

    /**
     * Get the stage icon for UI display.
     */
    public function getStageIcon(): string
    {
        return match ($this->stage) {
            'Targeted' => 'fas fa-crosshairs',
            'Contacted' => 'fas fa-phone',
            'Followed-up' => 'fas fa-redo',
            'Interested' => 'fas fa-heart',
            'Success' => 'fas fa-check-circle',
            'No Response' => 'fas fa-clock',
            'Declined' => 'fas fa-times-circle',
            default => 'fas fa-question-circle',
        };
    }

    /**
     * Check if the buyer has been contacted.
     */
    public function isContacted(): bool
    {
        return $this->contacted_at !== null;
    }

    /**
     * Check if the buyer has responded.
     */
    public function hasResponded(): bool
    {
        return $this->response_received_at !== null;
    }

    /**
     * Check if this is a successful conversion.
     */
    public function isSuccess(): bool
    {
        return $this->stage === 'Success';
    }

    /**
     * Check if the buyer declined.
     */
    public function isDeclined(): bool
    {
        return $this->stage === 'Declined';
    }

    /**
     * Check if there's no response.
     */
    public function hasNoResponse(): bool
    {
        return $this->stage === 'No Response';
    }

    /**
     * Get the response time in days.
     */
    public function getResponseTimeInDays(): ?int
    {
        if (!$this->contacted_at || !$this->response_received_at) {
            return null;
        }

        return $this->contacted_at->diffInDays($this->response_received_at);
    }

    /**
     * Get the time since contact in days.
     */
    public function getDaysSinceContact(): ?int
    {
        if (!$this->contacted_at) {
            return null;
        }

        return $this->contacted_at->diffInDays(now());
    }

    /**
     * Mark as contacted.
     */
    public function markAsContacted(?string $notes = null): bool
    {
        return $this->update([
            'stage' => 'Contacted',
            'contacted_at' => now(),
            'notes' => $notes ?: $this->notes,
        ]);
    }

    /**
     * Mark as responded.
     */
    public function markAsResponded(?string $notes = null): bool
    {
        return $this->update([
            'stage' => 'Interested',
            'response_received_at' => now(),
            'notes' => $notes ?: $this->notes,
        ]);
    }

    /**
     * Mark as success.
     */
    public function markAsSuccess(?string $notes = null): bool
    {
        return $this->update([
            'stage' => 'Success',
            'notes' => $notes ?: $this->notes,
        ]);
    }

    /**
     * Mark as declined.
     */
    public function markAsDeclined(?string $notes = null): bool
    {
        return $this->update([
            'stage' => 'Declined',
            'notes' => $notes ?: $this->notes,
        ]);
    }

    /**
     * Mark as no response.
     */
    public function markAsNoResponse(?string $notes = null): bool
    {
        return $this->update([
            'stage' => 'No Response',
            'notes' => $notes ?: $this->notes,
        ]);
    }

    /**
     * Update stage with optional notes.
     */
    public function updateStage(string $stage, ?string $notes = null): bool
    {
        $updateData = ['stage' => $stage];

        if ($notes) {
            $updateData['notes'] = $notes;
        }

        // Set timestamps based on stage
        if ($stage === 'Contacted' && !$this->contacted_at) {
            $updateData['contacted_at'] = now();
        }

        if (in_array($stage, ['Interested', 'Success', 'Declined']) && !$this->response_received_at) {
            $updateData['response_received_at'] = now();
        }

        return $this->update($updateData);
    }

    /**
     * Get the next logical stage.
     */
    public function getNextStage(): ?string
    {
        return match ($this->stage) {
            'Targeted' => 'Contacted',
            'Contacted' => 'Followed-up',
            'Followed-up' => 'Interested',
            'Interested' => 'Success',
            default => null,
        };
    }

    /**
     * Check if stage can be advanced.
     */
    public function canAdvanceStage(): bool
    {
        return !in_array($this->stage, ['Success', 'No Response', 'Declined']);
    }

    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory()
    {
        return \Database\Factories\BuyerCRM\MarketingCampaignBuyerFactory::new();
    }
}
