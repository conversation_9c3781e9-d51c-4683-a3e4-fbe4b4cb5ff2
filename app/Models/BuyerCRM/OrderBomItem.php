<?php

namespace App\Models\BuyerCRM;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Models\User;
use App\Models\Party;

/**
 * OrderBomItem Model
 * 
 * Represents Bill of Materials items for garment orders
 * 
 * @property int $id
 * @property int $garment_order_id
 * @property int|null $supplier_id
 * @property int $created_by
 * @property string $item_name
 * @property string|null $description
 * @property string|null $item_code
 * @property string $item_category
 * @property string $unit
 * @property float $consumption_per_piece
 * @property float $rate_per_unit
 * @property float $total_cost_per_piece
 * @property float $wastage_percentage
 * @property float $final_cost_per_piece
 * @property string|null $color
 * @property string|null $size
 * @property string|null $specifications
 * @property string $status
 * @property bool $is_critical
 * @property \Carbon\Carbon|null $required_date
 * @property int $version
 * @property bool $is_current_version
 * @property float|null $order_quantity
 * @property float|null $received_quantity
 * @property \Carbon\Carbon|null $order_date
 * @property \Carbon\Carbon|null $delivery_date
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class OrderBomItem extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'order_bom_items';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'garment_order_id',
        'supplier_id',
        'created_by',
        'item_name',
        'description',
        'item_code',
        'item_category',
        'unit',
        'consumption_per_piece',
        'rate_per_unit',
        'total_cost_per_piece',
        'wastage_percentage',
        'final_cost_per_piece',
        'color',
        'size',
        'specifications',
        'status',
        'is_critical',
        'required_date',
        'version',
        'is_current_version',
        'order_quantity',
        'received_quantity',
        'order_date',
        'delivery_date',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'consumption_per_piece' => 'decimal:4',
        'rate_per_unit' => 'decimal:4',
        'total_cost_per_piece' => 'decimal:4',
        'wastage_percentage' => 'decimal:2',
        'final_cost_per_piece' => 'decimal:4',
        'is_critical' => 'boolean',
        'version' => 'integer',
        'is_current_version' => 'boolean',
        'order_quantity' => 'decimal:2',
        'received_quantity' => 'decimal:2',
        'required_date' => 'date',
        'order_date' => 'date',
        'delivery_date' => 'date',
    ];

    /**
     * Available item categories.
     *
     * @var array<string>
     */
    public static array $itemCategories = [
        'fabric',
        'trim',
        'accessory',
        'thread',
        'button',
        'zipper',
        'label',
        'packaging',
        'other'
    ];

    /**
     * Available statuses.
     *
     * @var array<string>
     */
    public static array $statuses = [
        'draft',
        'confirmed',
        'ordered',
        'received'
    ];

    /**
     * Common units of measurement.
     *
     * @var array<string>
     */
    public static array $units = [
        'pcs', 'yards', 'meters', 'kg', 'grams', 'dozen', 'sets', 'rolls', 'sheets'
    ];

    /**
     * Get the garment order.
     */
    public function garmentOrder(): BelongsTo
    {
        return $this->belongsTo(GarmentOrder::class, 'garment_order_id');
    }

    /**
     * Get the supplier.
     */
    public function supplier(): BelongsTo
    {
        return $this->belongsTo(Party::class, 'supplier_id');
    }

    /**
     * Get the user who created the BOM item.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Calculate total cost per piece.
     */
    public function calculateTotalCost(): void
    {
        $this->total_cost_per_piece = $this->consumption_per_piece * $this->rate_per_unit;
        $this->calculateFinalCost();
    }

    /**
     * Calculate final cost including wastage.
     */
    public function calculateFinalCost(): void
    {
        $wastageMultiplier = 1 + ($this->wastage_percentage / 100);
        $this->final_cost_per_piece = $this->total_cost_per_piece * $wastageMultiplier;
        $this->save();
    }

    /**
     * Calculate total order quantity needed.
     */
    public function calculateOrderQuantity(int $totalGarmentQuantity): float
    {
        $baseQuantity = $this->consumption_per_piece * $totalGarmentQuantity;
        $wastageMultiplier = 1 + ($this->wastage_percentage / 100);
        return $baseQuantity * $wastageMultiplier;
    }

    /**
     * Get status badge color.
     */
    public function getStatusBadgeColorAttribute(): string
    {
        return match($this->status) {
            'draft' => 'secondary',
            'confirmed' => 'primary',
            'ordered' => 'warning',
            'received' => 'success',
            default => 'secondary'
        };
    }

    /**
     * Get category badge color.
     */
    public function getCategoryBadgeColorAttribute(): string
    {
        return match($this->item_category) {
            'fabric' => 'primary',
            'trim' => 'info',
            'accessory' => 'warning',
            'thread' => 'secondary',
            'button' => 'success',
            'zipper' => 'dark',
            'label' => 'light',
            'packaging' => 'danger',
            default => 'secondary'
        };
    }

    /**
     * Check if item is overdue.
     */
    public function isOverdue(): bool
    {
        return $this->required_date && 
               $this->required_date->isPast() && 
               !in_array($this->status, ['received']);
    }

    /**
     * Get days until required.
     */
    public function daysUntilRequired(): ?int
    {
        return $this->required_date ? now()->diffInDays($this->required_date, false) : null;
    }

    /**
     * Get received percentage.
     */
    public function getReceivedPercentageAttribute(): float
    {
        if (!$this->order_quantity || $this->order_quantity == 0) {
            return 0;
        }
        return ($this->received_quantity / $this->order_quantity) * 100;
    }

    /**
     * Check if fully received.
     */
    public function isFullyReceived(): bool
    {
        return $this->order_quantity && 
               $this->received_quantity && 
               $this->received_quantity >= $this->order_quantity;
    }

    /**
     * Scope for current version only.
     */
    public function scopeCurrentVersion($query)
    {
        return $query->where('is_current_version', true);
    }

    /**
     * Scope for specific category.
     */
    public function scopeByCategory($query, string $category)
    {
        return $query->where('item_category', $category);
    }

    /**
     * Scope for critical items.
     */
    public function scopeCritical($query)
    {
        return $query->where('is_critical', true);
    }

    /**
     * Scope for overdue items.
     */
    public function scopeOverdue($query)
    {
        return $query->where('required_date', '<', now())
            ->whereNotIn('status', ['received']);
    }

    /**
     * Scope for specific status.
     */
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Get BOM summary for order.
     */
    public static function getBomSummary(int $garmentOrderId): array
    {
        $bomItems = static::where('garment_order_id', $garmentOrderId)
            ->currentVersion()
            ->get();

        $summary = [
            'total_items' => $bomItems->count(),
            'total_cost' => $bomItems->sum('final_cost_per_piece'),
            'by_category' => [],
            'critical_items' => $bomItems->where('is_critical', true)->count(),
            'overdue_items' => 0,
            'received_items' => $bomItems->where('status', 'received')->count(),
        ];

        // Group by category
        foreach (self::$itemCategories as $category) {
            $categoryItems = $bomItems->where('item_category', $category);
            if ($categoryItems->count() > 0) {
                $summary['by_category'][$category] = [
                    'count' => $categoryItems->count(),
                    'total_cost' => $categoryItems->sum('final_cost_per_piece'),
                ];
            }
        }

        // Count overdue items
        foreach ($bomItems as $item) {
            if ($item->isOverdue()) {
                $summary['overdue_items']++;
            }
        }

        return $summary;
    }

    /**
     * Create new version of BOM item.
     */
    public function createNewVersion(array $data): self
    {
        // Mark current version as not current
        $this->is_current_version = false;
        $this->save();

        // Create new version
        return static::create(array_merge($data, [
            'garment_order_id' => $this->garment_order_id,
            'version' => $this->version + 1,
            'is_current_version' => true,
        ]));
    }

    /**
     * Boot method to handle model events.
     */
    protected static function boot()
    {
        parent::boot();

        static::saving(function ($bomItem) {
            // Auto-calculate costs when saving
            if ($bomItem->isDirty(['consumption_per_piece', 'rate_per_unit', 'wastage_percentage'])) {
                $bomItem->calculateTotalCost();
            }
        });
    }
}
