<?php

namespace App\Models\BuyerCRM;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Models\User;

/**
 * OrderCosting Model
 * 
 * Represents costing calculations and pricing for garment orders
 * 
 * @property int $id
 * @property int $garment_order_id
 * @property int $created_by
 * @property int|null $updated_by
 * @property float|null $fabric_consumption
 * @property float|null $fabric_rate
 * @property float|null $fabric_cost
 * @property float $fabric_wastage_percentage
 * @property float|null $fabric_final_cost
 * @property float $trims_cost
 * @property float $accessories_cost
 * @property float $total_material_cost
 * @property float|null $cm_cost
 * @property float|null $washing_cost
 * @property float|null $finishing_cost
 * @property float|null $packing_cost
 * @property float $total_manufacturing_cost
 * @property float|null $testing_cost
 * @property float|null $inspection_cost
 * @property float|null $freight_cost
 * @property float|null $other_costs
 * @property float $total_additional_costs
 * @property float $total_cost_per_piece
 * @property float|null $profit_margin_percentage
 * @property float|null $profit_amount
 * @property float|null $calculated_unit_price
 * @property float|null $quoted_price
 * @property float|null $final_negotiated_price
 * @property float|null $actual_margin_percentage
 * @property float|null $actual_margin_amount
 * @property string $currency
 * @property float $exchange_rate
 * @property string $status
 * @property \Carbon\Carbon|null $approved_date
 * @property int|null $approved_by
 * @property string|null $approval_comments
 * @property int $version
 * @property bool $is_current_version
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class OrderCosting extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'order_costing';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'garment_order_id',
        'created_by',
        'updated_by',
        'fabric_consumption',
        'fabric_rate',
        'fabric_cost',
        'fabric_wastage_percentage',
        'fabric_final_cost',
        'trims_cost',
        'accessories_cost',
        'total_material_cost',
        'cm_cost',
        'washing_cost',
        'finishing_cost',
        'packing_cost',
        'total_manufacturing_cost',
        'testing_cost',
        'inspection_cost',
        'freight_cost',
        'other_costs',
        'total_additional_costs',
        'total_cost_per_piece',
        'profit_margin_percentage',
        'profit_amount',
        'calculated_unit_price',
        'quoted_price',
        'final_negotiated_price',
        'actual_margin_percentage',
        'actual_margin_amount',
        'currency',
        'exchange_rate',
        'status',
        'approved_date',
        'approved_by',
        'approval_comments',
        'version',
        'is_current_version',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'fabric_consumption' => 'decimal:4',
        'fabric_rate' => 'decimal:4',
        'fabric_cost' => 'decimal:4',
        'fabric_wastage_percentage' => 'decimal:2',
        'fabric_final_cost' => 'decimal:4',
        'trims_cost' => 'decimal:4',
        'accessories_cost' => 'decimal:4',
        'total_material_cost' => 'decimal:4',
        'cm_cost' => 'decimal:4',
        'washing_cost' => 'decimal:4',
        'finishing_cost' => 'decimal:4',
        'packing_cost' => 'decimal:4',
        'total_manufacturing_cost' => 'decimal:4',
        'testing_cost' => 'decimal:4',
        'inspection_cost' => 'decimal:4',
        'freight_cost' => 'decimal:4',
        'other_costs' => 'decimal:4',
        'total_additional_costs' => 'decimal:4',
        'total_cost_per_piece' => 'decimal:4',
        'profit_margin_percentage' => 'decimal:2',
        'profit_amount' => 'decimal:4',
        'calculated_unit_price' => 'decimal:4',
        'quoted_price' => 'decimal:4',
        'final_negotiated_price' => 'decimal:4',
        'actual_margin_percentage' => 'decimal:2',
        'actual_margin_amount' => 'decimal:4',
        'exchange_rate' => 'decimal:4',
        'version' => 'integer',
        'is_current_version' => 'boolean',
        'approved_date' => 'date',
    ];

    /**
     * Available statuses.
     *
     * @var array<string>
     */
    public static array $statuses = [
        'draft',
        'pending_approval',
        'approved',
        'rejected'
    ];

    /**
     * Available currencies.
     *
     * @var array<string>
     */
    public static array $currencies = [
        'USD', 'EUR', 'GBP', 'BDT', 'INR', 'CNY'
    ];

    /**
     * Get the garment order.
     */
    public function garmentOrder(): BelongsTo
    {
        return $this->belongsTo(GarmentOrder::class, 'garment_order_id');
    }

    /**
     * Get the user who created the costing.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who last updated the costing.
     */
    public function updater(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Get the user who approved the costing.
     */
    public function approver(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Calculate fabric final cost including wastage.
     */
    public function calculateFabricFinalCost(): void
    {
        if ($this->fabric_cost) {
            $wastageMultiplier = 1 + ($this->fabric_wastage_percentage / 100);
            $this->fabric_final_cost = $this->fabric_cost * $wastageMultiplier;
        }
    }

    /**
     * Calculate total material cost.
     */
    public function calculateTotalMaterialCost(): void
    {
        $this->total_material_cost = ($this->fabric_final_cost ?? 0) + 
                                   ($this->trims_cost ?? 0) + 
                                   ($this->accessories_cost ?? 0);
    }

    /**
     * Calculate total manufacturing cost.
     */
    public function calculateTotalManufacturingCost(): void
    {
        $this->total_manufacturing_cost = ($this->cm_cost ?? 0) + 
                                        ($this->washing_cost ?? 0) + 
                                        ($this->finishing_cost ?? 0) + 
                                        ($this->packing_cost ?? 0);
    }

    /**
     * Calculate total additional costs.
     */
    public function calculateTotalAdditionalCosts(): void
    {
        $this->total_additional_costs = ($this->testing_cost ?? 0) + 
                                      ($this->inspection_cost ?? 0) + 
                                      ($this->freight_cost ?? 0) + 
                                      ($this->other_costs ?? 0);
    }

    /**
     * Calculate total cost per piece.
     */
    public function calculateTotalCostPerPiece(): void
    {
        $this->total_cost_per_piece = $this->total_material_cost + 
                                    $this->total_manufacturing_cost + 
                                    $this->total_additional_costs;
    }

    /**
     * Calculate profit amount and unit price.
     */
    public function calculateProfitAndPrice(): void
    {
        if ($this->profit_margin_percentage && $this->total_cost_per_piece) {
            $this->profit_amount = ($this->total_cost_per_piece * $this->profit_margin_percentage) / 100;
            $this->calculated_unit_price = $this->total_cost_per_piece + $this->profit_amount;
        }
    }

    /**
     * Calculate actual margin based on final negotiated price.
     */
    public function calculateActualMargin(): void
    {
        if ($this->final_negotiated_price && $this->total_cost_per_piece) {
            $this->actual_margin_amount = $this->final_negotiated_price - $this->total_cost_per_piece;
            $this->actual_margin_percentage = ($this->actual_margin_amount / $this->total_cost_per_piece) * 100;
        }
    }

    /**
     * Recalculate all costs and pricing.
     */
    public function recalculateAll(): void
    {
        $this->calculateFabricFinalCost();
        $this->updateBomCosts();
        $this->calculateTotalMaterialCost();
        $this->calculateTotalManufacturingCost();
        $this->calculateTotalAdditionalCosts();
        $this->calculateTotalCostPerPiece();
        $this->calculateProfitAndPrice();
        $this->calculateActualMargin();
        $this->save();
    }

    /**
     * Update BOM costs from related BOM items.
     */
    public function updateBomCosts(): void
    {
        $bomItems = OrderBomItem::where('garment_order_id', $this->garment_order_id)
            ->currentVersion()
            ->get();

        $this->trims_cost = $bomItems->whereIn('item_category', ['trim', 'thread', 'button', 'zipper', 'label'])
            ->sum('final_cost_per_piece');

        $this->accessories_cost = $bomItems->whereIn('item_category', ['accessory', 'packaging'])
            ->sum('final_cost_per_piece');
    }

    /**
     * Approve costing.
     */
    public function approve(int $approvedBy, string $comments = null): void
    {
        $this->status = 'approved';
        $this->approved_date = now();
        $this->approved_by = $approvedBy;
        $this->approval_comments = $comments;
        $this->save();

        // Update garment order pricing
        $this->garmentOrder->update([
            'unit_price' => $this->calculated_unit_price,
            'final_price' => $this->final_negotiated_price ?? $this->calculated_unit_price,
        ]);
        $this->garmentOrder->calculateTotalValue();
    }

    /**
     * Reject costing.
     */
    public function reject(int $rejectedBy, string $comments = null): void
    {
        $this->status = 'rejected';
        $this->approved_date = now();
        $this->approved_by = $rejectedBy;
        $this->approval_comments = $comments;
        $this->save();
    }

    /**
     * Get status badge color.
     */
    public function getStatusBadgeColorAttribute(): string
    {
        return match($this->status) {
            'draft' => 'secondary',
            'pending_approval' => 'warning',
            'approved' => 'success',
            'rejected' => 'danger',
            default => 'secondary'
        };
    }

    /**
     * Get formatted total cost.
     */
    public function getFormattedTotalCostAttribute(): string
    {
        return $this->currency . ' ' . number_format($this->total_cost_per_piece, 2);
    }

    /**
     * Get formatted calculated price.
     */
    public function getFormattedCalculatedPriceAttribute(): string
    {
        return $this->calculated_unit_price ? 
            $this->currency . ' ' . number_format($this->calculated_unit_price, 2) : 'N/A';
    }

    /**
     * Get formatted final price.
     */
    public function getFormattedFinalPriceAttribute(): string
    {
        return $this->final_negotiated_price ? 
            $this->currency . ' ' . number_format($this->final_negotiated_price, 2) : 'N/A';
    }

    /**
     * Scope for current version only.
     */
    public function scopeCurrentVersion($query)
    {
        return $query->where('is_current_version', true);
    }

    /**
     * Scope for approved costings.
     */
    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    /**
     * Create new version of costing.
     */
    public function createNewVersion(array $data): self
    {
        // Mark current version as not current
        $this->is_current_version = false;
        $this->save();

        // Create new version
        return static::create(array_merge($data, [
            'garment_order_id' => $this->garment_order_id,
            'version' => $this->version + 1,
            'is_current_version' => true,
            'status' => 'draft',
        ]));
    }

    /**
     * Boot method to handle model events.
     */
    protected static function boot()
    {
        parent::boot();

        static::saving(function ($costing) {
            // Auto-recalculate when certain fields change
            if ($costing->isDirty([
                'fabric_consumption', 'fabric_rate', 'fabric_wastage_percentage',
                'cm_cost', 'washing_cost', 'finishing_cost', 'packing_cost',
                'testing_cost', 'inspection_cost', 'freight_cost', 'other_costs',
                'profit_margin_percentage', 'final_negotiated_price'
            ])) {
                $costing->recalculateAll();
            }
        });
    }
}
