<?php

namespace App\Models\BuyerCRM;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Models\User;
use Carbon\Carbon;

/**
 * OrderDeliveryBatch Model
 * 
 * Represents delivery batches and shipment tracking for garment orders
 * 
 * @property int $id
 * @property int $garment_order_id
 * @property int $created_by
 * @property string $batch_no
 * @property int $batch_sequence
 * @property \Carbon\Carbon $delivery_date
 * @property int $quantity
 * @property float|null $batch_value
 * @property string|null $port_of_loading
 * @property string|null $port_of_discharge
 * @property string $shipping_mode
 * @property string|null $shipping_line
 * @property string|null $vessel_name
 * @property \Carbon\Carbon|null $etd
 * @property \Carbon\Carbon|null $eta
 * @property string|null $invoice_no
 * @property string|null $packing_list_no
 * @property string|null $bl_no
 * @property string|null $container_no
 * @property string|null $seal_no
 * @property string $production_status
 * @property string $shipment_status
 * @property \Carbon\Carbon|null $production_start_date
 * @property \Carbon\Carbon|null $production_completion_date
 * @property \Carbon\Carbon|null $actual_shipment_date
 * @property \Carbon\Carbon|null $actual_delivery_date
 * @property bool $quality_approved
 * @property \Carbon\Carbon|null $quality_approval_date
 * @property int|null $quality_approved_by
 * @property string|null $quality_comments
 * @property string|null $packing_instructions
 * @property string|null $shipping_instructions
 * @property string|null $delivery_instructions
 * @property string|null $remarks
 * @property bool $has_delay
 * @property string|null $delay_reason
 * @property \Carbon\Carbon|null $revised_delivery_date
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class OrderDeliveryBatch extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'order_delivery_batches';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'garment_order_id',
        'created_by',
        'batch_no',
        'batch_sequence',
        'delivery_date',
        'quantity',
        'batch_value',
        'port_of_loading',
        'port_of_discharge',
        'shipping_mode',
        'shipping_line',
        'vessel_name',
        'etd',
        'eta',
        'invoice_no',
        'packing_list_no',
        'bl_no',
        'container_no',
        'seal_no',
        'production_status',
        'shipment_status',
        'production_start_date',
        'production_completion_date',
        'actual_shipment_date',
        'actual_delivery_date',
        'quality_approved',
        'quality_approval_date',
        'quality_approved_by',
        'quality_comments',
        'packing_instructions',
        'shipping_instructions',
        'delivery_instructions',
        'remarks',
        'has_delay',
        'delay_reason',
        'revised_delivery_date',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'batch_sequence' => 'integer',
        'quantity' => 'integer',
        'batch_value' => 'decimal:2',
        'delivery_date' => 'date',
        'etd' => 'date',
        'eta' => 'date',
        'production_start_date' => 'date',
        'production_completion_date' => 'date',
        'actual_shipment_date' => 'date',
        'actual_delivery_date' => 'date',
        'quality_approved' => 'boolean',
        'quality_approval_date' => 'date',
        'has_delay' => 'boolean',
        'revised_delivery_date' => 'date',
    ];

    /**
     * Available production statuses.
     *
     * @var array<string>
     */
    public static array $productionStatuses = [
        'not_started',
        'cutting',
        'sewing',
        'finishing',
        'packing',
        'ready'
    ];

    /**
     * Available shipment statuses.
     *
     * @var array<string>
     */
    public static array $shipmentStatuses = [
        'pending',
        'ready_to_ship',
        'shipped',
        'in_transit',
        'delivered'
    ];

    /**
     * Available shipping modes.
     *
     * @var array<string>
     */
    public static array $shippingModes = [
        'Sea',
        'Air',
        'Road',
        'Rail',
        'Courier'
    ];

    /**
     * Get the garment order.
     */
    public function garmentOrder(): BelongsTo
    {
        return $this->belongsTo(GarmentOrder::class, 'garment_order_id');
    }

    /**
     * Get the user who created the batch.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who approved quality.
     */
    public function qualityApprover(): BelongsTo
    {
        return $this->belongsTo(User::class, 'quality_approved_by');
    }

    /**
     * Check if batch is overdue.
     */
    public function isOverdue(): bool
    {
        $targetDate = $this->has_delay && $this->revised_delivery_date 
            ? $this->revised_delivery_date 
            : $this->delivery_date;
            
        return $targetDate->isPast() && !in_array($this->shipment_status, ['delivered']);
    }

    /**
     * Get days until delivery.
     */
    public function daysUntilDelivery(): int
    {
        $targetDate = $this->has_delay && $this->revised_delivery_date 
            ? $this->revised_delivery_date 
            : $this->delivery_date;
            
        return now()->diffInDays($targetDate, false);
    }

    /**
     * Get production status badge color.
     */
    public function getProductionStatusBadgeColorAttribute(): string
    {
        return match($this->production_status) {
            'not_started' => 'secondary',
            'cutting' => 'info',
            'sewing' => 'primary',
            'finishing' => 'warning',
            'packing' => 'dark',
            'ready' => 'success',
            default => 'secondary'
        };
    }

    /**
     * Get shipment status badge color.
     */
    public function getShipmentStatusBadgeColorAttribute(): string
    {
        return match($this->shipment_status) {
            'pending' => 'secondary',
            'ready_to_ship' => 'info',
            'shipped' => 'primary',
            'in_transit' => 'warning',
            'delivered' => 'success',
            default => 'secondary'
        };
    }

    /**
     * Get effective delivery date (revised if delayed).
     */
    public function getEffectiveDeliveryDateAttribute(): Carbon
    {
        return $this->has_delay && $this->revised_delivery_date 
            ? $this->revised_delivery_date 
            : $this->delivery_date;
    }

    /**
     * Get formatted batch value.
     */
    public function getFormattedBatchValueAttribute(): string
    {
        return $this->batch_value ? '$' . number_format($this->batch_value, 2) : 'N/A';
    }

    /**
     * Calculate batch value based on order pricing.
     */
    public function calculateBatchValue(): void
    {
        $order = $this->garmentOrder;
        if ($order && $order->final_price) {
            $this->batch_value = $order->final_price * $this->quantity;
            $this->save();
        }
    }

    /**
     * Start production for this batch.
     */
    public function startProduction(): void
    {
        $this->production_status = 'cutting';
        $this->production_start_date = now();
        $this->save();
    }

    /**
     * Complete production for this batch.
     */
    public function completeProduction(): void
    {
        $this->production_status = 'ready';
        $this->production_completion_date = now();
        $this->save();
    }

    /**
     * Approve quality for this batch.
     */
    public function approveQuality(int $approvedBy, string $comments = null): void
    {
        $this->quality_approved = true;
        $this->quality_approval_date = now();
        $this->quality_approved_by = $approvedBy;
        $this->quality_comments = $comments;
        
        // Auto-update shipment status if ready
        if ($this->production_status === 'ready') {
            $this->shipment_status = 'ready_to_ship';
        }
        
        $this->save();
    }

    /**
     * Ship the batch.
     */
    public function ship(): void
    {
        $this->shipment_status = 'shipped';
        $this->actual_shipment_date = now();
        $this->save();
    }

    /**
     * Mark batch as delivered.
     */
    public function markDelivered(): void
    {
        $this->shipment_status = 'delivered';
        $this->actual_delivery_date = now();
        $this->save();
    }

    /**
     * Add delay to batch.
     */
    public function addDelay(string $reason, Carbon $newDeliveryDate): void
    {
        $this->has_delay = true;
        $this->delay_reason = $reason;
        $this->revised_delivery_date = $newDeliveryDate;
        $this->save();
    }

    /**
     * Remove delay from batch.
     */
    public function removeDelay(): void
    {
        $this->has_delay = false;
        $this->delay_reason = null;
        $this->revised_delivery_date = null;
        $this->save();
    }

    /**
     * Get production progress percentage.
     */
    public function getProductionProgressAttribute(): int
    {
        return match($this->production_status) {
            'not_started' => 0,
            'cutting' => 20,
            'sewing' => 50,
            'finishing' => 80,
            'packing' => 90,
            'ready' => 100,
            default => 0
        };
    }

    /**
     * Scope for overdue batches.
     */
    public function scopeOverdue($query)
    {
        return $query->where(function ($q) {
            $q->where('delivery_date', '<', now())
              ->orWhere('revised_delivery_date', '<', now());
        })->whereNotIn('shipment_status', ['delivered']);
    }

    /**
     * Scope for ready to ship batches.
     */
    public function scopeReadyToShip($query)
    {
        return $query->where('shipment_status', 'ready_to_ship');
    }

    /**
     * Scope for specific production status.
     */
    public function scopeByProductionStatus($query, string $status)
    {
        return $query->where('production_status', $status);
    }

    /**
     * Scope for specific shipment status.
     */
    public function scopeByShipmentStatus($query, string $status)
    {
        return $query->where('shipment_status', $status);
    }

    /**
     * Scope for quality approved batches.
     */
    public function scopeQualityApproved($query)
    {
        return $query->where('quality_approved', true);
    }

    /**
     * Get delivery summary for order.
     */
    public static function getDeliverySummary(int $garmentOrderId): array
    {
        $batches = static::where('garment_order_id', $garmentOrderId)
            ->orderBy('batch_sequence')
            ->get();

        return [
            'total_batches' => $batches->count(),
            'total_quantity' => $batches->sum('quantity'),
            'total_value' => $batches->sum('batch_value'),
            'completed_batches' => $batches->where('shipment_status', 'delivered')->count(),
            'overdue_batches' => $batches->filter(fn($batch) => $batch->isOverdue())->count(),
            'ready_to_ship' => $batches->where('shipment_status', 'ready_to_ship')->count(),
            'in_production' => $batches->whereNotIn('production_status', ['not_started', 'ready'])->count(),
            'next_delivery' => $batches->where('shipment_status', '!=', 'delivered')
                ->sortBy('effective_delivery_date')
                ->first()?->effective_delivery_date,
        ];
    }

    /**
     * Auto-generate batch number.
     */
    public static function generateBatchNumber(int $garmentOrderId): string
    {
        $order = GarmentOrder::find($garmentOrderId);
        $lastBatch = static::where('garment_order_id', $garmentOrderId)
            ->orderBy('batch_sequence', 'desc')
            ->first();
        
        $sequence = $lastBatch ? $lastBatch->batch_sequence + 1 : 1;
        
        return $order->order_no . '-B' . str_pad($sequence, 2, '0', STR_PAD_LEFT);
    }
}
