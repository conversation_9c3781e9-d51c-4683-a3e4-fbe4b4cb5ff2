<?php

namespace App\Models\BuyerCRM;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Models\User;

/**
 * OrderProductionChecklist Model
 * 
 * Represents production readiness checklist for garment orders
 * 
 * @property int $id
 * @property int $garment_order_id
 * @property int $created_by
 * @property int|null $updated_by
 * @property bool $tech_pack_uploaded
 * @property \Carbon\Carbon|null $tech_pack_uploaded_at
 * @property int|null $tech_pack_uploaded_by
 * @property bool $tech_pack_approved
 * @property \Carbon\Carbon|null $tech_pack_approved_at
 * @property int|null $tech_pack_approved_by
 * @property bool $fit_sample_approved
 * @property \Carbon\Carbon|null $fit_sample_approved_at
 * @property bool $pp_sample_approved
 * @property \Carbon\Carbon|null $pp_sample_approved_at
 * @property bool $top_sample_approved
 * @property \Carbon\Carbon|null $top_sample_approved_at
 * @property bool $all_samples_approved
 * @property bool $bom_finalized
 * @property \Carbon\Carbon|null $bom_finalized_at
 * @property int|null $bom_finalized_by
 * @property bool $costing_approved
 * @property \Carbon\Carbon|null $costing_approved_at
 * @property int|null $costing_approved_by
 * @property bool $fabric_booked
 * @property \Carbon\Carbon|null $fabric_booked_at
 * @property int|null $fabric_booked_by
 * @property bool $trims_ordered
 * @property \Carbon\Carbon|null $trims_ordered_at
 * @property bool $materials_received
 * @property \Carbon\Carbon|null $materials_received_at
 * @property bool $marker_ready
 * @property \Carbon\Carbon|null $marker_ready_at
 * @property int|null $marker_ready_by
 * @property bool $cutting_plan_ready
 * @property \Carbon\Carbon|null $cutting_plan_ready_at
 * @property bool $production_line_allocated
 * @property \Carbon\Carbon|null $production_line_allocated_at
 * @property bool $quality_standards_defined
 * @property \Carbon\Carbon|null $quality_standards_defined_at
 * @property bool $sop_compliance_verified
 * @property \Carbon\Carbon|null $sop_compliance_verified_at
 * @property bool $testing_requirements_met
 * @property \Carbon\Carbon|null $testing_requirements_met_at
 * @property bool $production_approved
 * @property \Carbon\Carbon|null $production_approved_at
 * @property int|null $production_approved_by
 * @property bool $production_locked
 * @property \Carbon\Carbon|null $production_locked_at
 * @property float $completion_percentage
 * @property bool $ready_for_production
 * @property string $status
 * @property string|null $production_notes
 * @property string|null $quality_notes
 * @property string|null $material_notes
 * @property string|null $general_comments
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class OrderProductionChecklist extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'order_production_checklist';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'garment_order_id',
        'created_by',
        'updated_by',
        'tech_pack_uploaded',
        'tech_pack_uploaded_at',
        'tech_pack_uploaded_by',
        'tech_pack_approved',
        'tech_pack_approved_at',
        'tech_pack_approved_by',
        'fit_sample_approved',
        'fit_sample_approved_at',
        'pp_sample_approved',
        'pp_sample_approved_at',
        'top_sample_approved',
        'top_sample_approved_at',
        'all_samples_approved',
        'bom_finalized',
        'bom_finalized_at',
        'bom_finalized_by',
        'costing_approved',
        'costing_approved_at',
        'costing_approved_by',
        'fabric_booked',
        'fabric_booked_at',
        'fabric_booked_by',
        'trims_ordered',
        'trims_ordered_at',
        'materials_received',
        'materials_received_at',
        'marker_ready',
        'marker_ready_at',
        'marker_ready_by',
        'cutting_plan_ready',
        'cutting_plan_ready_at',
        'production_line_allocated',
        'production_line_allocated_at',
        'quality_standards_defined',
        'quality_standards_defined_at',
        'sop_compliance_verified',
        'sop_compliance_verified_at',
        'testing_requirements_met',
        'testing_requirements_met_at',
        'production_approved',
        'production_approved_at',
        'production_approved_by',
        'production_locked',
        'production_locked_at',
        'completion_percentage',
        'ready_for_production',
        'status',
        'production_notes',
        'quality_notes',
        'material_notes',
        'general_comments',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'tech_pack_uploaded' => 'boolean',
        'tech_pack_uploaded_at' => 'datetime',
        'tech_pack_approved' => 'boolean',
        'tech_pack_approved_at' => 'datetime',
        'fit_sample_approved' => 'boolean',
        'fit_sample_approved_at' => 'datetime',
        'pp_sample_approved' => 'boolean',
        'pp_sample_approved_at' => 'datetime',
        'top_sample_approved' => 'boolean',
        'top_sample_approved_at' => 'datetime',
        'all_samples_approved' => 'boolean',
        'bom_finalized' => 'boolean',
        'bom_finalized_at' => 'datetime',
        'costing_approved' => 'boolean',
        'costing_approved_at' => 'datetime',
        'fabric_booked' => 'boolean',
        'fabric_booked_at' => 'datetime',
        'trims_ordered' => 'boolean',
        'trims_ordered_at' => 'datetime',
        'materials_received' => 'boolean',
        'materials_received_at' => 'datetime',
        'marker_ready' => 'boolean',
        'marker_ready_at' => 'datetime',
        'cutting_plan_ready' => 'boolean',
        'cutting_plan_ready_at' => 'datetime',
        'production_line_allocated' => 'boolean',
        'production_line_allocated_at' => 'datetime',
        'quality_standards_defined' => 'boolean',
        'quality_standards_defined_at' => 'datetime',
        'sop_compliance_verified' => 'boolean',
        'sop_compliance_verified_at' => 'datetime',
        'testing_requirements_met' => 'boolean',
        'testing_requirements_met_at' => 'datetime',
        'production_approved' => 'boolean',
        'production_approved_at' => 'datetime',
        'production_locked' => 'boolean',
        'production_locked_at' => 'datetime',
        'completion_percentage' => 'decimal:2',
        'ready_for_production' => 'boolean',
    ];

    /**
     * Available statuses.
     *
     * @var array<string>
     */
    public static array $statuses = [
        'not_started',
        'in_progress',
        'completed',
        'approved'
    ];

    /**
     * Checklist items with their weights for completion calculation.
     *
     * @var array<string, int>
     */
    public static array $checklistItems = [
        'tech_pack_uploaded' => 8,
        'tech_pack_approved' => 8,
        'fit_sample_approved' => 6,
        'pp_sample_approved' => 6,
        'top_sample_approved' => 6,
        'bom_finalized' => 10,
        'costing_approved' => 8,
        'fabric_booked' => 10,
        'trims_ordered' => 8,
        'materials_received' => 10,
        'marker_ready' => 6,
        'cutting_plan_ready' => 4,
        'production_line_allocated' => 4,
        'quality_standards_defined' => 3,
        'sop_compliance_verified' => 3,
    ];

    /**
     * Get the garment order.
     */
    public function garmentOrder(): BelongsTo
    {
        return $this->belongsTo(GarmentOrder::class, 'garment_order_id');
    }

    /**
     * Get the user who created the checklist.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who last updated the checklist.
     */
    public function updater(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Calculate completion percentage based on checklist items.
     */
    public function calculateCompletionPercentage(): void
    {
        $totalWeight = array_sum(self::$checklistItems);
        $completedWeight = 0;

        foreach (self::$checklistItems as $item => $weight) {
            if ($this->$item) {
                $completedWeight += $weight;
            }
        }

        $this->completion_percentage = ($completedWeight / $totalWeight) * 100;
        
        // Update status based on completion
        if ($this->completion_percentage == 0) {
            $this->status = 'not_started';
        } elseif ($this->completion_percentage < 100) {
            $this->status = 'in_progress';
        } else {
            $this->status = 'completed';
        }

        // Check if ready for production
        $this->ready_for_production = $this->completion_percentage >= 95 &&
                                    $this->tech_pack_approved &&
                                    $this->bom_finalized &&
                                    $this->costing_approved;

        // Note: Don't call save() here as this method is called from the saving event
    }

    /**
     * Update tech pack status from related tech pack files.
     */
    public function updateTechPackStatus(): void
    {
        $techPackStatus = OrderTechPack::getTechPackStatus($this->garment_order_id);
        
        $this->tech_pack_uploaded = $techPackStatus['tech_pack_uploaded'];
        if ($this->tech_pack_uploaded && !$this->tech_pack_uploaded_at) {
            $this->tech_pack_uploaded_at = now();
        }

        $this->fit_sample_approved = $techPackStatus['fit_sample_approved'];
        $this->pp_sample_approved = $techPackStatus['pp_sample_approved'];
        $this->top_sample_approved = $techPackStatus['top_sample_approved'];
        $this->all_samples_approved = $techPackStatus['all_samples_approved'];

        $this->calculateCompletionPercentage();
    }

    /**
     * Update BOM status from related BOM items.
     */
    public function updateBomStatus(): void
    {
        $bomSummary = OrderBomItem::getBomSummary($this->garment_order_id);
        
        // Consider BOM finalized if there are items and no critical overdue items
        $this->bom_finalized = $bomSummary['total_items'] > 0 && $bomSummary['overdue_items'] == 0;
        
        if ($this->bom_finalized && !$this->bom_finalized_at) {
            $this->bom_finalized_at = now();
        }

        $this->calculateCompletionPercentage();
    }

    /**
     * Update costing status from related costing.
     */
    public function updateCostingStatus(): void
    {
        $costing = OrderCosting::where('garment_order_id', $this->garment_order_id)
            ->currentVersion()
            ->first();
        
        $this->costing_approved = $costing && $costing->status === 'approved';
        
        if ($this->costing_approved && !$this->costing_approved_at) {
            $this->costing_approved_at = now();
            $this->costing_approved_by = $costing->approved_by;
        }

        $this->calculateCompletionPercentage();
    }

    /**
     * Update SOP compliance status.
     */
    public function updateSopComplianceStatus(): void
    {
        $sopSummary = OrderSopCompliance::getComplianceSummary($this->garment_order_id);
        
        // Consider SOP verified if all items are completed/verified
        $this->sop_compliance_verified = $sopSummary['total_items'] > 0 && 
                                       $sopSummary['completed_items'] == $sopSummary['total_items'];
        
        if ($this->sop_compliance_verified && !$this->sop_compliance_verified_at) {
            $this->sop_compliance_verified_at = now();
        }

        $this->calculateCompletionPercentage();
    }

    /**
     * Mark item as completed.
     */
    public function markItemCompleted(string $item, int $userId = null): void
    {
        if (array_key_exists($item, self::$checklistItems)) {
            $this->$item = true;
            
            $timestampField = $item . '_at';
            $userField = $item . '_by';
            
            if (!$this->$timestampField) {
                $this->$timestampField = now();
            }
            
            if ($userId && in_array($userField, $this->fillable)) {
                $this->$userField = $userId;
            }
            
            $this->calculateCompletionPercentage();
            $this->save();
        }
    }

    /**
     * Mark item as not completed.
     */
    public function markItemNotCompleted(string $item): void
    {
        if (array_key_exists($item, self::$checklistItems)) {
            $this->$item = false;
            
            $timestampField = $item . '_at';
            $userField = $item . '_by';
            
            $this->$timestampField = null;
            
            if (in_array($userField, $this->fillable)) {
                $this->$userField = null;
            }
            
            $this->calculateCompletionPercentage();
            $this->save();
        }
    }

    /**
     * Approve production.
     */
    public function approveProduction(int $approvedBy, string $comments = null): void
    {
        $this->production_approved = true;
        $this->production_approved_at = now();
        $this->production_approved_by = $approvedBy;
        $this->status = 'approved';
        
        if ($comments) {
            $this->general_comments = $comments;
        }

        // Update garment order status
        $this->garmentOrder->update([
            'production_ready' => true,
            'status' => 'Confirmed'
        ]);

        $this->save();
    }

    /**
     * Lock production (prevent further changes).
     */
    public function lockProduction(): void
    {
        $this->production_locked = true;
        $this->production_locked_at = now();
        $this->save();
    }

    /**
     * Get status badge color.
     */
    public function getStatusBadgeColorAttribute(): string
    {
        return match($this->status) {
            'not_started' => 'secondary',
            'in_progress' => 'primary',
            'completed' => 'info',
            'approved' => 'success',
            default => 'secondary'
        };
    }

    /**
     * Get completion progress bar color.
     */
    public function getProgressBarColorAttribute(): string
    {
        if ($this->completion_percentage < 30) return 'danger';
        if ($this->completion_percentage < 70) return 'warning';
        if ($this->completion_percentage < 95) return 'info';
        return 'success';
    }

    /**
     * Get checklist items with their status.
     */
    public function getChecklistItemsStatusAttribute(): array
    {
        $items = [];
        
        foreach (self::$checklistItems as $item => $weight) {
            $items[] = [
                'name' => $item,
                'label' => ucwords(str_replace('_', ' ', $item)),
                'completed' => $this->$item,
                'weight' => $weight,
                'timestamp' => $this->{$item . '_at'},
                'user' => $this->{$item . '_by'} ?? null,
            ];
        }
        
        return $items;
    }

    /**
     * Refresh all statuses from related models.
     */
    public function refreshAllStatuses(): void
    {
        $this->updateTechPackStatus();
        $this->updateBomStatus();
        $this->updateCostingStatus();
        $this->updateSopComplianceStatus();
    }

    /**
     * Scope for production ready orders.
     */
    public function scopeProductionReady($query)
    {
        return $query->where('ready_for_production', true);
    }

    /**
     * Scope for approved production.
     */
    public function scopeProductionApproved($query)
    {
        return $query->where('production_approved', true);
    }

    /**
     * Boot method to handle model events.
     */
    protected static function boot()
    {
        parent::boot();

        static::saving(function ($checklist) {
            // Auto-calculate completion percentage when saving
            $checklist->calculateCompletionPercentage();
        });
    }
}
