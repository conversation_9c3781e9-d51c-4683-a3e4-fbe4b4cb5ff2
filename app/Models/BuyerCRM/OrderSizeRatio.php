<?php

namespace App\Models\BuyerCRM;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * OrderSizeRatio Model
 * 
 * Represents size breakdown and quantities for garment orders
 * 
 * @property int $id
 * @property int $garment_order_id
 * @property string $size_label
 * @property string $size_type
 * @property int $sort_order
 * @property int $ratio
 * @property int $calculated_quantity
 * @property int|null $manual_quantity
 * @property int $final_quantity
 * @property bool $is_manual_override
 * @property string|null $notes
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class OrderSizeRatio extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'order_size_ratios';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'garment_order_id',
        'size_label',
        'size_type',
        'sort_order',
        'ratio',
        'calculated_quantity',
        'manual_quantity',
        'final_quantity',
        'is_manual_override',
        'notes',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'sort_order' => 'integer',
        'ratio' => 'integer',
        'calculated_quantity' => 'integer',
        'manual_quantity' => 'integer',
        'final_quantity' => 'integer',
        'is_manual_override' => 'boolean',
    ];

    /**
     * Available size types.
     *
     * @var array<string>
     */
    public static array $sizeTypes = [
        'text',
        'numeric'
    ];

    /**
     * Common text sizes.
     *
     * @var array<string>
     */
    public static array $textSizes = [
        'XXS', 'XS', 'S', 'M', 'L', 'XL', 'XXL', 'XXXL'
    ];

    /**
     * Common numeric sizes for different categories.
     *
     * @var array<string, array>
     */
    public static array $numericSizes = [
        'pants' => ['28', '30', '32', '34', '36', '38', '40', '42'],
        'shirts' => ['14', '15', '16', '17', '18', '19', '20'],
        'shoes' => ['6', '7', '8', '9', '10', '11', '12'],
    ];

    /**
     * Get the garment order.
     */
    public function garmentOrder(): BelongsTo
    {
        return $this->belongsTo(GarmentOrder::class, 'garment_order_id');
    }

    /**
     * Calculate quantity based on ratio and total order quantity.
     */
    public function calculateQuantity(int $totalOrderQuantity, int $totalRatio): void
    {
        if ($totalRatio > 0) {
            $this->calculated_quantity = (int) round(($this->ratio / $totalRatio) * $totalOrderQuantity);
            
            // Use manual quantity if override is enabled, otherwise use calculated
            $this->final_quantity = $this->is_manual_override && $this->manual_quantity !== null 
                ? $this->manual_quantity 
                : $this->calculated_quantity;
                
            $this->save();
        }
    }

    /**
     * Set manual override quantity.
     */
    public function setManualQuantity(int $quantity): void
    {
        $this->manual_quantity = $quantity;
        $this->is_manual_override = true;
        $this->final_quantity = $quantity;
        $this->save();
    }

    /**
     * Remove manual override and use calculated quantity.
     */
    public function removeManualOverride(): void
    {
        $this->is_manual_override = false;
        $this->manual_quantity = null;
        $this->final_quantity = $this->calculated_quantity;
        $this->save();
    }

    /**
     * Get the effective quantity (manual or calculated).
     */
    public function getEffectiveQuantityAttribute(): int
    {
        return $this->is_manual_override && $this->manual_quantity !== null 
            ? $this->manual_quantity 
            : $this->calculated_quantity;
    }

    /**
     * Get percentage of total quantity.
     */
    public function getPercentageAttribute(): float
    {
        $totalQuantity = $this->garmentOrder->total_quantity;
        return $totalQuantity > 0 ? ($this->final_quantity / $totalQuantity) * 100 : 0;
    }

    /**
     * Scope for ordering by sort order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order');
    }

    /**
     * Scope for text sizes.
     */
    public function scopeTextSizes($query)
    {
        return $query->where('size_type', 'text');
    }

    /**
     * Scope for numeric sizes.
     */
    public function scopeNumericSizes($query)
    {
        return $query->where('size_type', 'numeric');
    }

    /**
     * Static method to create size ratios from array.
     */
    public static function createFromArray(int $garmentOrderId, array $sizes): void
    {
        foreach ($sizes as $index => $sizeData) {
            static::create([
                'garment_order_id' => $garmentOrderId,
                'size_label' => $sizeData['label'],
                'size_type' => $sizeData['type'],
                'sort_order' => $index + 1,
                'ratio' => $sizeData['ratio'] ?? 1,
                'calculated_quantity' => 0,
                'final_quantity' => 0,
                'is_manual_override' => false,
            ]);
        }
    }

    /**
     * Static method to recalculate all quantities for an order.
     */
    public static function recalculateForOrder(int $garmentOrderId): void
    {
        $order = GarmentOrder::find($garmentOrderId);
        if (!$order) return;

        $sizeRatios = static::where('garment_order_id', $garmentOrderId)->get();
        $totalRatio = $sizeRatios->sum('ratio');

        foreach ($sizeRatios as $sizeRatio) {
            $sizeRatio->calculateQuantity($order->total_quantity, $totalRatio);
        }

        // Update order total quantity to match sum of final quantities
        $actualTotal = $sizeRatios->sum('final_quantity');
        if ($actualTotal !== $order->total_quantity) {
            $order->total_quantity = $actualTotal;
            $order->save();
        }
    }

    /**
     * Get size breakdown summary.
     */
    public static function getSizeBreakdownSummary(int $garmentOrderId): array
    {
        $sizeRatios = static::where('garment_order_id', $garmentOrderId)
            ->ordered()
            ->get();

        return [
            'total_sizes' => $sizeRatios->count(),
            'total_ratio' => $sizeRatios->sum('ratio'),
            'total_quantity' => $sizeRatios->sum('final_quantity'),
            'has_manual_overrides' => $sizeRatios->where('is_manual_override', true)->count() > 0,
            'sizes' => $sizeRatios->map(function ($size) {
                return [
                    'label' => $size->size_label,
                    'ratio' => $size->ratio,
                    'quantity' => $size->final_quantity,
                    'percentage' => $size->percentage,
                    'is_override' => $size->is_manual_override,
                ];
            })->toArray(),
        ];
    }
}
