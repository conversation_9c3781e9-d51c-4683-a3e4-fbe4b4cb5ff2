<?php

namespace App\Models\BuyerCRM;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Models\User;
use Carbon\Carbon;

/**
 * OrderSopCompliance Model
 * 
 * Represents SOP compliance tracking for garment orders
 * 
 * @property int $id
 * @property int $garment_order_id
 * @property int $buyer_id
 * @property int|null $assigned_to
 * @property string $sop_category
 * @property string $sop_title
 * @property string $sop_description
 * @property string $priority
 * @property string $compliance_status
 * @property float $completion_percentage
 * @property array|null $requirements
 * @property string|null $buyer_instructions
 * @property string|null $internal_notes
 * @property array|null $required_documents
 * @property array|null $uploaded_documents
 * @property bool $documents_verified
 * @property bool $testing_required
 * @property string|null $testing_type
 * @property string|null $testing_standard
 * @property string $testing_status
 * @property \Carbon\Carbon|null $testing_completion_date
 * @property string|null $testing_comments
 * @property bool $labeling_required
 * @property array|null $label_specifications
 * @property string $labeling_status
 * @property bool $special_packing_required
 * @property array|null $packing_specifications
 * @property string $packing_status
 * @property \Carbon\Carbon|null $due_date
 * @property \Carbon\Carbon|null $started_date
 * @property \Carbon\Carbon|null $completed_date
 * @property \Carbon\Carbon|null $verified_date
 * @property int|null $verified_by
 * @property string|null $verification_comments
 * @property bool $buyer_approved
 * @property \Carbon\Carbon|null $buyer_approval_date
 * @property string|null $buyer_feedback
 * @property bool $alert_enabled
 * @property int $alert_days_before
 * @property \Carbon\Carbon|null $last_alert_sent
 * @property bool $is_non_compliant
 * @property string|null $non_compliance_reason
 * @property string|null $corrective_action
 * @property \Carbon\Carbon|null $corrective_action_due_date
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class OrderSopCompliance extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'order_sop_compliance';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'garment_order_id',
        'buyer_id',
        'assigned_to',
        'sop_category',
        'sop_title',
        'sop_description',
        'priority',
        'compliance_status',
        'completion_percentage',
        'requirements',
        'buyer_instructions',
        'internal_notes',
        'required_documents',
        'uploaded_documents',
        'documents_verified',
        'testing_required',
        'testing_type',
        'testing_standard',
        'testing_status',
        'testing_completion_date',
        'testing_comments',
        'labeling_required',
        'label_specifications',
        'labeling_status',
        'special_packing_required',
        'packing_specifications',
        'packing_status',
        'due_date',
        'started_date',
        'completed_date',
        'verified_date',
        'verified_by',
        'verification_comments',
        'buyer_approved',
        'buyer_approval_date',
        'buyer_feedback',
        'alert_enabled',
        'alert_days_before',
        'last_alert_sent',
        'is_non_compliant',
        'non_compliance_reason',
        'corrective_action',
        'corrective_action_due_date',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'completion_percentage' => 'decimal:2',
        'requirements' => 'array',
        'required_documents' => 'array',
        'uploaded_documents' => 'array',
        'documents_verified' => 'boolean',
        'testing_required' => 'boolean',
        'testing_completion_date' => 'date',
        'labeling_required' => 'boolean',
        'label_specifications' => 'array',
        'special_packing_required' => 'boolean',
        'packing_specifications' => 'array',
        'due_date' => 'date',
        'started_date' => 'date',
        'completed_date' => 'date',
        'verified_date' => 'date',
        'buyer_approved' => 'boolean',
        'buyer_approval_date' => 'date',
        'alert_enabled' => 'boolean',
        'alert_days_before' => 'integer',
        'last_alert_sent' => 'datetime',
        'is_non_compliant' => 'boolean',
        'corrective_action_due_date' => 'date',
    ];

    /**
     * Available priorities.
     *
     * @var array<string>
     */
    public static array $priorities = [
        'low',
        'medium',
        'high',
        'critical'
    ];

    /**
     * Available compliance statuses.
     *
     * @var array<string>
     */
    public static array $complianceStatuses = [
        'not_started',
        'in_progress',
        'completed',
        'verified',
        'non_compliant'
    ];

    /**
     * Available testing statuses.
     *
     * @var array<string>
     */
    public static array $testingStatuses = [
        'not_required',
        'pending',
        'in_progress',
        'passed',
        'failed'
    ];

    /**
     * Available labeling/packing statuses.
     *
     * @var array<string>
     */
    public static array $approvalStatuses = [
        'not_required',
        'pending',
        'approved',
        'rejected'
    ];

    /**
     * Common SOP categories.
     *
     * @var array<string>
     */
    public static array $sopCategories = [
        'Labeling',
        'Testing',
        'Packing',
        'Documentation',
        'Quality',
        'Compliance',
        'Certification',
        'Other'
    ];

    /**
     * Get the garment order.
     */
    public function garmentOrder(): BelongsTo
    {
        return $this->belongsTo(GarmentOrder::class, 'garment_order_id');
    }

    /**
     * Get the buyer profile.
     */
    public function buyer(): BelongsTo
    {
        return $this->belongsTo(BuyerProfile::class, 'buyer_id');
    }

    /**
     * Get the assigned user.
     */
    public function assignedUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    /**
     * Get the user who verified compliance.
     */
    public function verifier(): BelongsTo
    {
        return $this->belongsTo(User::class, 'verified_by');
    }

    /**
     * Check if SOP is overdue.
     */
    public function isOverdue(): bool
    {
        return $this->due_date && 
               $this->due_date->isPast() && 
               !in_array($this->compliance_status, ['completed', 'verified']);
    }

    /**
     * Get days until due.
     */
    public function daysUntilDue(): ?int
    {
        return $this->due_date ? now()->diffInDays($this->due_date, false) : null;
    }

    /**
     * Get priority badge color.
     */
    public function getPriorityBadgeColorAttribute(): string
    {
        return match($this->priority) {
            'low' => 'success',
            'medium' => 'warning',
            'high' => 'danger',
            'critical' => 'dark',
            default => 'secondary'
        };
    }

    /**
     * Get compliance status badge color.
     */
    public function getComplianceStatusBadgeColorAttribute(): string
    {
        return match($this->compliance_status) {
            'not_started' => 'secondary',
            'in_progress' => 'primary',
            'completed' => 'info',
            'verified' => 'success',
            'non_compliant' => 'danger',
            default => 'secondary'
        };
    }

    /**
     * Start compliance work.
     */
    public function start(): void
    {
        $this->compliance_status = 'in_progress';
        $this->started_date = now();
        $this->save();
    }

    /**
     * Complete compliance work.
     */
    public function complete(): void
    {
        $this->compliance_status = 'completed';
        $this->completed_date = now();
        $this->completion_percentage = 100;
        $this->save();
    }

    /**
     * Verify compliance.
     */
    public function verify(int $verifiedBy, string $comments = null): void
    {
        $this->compliance_status = 'verified';
        $this->verified_date = now();
        $this->verified_by = $verifiedBy;
        $this->verification_comments = $comments;
        $this->save();
    }

    /**
     * Mark as non-compliant.
     */
    public function markNonCompliant(string $reason, string $correctiveAction = null, Carbon $correctiveDueDate = null): void
    {
        $this->compliance_status = 'non_compliant';
        $this->is_non_compliant = true;
        $this->non_compliance_reason = $reason;
        $this->corrective_action = $correctiveAction;
        $this->corrective_action_due_date = $correctiveDueDate;
        $this->save();
    }

    /**
     * Approve by buyer.
     */
    public function buyerApprove(string $feedback = null): void
    {
        $this->buyer_approved = true;
        $this->buyer_approval_date = now();
        $this->buyer_feedback = $feedback;
        $this->save();
    }

    /**
     * Update completion percentage.
     */
    public function updateProgress(float $percentage): void
    {
        $this->completion_percentage = min(100, max(0, $percentage));
        
        // Auto-update status based on percentage
        if ($this->completion_percentage == 0) {
            $this->compliance_status = 'not_started';
        } elseif ($this->completion_percentage < 100) {
            $this->compliance_status = 'in_progress';
            if (!$this->started_date) {
                $this->started_date = now();
            }
        } else {
            $this->compliance_status = 'completed';
            $this->completed_date = now();
        }
        
        $this->save();
    }

    /**
     * Add uploaded document.
     */
    public function addDocument(string $documentPath, string $documentName): void
    {
        $documents = $this->uploaded_documents ?? [];
        $documents[] = [
            'path' => $documentPath,
            'name' => $documentName,
            'uploaded_at' => now()->toISOString(),
        ];
        $this->uploaded_documents = $documents;
        $this->save();
    }

    /**
     * Check if all required documents are uploaded.
     */
    public function hasAllRequiredDocuments(): bool
    {
        if (!$this->required_documents) {
            return true;
        }
        
        $uploadedCount = count($this->uploaded_documents ?? []);
        $requiredCount = count($this->required_documents);
        
        return $uploadedCount >= $requiredCount;
    }

    /**
     * Check if alert should be sent.
     */
    public function shouldSendAlert(): bool
    {
        if (!$this->alert_enabled || !$this->due_date) {
            return false;
        }
        
        $alertDate = $this->due_date->subDays($this->alert_days_before);
        $lastSent = $this->last_alert_sent;
        
        return now()->gte($alertDate) && 
               (!$lastSent || $lastSent->diffInHours(now()) >= 24) &&
               !in_array($this->compliance_status, ['completed', 'verified']);
    }

    /**
     * Mark alert as sent.
     */
    public function markAlertSent(): void
    {
        $this->last_alert_sent = now();
        $this->save();
    }

    /**
     * Scope for overdue items.
     */
    public function scopeOverdue($query)
    {
        return $query->where('due_date', '<', now())
            ->whereNotIn('compliance_status', ['completed', 'verified']);
    }

    /**
     * Scope for specific priority.
     */
    public function scopeByPriority($query, string $priority)
    {
        return $query->where('priority', $priority);
    }

    /**
     * Scope for specific status.
     */
    public function scopeByStatus($query, string $status)
    {
        return $query->where('compliance_status', $status);
    }

    /**
     * Scope for non-compliant items.
     */
    public function scopeNonCompliant($query)
    {
        return $query->where('is_non_compliant', true);
    }

    /**
     * Scope for items needing alerts.
     */
    public function scopeNeedingAlerts($query)
    {
        return $query->where('alert_enabled', true)
            ->whereNotNull('due_date')
            ->whereNotIn('compliance_status', ['completed', 'verified']);
    }

    /**
     * Get SOP compliance summary for order.
     */
    public static function getComplianceSummary(int $garmentOrderId): array
    {
        $items = static::where('garment_order_id', $garmentOrderId)->get();

        return [
            'total_items' => $items->count(),
            'completed_items' => $items->whereIn('compliance_status', ['completed', 'verified'])->count(),
            'overdue_items' => $items->filter(fn($item) => $item->isOverdue())->count(),
            'critical_items' => $items->where('priority', 'critical')->count(),
            'non_compliant_items' => $items->where('is_non_compliant', true)->count(),
            'average_completion' => $items->avg('completion_percentage') ?? 0,
            'buyer_approved_items' => $items->where('buyer_approved', true)->count(),
            'testing_required_items' => $items->where('testing_required', true)->count(),
            'testing_passed_items' => $items->where('testing_status', 'passed')->count(),
        ];
    }
}
