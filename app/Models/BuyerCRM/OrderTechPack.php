<?php

namespace App\Models\BuyerCRM;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Storage;
use App\Models\User;

/**
 * OrderTechPack Model
 * 
 * Represents tech pack files and sample tracking for garment orders
 * 
 * @property int $id
 * @property int $garment_order_id
 * @property int $uploaded_by
 * @property string $file_name
 * @property string $file_path
 * @property string $file_type
 * @property int $file_size
 * @property string $mime_type
 * @property string $file_category
 * @property string|null $description
 * @property string|null $sample_type
 * @property string|null $sample_status
 * @property \Carbon\Carbon|null $sample_approval_date
 * @property string|null $sample_comments
 * @property int|null $approved_by
 * @property int $version
 * @property bool $is_current_version
 * @property bool $is_active
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class OrderTechPack extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'order_tech_packs';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'garment_order_id',
        'uploaded_by',
        'file_name',
        'file_path',
        'file_type',
        'file_size',
        'mime_type',
        'file_category',
        'description',
        'sample_type',
        'sample_status',
        'sample_approval_date',
        'sample_comments',
        'approved_by',
        'version',
        'is_current_version',
        'is_active',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'file_size' => 'integer',
        'version' => 'integer',
        'is_current_version' => 'boolean',
        'is_active' => 'boolean',
        'sample_approval_date' => 'date',
    ];

    /**
     * Available file categories.
     *
     * @var array<string>
     */
    public static array $fileCategories = [
        'tech_pack',
        'image',
        'sample_image',
        'reference',
        'other'
    ];

    /**
     * Available sample types.
     *
     * @var array<string>
     */
    public static array $sampleTypes = [
        'fit_sample',
        'pp_sample',
        'top_sample',
        'other'
    ];

    /**
     * Available sample statuses.
     *
     * @var array<string>
     */
    public static array $sampleStatuses = [
        'pending',
        'approved',
        'rejected'
    ];

    /**
     * Allowed file types for tech packs.
     *
     * @var array<string>
     */
    public static array $allowedTechPackTypes = [
        'pdf', 'doc', 'docx'
    ];

    /**
     * Allowed file types for images.
     *
     * @var array<string>
     */
    public static array $allowedImageTypes = [
        'jpg', 'jpeg', 'png', 'gif', 'webp'
    ];

    /**
     * Get the garment order.
     */
    public function garmentOrder(): BelongsTo
    {
        return $this->belongsTo(GarmentOrder::class, 'garment_order_id');
    }

    /**
     * Get the user who uploaded the file.
     */
    public function uploader(): BelongsTo
    {
        return $this->belongsTo(User::class, 'uploaded_by');
    }

    /**
     * Get the user who approved the sample.
     */
    public function approver(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Get the file URL.
     */
    public function getFileUrlAttribute(): string
    {
        return Storage::url($this->file_path);
    }

    /**
     * Get formatted file size.
     */
    public function getFormattedFileSizeAttribute(): string
    {
        $bytes = $this->file_size;
        $units = ['B', 'KB', 'MB', 'GB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Check if file is an image.
     */
    public function isImage(): bool
    {
        return in_array(strtolower($this->file_type), self::$allowedImageTypes);
    }

    /**
     * Check if file is a tech pack document.
     */
    public function isTechPack(): bool
    {
        return in_array(strtolower($this->file_type), self::$allowedTechPackTypes);
    }

    /**
     * Get sample status badge color.
     */
    public function getSampleStatusBadgeColorAttribute(): string
    {
        return match($this->sample_status) {
            'pending' => 'warning',
            'approved' => 'success',
            'rejected' => 'danger',
            default => 'secondary'
        };
    }

    /**
     * Approve sample.
     */
    public function approveSample(int $approvedBy, string $comments = null): void
    {
        $this->sample_status = 'approved';
        $this->sample_approval_date = now();
        $this->approved_by = $approvedBy;
        $this->sample_comments = $comments;
        $this->save();
    }

    /**
     * Reject sample.
     */
    public function rejectSample(int $rejectedBy, string $comments = null): void
    {
        $this->sample_status = 'rejected';
        $this->sample_approval_date = now();
        $this->approved_by = $rejectedBy;
        $this->sample_comments = $comments;
        $this->save();
    }

    /**
     * Create new version of file.
     */
    public function createNewVersion(array $fileData): self
    {
        // Mark current version as not current
        $this->is_current_version = false;
        $this->save();

        // Create new version
        return static::create(array_merge($fileData, [
            'garment_order_id' => $this->garment_order_id,
            'file_category' => $this->file_category,
            'sample_type' => $this->sample_type,
            'version' => $this->version + 1,
            'is_current_version' => true,
            'is_active' => true,
        ]));
    }

    /**
     * Delete file from storage.
     */
    public function deleteFile(): bool
    {
        if (Storage::exists($this->file_path)) {
            return Storage::delete($this->file_path);
        }
        return true;
    }

    /**
     * Scope for current versions only.
     */
    public function scopeCurrentVersion($query)
    {
        return $query->where('is_current_version', true);
    }

    /**
     * Scope for active files only.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for specific file category.
     */
    public function scopeByCategory($query, string $category)
    {
        return $query->where('file_category', $category);
    }

    /**
     * Scope for specific sample type.
     */
    public function scopeBySampleType($query, string $sampleType)
    {
        return $query->where('sample_type', $sampleType);
    }

    /**
     * Scope for approved samples.
     */
    public function scopeApproved($query)
    {
        return $query->where('sample_status', 'approved');
    }

    /**
     * Scope for pending samples.
     */
    public function scopePending($query)
    {
        return $query->where('sample_status', 'pending');
    }

    /**
     * Get tech pack status for order.
     */
    public static function getTechPackStatus(int $garmentOrderId): array
    {
        $techPacks = static::where('garment_order_id', $garmentOrderId)
            ->byCategory('tech_pack')
            ->currentVersion()
            ->active()
            ->get();

        $samples = static::where('garment_order_id', $garmentOrderId)
            ->whereIn('file_category', ['sample_image', 'image'])
            ->whereNotNull('sample_type')
            ->currentVersion()
            ->active()
            ->get();

        return [
            'tech_pack_uploaded' => $techPacks->count() > 0,
            'tech_pack_count' => $techPacks->count(),
            'fit_sample_approved' => $samples->where('sample_type', 'fit_sample')->where('sample_status', 'approved')->count() > 0,
            'pp_sample_approved' => $samples->where('sample_type', 'pp_sample')->where('sample_status', 'approved')->count() > 0,
            'top_sample_approved' => $samples->where('sample_type', 'top_sample')->where('sample_status', 'approved')->count() > 0,
            'all_samples_approved' => $samples->where('sample_status', 'approved')->count() === $samples->count() && $samples->count() > 0,
            'pending_samples' => $samples->where('sample_status', 'pending')->count(),
            'rejected_samples' => $samples->where('sample_status', 'rejected')->count(),
        ];
    }

    /**
     * Boot method to handle model events.
     */
    protected static function boot()
    {
        parent::boot();

        static::deleting(function ($techPack) {
            $techPack->deleteFile();
        });
    }
}
