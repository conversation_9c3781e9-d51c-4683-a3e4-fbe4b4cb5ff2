<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class BuyerUse extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'party_id',
        'user_id',
        'style',
        'color',
        'item',
        'size',
        'qty_used',
        'usage_date',
        'purpose',
        'remarks',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'usage_date' => 'date',
        'qty_used' => 'integer',
    ];

    /**
     * Get the party (buyer) that used the items.
     */
    public function party(): BelongsTo
    {
        return $this->belongsTo(Party::class);
    }

    /**
     * Get the user who recorded the usage.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope to filter by party (buyer).
     */
    public function scopeForParty($query, $partyId)
    {
        return $query->where('party_id', $partyId);
    }

    /**
     * Scope to filter by date range.
     */
    public function scopeBetweenDates($query, $startDate, $endDate)
    {
        return $query->whereBetween('usage_date', [$startDate, $endDate]);
    }

    /**
     * Scope to filter by item variation (style, color, item, size).
     */
    public function scopeForVariation($query, $style, $color = null, $item = null, $size = null)
    {
        $query->where('style', $style);
        
        if ($color !== null) {
            $query->where('color', $color);
        }
        
        if ($item !== null) {
            $query->where('item', $item);
        }
        
        if ($size !== null) {
            $query->where('size', $size);
        }
        
        return $query;
    }
}
