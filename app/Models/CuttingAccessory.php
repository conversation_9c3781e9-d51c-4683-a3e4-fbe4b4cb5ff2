<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CuttingAccessory extends Model
{
    use HasFactory;

    protected $fillable = [
        'cutting_marker_id',
        'name',
        'description',
        'width',
        'length',
        'quantity_per_pant',
        'total_quantity',
        'row_number',
        'x_position',
        'y_position',
        'rotation',
        'mirrored',
        'grain_locked',
        'color_code',
        'metadata',
    ];

    protected $casts = [
        'width' => 'decimal:2',
        'length' => 'decimal:2',
        'x_position' => 'decimal:2',
        'y_position' => 'decimal:2',
        'mirrored' => 'boolean',
        'grain_locked' => 'boolean',
        'metadata' => 'json',
    ];

    /**
     * Relationships
     */
    public function cuttingMarker(): BelongsTo
    {
        return $this->belongsTo(CuttingMarker::class);
    }

    /**
     * Boot method to auto-calculate total quantity
     */
    protected static function boot()
    {
        parent::boot();

        static::saving(function ($accessory) {
            if ($accessory->cuttingMarker) {
                $accessory->total_quantity = $accessory->quantity_per_pant * $accessory->cuttingMarker->total_pants_producible;
            }
        });
    }

    /**
     * Get the calculated area of the accessory
     */
    public function getAreaAttribute(): float
    {
        return round($this->width * $this->length, 2);
    }

    /**
     * Get the total area considering total quantity
     */
    public function getTotalAreaAttribute(): float
    {
        return round($this->area * $this->total_quantity, 2);
    }

    /**
     * Get the effective width considering rotation
     */
    public function getEffectiveWidthAttribute(): float
    {
        return $this->rotation % 180 === 0 ? $this->width : $this->length;
    }

    /**
     * Get the effective length considering rotation
     */
    public function getEffectiveLengthAttribute(): float
    {
        return $this->rotation % 180 === 0 ? $this->length : $this->width;
    }

    /**
     * Check if accessory can be rotated
     */
    public function canRotate(): bool
    {
        return !$this->grain_locked;
    }

    /**
     * Rotate accessory by 90 degrees
     */
    public function rotate(): void
    {
        if ($this->canRotate()) {
            $this->rotation = ($this->rotation + 90) % 360;
            $this->save();
        }
    }

    /**
     * Mirror accessory horizontally
     */
    public function mirror(): void
    {
        $this->mirrored = !$this->mirrored;
        $this->save();
    }

    /**
     * Move accessory to new position
     */
    public function moveTo(int $row, float $x, float $y): void
    {
        $this->update([
            'row_number' => $row,
            'x_position' => $x,
            'y_position' => $y,
        ]);
    }

    /**
     * Check if accessory fits within fabric width
     */
    public function fitsInFabricWidth(float $fabricWidth): bool
    {
        return ($this->x_position + $this->effective_width) <= $fabricWidth;
    }

    /**
     * Get common accessory types
     */
    public static function getCommonAccessories(): array
    {
        return [
            'belt_loop' => ['name' => 'Belt Loop', 'width' => 1.5, 'length' => 3, 'quantity' => 5],
            'pocket' => ['name' => 'Pocket', 'width' => 6, 'length' => 7, 'quantity' => 2],
            'waistband' => ['name' => 'Waistband', 'width' => 3, 'length' => 40, 'quantity' => 1],
            'fly_piece' => ['name' => 'Fly Piece', 'width' => 4, 'length' => 8, 'quantity' => 1],
            'coin_pocket' => ['name' => 'Coin Pocket', 'width' => 3, 'length' => 4, 'quantity' => 1],
        ];
    }

    /**
     * Get display name for the accessory
     */
    public function getDisplayNameAttribute(): string
    {
        return "{$this->name} (x{$this->quantity_per_pant})";
    }

    /**
     * Get CSS style for accessory display
     */
    public function getCssStyleAttribute(): string
    {
        $transform = '';

        if ($this->rotation > 0) {
            $transform .= "rotate({$this->rotation}deg) ";
        }

        if ($this->mirrored) {
            $transform .= "scaleX(-1) ";
        }

        return "
            position: absolute;
            left: {$this->x_position}px;
            top: {$this->y_position}px;
            width: {$this->effective_width}px;
            height: {$this->effective_length}px;
            background-color: {$this->color_code};
            border: 2px dashed #666;
            transform: {$transform};
            cursor: move;
            opacity: 0.8;
        ";
    }

    /**
     * Scopes
     */
    public function scopeByRow($query, int $row)
    {
        return $query->where('row_number', $row);
    }
}
