<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CuttingMarker extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'fabric_width',
        'shrinkage_percentage',
        'wastage_percentage',
        'total_set_count',
        'total_fabric_required',
        'total_panels_count',
        'total_pants_producible',
        'fabric_utilization_percentage',
        'layout_data',
        'size_ratios',
        'accessories_data',
        'status',
        'version',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'fabric_width' => 'decimal:2',
        'shrinkage_percentage' => 'decimal:2',
        'wastage_percentage' => 'decimal:2',
        'total_fabric_required' => 'decimal:2',
        'fabric_utilization_percentage' => 'decimal:2',
        'layout_data' => 'json',
        'size_ratios' => 'json',
        'accessories_data' => 'json',
    ];

    /**
     * Relationships
     */
    public function panels(): HasMany
    {
        return $this->hasMany(CuttingPanel::class);
    }

    public function accessories(): HasMany
    {
        return $this->hasMany(CuttingAccessory::class);
    }

    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updater(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Scopes
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeDraft($query)
    {
        return $query->where('status', 'draft');
    }

    /**
     * Calculate total fabric required including shrinkage and wastage
     */
    public function calculateTotalFabricRequired(): float
    {
        $baseFabric = $this->calculateBaseFabricRequired();

        // Add shrinkage
        $withShrinkage = $baseFabric * (1 + ($this->shrinkage_percentage / 100));

        // Add wastage
        $withWastage = $withShrinkage * (1 + ($this->wastage_percentage / 100));

        return round($withWastage, 2);
    }

    /**
     * Calculate base fabric required without shrinkage/wastage
     */
    public function calculateBaseFabricRequired(): float
    {
        $totalLength = 0;

        // Group panels by row and calculate total length
        $panelsByRow = $this->panels->groupBy('row_number');

        foreach ($panelsByRow as $rowPanels) {
            $rowLength = $rowPanels->max(function ($panel) {
                return $panel->y_position + ($panel->rotation % 180 === 0 ? $panel->length : $panel->width);
            });
            $totalLength += $rowLength;
        }

        // Convert inches to yards (36 inches = 1 yard)
        return round($totalLength / 36, 2);
    }

    /**
     * Calculate fabric utilization percentage
     */
    public function calculateFabricUtilization(): float
    {
        $totalPanelArea = $this->panels->sum(function ($panel) {
            return $panel->width * $panel->length * $panel->quantity;
        });

        $totalAccessoryArea = $this->accessories->sum(function ($accessory) {
            return $accessory->width * $accessory->length * $accessory->total_quantity;
        });

        $usedArea = $totalPanelArea + $totalAccessoryArea;
        $totalFabricArea = $this->fabric_width * ($this->calculateBaseFabricRequired() * 36); // convert yards to inches

        return $totalFabricArea > 0 ? round(($usedArea / $totalFabricArea) * 100, 2) : 0;
    }

    /**
     * Get available pant sizes
     */
    public static function getAvailableSizes(): array
    {
        return [
            '28' => '28',
            '30' => '30',
            '32' => '32',
            '34' => '34',
            '36' => '36',
            '38' => '38',
            '40' => '40',
            '42' => '42',
            '44' => '44',
            '46' => '46',
        ];
    }

    /**
     * Get status options
     */
    public static function getStatusOptions(): array
    {
        return [
            'draft' => 'Draft',
            'active' => 'Active',
            'archived' => 'Archived',
        ];
    }

    /**
     * Generate panels for a specific size
     */
    public function generatePanelsForSize(string $size, array $dimensions, int $ratio): void
    {
        $panelTypes = [
            'front_left' => ['name' => 'Front Left', 'type' => 'front'],
            'front_right' => ['name' => 'Front Right', 'type' => 'front'],
            'back_left' => ['name' => 'Back Left', 'type' => 'back'],
            'back_right' => ['name' => 'Back Right', 'type' => 'back'],
        ];

        foreach ($panelTypes as $panelInfo) {
            $this->panels()->create([
                'panel_name' => $panelInfo['name'] . " - Size {$size}",
                'panel_type' => $panelInfo['type'],
                'size' => $size,
                'quantity' => $ratio,
                'width' => $dimensions['width'],
                'length' => $dimensions['length'],
                'color_code' => $this->getColorForSize($size),
            ]);
        }
    }

    /**
     * Get color code for size visualization
     */
    private function getColorForSize(string $size): string
    {
        $colors = [
            '28' => '#FF6B6B',
            '30' => '#4ECDC4',
            '32' => '#45B7D1',
            '34' => '#96CEB4',
            '36' => '#FFEAA7',
            '38' => '#DDA0DD',
            '40' => '#98D8C8',
            '42' => '#F7DC6F',
            '44' => '#BB8FCE',
            '46' => '#85C1E9',
        ];

        return $colors[$size] ?? '#95A5A6';
    }
}
