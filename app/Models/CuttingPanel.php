<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CuttingPanel extends Model
{
    use HasFactory;

    protected $fillable = [
        'cutting_marker_id',
        'panel_name',
        'panel_type',
        'size',
        'quantity',
        'width',
        'length',
        'row_number',
        'x_position',
        'y_position',
        'rotation',
        'mirrored',
        'grain_locked',
        'color_code',
        'metadata',
    ];

    protected $casts = [
        'width' => 'decimal:2',
        'length' => 'decimal:2',
        'x_position' => 'decimal:2',
        'y_position' => 'decimal:2',
        'mirrored' => 'boolean',
        'grain_locked' => 'boolean',
        'metadata' => 'json',
    ];

    /**
     * Relationships
     */
    public function cuttingMarker(): BelongsTo
    {
        return $this->belongsTo(CuttingMarker::class);
    }

    /**
     * Scopes
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('panel_type', $type);
    }

    public function scopeBySize($query, string $size)
    {
        return $query->where('size', $size);
    }

    public function scopeByRow($query, int $row)
    {
        return $query->where('row_number', $row);
    }

    /**
     * Get the calculated area of the panel
     */
    public function getAreaAttribute(): float
    {
        return round($this->width * $this->length, 2);
    }

    /**
     * Get the total area considering quantity
     */
    public function getTotalAreaAttribute(): float
    {
        return round($this->area * $this->quantity, 2);
    }

    /**
     * Get the effective width considering rotation
     */
    public function getEffectiveWidthAttribute(): float
    {
        return $this->rotation % 180 === 0 ? $this->width : $this->length;
    }

    /**
     * Get the effective length considering rotation
     */
    public function getEffectiveLengthAttribute(): float
    {
        return $this->rotation % 180 === 0 ? $this->length : $this->width;
    }

    /**
     * Check if panel can be rotated
     */
    public function canRotate(): bool
    {
        return !$this->grain_locked;
    }

    /**
     * Rotate panel by 90 degrees
     */
    public function rotate(): void
    {
        if ($this->canRotate()) {
            $this->rotation = ($this->rotation + 90) % 360;
            $this->save();
        }
    }

    /**
     * Mirror panel horizontally
     */
    public function mirror(): void
    {
        $this->mirrored = !$this->mirrored;
        $this->save();
    }

    /**
     * Move panel to new position
     */
    public function moveTo(int $row, float $x, float $y): void
    {
        $this->update([
            'row_number' => $row,
            'x_position' => $x,
            'y_position' => $y,
        ]);
    }

    /**
     * Check if panel fits within fabric width
     */
    public function fitsInFabricWidth(float $fabricWidth): bool
    {
        return ($this->x_position + $this->effective_width) <= $fabricWidth;
    }

    /**
     * Get panel type options
     */
    public static function getPanelTypes(): array
    {
        return [
            'front' => 'Front',
            'back' => 'Back',
            'accessory' => 'Accessory',
        ];
    }

    /**
     * Get display name for the panel
     */
    public function getDisplayNameAttribute(): string
    {
        return "{$this->panel_name} ({$this->size})";
    }

    /**
     * Get CSS style for panel display
     */
    public function getCssStyleAttribute(): string
    {
        $transform = '';

        if ($this->rotation > 0) {
            $transform .= "rotate({$this->rotation}deg) ";
        }

        if ($this->mirrored) {
            $transform .= "scaleX(-1) ";
        }

        return "
            position: absolute;
            left: {$this->x_position}px;
            top: {$this->y_position}px;
            width: {$this->effective_width}px;
            height: {$this->effective_length}px;
            background-color: {$this->color_code};
            border: 2px solid #333;
            transform: {$transform};
            cursor: move;
        ";
    }
}
