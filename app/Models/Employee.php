<?php

namespace App\Models;

use App\Traits\TenantScoped;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Employee extends Model
{
    use HasFactory, TenantScoped;

    protected $fillable = [
        'employee_id',
        'designation_id',
        'name',
        'email',
        'address',
        'gender',
        'phone',
        'salary',
        'employee_type',
        'join_date',
        'birth_date',
        'status',
        'meta',
        'machine_user_id',
        'wage_type',
        'basic_salary'
    ];

    protected $casts = [
        'meta' => 'json',
        'salary' => 'decimal:2',
        'basic_salary' => 'decimal:2',
        'join_date' => 'date',
        'birth_date' => 'date',
        'status' => 'boolean',
    ];

    /**
     * Relationships
     */
    public function designation(): BelongsTo
    {
        return $this->belongsTo(Designation::class, 'designation_id');
    }

    public function salaries(): HasMany
    {
        return $this->hasMany(Salary::class);
    }

    public function attendances(): HasMany
    {
        return $this->hasMany(Attendance::class);
    }

    /**
     * Get the latest salary record for this employee
     */
    public function latestSalary(): HasOne
    {
        return $this->hasOne(Salary::class)->latest();
    }

    /**
     * Get salary for a specific period
     */
    public function salaryForPeriod($year, $month)
    {
        return $this->salaries()->forPeriod($year, $month)->first();
    }

    /**
     * Generate unique employee ID based on name initials + company abbreviation + join date
     * Format: "JD-ABC-0124" (John Doe, ABC Company, joined Jan 2024)
     */
    public static function generateEmployeeId($name = null, $joinDate = null)
    {
        if ($name) {
            // Get name initials (max 3 characters)
            $nameParts = explode(' ', strtoupper(trim($name)));
            $initials = '';

            foreach ($nameParts as $part) {
                if (!empty($part)) {
                    $initials .= substr($part, 0, 1);
                }
            }
            $initials = substr($initials, 0, 3);

            // Get company abbreviation from settings
            $companyName = get_option('company')['name'] ?? config('app.name', 'Company');
            $companyWords = explode(' ', strtoupper(trim($companyName)));
            $companyAbbr = '';

            foreach ($companyWords as $word) {
                if (!empty($word) && strlen($companyAbbr) < 3) {
                    $companyAbbr .= substr($word, 0, 1);
                }
            }

            // Ensure company abbreviation is at least 2 characters
            if (strlen($companyAbbr) < 2) {
                $companyAbbr = substr(strtoupper($companyName), 0, 3);
            }
            $companyAbbr = substr($companyAbbr, 0, 3);

            // Get join date in MMYY format
            $joinDateFormatted = '';
            if ($joinDate) {
                try {
                    $date = \Carbon\Carbon::parse($joinDate);
                    $joinDateFormatted = $date->format('my'); // MMYY format
                } catch (\Exception $e) {
                    $joinDateFormatted = now()->format('my');
                }
            } else {
                $joinDateFormatted = now()->format('my');
            }

            // Create base ID: initials-company-date
            $baseId = $initials . '-' . $companyAbbr . '-' . $joinDateFormatted;

            // Check if this ID already exists
            $employeeId = $baseId;
            $counter = 1;

            while (self::where('employee_id', $employeeId)->exists()) {
                // If exists, add a suffix number
                $employeeId = $baseId . '-' . $counter;
                $counter++;

                // Prevent infinite loop
                if ($counter > 999) {
                    break;
                }
            }

        } else {
            // Fallback to old method if no name provided
            do {
                $employeeId = 'EMP-' . now()->format('my') . '-' . str_pad(rand(1, 999), 3, '0', STR_PAD_LEFT);
            } while (self::where('employee_id', $employeeId)->exists());
        }

        return $employeeId;
    }

    /**
     * Auto-generate employee_id if not provided
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($employee) {
            if (empty($employee->employee_id)) {
                $employee->employee_id = self::generateEmployeeId($employee->name, $employee->join_date);
            }
        });
    }

    /**
     * Scope for active employees
     */
    public function scopeActive($query)
    {
        return $query->where('status', 1);
    }

    /**
     * Scope for inactive employees
     */
    public function scopeInactive($query)
    {
        return $query->where('status', 0);
    }

    /**
     * Get formatted salary
     */
    public function getFormattedSalaryAttribute()
    {
        return '৳' . number_format($this->salary, 2);
    }

    /**
     * Get full name with employee ID
     */
    public function getFullNameAttribute()
    {
        return $this->name . ' (' . $this->employee_id . ')';
    }

    /**
     * Get employee status badge
     */
    public function getStatusBadgeAttribute()
    {
        return $this->status ? 'success' : 'danger';
    }

    /**
     * Get employee status text
     */
    public function getStatusTextAttribute()
    {
        return $this->status ? 'Active' : 'Inactive';
    }
}
