<?php

namespace App\Models;

use App\Traits\TenantScoped;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Party extends Model
{
    use HasFactory, SoftDeletes, TenantScoped;

    protected $fillable = ['name', 'type', 'creator_id', 'user_id', 'currency_id', 'address', 'total_bill', 'balance', 'advance_amount', 'due_amount', 'pay_amount', 'opening_balance', 'opening_balance_type', 'remarks', 'receivable_type', 'meta'];

    public function currency(){
        return $this->belongsTo(Currency::class, 'currency_id');
    }

    public function vouchers(){
        return $this->hasMany(Voucher::class);
    }

    public function user(){
        return $this->belongsTo(User::class, 'user_id');
    }

    public function bank(){
        return $this->belongsTo(Bank::class);
    }

    public function orders(){
        return $this->hasMany(Order::class);
    }

    public function buyerUses(){
        return $this->hasMany(BuyerUse::class);
    }

    /**
     * Scope to get only buyers.
     */
    public function scopeBuyers($query)
    {
        return $query->where('type', 'buyer');
    }

    /**
     * Check if this party is a buyer.
     */
    public function isBuyer()
    {
        return $this->type === 'buyer';
    }

    protected $casts = [
        'meta' => 'json',
    ];
}
