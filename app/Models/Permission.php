<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

/**
 * Permission Model
 * 
 * Represents system permissions for different modules and actions
 * 
 * @property int $id
 * @property string $name
 * @property string $module
 * @property string $action
 * @property string|null $description
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class Permission extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'module',
        'action',
        'description',
    ];

    /**
     * Available modules in the system.
     *
     * @var array<string>
     */
    public static array $modules = [
        'dashboard',
        'buyer_profiles',
        'buyer_pipeline',
        'buyer_meetings',
        'buyer_follow_ups',
        'buyer_documents',
        'buyer_analysis',
        'buyer_scoring',
        'marketing_campaigns',
        'buyer_drop_reasons',
        'reports',
    ];

    /**
     * Available actions for permissions.
     *
     * @var array<string>
     */
    public static array $actions = [
        'view',
        'create',
        'edit',
        'delete',
        'manage',
    ];

    /**
     * Get the users that have this permission.
     */
    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'user_permissions');
    }

    /**
     * Scope a query to only include permissions for a specific module.
     */
    public function scopeForModule($query, string $module)
    {
        return $query->where('module', $module);
    }

    /**
     * Scope a query to only include permissions for a specific action.
     */
    public function scopeForAction($query, string $action)
    {
        return $query->where('action', $action);
    }

    /**
     * Get formatted permission name for display.
     */
    public function getDisplayNameAttribute(): string
    {
        return ucwords(str_replace('_', ' ', $this->module)) . ' - ' . ucfirst($this->action);
    }

    /**
     * Check if this is a management permission.
     */
    public function isManagePermission(): bool
    {
        return $this->action === 'manage';
    }

    /**
     * Generate all permissions for a module.
     */
    public static function generateForModule(string $module, array $actions = null): array
    {
        $actions = $actions ?? self::$actions;
        $permissions = [];

        foreach ($actions as $action) {
            $permissions[] = [
                'name' => "{$module}.{$action}",
                'module' => $module,
                'action' => $action,
                'description' => "Can {$action} " . str_replace('_', ' ', $module),
            ];
        }

        return $permissions;
    }

    /**
     * Create all system permissions.
     */
    public static function createSystemPermissions(): void
    {
        $allPermissions = [];

        foreach (self::$modules as $module) {
            $modulePermissions = self::generateForModule($module);
            $allPermissions = array_merge($allPermissions, $modulePermissions);
        }

        foreach ($allPermissions as $permissionData) {
            self::firstOrCreate(
                ['name' => $permissionData['name']],
                $permissionData
            );
        }
    }

    /**
     * Get permissions grouped by module.
     */
    public static function getGroupedPermissions(): array
    {
        return self::all()
            ->groupBy('module')
            ->map(function ($permissions) {
                return $permissions->keyBy('action');
            })
            ->toArray();
    }
}
