<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProductionSetting extends Model
{
    use HasFactory;

    protected $fillable = ['key', 'value', 'description'];

    /**
     * Get a setting value by key
     */
    public static function getValue($key, $default = 0)
    {
        $setting = self::where('key', $key)->first();
        return $setting ? (float) $setting->value : $default;
    }

    /**
     * Set a setting value by key
     */
    public static function setValue($key, $value, $description = null)
    {
        return self::updateOrCreate(
            ['key' => $key],
            [
                'value' => $value,
                'description' => $description
            ]
        );
    }

    /**
     * Get all production target settings
     */
    public static function getProductionTargetSettings()
    {
        return [
            'daily_floor_cost' => self::getValue('daily_floor_cost', 200),
            'daily_electricity_cost' => self::getValue('daily_electricity_cost', 200),
            'daily_depreciation_cost' => self::getValue('daily_depreciation_cost', 1481),
            'per_piece_cost' => self::getValue('per_piece_cost', 65),
        ];
    }

    /**
     * Calculate production target based on settings
     */
    public static function calculateProductionTarget($laborCost)
    {
        $settings = self::getProductionTargetSettings();
        
        $totalCost = $laborCost + 
                    $settings['daily_floor_cost'] + 
                    $settings['daily_electricity_cost'] + 
                    $settings['daily_depreciation_cost'];
        
        $perPieceCost = $settings['per_piece_cost'];
        
        return $perPieceCost > 0 ? $totalCost / $perPieceCost : 0;
    }
}
