<?php

namespace App\Models;

use App\Traits\TenantScoped;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Salary extends Model
{
    use HasFactory;

    protected $fillable = [
        'employee_id',
        'user_id',
        'bank_id',
        'year',
        'month',
        'month_number',
        'base_salary',
        'attendance_days',
        'total_working_days',
        'absent_days',
        'late_days',
        'overtime_hours',
        'overtime_rate',
        'overtime_amount',
        'attendance_bonus',
        'attendance_bonus_eligible',
        'calculated_salary',
        'advance_amount',
        'other_deductions',
        'net_salary',
        'payment_status',
        'payment_date',
        'payment_account_id',
        'payment_reference',
        'notes',
        'remarks',
        'meta',
        // Legacy fields for compatibility
        'voucher_id',
        'payment_method',
        'amount',
        'due_salary',
    ];

    protected $casts = [
        'meta' => 'json',
        'base_salary' => 'decimal:2',
        'attendance_days' => 'decimal:2',
        'overtime_hours' => 'decimal:2',
        'overtime_rate' => 'decimal:2',
        'overtime_amount' => 'decimal:2',
        'attendance_bonus' => 'decimal:2',
        'attendance_bonus_eligible' => 'boolean',
        'calculated_salary' => 'decimal:2',
        'advance_amount' => 'decimal:2',
        'other_deductions' => 'decimal:2',
        'net_salary' => 'decimal:2',
        'payment_date' => 'date',
        // Legacy fields
        'amount' => 'decimal:2',
        'due_salary' => 'decimal:2',
    ];

    /**
     * Relationships
     */
    public function employee(): BelongsTo
    {
        return $this->belongsTo(Employee::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function bank(): BelongsTo
    {
        return $this->belongsTo(Bank::class);
    }

    public function paymentAccount(): BelongsTo
    {
        return $this->belongsTo(Bank::class, 'payment_account_id');
    }

    /**
     * Scope for specific period
     */
    public function scopeForPeriod($query, $year, $month)
    {
        return $query->where('year', $year)->where('month', $month);
    }

    /**
     * Scope for paid salaries
     */
    public function scopePaid($query)
    {
        return $query->where('payment_status', 'paid');
    }

    /**
     * Scope for unpaid salaries
     */
    public function scopeUnpaid($query)
    {
        return $query->where('payment_status', 'unpaid');
    }

    /**
     * Get formatted base salary
     */
    public function getFormattedBaseSalaryAttribute()
    {
        return '৳' . number_format($this->base_salary, 2);
    }

    /**
     * Get formatted calculated salary
     */
    public function getFormattedCalculatedSalaryAttribute()
    {
        return '৳' . number_format($this->calculated_salary, 2);
    }

    /**
     * Get payment status badge
     */
    public function getPaymentStatusBadgeAttribute()
    {
        return match($this->payment_status) {
            'paid' => 'success',
            'unpaid' => 'danger',
            'partial' => 'warning',
            default => 'secondary',
        };
    }

    /**
     * Get month name
     */
    public function getMonthNameAttribute()
    {
        return date('F', mktime(0, 0, 0, $this->month, 1));
    }

    /**
     * Get period string
     */
    public function getPeriodAttribute()
    {
        return $this->month_name . ' ' . $this->year;
    }

    /**
     * Calculate comprehensive salary based on attendance and overtime
     */
    public static function calculateComprehensiveSalary($employee, $year, $month, $overtimeHours = 0, $advanceAmount = 0, $otherDeductions = 0)
    {
        // Get attendance data for the month
        $attendanceData = static::getAttendanceData($employee, $year, $month);

        // Get salary settings
        $workingDaysPerMonth = SalarySetting::get('working_days_per_month', 30);
        $overtimeRate = SalarySetting::get('overtime_rate_per_hour', 50);
        $attendanceBonusAmount = SalarySetting::get('attendance_bonus_amount', 500);
        $maxLateDays = SalarySetting::get('max_late_days_for_bonus', 2);
        $maxAbsentDays = SalarySetting::get('max_absent_days_for_bonus', 0);

        // Calculate base salary components
        $baseSalary = $employee->basic_salary ?? $employee->salary ?? 0;
        $dailyRate = $baseSalary / $workingDaysPerMonth;
        $attendanceBasedSalary = $dailyRate * $attendanceData['present_days'];

        // Calculate overtime
        $overtimeAmount = $overtimeHours * $overtimeRate;

        // Check attendance bonus eligibility
        $bonusEligible = ($attendanceData['late_days'] <= $maxLateDays) &&
                        ($attendanceData['absent_days'] <= $maxAbsentDays);
        $attendanceBonus = $bonusEligible ? $attendanceBonusAmount : 0;

        // Calculate totals
        $calculatedSalary = $attendanceBasedSalary + $overtimeAmount + $attendanceBonus;
        $netSalary = $calculatedSalary - $advanceAmount - $otherDeductions;

        return [
            'base_salary' => $baseSalary,
            'attendance_days' => $attendanceData['present_days'],
            'total_working_days' => $attendanceData['total_days'],
            'absent_days' => $attendanceData['absent_days'],
            'late_days' => $attendanceData['late_days'],
            'overtime_hours' => $overtimeHours,
            'overtime_rate' => $overtimeRate,
            'overtime_amount' => $overtimeAmount,
            'attendance_bonus_eligible' => $bonusEligible,
            'attendance_bonus' => $attendanceBonus,
            'calculated_salary' => $calculatedSalary,
            'advance_amount' => $advanceAmount,
            'other_deductions' => $otherDeductions,
            'net_salary' => $netSalary,
        ];
    }

    /**
     * Get attendance data for an employee for a specific month
     */
    public static function getAttendanceData($employee, $year, $month)
    {
        $startDate = \Carbon\Carbon::create($year, $month, 1)->startOfMonth();
        $endDate = \Carbon\Carbon::create($year, $month, 1)->endOfMonth();
        $totalDays = $startDate->daysInMonth;

        $attendances = Attendance::where('employee_id', $employee->id)
                                ->whereBetween('date', [$startDate, $endDate])
                                ->get();

        $presentDays = $attendances->where('status', 'present')->count();
        $halfDays = $attendances->where('status', 'half_day')->count();
        $absentDays = $attendances->where('status', 'absent')->count();

        // Calculate late days (this would need to be enhanced based on your attendance tracking)
        // For now, we'll assume late days are tracked separately or calculated from in_time
        $lateDays = $attendances->filter(function ($attendance) {
            // Assuming 9:00 AM is the standard start time
            return $attendance->in_time && $attendance->in_time > '09:00:00';
        })->count();

        // Half days count as 0.5 for present days calculation
        $effectivePresentDays = $presentDays + ($halfDays * 0.5);

        return [
            'present_days' => $effectivePresentDays,
            'total_days' => $totalDays,
            'absent_days' => $absentDays,
            'late_days' => $lateDays,
            'half_days' => $halfDays,
            'attendance_percentage' => ($effectivePresentDays / $totalDays) * 100,
        ];
    }

    /**
     * Legacy method for backward compatibility
     */
    public static function calculateSalary($baseSalary, $attendanceDays, $overtimeHours = 0, $attendanceBonus = 0)
    {
        $workingDaysPerMonth = SalarySetting::get('working_days_per_month', 30);
        $overtimeRate = SalarySetting::get('overtime_rate_per_hour', 50);

        $dailyRate = $baseSalary / $workingDaysPerMonth;
        $attendanceBasedSalary = $dailyRate * $attendanceDays;
        $overtimeAmount = $overtimeHours * $overtimeRate;

        return $attendanceBasedSalary + $overtimeAmount + $attendanceBonus;
    }
}
