<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Traits\TenantScoped;
use Illuminate\Database\Eloquent\Model;

class ShipmentDetail extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'qty',
        'size',
        'item',
        'style',
        'color',
        'total_cm',
        'total_sale',
        'shipment_id',
        'shipment_date',
    ];

    public function shipment()
    {
        return $this->belongsTo(Shipment::class);
    }

    /**
     * Get the buyer (party) through shipment -> order -> party relationship.
     */
    public function buyer()
    {
        return $this->hasOneThrough(
            Party::class,
            Shipment::class,
            'id', // Foreign key on shipments table
            'id', // Foreign key on parties table
            'shipment_id', // Local key on shipment_details table
            'order_id' // Local key on shipments table
        )->join('orders', 'orders.id', '=', 'shipments.order_id')
         ->where('orders.party_id', '=', 'parties.id');
    }

    /**
     * Get the order through shipment.
     */
    public function order()
    {
        return $this->hasOneThrough(Order::class, Shipment::class, 'id', 'id', 'shipment_id', 'order_id');
    }

    /**
     * Cast qty to integer.
     */
    protected $casts = [
        'qty' => 'integer',
        'total_cm' => 'decimal:3',
        'total_sale' => 'decimal:3',
    ];
}
