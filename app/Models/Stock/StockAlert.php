<?php

namespace App\Models\Stock;

use App\Traits\TenantScoped;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class StockAlert extends Model
{
    use HasFactory;

    protected $table = 'stock_alerts';

    protected $fillable = [
        'item_id',
        'location_id',
        'alert_type',
        'title',
        'message',
        'priority',
        'current_quantity',
        'threshold_quantity',
        'reorder_level',
        'expiry_date',
        'days_to_expiry',
        'status',
        'alert_date',
        'acknowledged_at',
        'resolved_at',
        'acknowledged_by',
        'resolved_by',
        'resolution_notes',
        'email_sent',
        'email_sent_at',
        'notified_users',
    ];

    protected $casts = [
        'current_quantity' => 'decimal:4',
        'threshold_quantity' => 'decimal:4',
        'reorder_level' => 'decimal:4',
        'expiry_date' => 'date',
        'alert_date' => 'datetime',
        'acknowledged_at' => 'datetime',
        'resolved_at' => 'datetime',
        'email_sent' => 'boolean',
        'email_sent_at' => 'datetime',
        'notified_users' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * Get the item that owns the alert
     */
    public function item(): BelongsTo
    {
        return $this->belongsTo(StockItem::class, 'item_id');
    }

    /**
     * Get the location that owns the alert
     */
    public function location(): BelongsTo
    {
        return $this->belongsTo(StockLocation::class, 'location_id');
    }

    /**
     * Get the user who acknowledged the alert
     */
    public function acknowledger(): BelongsTo
    {
        return $this->belongsTo(User::class, 'acknowledged_by');
    }

    /**
     * Get the user who resolved the alert
     */
    public function resolver(): BelongsTo
    {
        return $this->belongsTo(User::class, 'resolved_by');
    }

    /**
     * Scope to get active alerts
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope to get acknowledged alerts
     */
    public function scopeAcknowledged($query)
    {
        return $query->where('status', 'acknowledged');
    }

    /**
     * Scope to get resolved alerts
     */
    public function scopeResolved($query)
    {
        return $query->where('status', 'resolved');
    }

    /**
     * Scope to filter by alert type
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('alert_type', $type);
    }

    /**
     * Scope to filter by priority
     */
    public function scopeWithPriority($query, $priority)
    {
        return $query->where('priority', $priority);
    }

    /**
     * Scope to get critical alerts
     */
    public function scopeCritical($query)
    {
        return $query->where('priority', 'critical');
    }

    /**
     * Scope to get high priority alerts
     */
    public function scopeHighPriority($query)
    {
        return $query->where('priority', 'high');
    }

    /**
     * Get the alert type options
     */
    public static function getAlertTypeOptions(): array
    {
        return [
            'low_stock' => 'Low Stock',
            'out_of_stock' => 'Out of Stock',
            'overstock' => 'Overstock',
            'expiry_warning' => 'Expiry Warning',
            'expiry_critical' => 'Expiry Critical',
            'negative_stock' => 'Negative Stock',
        ];
    }

    /**
     * Get the priority options
     */
    public static function getPriorityOptions(): array
    {
        return [
            'low' => 'Low',
            'medium' => 'Medium',
            'high' => 'High',
            'critical' => 'Critical',
        ];
    }

    /**
     * Get the status options
     */
    public static function getStatusOptions(): array
    {
        return [
            'active' => 'Active',
            'acknowledged' => 'Acknowledged',
            'resolved' => 'Resolved',
            'dismissed' => 'Dismissed',
        ];
    }

    /**
     * Get the formatted alert type name
     */
    public function getAlertTypeNameAttribute(): string
    {
        return self::getAlertTypeOptions()[$this->alert_type] ?? $this->alert_type;
    }

    /**
     * Get the formatted priority name
     */
    public function getPriorityNameAttribute(): string
    {
        return self::getPriorityOptions()[$this->priority] ?? $this->priority;
    }

    /**
     * Get the formatted status name
     */
    public function getStatusNameAttribute(): string
    {
        return self::getStatusOptions()[$this->status] ?? $this->status;
    }

    /**
     * Get alert type color for UI
     */
    public function getAlertTypeColorAttribute(): string
    {
        return match($this->alert_type) {
            'low_stock' => 'warning',
            'out_of_stock' => 'danger',
            'overstock' => 'info',
            'expiry_warning' => 'warning',
            'expiry_critical' => 'danger',
            'negative_stock' => 'danger',
            default => 'secondary'
        };
    }

    /**
     * Get priority color for UI
     */
    public function getPriorityColorAttribute(): string
    {
        return match($this->priority) {
            'low' => 'secondary',
            'medium' => 'info',
            'high' => 'warning',
            'critical' => 'danger',
            default => 'secondary'
        };
    }

    /**
     * Get status color for UI
     */
    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            'active' => 'danger',
            'acknowledged' => 'warning',
            'resolved' => 'success',
            'dismissed' => 'secondary',
            default => 'secondary'
        };
    }

    /**
     * Check if alert can be acknowledged
     */
    public function canBeAcknowledged(): bool
    {
        return $this->status === 'active';
    }

    /**
     * Check if alert can be resolved
     */
    public function canBeResolved(): bool
    {
        return in_array($this->status, ['active', 'acknowledged']);
    }

    /**
     * Check if alert can be dismissed
     */
    public function canBeDismissed(): bool
    {
        return in_array($this->status, ['active', 'acknowledged']);
    }

    /**
     * Acknowledge the alert
     */
    public function acknowledge(User $user): bool
    {
        if (!$this->canBeAcknowledged()) {
            return false;
        }

        $this->update([
            'status' => 'acknowledged',
            'acknowledged_by' => $user->id,
            'acknowledged_at' => now(),
        ]);

        return true;
    }

    /**
     * Resolve the alert
     */
    public function resolve(User $user, string $notes = null): bool
    {
        if (!$this->canBeResolved()) {
            return false;
        }

        $this->update([
            'status' => 'resolved',
            'resolved_by' => $user->id,
            'resolved_at' => now(),
            'resolution_notes' => $notes,
        ]);

        return true;
    }

    /**
     * Dismiss the alert
     */
    public function dismiss(): bool
    {
        if (!$this->canBeDismissed()) {
            return false;
        }

        $this->update(['status' => 'dismissed']);

        return true;
    }

    /**
     * Mark email as sent
     */
    public function markEmailSent(array $userIds = []): void
    {
        $this->update([
            'email_sent' => true,
            'email_sent_at' => now(),
            'notified_users' => $userIds,
        ]);
    }

    /**
     * Check if alert is expired (for expiry alerts)
     */
    public function getIsExpiredAttribute(): bool
    {
        if (!$this->expiry_date) {
            return false;
        }

        return $this->expiry_date->isPast();
    }

    /**
     * Get days until expiry
     */
    public function getDaysUntilExpiryAttribute(): int
    {
        if (!$this->expiry_date) {
            return 0;
        }

        return now()->diffInDays($this->expiry_date, false);
    }

    /**
     * Create a low stock alert
     */
    public static function createLowStockAlert(StockItem $item, ?StockLocation $location = null): self
    {
        return self::create([
            'item_id' => $item->id,
            'location_id' => $location?->id,
            'alert_type' => 'low_stock',
            'title' => "Low Stock Alert: {$item->name}",
            'message' => "Item {$item->sku} is running low. Current quantity: {$item->current_quantity}, Reorder level: {$item->reorder_level}",
            'priority' => 'medium',
            'current_quantity' => $item->current_quantity,
            'threshold_quantity' => $item->reorder_level,
            'reorder_level' => $item->reorder_level,
            'alert_date' => now(),
        ]);
    }

    /**
     * Create an out of stock alert
     */
    public static function createOutOfStockAlert(StockItem $item, ?StockLocation $location = null): self
    {
        return self::create([
            'item_id' => $item->id,
            'location_id' => $location?->id,
            'alert_type' => 'out_of_stock',
            'title' => "Out of Stock Alert: {$item->name}",
            'message' => "Item {$item->sku} is out of stock. Current quantity: {$item->current_quantity}",
            'priority' => 'high',
            'current_quantity' => $item->current_quantity,
            'threshold_quantity' => 0,
            'reorder_level' => $item->reorder_level,
            'alert_date' => now(),
        ]);
    }

    /**
     * Create an expiry alert
     */
    public static function createExpiryAlert(StockItem $item, \Carbon\Carbon $expiryDate, ?StockLocation $location = null): self
    {
        $daysToExpiry = now()->diffInDays($expiryDate, false);
        $priority = $daysToExpiry <= 7 ? 'critical' : 'medium';
        $alertType = $daysToExpiry <= 7 ? 'expiry_critical' : 'expiry_warning';

        return self::create([
            'item_id' => $item->id,
            'location_id' => $location?->id,
            'alert_type' => $alertType,
            'title' => "Expiry Alert: {$item->name}",
            'message' => "Item {$item->sku} will expire in {$daysToExpiry} days on {$expiryDate->format('Y-m-d')}",
            'priority' => $priority,
            'current_quantity' => $item->current_quantity,
            'expiry_date' => $expiryDate,
            'days_to_expiry' => $daysToExpiry,
            'alert_date' => now(),
        ]);
    }
}
