<?php

namespace App\Models\Stock;

use App\Traits\TenantScoped;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class StockItem extends Model
{
    use HasFactory, TenantScoped;

    protected $table = 'stock_items';

    protected $fillable = [
        'category_id',
        'name',
        'sku',
        'type',
        'description',
        'color',
        'size',
        'unit_of_measure',
        'current_quantity',
        'available_quantity',
        'reserved_quantity',
        'reorder_level',
        'max_level',
        'cost_price',
        'average_cost',
        'costing_method',
        'location_id',
        'is_active',
        'dimensions',
        'barcode',
        'barcode_type',
        'custom_attributes',
        'tags',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'current_quantity' => 'decimal:4',
        'available_quantity' => 'decimal:4',
        'reserved_quantity' => 'decimal:4',
        'reorder_level' => 'decimal:4',
        'max_level' => 'decimal:4',
        'cost_price' => 'decimal:4',
        'is_active' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    protected $appends = [
        'stock_status',
        'total_value',
    ];

    /**
     * Get the category that owns the item
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(StockItemCategory::class, 'category_id');
    }

    /**
     * Get the primary location
     */
    public function primaryLocation(): BelongsTo
    {
        return $this->belongsTo(StockLocation::class, 'primary_location_id');
    }

    /**
     * Get the user who created this item
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who last updated this item
     */
    public function updater(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Get all stock transactions for this item
     */
    public function stockTransactions(): HasMany
    {
        return $this->hasMany(StockTransaction::class, 'item_id');
    }

    /**
     * Get all stock transfers for this item
     */
    public function stockTransfers(): HasMany
    {
        return $this->hasMany(StockTransfer::class, 'item_id');
    }

    /**
     * Get all physical counts for this item
     */
    public function physicalCounts(): HasMany
    {
        return $this->hasMany(StockPhysicalCount::class, 'item_id');
    }

    /**
     * Get all location items (stock levels per location)
     */
    public function locationItems(): HasMany
    {
        return $this->hasMany(StockLocationItem::class, 'item_id');
    }

    /**
     * Get all locations where this item is stored
     */
    public function locations()
    {
        return $this->belongsToMany(StockLocation::class, 'stock_location_items', 'item_id', 'location_id')
            ->withPivot(['quantity'])
            ->withTimestamps();
    }

    /**
     * Get all alerts for this item
     */
    public function alerts(): HasMany
    {
        return $this->hasMany(StockAlert::class, 'item_id');
    }

    /**
     * Get the stock movements for the item.
     */
    public function stockMovements(): HasMany
    {
        return $this->hasMany(StockMovement::class, 'item_id');
    }

    /**
     * Get the batches for the stock item.
     */
    public function batches(): HasMany
    {
        return $this->hasMany(StockBatch::class, 'item_id');
    }

    /**
     * Get the reservations for the stock item.
     */
    public function reservations(): HasMany
    {
        return $this->hasMany(StockReservation::class, 'item_id');
    }

    /**
     * Scope to get only active items
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get only inactive items
     */
    public function scopeInactive($query)
    {
        return $query->where('is_active', false);
    }

    /**
     * Scope to filter by item type
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('item_type', $type);
    }

    /**
     * Scope to filter by location.
     */
    public function scopeInLocation($query, int $locationId)
    {
        return $query->whereHas('locationItems', function ($q) use ($locationId) {
            $q->where('location_id', $locationId)->where('quantity', '>', 0);
        });
    }

    /**
     * Scope to search by name, SKU, or description.
     */
    public function scopeSearch($query, string $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('name', 'like', "%{$search}%")
              ->orWhere('sku', 'like', "%{$search}%")
              ->orWhere('description', 'like', "%{$search}%")
              ->orWhere('style_number', 'like', "%{$search}%");
        });
    }

    /**
     * Scope to filter by season.
     */
    public function scopeBySeason($query, string $season)
    {
        return $query->where('season', $season);
    }

    /**
     * Scope to filter by quality grade.
     */
    public function scopeByQualityGrade($query, string $grade)
    {
        return $query->where('quality_grade', $grade);
    }

    /**
     * Scope to filter items requiring reorder.
     */
    public function scopeRequiringReorder($query)
    {
        return $query->lowStock()->active();
    }

    /**
     * Scope to filter by category
     */
    public function scopeInCategory($query, $categoryId)
    {
        return $query->where('category_id', $categoryId);
    }

    /**
     * Scope to get low stock items
     */
    public function scopeLowStock($query)
    {
        return $query->whereColumn('current_quantity', '<=', 'reorder_level');
    }

    /**
     * Scope to get out of stock items
     */
    public function scopeOutOfStock($query)
    {
        return $query->where('current_quantity', '<=', 0);
    }

    /**
     * Scope to get overstock items
     */
    public function scopeOverstock($query)
    {
        return $query->whereColumn('current_quantity', '>', 'max_level')
            ->where('max_level', '>', 0);
    }

    /**
     * Get the item type options
     */
    public static function getItemTypeOptions(): array
    {
        return [
            'fabric' => 'Fabric',
            'accessory' => 'Accessory',
            'wip' => 'Work in Progress',
            'finished_goods' => 'Finished Goods',
        ];
    }

    /**
     * Get the costing method options
     */
    public static function getCostingMethodOptions(): array
    {
        return [
            'fifo' => 'First In, First Out (FIFO)',
            'lifo' => 'Last In, First Out (LIFO)',
            'average' => 'Weighted Average',
        ];
    }

    /**
     * Get the unit of measure options
     */
    public static function getUnitOfMeasureOptions(): array
    {
        return [
            'pcs' => 'Pieces',
            'yards' => 'Yards',
            'meters' => 'Meters',
            'kg' => 'Kilograms',
            'grams' => 'Grams',
            'dozen' => 'Dozen',
            'rolls' => 'Rolls',
            'boxes' => 'Boxes',
        ];
    }

    /**
     * Get the formatted item type name
     */
    public function getItemTypeNameAttribute(): string
    {
        return self::getItemTypeOptions()[$this->item_type] ?? $this->item_type;
    }

    /**
     * Get the formatted costing method name
     */
    public function getCostingMethodNameAttribute(): string
    {
        return self::getCostingMethodOptions()[$this->costing_method] ?? $this->costing_method;
    }

    /**
     * Get the formatted unit of measure name
     */
    public function getUnitOfMeasureNameAttribute(): string
    {
        return self::getUnitOfMeasureOptions()[$this->unit_of_measure] ?? $this->unit_of_measure;
    }

    /**
     * Check if item is low stock
     */
    public function getIsLowStockAttribute(): bool
    {
        return $this->current_quantity <= $this->reorder_level;
    }

    /**
     * Check if item is out of stock
     */
    public function getIsOutOfStockAttribute(): bool
    {
        return $this->current_quantity <= 0;
    }

    /**
     * Check if item is overstock
     */
    public function getIsOverstockAttribute(): bool
    {
        return $this->max_level > 0 && $this->current_quantity > $this->max_level;
    }

    /**
     * Get stock status
     */
    public function getStockStatusAttribute(): string
    {
        if ($this->is_out_of_stock) {
            return 'out_of_stock';
        } elseif ($this->is_low_stock) {
            return 'low_stock';
        } elseif ($this->is_overstock) {
            return 'overstock';
        } else {
            return 'normal';
        }
    }

    /**
     * Get stock status color
     */
    public function getStockStatusColorAttribute(): string
    {
        return match($this->stock_status) {
            'out_of_stock' => 'danger',
            'low_stock' => 'warning',
            'overstock' => 'info',
            default => 'success'
        };
    }

    /**
     * Get total stock value
     */
    public function getTotalValueAttribute(): float
    {
        return $this->current_quantity * $this->cost_price;
    }

    /**
     * Generate next SKU
     */
    public static function generateSku(string $itemType, ?int $categoryId = null): string
    {
        $prefix = match($itemType) {
            'fabric' => 'FAB',
            'accessory' => 'ACC',
            'wip' => 'WIP',
            'finished_goods' => 'FG',
            default => 'ITM'
        };

        if ($categoryId) {
            $category = StockItemCategory::find($categoryId);
            if ($category) {
                $prefix = $category->code;
            }
        }

        $lastItem = self::where('sku', 'like', $prefix . '-%')
            ->orderBy('sku', 'desc')
            ->first();

        if ($lastItem) {
            $lastNumber = (int) substr($lastItem->sku, strlen($prefix) + 1);
            $nextNumber = $lastNumber + 1;
        } else {
            $nextNumber = 1;
        }

        return $prefix . '-' . str_pad($nextNumber, 6, '0', STR_PAD_LEFT);
    }

    /**
     * Reserve quantity for the item.
     */
    public function reserveQuantity(float $quantity): bool
    {
        if ($this->current_quantity < $quantity) {
            return false;
        }

        // For now, just reduce current quantity
        // In future, implement proper reservation system
        $this->current_quantity -= $quantity;

        return $this->save();
    }

    /**
     * Release reserved quantity for the item.
     */
    public function releaseReservedQuantity(float $quantity): bool
    {
        // For now, just increase current quantity
        // In future, implement proper reservation system
        $this->current_quantity += $quantity;

        return $this->save();
    }

    /**
     * Adjust stock quantity.
     */
    public function adjustQuantity(float $adjustment, string $reason = null): bool
    {
        $newQuantity = $this->current_quantity + $adjustment;

        if ($newQuantity < 0) {
            return false;
        }

        $this->current_quantity = $newQuantity;

        return $this->save();
    }

    /**
     * Update available quantity based on current and reserved quantities.
     */
    public function updateAvailableQuantity(): void
    {
        // For now, this is a no-op since we don't have separate available_quantity
        // In future, implement proper reservation system
    }

    /**
     * Get the stock level percentage.
     */
    public function getStockLevelPercentage(): float
    {
        if ($this->max_level <= 0) {
            return 0;
        }

        return ($this->current_quantity / $this->max_level) * 100;
    }

    /**
     * Check if the item requires reorder.
     */
    public function requiresReorder(): bool
    {
        return $this->isLowStock() && $this->is_active;
    }

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($item) {
            if (empty($item->sku)) {
                $item->sku = self::generateSku($item->type, $item->category_id);
            }
        });

        static::updating(function ($item) {
            // No additional processing needed for now
        });
    }
}
