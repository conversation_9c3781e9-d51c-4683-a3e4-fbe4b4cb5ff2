<?php

namespace App\Models\Stock;

use App\Traits\TenantScoped;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class StockItemCategory extends Model
{
    use HasFactory, SoftDeletes, TenantScoped;

    protected $table = 'stock_item_categories';

    protected $fillable = [
        'name',
        'code',
        'description',
        'type',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * Get all items in this category
     */
    public function items(): HasMany
    {
        return $this->hasMany(StockItem::class, 'category_id');
    }

    /**
     * Get active items in this category
     */
    public function activeItems(): HasMany
    {
        return $this->hasMany(StockItem::class, 'category_id')->where('is_active', true);
    }

    /**
     * Scope to get only active categories
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to filter by type
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Get the type options
     */
    public static function getTypeOptions(): array
    {
        return [
            'fabric' => 'Fabric',
            'accessory' => 'Accessory',
            'wip' => 'Work in Progress',
            'finished_goods' => 'Finished Goods',
        ];
    }

    /**
     * Get the formatted type name
     */
    public function getTypeNameAttribute(): string
    {
        return self::getTypeOptions()[$this->type] ?? $this->type;
    }

    /**
     * Get items count for this category
     */
    public function getItemsCountAttribute(): int
    {
        return $this->items()->count();
    }

    /**
     * Get active items count for this category
     */
    public function getActiveItemsCountAttribute(): int
    {
        return $this->activeItems()->count();
    }

    /**
     * Generate next category code
     */
    public static function generateCode(string $type): string
    {
        $prefix = match($type) {
            'fabric' => 'FAB',
            'accessory' => 'ACC',
            'wip' => 'WIP',
            'finished_goods' => 'FG',
            default => 'CAT'
        };

        $lastCategory = self::where('code', 'like', $prefix . '-%')
            ->orderBy('code', 'desc')
            ->first();

        if ($lastCategory) {
            $lastNumber = (int) substr($lastCategory->code, strlen($prefix) + 1);
            $nextNumber = $lastNumber + 1;
        } else {
            $nextNumber = 1;
        }

        return $prefix . '-' . str_pad($nextNumber, 3, '0', STR_PAD_LEFT);
    }

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($category) {
            if (empty($category->code)) {
                $category->code = self::generateCode($category->type);
            }
        });
    }
}
