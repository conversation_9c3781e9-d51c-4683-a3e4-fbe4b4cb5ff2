<?php

namespace App\Models\Stock;

use App\Traits\TenantScoped;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class StockLocation extends Model
{
    use HasFactory, SoftDeletes, TenantScoped;

    protected $table = 'stock_locations';

    protected $fillable = [
        'name',
        'code',
        'address',
        'type',
        'manager_name',
        'contact_number',
        'capacity',
        'is_active',
    ];

    protected $casts = [
        'capacity' => 'decimal:2',
        'is_active' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * Get all stock transactions for this location
     */
    public function stockTransactions(): HasMany
    {
        return $this->hasMany(StockTransaction::class, 'location_id');
    }

    /**
     * Get all stock transfers from this location
     */
    public function transfersFrom(): HasMany
    {
        return $this->hasMany(StockTransfer::class, 'from_location_id');
    }

    /**
     * Get all stock transfers to this location
     */
    public function transfersTo(): HasMany
    {
        return $this->hasMany(StockTransfer::class, 'to_location_id');
    }

    /**
     * Get all physical counts for this location
     */
    public function physicalCounts(): HasMany
    {
        return $this->hasMany(StockPhysicalCount::class, 'location_id');
    }

    /**
     * Get all location items (stock levels per item)
     */
    public function locationItems(): HasMany
    {
        return $this->hasMany(StockLocationItem::class, 'location_id');
    }

    /**
     * Get all items stored in this location
     */
    public function items()
    {
        return $this->belongsToMany(StockItem::class, 'stock_location_items', 'location_id', 'item_id')
            ->withPivot(['quantity', 'reserved_quantity', 'available_quantity', 'damaged_quantity'])
            ->withTimestamps();
    }

    /**
     * Get all alerts for this location
     */
    public function alerts(): HasMany
    {
        return $this->hasMany(StockAlert::class, 'location_id');
    }

    /**
     * Scope to get only active locations
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to filter by type
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Get the type options
     */
    public static function getTypeOptions(): array
    {
        return [
            'warehouse' => 'Warehouse',
            'factory' => 'Factory',
            'store' => 'Store',
            'production_floor' => 'Production Floor',
        ];
    }

    /**
     * Get the formatted type name
     */
    public function getTypeNameAttribute(): string
    {
        return self::getTypeOptions()[$this->type] ?? $this->type;
    }

    /**
     * Get total stock value for this location
     */
    public function getTotalStockValueAttribute(): float
    {
        return $this->locationItems()->sum('total_value');
    }

    /**
     * Get total items count for this location
     */
    public function getTotalItemsCountAttribute(): int
    {
        return $this->locationItems()->count();
    }

    /**
     * Get capacity utilization percentage
     */
    public function getCapacityUtilizationAttribute(): float
    {
        if (!$this->capacity || $this->capacity <= 0) {
            return 0;
        }

        $usedCapacity = $this->locationItems()->sum('quantity');
        return ($usedCapacity / $this->capacity) * 100;
    }

    /**
     * Check if location has sufficient capacity
     */
    public function hasSufficientCapacity(float $additionalQuantity = 0): bool
    {
        if (!$this->capacity || $this->capacity <= 0) {
            return true; // No capacity limit set
        }

        $currentUsage = $this->locationItems()->sum('quantity');
        return ($currentUsage + $additionalQuantity) <= $this->capacity;
    }

    /**
     * Generate next location code
     */
    public static function generateCode(string $type): string
    {
        $prefix = match($type) {
            'warehouse' => 'WH',
            'factory' => 'FAC',
            'store' => 'STR',
            'production_floor' => 'PF',
            default => 'LOC'
        };

        $lastLocation = self::where('code', 'like', $prefix . '-%')
            ->orderBy('code', 'desc')
            ->first();

        if ($lastLocation) {
            $lastNumber = (int) substr($lastLocation->code, strlen($prefix) + 1);
            $nextNumber = $lastNumber + 1;
        } else {
            $nextNumber = 1;
        }

        return $prefix . '-' . str_pad($nextNumber, 3, '0', STR_PAD_LEFT);
    }

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($location) {
            if (empty($location->code)) {
                $location->code = self::generateCode($location->type);
            }
        });
    }
}
