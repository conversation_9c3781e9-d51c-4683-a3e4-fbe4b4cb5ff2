<?php

namespace App\Models\Stock;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class StockLocationItem extends Model
{
    use HasFactory;

    protected $table = 'stock_location_items';

    protected $fillable = [
        'item_id',
        'location_id',
        'quantity',
        'reserved_quantity',
        'available_quantity',
        'damaged_quantity',
        'min_quantity',
        'max_quantity',
        'reorder_point',
        'average_cost',
        'total_value',
        'last_transaction_date',
        'last_transaction_type',
        'last_count_date',
        'last_count_quantity',
        'last_count_variance',
    ];

    protected $casts = [
        'quantity' => 'decimal:4',
        'reserved_quantity' => 'decimal:4',
        'available_quantity' => 'decimal:4',
        'damaged_quantity' => 'decimal:4',
        'min_quantity' => 'decimal:4',
        'max_quantity' => 'decimal:4',
        'reorder_point' => 'decimal:4',
        'average_cost' => 'decimal:4',
        'total_value' => 'decimal:4',
        'last_count_quantity' => 'decimal:4',
        'last_count_variance' => 'decimal:4',
        'last_transaction_date' => 'datetime',
        'last_count_date' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the item that owns this location item
     */
    public function item(): BelongsTo
    {
        return $this->belongsTo(StockItem::class, 'item_id');
    }

    /**
     * Get the location that owns this location item
     */
    public function location(): BelongsTo
    {
        return $this->belongsTo(StockLocation::class, 'location_id');
    }

    /**
     * Scope to get items with low stock
     */
    public function scopeLowStock($query)
    {
        return $query->whereColumn('quantity', '<=', 'min_quantity')
            ->where('min_quantity', '>', 0);
    }

    /**
     * Scope to get items with no stock
     */
    public function scopeOutOfStock($query)
    {
        return $query->where('quantity', '<=', 0);
    }

    /**
     * Scope to get items with overstock
     */
    public function scopeOverstock($query)
    {
        return $query->whereColumn('quantity', '>', 'max_quantity')
            ->where('max_quantity', '>', 0);
    }

    /**
     * Scope to get items with negative stock
     */
    public function scopeNegativeStock($query)
    {
        return $query->where('quantity', '<', 0);
    }

    /**
     * Check if item is low stock at this location
     */
    public function getIsLowStockAttribute(): bool
    {
        return $this->min_quantity > 0 && $this->quantity <= $this->min_quantity;
    }

    /**
     * Check if item is out of stock at this location
     */
    public function getIsOutOfStockAttribute(): bool
    {
        return $this->quantity <= 0;
    }

    /**
     * Check if item is overstock at this location
     */
    public function getIsOverstockAttribute(): bool
    {
        return $this->max_quantity > 0 && $this->quantity > $this->max_quantity;
    }

    /**
     * Check if item has negative stock at this location
     */
    public function getIsNegativeStockAttribute(): bool
    {
        return $this->quantity < 0;
    }

    /**
     * Get stock status at this location
     */
    public function getStockStatusAttribute(): string
    {
        if ($this->is_negative_stock) {
            return 'negative_stock';
        } elseif ($this->is_out_of_stock) {
            return 'out_of_stock';
        } elseif ($this->is_low_stock) {
            return 'low_stock';
        } elseif ($this->is_overstock) {
            return 'overstock';
        } else {
            return 'normal';
        }
    }

    /**
     * Get stock status color for UI
     */
    public function getStockStatusColorAttribute(): string
    {
        return match($this->stock_status) {
            'negative_stock' => 'danger',
            'out_of_stock' => 'danger',
            'low_stock' => 'warning',
            'overstock' => 'info',
            default => 'success'
        };
    }

    /**
     * Get turnover rate (how many times stock turns over per year)
     */
    public function getTurnoverRateAttribute(): float
    {
        // This would need transaction history to calculate properly
        // For now, return a placeholder
        return 0;
    }

    /**
     * Get days of stock remaining
     */
    public function getDaysOfStockAttribute(): int
    {
        // This would need consumption history to calculate properly
        // For now, return a placeholder
        return 0;
    }

    /**
     * Update stock quantity
     */
    public function updateQuantity(float $newQuantity, string $transactionType = null): void
    {
        $this->update([
            'quantity' => $newQuantity,
            'available_quantity' => $newQuantity - $this->reserved_quantity,
            'total_value' => $newQuantity * $this->average_cost,
            'last_transaction_date' => now(),
            'last_transaction_type' => $transactionType,
        ]);
    }

    /**
     * Add to stock quantity
     */
    public function addQuantity(float $quantity, string $transactionType = null): void
    {
        $newQuantity = $this->quantity + $quantity;
        $this->updateQuantity($newQuantity, $transactionType);
    }

    /**
     * Subtract from stock quantity
     */
    public function subtractQuantity(float $quantity, string $transactionType = null): void
    {
        $newQuantity = $this->quantity - $quantity;
        $this->updateQuantity($newQuantity, $transactionType);
    }

    /**
     * Reserve stock quantity
     */
    public function reserveQuantity(float $quantity): bool
    {
        if ($this->available_quantity < $quantity) {
            return false; // Not enough available stock
        }

        $this->update([
            'reserved_quantity' => $this->reserved_quantity + $quantity,
            'available_quantity' => $this->available_quantity - $quantity,
        ]);

        return true;
    }

    /**
     * Release reserved stock quantity
     */
    public function releaseReservedQuantity(float $quantity): void
    {
        $releaseQuantity = min($quantity, $this->reserved_quantity);
        
        $this->update([
            'reserved_quantity' => $this->reserved_quantity - $releaseQuantity,
            'available_quantity' => $this->available_quantity + $releaseQuantity,
        ]);
    }

    /**
     * Update average cost using weighted average
     */
    public function updateAverageCost(float $newQuantity, float $newCost): void
    {
        if ($newQuantity <= 0) {
            return;
        }

        $currentValue = $this->quantity * $this->average_cost;
        $newValue = $newQuantity * $newCost;
        $totalQuantity = $this->quantity + $newQuantity;

        if ($totalQuantity > 0) {
            $newAverageCost = ($currentValue + $newValue) / $totalQuantity;
            $this->update([
                'average_cost' => $newAverageCost,
                'total_value' => $totalQuantity * $newAverageCost,
            ]);
        }
    }

    /**
     * Record physical count
     */
    public function recordPhysicalCount(float $countedQuantity): void
    {
        $variance = $countedQuantity - $this->quantity;
        
        $this->update([
            'last_count_date' => now(),
            'last_count_quantity' => $countedQuantity,
            'last_count_variance' => $variance,
        ]);
    }

    /**
     * Check if reorder is needed
     */
    public function needsReorder(): bool
    {
        return $this->reorder_point > 0 && $this->available_quantity <= $this->reorder_point;
    }

    /**
     * Get suggested reorder quantity
     */
    public function getSuggestedReorderQuantity(): float
    {
        if (!$this->needsReorder()) {
            return 0;
        }

        // Simple reorder logic: order up to max quantity
        if ($this->max_quantity > 0) {
            return $this->max_quantity - $this->quantity;
        }

        // If no max quantity set, order 2x the reorder point
        return $this->reorder_point * 2;
    }

    /**
     * Sync parent item totals
     */
    public function syncParentItem(): void
    {
        $item = $this->item;
        if (!$item) return;

        $totalQuantity = $item->locationItems()->sum('quantity');
        $totalReserved = $item->locationItems()->sum('reserved_quantity');
        $totalAvailable = $totalQuantity - $totalReserved;

        $item->update([
            'current_quantity' => $totalQuantity,
            'reserved_quantity' => $totalReserved,
            'available_quantity' => $totalAvailable,
        ]);
    }

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($locationItem) {
            // Calculate available quantity
            $locationItem->available_quantity = $locationItem->quantity - $locationItem->reserved_quantity;

            // Calculate total value
            $locationItem->total_value = $locationItem->quantity * $locationItem->average_cost;
        });

        static::created(function ($locationItem) {
            // Sync parent item after creation
            $locationItem->syncParentItem();
        });

        static::updating(function ($locationItem) {
            // Recalculate available quantity if quantity or reserved quantity changed
            if ($locationItem->isDirty(['quantity', 'reserved_quantity'])) {
                $locationItem->available_quantity = $locationItem->quantity - $locationItem->reserved_quantity;
            }

            // Recalculate total value if quantity or average cost changed
            if ($locationItem->isDirty(['quantity', 'average_cost'])) {
                $locationItem->total_value = $locationItem->quantity * $locationItem->average_cost;
            }
        });

        static::updated(function ($locationItem) {
            // Sync parent item after update if quantity-related fields changed
            if ($locationItem->wasChanged(['quantity', 'reserved_quantity'])) {
                $locationItem->syncParentItem();
            }
        });

        static::deleted(function ($locationItem) {
            // Sync parent item after deletion
            $locationItem->syncParentItem();
        });
    }
}
