<?php

namespace App\Models\Stock;

use App\Traits\TenantScoped;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class StockPhysicalCount extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'stock_physical_counts';

    protected $fillable = [
        'count_number',
        'item_id',
        'location_id',
        'system_quantity',
        'physical_quantity',
        'variance',
        'variance_percentage',
        'batch_number',
        'lot_number',
        'count_date',
        'count_time',
        'count_type',
        'adjustment_reason',
        'notes',
        'status',
        'counted_by',
        'verified_by',
        'approved_by',
        'counted_at',
        'verified_at',
        'approved_at',
        'unit_cost',
        'variance_value',
        'item_condition',
        'condition_notes',
    ];

    protected $casts = [
        'system_quantity' => 'decimal:4',
        'physical_quantity' => 'decimal:4',
        'variance' => 'decimal:4',
        'variance_percentage' => 'decimal:4',
        'unit_cost' => 'decimal:4',
        'variance_value' => 'decimal:4',
        'count_date' => 'date',
        'count_time' => 'datetime',
        'counted_at' => 'datetime',
        'verified_at' => 'datetime',
        'approved_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * Get the item being counted
     */
    public function item(): BelongsTo
    {
        return $this->belongsTo(StockItem::class, 'item_id');
    }

    /**
     * Get the location where count was performed
     */
    public function location(): BelongsTo
    {
        return $this->belongsTo(StockLocation::class, 'location_id');
    }

    /**
     * Get the user who performed the count
     */
    public function counter(): BelongsTo
    {
        return $this->belongsTo(User::class, 'counted_by');
    }

    /**
     * Get the user who verified the count
     */
    public function verifier(): BelongsTo
    {
        return $this->belongsTo(User::class, 'verified_by');
    }

    /**
     * Get the user who approved the count
     */
    public function approver(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Scope to filter by status
     */
    public function scopeWithStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to get pending counts
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope to get approved counts
     */
    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    /**
     * Scope to get posted counts
     */
    public function scopePosted($query)
    {
        return $query->where('status', 'posted');
    }

    /**
     * Scope to filter by count type
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('count_type', $type);
    }

    /**
     * Scope to filter by date range
     */
    public function scopeBetweenDates($query, $startDate, $endDate)
    {
        return $query->whereBetween('count_date', [$startDate, $endDate]);
    }

    /**
     * Scope to get counts with variance
     */
    public function scopeWithVariance($query)
    {
        return $query->where('variance', '!=', 0);
    }

    /**
     * Get the count type options
     */
    public static function getCountTypeOptions(): array
    {
        return [
            'full' => 'Full Count',
            'cycle' => 'Cycle Count',
            'spot' => 'Spot Count',
            'annual' => 'Annual Count',
        ];
    }

    /**
     * Get the status options
     */
    public static function getStatusOptions(): array
    {
        return [
            'draft' => 'Draft',
            'in_progress' => 'In Progress',
            'completed' => 'Completed',
            'pending' => 'Pending',
            'approved' => 'Approved',
            'posted' => 'Posted',
            'rejected' => 'Rejected',
            'cancelled' => 'Cancelled',
        ];
    }

    /**
     * Get the item condition options
     */
    public static function getItemConditionOptions(): array
    {
        return [
            'good' => 'Good',
            'damaged' => 'Damaged',
            'expired' => 'Expired',
            'obsolete' => 'Obsolete',
            'missing' => 'Missing',
        ];
    }

    /**
     * Get the formatted count type name
     */
    public function getCountTypeNameAttribute(): string
    {
        return self::getCountTypeOptions()[$this->count_type] ?? $this->count_type;
    }

    /**
     * Get the formatted status name
     */
    public function getStatusNameAttribute(): string
    {
        return self::getStatusOptions()[$this->status] ?? $this->status;
    }

    /**
     * Get the formatted item condition name
     */
    public function getItemConditionNameAttribute(): string
    {
        return self::getItemConditionOptions()[$this->item_condition] ?? $this->item_condition;
    }

    /**
     * Get status color for UI
     */
    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            'draft' => 'secondary',
            'in_progress' => 'primary',
            'completed' => 'info',
            'pending' => 'warning',
            'approved' => 'info',
            'posted' => 'success',
            'rejected' => 'danger',
            'cancelled' => 'danger',
            default => 'secondary'
        };
    }

    /**
     * Get variance color for UI
     */
    public function getVarianceColorAttribute(): string
    {
        if ($this->variance == 0) {
            return 'success';
        } elseif ($this->variance > 0) {
            return 'info'; // Positive variance (more than expected)
        } else {
            return 'warning'; // Negative variance (less than expected)
        }
    }

    /**
     * Check if count has variance
     */
    public function getHasVarianceAttribute(): bool
    {
        return abs($this->variance) > 0.01; // Allow for small rounding differences
    }

    /**
     * Check if variance is significant (more than 5%)
     */
    public function getHasSignificantVarianceAttribute(): bool
    {
        return abs($this->variance_percentage) > 5;
    }

    /**
     * Check if count can be verified
     */
    public function canBeVerified(): bool
    {
        return $this->status === 'pending' && !$this->verified_by;
    }

    /**
     * Check if count can be approved
     */
    public function canBeApproved(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Check if count can be posted
     */
    public function canBePosted(): bool
    {
        return $this->status === 'approved';
    }

    /**
     * Check if count can be rejected
     */
    public function canBeRejected(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Check if count can be started
     */
    public function canBeStarted(): bool
    {
        return $this->status === 'draft';
    }

    /**
     * Check if count can be cancelled
     */
    public function canBeCancelled(): bool
    {
        return in_array($this->status, ['draft', 'in_progress']);
    }

    /**
     * Verify the count
     */
    public function verify(User $user): bool
    {
        if (!$this->canBeVerified()) {
            return false;
        }

        $this->update([
            'verified_by' => $user->id,
            'verified_at' => now(),
        ]);

        return true;
    }

    /**
     * Approve the count
     */
    public function approve(User $user): bool
    {
        if (!$this->canBeApproved()) {
            return false;
        }

        $this->update([
            'status' => 'approved',
            'approved_by' => $user->id,
            'approved_at' => now(),
        ]);

        return true;
    }

    /**
     * Post the count (create adjustment transaction)
     */
    public function post(): bool
    {
        if (!$this->canBePosted()) {
            return false;
        }

        // This will be handled by the StockMovementService
        $this->update(['status' => 'posted']);

        return true;
    }

    /**
     * Reject the count
     */
    public function reject(string $reason = null): bool
    {
        if (!$this->canBeRejected()) {
            return false;
        }

        $this->update([
            'status' => 'rejected',
            'adjustment_reason' => $reason,
        ]);

        return true;
    }

    /**
     * Start the count session
     */
    public function start(User $user): bool
    {
        if (!$this->canBeStarted()) {
            return false;
        }

        $this->update([
            'status' => 'in_progress',
            'counted_by' => $user->id,
            'counted_at' => now(),
        ]);

        return true;
    }

    /**
     * Cancel the count
     */
    public function cancel(string $reason): bool
    {
        if (!$this->canBeCancelled()) {
            return false;
        }

        $this->update([
            'status' => 'cancelled',
            'adjustment_reason' => $reason,
        ]);

        return true;
    }

    /**
     * Generate next count number
     */
    public static function generateCountNumber(): string
    {
        $prefix = 'CNT';
        $date = now()->format('Ymd');
        
        $lastCount = self::where('count_number', 'like', $prefix . $date . '-%')
            ->orderBy('count_number', 'desc')
            ->first();

        if ($lastCount) {
            $lastNumber = (int) substr($lastCount->count_number, -4);
            $nextNumber = $lastNumber + 1;
        } else {
            $nextNumber = 1;
        }

        return $prefix . $date . '-' . str_pad($nextNumber, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($count) {
            if (empty($count->count_number)) {
                $count->count_number = self::generateCountNumber();
            }

            // Calculate variance
            $count->variance = $count->physical_quantity - $count->system_quantity;
            
            // Calculate variance percentage
            if ($count->system_quantity > 0) {
                $count->variance_percentage = ($count->variance / $count->system_quantity) * 100;
            } else {
                $count->variance_percentage = 0;
            }

            // Calculate variance value
            $count->variance_value = $count->variance * $count->unit_cost;

            // Set counted timestamp
            if (!$count->counted_at) {
                $count->counted_at = now();
            }
        });

        static::updating(function ($count) {
            // Recalculate variance if quantities changed
            if ($count->isDirty(['physical_quantity', 'system_quantity'])) {
                $count->variance = $count->physical_quantity - $count->system_quantity;
                
                if ($count->system_quantity > 0) {
                    $count->variance_percentage = ($count->variance / $count->system_quantity) * 100;
                } else {
                    $count->variance_percentage = 0;
                }

                $count->variance_value = $count->variance * $count->unit_cost;
            }
        });
    }
}
