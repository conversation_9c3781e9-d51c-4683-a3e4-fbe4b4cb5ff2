<?php

namespace App\Models\Stock;

use App\Traits\TenantScoped;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class StockTransaction extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'stock_transactions';

    protected $fillable = [
        'item_id',
        'location_id',
        'transaction_type',
        'quantity',
        'unit_cost',
        'total_cost',
        'batch_number',
        'lot_number',
        'expiry_date',
        'manufacturing_date',
        'reference_type',
        'reference_id',
        'reference_number',
        'transaction_date',
        'notes',
        'reason',
        'status',
        'quality_grade',
        'is_damaged',
        'damage_reason',
        'created_by',
        'approved_by',
        'approved_at',
    ];

    protected $casts = [
        'quantity' => 'decimal:4',
        'unit_cost' => 'decimal:4',
        'total_cost' => 'decimal:4',
        'transaction_date' => 'datetime',
        'expiry_date' => 'date',
        'manufacturing_date' => 'date',
        'is_damaged' => 'boolean',
        'approved_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * Get the item that owns the transaction
     */
    public function item(): BelongsTo
    {
        return $this->belongsTo(StockItem::class, 'item_id');
    }

    /**
     * Get the location that owns the transaction
     */
    public function location(): BelongsTo
    {
        return $this->belongsTo(StockLocation::class, 'location_id');
    }

    /**
     * Get the user who created this transaction
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who approved this transaction
     */
    public function approver(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Scope to filter by transaction type
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('transaction_type', $type);
    }

    /**
     * Scope to filter by status
     */
    public function scopeWithStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to filter by date range
     */
    public function scopeBetweenDates($query, $startDate, $endDate)
    {
        return $query->whereBetween('transaction_date', [$startDate, $endDate]);
    }

    /**
     * Scope to get pending transactions
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope to get approved transactions
     */
    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    /**
     * Scope to get posted transactions
     */
    public function scopePosted($query)
    {
        return $query->where('status', 'posted');
    }

    /**
     * Scope to get stock in transactions
     */
    public function scopeStockIn($query)
    {
        return $query->whereIn('transaction_type', ['in', 'transfer_in', 'production_in', 'return']);
    }

    /**
     * Scope to get stock out transactions
     */
    public function scopeStockOut($query)
    {
        return $query->whereIn('transaction_type', ['out', 'transfer_out', 'production_out', 'damage', 'loss']);
    }

    /**
     * Get the transaction type options
     */
    public static function getTransactionTypeOptions(): array
    {
        return [
            'in' => 'Stock In',
            'out' => 'Stock Out',
            'transfer_in' => 'Transfer In',
            'transfer_out' => 'Transfer Out',
            'adjustment' => 'Adjustment',
            'production_in' => 'Production In',
            'production_out' => 'Production Out',
            'return' => 'Return',
            'damage' => 'Damage',
            'loss' => 'Loss',
        ];
    }

    /**
     * Get the reference type options
     */
    public static function getReferenceTypeOptions(): array
    {
        return [
            'purchase_order' => 'Purchase Order',
            'production_order' => 'Production Order',
            'sales_order' => 'Sales Order',
            'transfer' => 'Transfer',
            'adjustment' => 'Adjustment',
            'return' => 'Return',
            'opening_balance' => 'Opening Balance',
        ];
    }

    /**
     * Get the status options
     */
    public static function getStatusOptions(): array
    {
        return [
            'pending' => 'Pending',
            'approved' => 'Approved',
            'posted' => 'Posted',
            'cancelled' => 'Cancelled',
            'rejected' => 'Rejected',
        ];
    }

    /**
     * Get the formatted transaction type name
     */
    public function getTransactionTypeNameAttribute(): string
    {
        return self::getTransactionTypeOptions()[$this->transaction_type] ?? $this->transaction_type;
    }

    /**
     * Get the formatted reference type name
     */
    public function getReferenceTypeNameAttribute(): string
    {
        return self::getReferenceTypeOptions()[$this->reference_type] ?? $this->reference_type;
    }

    /**
     * Get the formatted status name
     */
    public function getStatusNameAttribute(): string
    {
        return self::getStatusOptions()[$this->status] ?? $this->status;
    }

    /**
     * Check if transaction increases stock
     */
    public function getIsStockInAttribute(): bool
    {
        return in_array($this->transaction_type, ['in', 'transfer_in', 'production_in', 'return']);
    }

    /**
     * Check if transaction decreases stock
     */
    public function getIsStockOutAttribute(): bool
    {
        return in_array($this->transaction_type, ['out', 'transfer_out', 'production_out', 'damage', 'loss']);
    }

    /**
     * Get the effective quantity (positive for stock in, negative for stock out)
     */
    public function getEffectiveQuantityAttribute(): float
    {
        return $this->is_stock_in ? $this->quantity : -$this->quantity;
    }

    /**
     * Get status color for UI
     */
    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            'pending' => 'warning',
            'approved' => 'info',
            'posted' => 'success',
            'cancelled' => 'danger',
            'rejected' => 'danger',
            default => 'secondary'
        };
    }

    /**
     * Get transaction type color for UI
     */
    public function getTransactionTypeColorAttribute(): string
    {
        return match($this->transaction_type) {
            'in', 'transfer_in', 'production_in', 'return' => 'success',
            'out', 'transfer_out', 'production_out' => 'primary',
            'damage', 'loss' => 'danger',
            'adjustment' => 'warning',
            default => 'secondary'
        };
    }

    /**
     * Check if transaction can be approved
     */
    public function canBeApproved(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Check if transaction can be posted
     */
    public function canBePosted(): bool
    {
        return $this->status === 'approved';
    }

    /**
     * Check if transaction can be cancelled
     */
    public function canBeCancelled(): bool
    {
        return in_array($this->status, ['pending', 'approved']);
    }

    /**
     * Check if transaction can be rejected
     */
    public function canBeRejected(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Approve the transaction
     */
    public function approve(User $user): bool
    {
        if (!$this->canBeApproved()) {
            return false;
        }

        $this->update([
            'status' => 'approved',
            'approved_by' => $user->id,
            'approved_at' => now(),
        ]);

        return true;
    }

    /**
     * Post the transaction (update stock levels)
     */
    public function post(): bool
    {
        if (!$this->canBePosted()) {
            return false;
        }

        // This will be handled by the StockMovementService
        $this->update(['status' => 'posted']);

        return true;
    }

    /**
     * Cancel the transaction
     */
    public function cancel(): bool
    {
        if (!$this->canBeCancelled()) {
            return false;
        }

        $this->update(['status' => 'cancelled']);

        return true;
    }

    /**
     * Reject the transaction
     */
    public function reject(string $reason, User $user): bool
    {
        if (!$this->canBeRejected()) {
            return false;
        }

        $this->update([
            'status' => 'rejected',
            'notes' => ($this->notes ? $this->notes . "\n\n" : '') . "Rejected by {$user->name}: {$reason}",
        ]);

        return true;
    }

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($transaction) {
            // Calculate total cost if not provided
            if (!$transaction->total_cost) {
                $transaction->total_cost = $transaction->quantity * $transaction->unit_cost;
            }

            // Set transaction date if not provided
            if (!$transaction->transaction_date) {
                $transaction->transaction_date = now();
            }
        });

        static::updating(function ($transaction) {
            // Recalculate total cost if quantity or unit cost changed
            if ($transaction->isDirty(['quantity', 'unit_cost'])) {
                $transaction->total_cost = $transaction->quantity * $transaction->unit_cost;
            }
        });
    }
}
