<?php

namespace App\Models\Stock;

use App\Traits\TenantScoped, SoftDeletes;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class StockTransfer extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'stock_transfers';

    protected $fillable = [
        'transfer_number',
        'from_location_id',
        'to_location_id',
        'item_id',
        'quantity',
        'unit_cost',
        'total_cost',
        'batch_number',
        'lot_number',
        'status',
        'transfer_date',
        'expected_delivery_date',
        'actual_delivery_date',
        'requested_by',
        'approved_by',
        'dispatched_by',
        'received_by',
        'requested_at',
        'approved_at',
        'dispatched_at',
        'received_at',
        'notes',
        'rejection_reason',
        'vehicle_number',
        'driver_name',
        'driver_contact',
        'received_quantity',
        'quality_notes',
        'quality_approved',
    ];

    protected $casts = [
        'quantity' => 'decimal:4',
        'unit_cost' => 'decimal:4',
        'total_cost' => 'decimal:4',
        'received_quantity' => 'decimal:4',
        'transfer_date' => 'date',
        'expected_delivery_date' => 'date',
        'actual_delivery_date' => 'date',
        'requested_at' => 'datetime',
        'approved_at' => 'datetime',
        'dispatched_at' => 'datetime',
        'received_at' => 'datetime',
        'quality_approved' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * Get the item being transferred
     */
    public function item(): BelongsTo
    {
        return $this->belongsTo(StockItem::class, 'item_id');
    }

    /**
     * Get the source location
     */
    public function fromLocation(): BelongsTo
    {
        return $this->belongsTo(StockLocation::class, 'from_location_id');
    }

    /**
     * Get the destination location
     */
    public function toLocation(): BelongsTo
    {
        return $this->belongsTo(StockLocation::class, 'to_location_id');
    }

    /**
     * Get the user who requested the transfer
     */
    public function requester(): BelongsTo
    {
        return $this->belongsTo(User::class, 'requested_by');
    }

    /**
     * Get the user who approved the transfer
     */
    public function approver(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Get the user who dispatched the transfer
     */
    public function dispatcher(): BelongsTo
    {
        return $this->belongsTo(User::class, 'dispatched_by');
    }

    /**
     * Get the user who received the transfer
     */
    public function receiver(): BelongsTo
    {
        return $this->belongsTo(User::class, 'received_by');
    }

    /**
     * Scope to filter by status
     */
    public function scopeWithStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to get pending transfers
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope to get approved transfers
     */
    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    /**
     * Scope to get in transit transfers
     */
    public function scopeInTransit($query)
    {
        return $query->where('status', 'in_transit');
    }

    /**
     * Scope to get received transfers
     */
    public function scopeReceived($query)
    {
        return $query->where('status', 'received');
    }

    /**
     * Scope to filter by date range
     */
    public function scopeBetweenDates($query, $startDate, $endDate)
    {
        return $query->whereBetween('transfer_date', [$startDate, $endDate]);
    }

    /**
     * Get the status options
     */
    public static function getStatusOptions(): array
    {
        return [
            'pending' => 'Pending Approval',
            'approved' => 'Approved',
            'in_transit' => 'In Transit',
            'received' => 'Received',
            'cancelled' => 'Cancelled',
        ];
    }

    /**
     * Get the formatted status name
     */
    public function getStatusNameAttribute(): string
    {
        return self::getStatusOptions()[$this->status] ?? $this->status;
    }

    /**
     * Get status color for UI
     */
    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            'pending' => 'warning',
            'approved' => 'info',
            'in_transit' => 'primary',
            'received' => 'success',
            'cancelled' => 'danger',
            default => 'secondary'
        };
    }

    /**
     * Check if transfer can be approved
     */
    public function canBeApproved(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Check if transfer can be dispatched
     */
    public function canBeDispatched(): bool
    {
        return $this->status === 'approved';
    }

    /**
     * Check if transfer can be received
     */
    public function canBeReceived(): bool
    {
        return $this->status === 'in_transit';
    }

    /**
     * Check if transfer can be cancelled
     */
    public function canBeCancelled(): bool
    {
        return in_array($this->status, ['pending', 'approved']);
    }

    /**
     * Get variance quantity (difference between sent and received)
     */
    public function getVarianceQuantityAttribute(): float
    {
        if (!$this->received_quantity) {
            return 0;
        }
        return $this->received_quantity - $this->quantity;
    }

    /**
     * Get variance percentage
     */
    public function getVariancePercentageAttribute(): float
    {
        if (!$this->received_quantity || $this->quantity <= 0) {
            return 0;
        }
        return (($this->received_quantity - $this->quantity) / $this->quantity) * 100;
    }

    /**
     * Check if there's a significant variance
     */
    public function hasVariance(): bool
    {
        return abs($this->variance_quantity) > 0.01; // Allow for small rounding differences
    }

    /**
     * Approve the transfer
     */
    public function approve(User $user): bool
    {
        if (!$this->canBeApproved()) {
            return false;
        }

        $this->update([
            'status' => 'approved',
            'approved_by' => $user->id,
            'approved_at' => now(),
        ]);

        return true;
    }

    /**
     * Dispatch the transfer
     */
    public function dispatch(User $user, array $data = []): bool
    {
        if (!$this->canBeDispatched()) {
            return false;
        }

        $updateData = [
            'status' => 'in_transit',
            'dispatched_by' => $user->id,
            'dispatched_at' => now(),
        ];

        // Add optional dispatch data
        if (isset($data['vehicle_number'])) {
            $updateData['vehicle_number'] = $data['vehicle_number'];
        }
        if (isset($data['driver_name'])) {
            $updateData['driver_name'] = $data['driver_name'];
        }
        if (isset($data['driver_contact'])) {
            $updateData['driver_contact'] = $data['driver_contact'];
        }

        $this->update($updateData);

        return true;
    }

    /**
     * Receive the transfer
     */
    public function receive(User $user, array $data): bool
    {
        if (!$this->canBeReceived()) {
            return false;
        }

        $updateData = [
            'status' => 'received',
            'received_by' => $user->id,
            'received_at' => now(),
            'actual_delivery_date' => $data['actual_delivery_date'] ?? now()->toDateString(),
            'received_quantity' => $data['received_quantity'],
            'quality_approved' => $data['quality_approved'] ?? true,
        ];

        if (isset($data['quality_notes'])) {
            $updateData['quality_notes'] = $data['quality_notes'];
        }

        $this->update($updateData);

        return true;
    }

    /**
     * Cancel the transfer
     */
    public function cancel(string $reason = null): bool
    {
        if (!$this->canBeCancelled()) {
            return false;
        }

        $this->update([
            'status' => 'cancelled',
            'rejection_reason' => $reason,
        ]);

        return true;
    }

    /**
     * Generate next transfer number
     */
    public static function generateTransferNumber(): string
    {
        $prefix = 'TRF';
        $date = now()->format('Ymd');
        
        $lastTransfer = self::where('transfer_number', 'like', $prefix . $date . '-%')
            ->orderBy('transfer_number', 'desc')
            ->first();

        if ($lastTransfer) {
            $lastNumber = (int) substr($lastTransfer->transfer_number, -4);
            $nextNumber = $lastNumber + 1;
        } else {
            $nextNumber = 1;
        }

        return $prefix . $date . '-' . str_pad($nextNumber, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($transfer) {
            if (empty($transfer->transfer_number)) {
                $transfer->transfer_number = self::generateTransferNumber();
            }

            // Calculate total cost if not provided
            if (!$transfer->total_cost) {
                $transfer->total_cost = $transfer->quantity * $transfer->unit_cost;
            }

            // Set requested timestamp
            if (!$transfer->requested_at) {
                $transfer->requested_at = now();
            }
        });

        static::updating(function ($transfer) {
            // Recalculate total cost if quantity or unit cost changed
            if ($transfer->isDirty(['quantity', 'unit_cost'])) {
                $transfer->total_cost = $transfer->quantity * $transfer->unit_cost;
            }
        });
    }
}
