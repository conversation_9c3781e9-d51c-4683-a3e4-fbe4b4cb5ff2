<?php

namespace App\Models\SubscriptionManagement;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Models\User;
use App\Models\Tenant;

class SubscriptionAdminAction extends Model
{
    use HasFactory;

    protected $fillable = [
        'admin_user_id',
        'target_user_id',
        'user_subscription_id',
        'tenant_id',
        'action_type',
        'action_description',
        'before_values',
        'after_values',
        'reason',
        'notes',
        'ip_address',
        'user_agent',
        'request_url',
        'status',
        'error_message',
    ];

    protected $casts = [
        'before_values' => 'array',
        'after_values' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the admin user who performed the action.
     */
    public function adminUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'admin_user_id');
    }

    /**
     * Get the target user affected by the action.
     */
    public function targetUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'target_user_id');
    }

    /**
     * Get the subscription affected by the action.
     */
    public function userSubscription(): BelongsTo
    {
        return $this->belongsTo(UserSubscription::class);
    }

    /**
     * Get the tenant affected by the action.
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class);
    }

    /**
     * Scope to get actions by admin user.
     */
    public function scopeByAdmin($query, $adminUserId)
    {
        return $query->where('admin_user_id', $adminUserId);
    }

    /**
     * Scope to get actions for a specific target user.
     */
    public function scopeForUser($query, $targetUserId)
    {
        return $query->where('target_user_id', $targetUserId);
    }

    /**
     * Scope to get actions for a specific subscription.
     */
    public function scopeForSubscription($query, $subscriptionId)
    {
        return $query->where('user_subscription_id', $subscriptionId);
    }

    /**
     * Scope to get actions by type.
     */
    public function scopeByType($query, $actionType)
    {
        return $query->where('action_type', $actionType);
    }

    /**
     * Scope to get recent actions.
     */
    public function scopeRecent($query, $days = 30)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }

    /**
     * Create a new admin action log entry.
     */
    public static function logAction(array $data): self
    {
        // Add request metadata if not provided
        if (request()) {
            $data = array_merge([
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
                'request_url' => request()->fullUrl(),
            ], $data);
        }

        return self::create($data);
    }

    /**
     * Get formatted action description for display.
     */
    public function getFormattedDescriptionAttribute(): string
    {
        return ucfirst(str_replace('_', ' ', $this->action_description));
    }

    /**
     * Get the action type label for display.
     */
    public function getActionTypeLabelAttribute(): string
    {
        $labels = [
            'update_billing_date' => 'Update Billing Date',
            'reactivate_subscription' => 'Reactivate Subscription',
            'extend_trial' => 'Extend Trial Period',
            'suspend_subscription' => 'Suspend Subscription',
            'upgrade_subscription' => 'Upgrade Subscription',
            'downgrade_subscription' => 'Downgrade Subscription',
            'cancel_subscription' => 'Cancel Subscription',
            'reset_usage' => 'Reset Usage Limits',
            'manual_payment_approval' => 'Manual Payment Approval',
            'billing_cycle_change' => 'Billing Cycle Change',
        ];

        return $labels[$this->action_type] ?? ucfirst(str_replace('_', ' ', $this->action_type));
    }

    /**
     * Get status badge class for UI display.
     */
    public function getStatusBadgeClassAttribute(): string
    {
        return match($this->status) {
            'completed' => 'badge-success',
            'failed' => 'badge-danger',
            'pending' => 'badge-warning',
            default => 'badge-secondary',
        };
    }
}
