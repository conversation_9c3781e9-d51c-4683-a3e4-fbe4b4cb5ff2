<?php

namespace App\Models\SubscriptionManagement;

use App\Models\User;
use App\Models\Tenant;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;

class SubscriptionCode extends Model
{
    use HasFactory;

    protected $fillable = [
        'code',
        'subscription_plan_id',
        'generated_by',
        'type',
        'description',
        'duration_months',
        'is_unlimited',
        'status',
        'expires_at',
        'used_at',
        'used_by',
        'tenant_id',
        'email_restriction',
        'domain_restriction',
        'max_uses',
        'current_uses',
        'discount_amount',
        'discount_percentage',
        'payment_reference',
        'admin_notes',
        'metadata',
    ];

    protected $casts = [
        'expires_at' => 'datetime',
        'used_at' => 'datetime',
        'is_unlimited' => 'boolean',
        'discount_amount' => 'decimal:2',
        'metadata' => 'array',
    ];

    /**
     * Get the subscription plan this code is for.
     */
    public function subscriptionPlan(): BelongsTo
    {
        return $this->belongsTo(SubscriptionPlan::class);
    }

    /**
     * Get the user who generated this code.
     */
    public function generatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'generated_by');
    }

    /**
     * Get the user who used this code.
     */
    public function usedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'used_by');
    }

    /**
     * Get the tenant this code was used by.
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class);
    }

    /**
     * Scope to get active codes.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active')
                    ->where(function ($q) {
                        $q->whereNull('expires_at')
                          ->orWhere('expires_at', '>', now());
                    });
    }

    /**
     * Scope to get used codes.
     */
    public function scopeUsed($query)
    {
        return $query->where('status', 'used');
    }

    /**
     * Scope to get expired codes.
     */
    public function scopeExpired($query)
    {
        return $query->where('status', 'expired')
                    ->orWhere(function ($q) {
                        $q->where('expires_at', '<=', now())
                          ->where('status', '!=', 'used');
                    });
    }

    /**
     * Check if the code is valid for use.
     */
    public function isValid(): bool
    {
        return $this->status === 'active' &&
               ($this->expires_at === null || $this->expires_at->isFuture()) &&
               $this->current_uses < $this->max_uses;
    }

    /**
     * Check if the code is expired.
     */
    public function isExpired(): bool
    {
        return $this->status === 'expired' ||
               ($this->expires_at && $this->expires_at->isPast());
    }

    /**
     * Check if the code has been used.
     */
    public function isUsed(): bool
    {
        return $this->status === 'used' || $this->current_uses >= $this->max_uses;
    }

    /**
     * Check if the code can be used by a specific user.
     */
    public function canBeUsedBy(User $user): bool
    {
        if (!$this->isValid()) {
            return false;
        }

        // Check email restriction
        if ($this->email_restriction && $user->email !== $this->email_restriction) {
            return false;
        }

        // Check domain restriction
        if ($this->domain_restriction) {
            $userDomain = substr(strrchr($user->email, "@"), 1);
            if ($userDomain !== $this->domain_restriction) {
                return false;
            }
        }

        return true;
    }

    /**
     * Use the code for a specific user.
     */
    public function useFor(User $user, Tenant $tenant = null): bool
    {
        if (!$this->canBeUsedBy($user)) {
            return false;
        }

        $this->update([
            'status' => $this->current_uses + 1 >= $this->max_uses ? 'used' : 'active',
            'used_at' => now(),
            'used_by' => $user->id,
            'tenant_id' => $tenant?->id,
            'current_uses' => $this->current_uses + 1,
        ]);

        return true;
    }

    /**
     * Generate a new subscription code.
     */
    public static function generate(array $attributes = []): self
    {
        $code = self::generateUniqueCode();
        
        return self::create(array_merge([
            'code' => $code,
            'generated_by' => auth()->id(),
            'type' => 'upgrade',
            'duration_months' => 12,
            'status' => 'active',
            'max_uses' => 1,
            'current_uses' => 0,
        ], $attributes));
    }

    /**
     * Generate a unique code string.
     */
    protected static function generateUniqueCode(): string
    {
        do {
            $code = 'PREM-' . date('Y') . '-' . strtoupper(Str::random(8));
        } while (self::where('code', $code)->exists());

        return $code;
    }

    /**
     * Generate a bulk set of codes.
     */
    public static function generateBulk(int $count, array $attributes = []): array
    {
        $codes = [];
        
        for ($i = 0; $i < $count; $i++) {
            $codes[] = self::generate($attributes);
        }

        return $codes;
    }

    /**
     * Get the remaining uses for this code.
     */
    public function getRemainingUses(): int
    {
        return max(0, $this->max_uses - $this->current_uses);
    }

    /**
     * Get the usage percentage.
     */
    public function getUsagePercentage(): float
    {
        if ($this->max_uses === 0) {
            return 0;
        }

        return ($this->current_uses / $this->max_uses) * 100;
    }

    /**
     * Get formatted expiry date.
     */
    public function getFormattedExpiryDate(): string
    {
        if (!$this->expires_at) {
            return 'Never expires';
        }

        return $this->expires_at->format('M j, Y');
    }

    /**
     * Get the code type label.
     */
    public function getTypeLabel(): string
    {
        return match($this->type) {
            'upgrade' => 'Plan Upgrade',
            'extension' => 'Subscription Extension',
            'trial' => 'Trial Extension',
            default => ucfirst($this->type),
        };
    }

    /**
     * Get the status label.
     */
    public function getStatusLabel(): string
    {
        return match($this->status) {
            'active' => 'Active',
            'used' => 'Used',
            'expired' => 'Expired',
            'cancelled' => 'Cancelled',
            default => ucfirst($this->status),
        };
    }

    /**
     * Get the status color for UI.
     */
    public function getStatusColor(): string
    {
        return match($this->status) {
            'active' => 'success',
            'used' => 'info',
            'expired' => 'warning',
            'cancelled' => 'danger',
            default => 'secondary',
        };
    }

    /**
     * Expire the code.
     */
    public function expire(): void
    {
        $this->update(['status' => 'expired']);
    }

    /**
     * Cancel the code.
     */
    public function cancel(): void
    {
        $this->update(['status' => 'cancelled']);
    }

    /**
     * Extend the expiry date.
     */
    public function extendExpiry(int $days): void
    {
        $newExpiry = $this->expires_at ? 
                    $this->expires_at->addDays($days) : 
                    now()->addDays($days);
                    
        $this->update(['expires_at' => $newExpiry]);
    }
}
