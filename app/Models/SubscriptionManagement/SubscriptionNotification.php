<?php

namespace App\Models\SubscriptionManagement;

use App\Models\User;
use App\Models\Tenant;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class SubscriptionNotification extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_subscription_id',
        'tenant_id',
        'user_id',
        'type',
        'title',
        'message',
        'priority',
        'status',
        'channels',
        'scheduled_at',
        'sent_at',
        'failed_at',
        'email_subject',
        'email_body',
        'email_template',
        'email_sent',
        'email_sent_at',
        'email_failure_reason',
        'in_app_read',
        'in_app_read_at',
        'in_app_dismissed',
        'in_app_dismissed_at',
        'action_url',
        'action_text',
        'action_taken',
        'action_taken_at',
        'retry_count',
        'max_retries',
        'next_retry_at',
        'related_model_type',
        'related_model_id',
        'data',
        'notes',
        'metadata',
    ];

    protected $casts = [
        'channels' => 'array',
        'scheduled_at' => 'datetime',
        'sent_at' => 'datetime',
        'failed_at' => 'datetime',
        'email_sent_at' => 'datetime',
        'in_app_read_at' => 'datetime',
        'in_app_dismissed_at' => 'datetime',
        'action_taken_at' => 'datetime',
        'next_retry_at' => 'datetime',
        'email_sent' => 'boolean',
        'in_app_read' => 'boolean',
        'in_app_dismissed' => 'boolean',
        'action_taken' => 'boolean',
        'data' => 'array',
        'metadata' => 'array',
    ];

    /**
     * Get the user subscription this notification belongs to.
     */
    public function userSubscription(): BelongsTo
    {
        return $this->belongsTo(UserSubscription::class);
    }

    /**
     * Get the tenant this notification belongs to.
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class);
    }

    /**
     * Get the user this notification is for.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the related model (polymorphic).
     */
    public function relatedModel(): MorphTo
    {
        return $this->morphTo('related_model', 'related_model_type', 'related_model_id');
    }

    /**
     * Scope to get pending notifications.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope to get sent notifications.
     */
    public function scopeSent($query)
    {
        return $query->where('status', 'sent');
    }

    /**
     * Scope to get failed notifications.
     */
    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }

    /**
     * Scope to get notifications ready to send.
     */
    public function scopeReadyToSend($query)
    {
        return $query->where('status', 'pending')
                    ->where(function ($q) {
                        $q->whereNull('scheduled_at')
                          ->orWhere('scheduled_at', '<=', now());
                    });
    }

    /**
     * Scope to get unread in-app notifications.
     */
    public function scopeUnread($query)
    {
        return $query->where('in_app_read', false)
                    ->whereJsonContains('channels', 'in_app');
    }

    /**
     * Scope to get notifications by priority.
     */
    public function scopeByPriority($query, string $priority)
    {
        return $query->where('priority', $priority);
    }

    /**
     * Scope to get critical notifications.
     */
    public function scopeCritical($query)
    {
        return $query->where('priority', 'critical');
    }

    /**
     * Check if notification is pending.
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Check if notification has been sent.
     */
    public function isSent(): bool
    {
        return $this->status === 'sent';
    }

    /**
     * Check if notification has failed.
     */
    public function hasFailed(): bool
    {
        return $this->status === 'failed';
    }

    /**
     * Check if notification is ready to send.
     */
    public function isReadyToSend(): bool
    {
        return $this->isPending() && 
               ($this->scheduled_at === null || $this->scheduled_at->isPast());
    }

    /**
     * Check if notification can be retried.
     */
    public function canRetry(): bool
    {
        return $this->hasFailed() && 
               $this->retry_count < $this->max_retries &&
               ($this->next_retry_at === null || $this->next_retry_at->isPast());
    }

    /**
     * Check if notification should be sent via email.
     */
    public function shouldSendEmail(): bool
    {
        return in_array('email', $this->channels ?? []) && !$this->email_sent;
    }

    /**
     * Check if notification should be shown in-app.
     */
    public function shouldShowInApp(): bool
    {
        return in_array('in_app', $this->channels ?? []);
    }

    /**
     * Mark notification as sent.
     */
    public function markSent(): void
    {
        $this->update([
            'status' => 'sent',
            'sent_at' => now(),
        ]);
    }

    /**
     * Mark notification as failed.
     */
    public function markFailed(string $reason = null): void
    {
        $this->update([
            'status' => 'failed',
            'failed_at' => now(),
            'email_failure_reason' => $reason,
            'retry_count' => $this->retry_count + 1,
            'next_retry_at' => now()->addMinutes(30 * $this->retry_count), // Exponential backoff
        ]);
    }

    /**
     * Mark email as sent.
     */
    public function markEmailSent(): void
    {
        $this->update([
            'email_sent' => true,
            'email_sent_at' => now(),
        ]);
    }

    /**
     * Mark in-app notification as read.
     */
    public function markRead(): void
    {
        $this->update([
            'in_app_read' => true,
            'in_app_read_at' => now(),
        ]);
    }

    /**
     * Mark in-app notification as dismissed.
     */
    public function markDismissed(): void
    {
        $this->update([
            'in_app_dismissed' => true,
            'in_app_dismissed_at' => now(),
        ]);
    }

    /**
     * Mark action as taken.
     */
    public function markActionTaken(): void
    {
        $this->update([
            'action_taken' => true,
            'action_taken_at' => now(),
        ]);
    }

    /**
     * Get priority label.
     */
    public function getPriorityLabel(): string
    {
        return match($this->priority) {
            'low' => 'Low',
            'medium' => 'Medium',
            'high' => 'High',
            'critical' => 'Critical',
            default => ucfirst($this->priority),
        };
    }

    /**
     * Get priority color for UI.
     */
    public function getPriorityColor(): string
    {
        return match($this->priority) {
            'low' => 'info',
            'medium' => 'primary',
            'high' => 'warning',
            'critical' => 'danger',
            default => 'secondary',
        };
    }

    /**
     * Get type label.
     */
    public function getTypeLabel(): string
    {
        return match($this->type) {
            'trial_ending' => 'Trial Ending',
            'trial_ended' => 'Trial Ended',
            'subscription_ending' => 'Subscription Ending',
            'subscription_ended' => 'Subscription Ended',
            'payment_failed' => 'Payment Failed',
            'payment_succeeded' => 'Payment Succeeded',
            'grace_period_started' => 'Grace Period Started',
            'grace_period_ending' => 'Grace Period Ending',
            'account_suspended' => 'Account Suspended',
            'account_reactivated' => 'Account Reactivated',
            'usage_limit_warning' => 'Usage Limit Warning',
            'usage_limit_exceeded' => 'Usage Limit Exceeded',
            'plan_upgraded' => 'Plan Upgraded',
            'plan_downgraded' => 'Plan Downgraded',
            'subscription_cancelled' => 'Subscription Cancelled',
            default => ucfirst(str_replace('_', ' ', $this->type)),
        };
    }

    /**
     * Create a new notification.
     */
    public static function createNotification(array $data): self
    {
        return self::create(array_merge([
            'status' => 'pending',
            'priority' => 'medium',
            'channels' => ['email', 'in_app'],
            'retry_count' => 0,
            'max_retries' => 3,
        ], $data));
    }

    /**
     * Create a trial ending notification.
     */
    public static function createTrialEndingNotification(UserSubscription $subscription, int $daysRemaining): self
    {
        return self::createNotification([
            'user_subscription_id' => $subscription->id,
            'tenant_id' => $subscription->tenant_id,
            'user_id' => $subscription->user_id,
            'type' => 'trial_ending',
            'title' => 'Your free trial is ending soon',
            'message' => "Your free trial will end in {$daysRemaining} days. Upgrade to Premium to continue using all features.",
            'priority' => 'high',
            'action_url' => route('subscription-panel.index'),
            'action_text' => 'Upgrade Now',
        ]);
    }

    /**
     * Create a usage limit warning notification.
     */
    public static function createUsageLimitWarning(UserSubscription $subscription, string $metric, float $percentage): self
    {
        return self::createNotification([
            'user_subscription_id' => $subscription->id,
            'tenant_id' => $subscription->tenant_id,
            'user_id' => $subscription->user_id,
            'type' => 'usage_limit_warning',
            'title' => 'Usage limit warning',
            'message' => "You've used {$percentage}% of your {$metric} limit. Consider upgrading to avoid service interruption.",
            'priority' => 'medium',
            'action_url' => route('subscription-panel.index'),
            'action_text' => 'View Usage',
        ]);
    }

    /**
     * Create a payment failed notification.
     */
    public static function createPaymentFailedNotification(UserSubscription $subscription, string $reason): self
    {
        return self::createNotification([
            'user_subscription_id' => $subscription->id,
            'tenant_id' => $subscription->tenant_id,
            'user_id' => $subscription->user_id,
            'type' => 'payment_failed',
            'title' => 'Payment failed',
            'message' => "Your payment could not be processed: {$reason}. Please update your payment method.",
            'priority' => 'critical',
            'action_url' => route('subscription-panel.payment-methods'),
            'action_text' => 'Update Payment Method',
        ]);
    }
}
