<?php

namespace App\Models\SubscriptionManagement;

use App\Models\User;
use App\Models\Tenant;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;

class SubscriptionPayment extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_subscription_id',
        'tenant_id',
        'user_id',
        'payment_id',
        'transaction_id',
        'amount',
        'currency',
        'status',
        'payment_method',
        'billing_period_start',
        'billing_period_end',
        'billing_cycle',
        'stripe_payment_intent_id',
        'stripe_invoice_id',
        'stripe_charge_id',
        'stripe_metadata',
        'subtotal',
        'tax_amount',
        'discount_amount',
        'discount_code',
        'credit_applied',
        'payment_date',
        'due_date',
        'failed_at',
        'refunded_at',
        'failure_reason',
        'failure_message',
        'retry_count',
        'next_retry_at',
        'invoice_number',
        'invoice_url',
        'invoice_sent',
        'invoice_sent_at',
        'refunded_amount',
        'refund_reason',
        'refunded_by',
        'notes',
        'metadata',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'subtotal' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'credit_applied' => 'decimal:2',
        'refunded_amount' => 'decimal:2',
        'billing_period_start' => 'date',
        'billing_period_end' => 'date',
        'payment_date' => 'datetime',
        'due_date' => 'datetime',
        'failed_at' => 'datetime',
        'refunded_at' => 'datetime',
        'next_retry_at' => 'datetime',
        'invoice_sent_at' => 'datetime',
        'invoice_sent' => 'boolean',
        'stripe_metadata' => 'array',
        'metadata' => 'array',
    ];

    /**
     * Get the user subscription this payment belongs to.
     */
    public function userSubscription(): BelongsTo
    {
        return $this->belongsTo(UserSubscription::class);
    }

    /**
     * Get the tenant this payment belongs to.
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class);
    }

    /**
     * Get the user who made this payment.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the user who processed the refund.
     */
    public function refundedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'refunded_by');
    }

    /**
     * Scope to get successful payments.
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope to get failed payments.
     */
    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }

    /**
     * Scope to get pending payments.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope to get refunded payments.
     */
    public function scopeRefunded($query)
    {
        return $query->whereIn('status', ['refunded', 'partially_refunded']);
    }

    /**
     * Check if payment is completed.
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * Check if payment is pending.
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Check if payment has failed.
     */
    public function hasFailed(): bool
    {
        return $this->status === 'failed';
    }

    /**
     * Check if payment is refunded.
     */
    public function isRefunded(): bool
    {
        return in_array($this->status, ['refunded', 'partially_refunded']);
    }

    /**
     * Check if payment can be retried.
     */
    public function canRetry(): bool
    {
        return $this->hasFailed() && 
               $this->retry_count < 3 && 
               ($this->next_retry_at === null || $this->next_retry_at->isPast());
    }

    /**
     * Mark payment as completed.
     */
    public function markCompleted(array $data = []): void
    {
        $this->update(array_merge([
            'status' => 'completed',
            'payment_date' => now(),
            'failed_at' => null,
            'failure_reason' => null,
            'failure_message' => null,
        ], $data));
    }

    /**
     * Mark payment as failed.
     */
    public function markFailed(string $reason = null, string $message = null): void
    {
        $this->update([
            'status' => 'failed',
            'failed_at' => now(),
            'failure_reason' => $reason,
            'failure_message' => $message,
            'retry_count' => $this->retry_count + 1,
            'next_retry_at' => now()->addHours(24 * $this->retry_count), // Exponential backoff
        ]);
    }

    /**
     * Process a refund.
     */
    public function processRefund(float $amount = null, string $reason = null): void
    {
        $refundAmount = $amount ?? $this->amount;
        $totalRefunded = $this->refunded_amount + $refundAmount;

        $status = $totalRefunded >= $this->amount ? 'refunded' : 'partially_refunded';

        $this->update([
            'status' => $status,
            'refunded_amount' => $totalRefunded,
            'refund_reason' => $reason,
            'refunded_at' => now(),
            'refunded_by' => auth()->id(),
        ]);
    }

    /**
     * Generate a unique payment ID.
     */
    public static function generatePaymentId(): string
    {
        do {
            $paymentId = 'PAY-' . date('Ymd') . '-' . strtoupper(Str::random(8));
        } while (self::where('payment_id', $paymentId)->exists());

        return $paymentId;
    }

    /**
     * Generate a unique invoice number.
     */
    public static function generateInvoiceNumber(): string
    {
        do {
            $invoiceNumber = 'INV-' . date('Y') . '-' . str_pad(
                self::whereYear('created_at', date('Y'))->count() + 1,
                6,
                '0',
                STR_PAD_LEFT
            );
        } while (self::where('invoice_number', $invoiceNumber)->exists());

        return $invoiceNumber;
    }

    /**
     * Get the net amount (after discounts and credits).
     */
    public function getNetAmount(): float
    {
        return $this->subtotal - $this->discount_amount - $this->credit_applied + $this->tax_amount;
    }

    /**
     * Get the refundable amount.
     */
    public function getRefundableAmount(): float
    {
        return max(0, $this->amount - $this->refunded_amount);
    }

    /**
     * Get formatted amount.
     */
    public function getFormattedAmount(): string
    {
        return $this->currency . ' ' . number_format($this->amount, 2);
    }

    /**
     * Get formatted refunded amount.
     */
    public function getFormattedRefundedAmount(): string
    {
        return $this->currency . ' ' . number_format($this->refunded_amount, 2);
    }

    /**
     * Get status label.
     */
    public function getStatusLabel(): string
    {
        return match($this->status) {
            'pending' => 'Pending',
            'processing' => 'Processing',
            'completed' => 'Completed',
            'failed' => 'Failed',
            'cancelled' => 'Cancelled',
            'refunded' => 'Refunded',
            'partially_refunded' => 'Partially Refunded',
            default => ucfirst($this->status),
        };
    }

    /**
     * Get status color for UI.
     */
    public function getStatusColor(): string
    {
        return match($this->status) {
            'pending' => 'warning',
            'processing' => 'info',
            'completed' => 'success',
            'failed' => 'danger',
            'cancelled' => 'secondary',
            'refunded' => 'info',
            'partially_refunded' => 'warning',
            default => 'secondary',
        };
    }

    /**
     * Get payment method label.
     */
    public function getPaymentMethodLabel(): string
    {
        return match($this->payment_method) {
            'stripe' => 'Credit Card (Stripe)',
            'paypal' => 'PayPal',
            'bank_transfer' => 'Bank Transfer',
            'manual' => 'Manual Payment',
            'subscription_code' => 'Subscription Code',
            'credit' => 'Account Credit',
            default => ucfirst(str_replace('_', ' ', $this->payment_method)),
        };
    }

    /**
     * Send invoice email.
     */
    public function sendInvoice(): void
    {
        // This would integrate with your email system
        // For now, just mark as sent
        $this->update([
            'invoice_sent' => true,
            'invoice_sent_at' => now(),
        ]);
    }

    /**
     * Create a new payment record.
     */
    public static function createPayment(array $data): self
    {
        return self::create(array_merge([
            'payment_id' => self::generatePaymentId(),
            'invoice_number' => self::generateInvoiceNumber(),
            'status' => 'pending',
            'retry_count' => 0,
        ], $data));
    }
}
