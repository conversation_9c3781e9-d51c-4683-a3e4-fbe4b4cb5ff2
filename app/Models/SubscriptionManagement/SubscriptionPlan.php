<?php

namespace App\Models\SubscriptionManagement;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class SubscriptionPlan extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'price',
        'yearly_price',
        'currency',
        'is_active',
        'trial_days',
        'max_users',
        'max_orders',
        'max_storage_gb',
        'max_buyers',
        'max_garment_orders',
        'max_stock_items',
        'has_buyer_crm',
        'has_garment_orders',
        'has_stock_management',
        'has_financial_reports',
        'has_advanced_analytics',
        'has_api_access',
        'has_custom_branding',
        'has_priority_support',
        'has_data_export',
        'has_multi_location',
        'stripe_price_id',
        'stripe_yearly_price_id',
        'stripe_product_id',
        'sort_order',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'yearly_price' => 'decimal:2',
        'is_active' => 'boolean',
        'has_buyer_crm' => 'boolean',
        'has_garment_orders' => 'boolean',
        'has_stock_management' => 'boolean',
        'has_financial_reports' => 'boolean',
        'has_advanced_analytics' => 'boolean',
        'has_api_access' => 'boolean',
        'has_custom_branding' => 'boolean',
        'has_priority_support' => 'boolean',
        'has_data_export' => 'boolean',
        'has_multi_location' => 'boolean',
    ];

    /**
     * Get the user subscriptions for this plan.
     */
    public function userSubscriptions(): HasMany
    {
        return $this->hasMany(UserSubscription::class);
    }

    /**
     * Get the subscription codes for this plan.
     */
    public function subscriptionCodes(): HasMany
    {
        return $this->hasMany(SubscriptionCode::class);
    }

    /**
     * Scope to get only active plans.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get plans ordered by sort order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('price');
    }

    /**
     * Check if this is the free plan.
     */
    public function isFree(): bool
    {
        return $this->slug === 'free' || $this->price == 0;
    }

    /**
     * Check if this is the premium plan.
     */
    public function isPremium(): bool
    {
        return $this->slug === 'premium' || $this->price > 0;
    }

    /**
     * Get the monthly price for display.
     */
    public function getMonthlyPriceAttribute(): string
    {
        return number_format($this->price, 2);
    }

    /**
     * Get the yearly price for display.
     */
    public function getYearlyPriceDisplayAttribute(): string
    {
        return number_format($this->attributes['yearly_price'] ?? 0, 2);
    }

    /**
     * Get yearly savings amount.
     */
    public function getYearlySavingsAttribute(): float
    {
        $monthlyTotal = $this->attributes['price'] * 12;
        return $monthlyTotal - ($this->attributes['yearly_price'] ?? 0);
    }

    /**
     * Get yearly savings percentage.
     */
    public function getYearlySavingsPercentageAttribute(): int
    {
        $price = $this->attributes['price'] ?? 0;
        if ($price == 0) return 0;

        $monthlyTotal = $price * 12;
        if ($monthlyTotal == 0) return 0;

        $yearlyPrice = $this->attributes['yearly_price'] ?? 0;
        return round((($monthlyTotal - $yearlyPrice) / $monthlyTotal) * 100);
    }

    /**
     * Check if a feature is available in this plan.
     */
    public function hasFeature(string $feature): bool
    {
        $featureColumn = 'has_' . $feature;
        return $this->$featureColumn ?? false;
    }

    /**
     * Get the limit for a specific metric.
     */
    public function getLimit(string $metric): int
    {
        $limitColumn = 'max_' . $metric;
        return $this->$limitColumn ?? 0;
    }

    /**
     * Check if a limit is unlimited (0 means unlimited).
     */
    public function isUnlimited(string $metric): bool
    {
        return $this->getLimit($metric) === 0;
    }

    /**
     * Get all available features for this plan.
     */
    public function getAvailableFeatures(): array
    {
        $features = [];
        $featureColumns = [
            'buyer_crm',
            'garment_orders',
            'stock_management',
            'financial_reports',
            'advanced_analytics',
            'api_access',
            'custom_branding',
            'priority_support',
            'data_export',
            'multi_location',
        ];

        foreach ($featureColumns as $feature) {
            if ($this->hasFeature($feature)) {
                $features[] = $feature;
            }
        }

        return $features;
    }

    /**
     * Get all limits for this plan.
     */
    public function getLimits(): array
    {
        return [
            'users' => $this->max_users,
            'orders' => $this->max_orders,
            'storage_gb' => $this->max_storage_gb,
            'buyers' => $this->max_buyers,
            'garment_orders' => $this->max_garment_orders,
            'stock_items' => $this->max_stock_items,
        ];
    }

    /**
     * Get formatted limits for display.
     */
    public function getFormattedLimits(): array
    {
        $limits = $this->getLimits();
        $formatted = [];

        foreach ($limits as $key => $value) {
            $formatted[$key] = $value === 0 ? 'Unlimited' : number_format($value);
        }

        return $formatted;
    }
}
