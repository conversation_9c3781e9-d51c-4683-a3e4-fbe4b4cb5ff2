<?php

namespace App\Models\SubscriptionManagement;

use App\Models\Tenant;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class SubscriptionUsage extends Model
{
    use HasFactory;

    protected $table = 'subscription_usage';

    protected $fillable = [
        'user_subscription_id',
        'tenant_id',
        'metric_type',
        'current_count',
        'limit_count',
        'percentage_used',
        'period_start',
        'period_end',
        'period_type',
        'previous_count',
        'peak_count',
        'peak_reached_at',
        'limit_exceeded',
        'warning_sent',
        'limit_notification_sent',
        'last_warning_at',
        'last_limit_notification_at',
        'details',
        'notes',
    ];

    protected $casts = [
        'period_start' => 'date',
        'period_end' => 'date',
        'peak_reached_at' => 'datetime',
        'last_warning_at' => 'datetime',
        'last_limit_notification_at' => 'datetime',
        'percentage_used' => 'decimal:2',
        'limit_exceeded' => 'boolean',
        'warning_sent' => 'boolean',
        'limit_notification_sent' => 'boolean',
        'details' => 'array',
    ];

    /**
     * Get the user subscription this usage belongs to.
     */
    public function userSubscription(): BelongsTo
    {
        return $this->belongsTo(UserSubscription::class);
    }

    /**
     * Get the tenant this usage belongs to.
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class);
    }

    /**
     * Scope to get current period usage.
     */
    public function scopeCurrentPeriod($query)
    {
        return $query->where('period_start', '<=', now())
                    ->where('period_end', '>=', now());
    }

    /**
     * Scope to get usage for a specific metric.
     */
    public function scopeForMetric($query, string $metric)
    {
        return $query->where('metric_type', $metric);
    }

    /**
     * Scope to get usage that has exceeded limits.
     */
    public function scopeLimitExceeded($query)
    {
        return $query->where('limit_exceeded', true);
    }

    /**
     * Scope to get usage that needs warnings.
     */
    public function scopeNeedsWarning($query)
    {
        return $query->where('percentage_used', '>=', 80)
                    ->where('warning_sent', false);
    }

    /**
     * Update the usage count and calculate percentage.
     */
    public function updateUsage(int $count): void
    {
        $this->current_count = $count;
        $this->calculatePercentage();
        
        // Update peak if this is higher
        if ($count > $this->peak_count) {
            $this->peak_count = $count;
            $this->peak_reached_at = now();
        }

        // Check if limit is exceeded
        $this->limit_exceeded = $this->limit_count > 0 && $count >= $this->limit_count;

        $this->save();
    }

    /**
     * Calculate the usage percentage.
     */
    public function calculatePercentage(): void
    {
        if ($this->limit_count === 0) {
            $this->percentage_used = 0; // Unlimited
        } else {
            $this->percentage_used = min(100, ($this->current_count / $this->limit_count) * 100);
        }
    }

    /**
     * Check if usage is approaching limit (80% or more).
     */
    public function isApproachingLimit(): bool
    {
        return $this->percentage_used >= 80;
    }

    /**
     * Check if usage has exceeded limit.
     */
    public function hasExceededLimit(): bool
    {
        return $this->limit_exceeded;
    }

    /**
     * Check if warning should be sent.
     */
    public function shouldSendWarning(): bool
    {
        return $this->isApproachingLimit() && !$this->warning_sent;
    }

    /**
     * Check if limit notification should be sent.
     */
    public function shouldSendLimitNotification(): bool
    {
        return $this->hasExceededLimit() && !$this->limit_notification_sent;
    }

    /**
     * Mark warning as sent.
     */
    public function markWarningSent(): void
    {
        $this->update([
            'warning_sent' => true,
            'last_warning_at' => now(),
        ]);
    }

    /**
     * Mark limit notification as sent.
     */
    public function markLimitNotificationSent(): void
    {
        $this->update([
            'limit_notification_sent' => true,
            'last_limit_notification_at' => now(),
        ]);
    }

    /**
     * Get the growth rate compared to previous period.
     */
    public function getGrowthRate(): float
    {
        if ($this->previous_count === 0) {
            return $this->current_count > 0 ? 100 : 0;
        }

        return (($this->current_count - $this->previous_count) / $this->previous_count) * 100;
    }

    /**
     * Get remaining capacity.
     */
    public function getRemainingCapacity(): int
    {
        if ($this->limit_count === 0) {
            return PHP_INT_MAX; // Unlimited
        }

        return max(0, $this->limit_count - $this->current_count);
    }

    /**
     * Get formatted metric type.
     */
    public function getFormattedMetricType(): string
    {
        return match($this->metric_type) {
            'users' => 'Users',
            'orders' => 'Orders',
            'storage' => 'Storage (GB)',
            'buyers' => 'Buyers',
            'garment_orders' => 'Garment Orders',
            'stock_items' => 'Stock Items',
            default => ucfirst(str_replace('_', ' ', $this->metric_type)),
        };
    }

    /**
     * Get formatted current count.
     */
    public function getFormattedCurrentCount(): string
    {
        if ($this->metric_type === 'storage') {
            return number_format($this->current_count, 2) . ' GB';
        }

        return number_format($this->current_count);
    }

    /**
     * Get formatted limit.
     */
    public function getFormattedLimit(): string
    {
        if ($this->limit_count === 0) {
            return 'Unlimited';
        }

        if ($this->metric_type === 'storage') {
            return number_format($this->limit_count, 2) . ' GB';
        }

        return number_format($this->limit_count);
    }

    /**
     * Get status color for UI.
     */
    public function getStatusColor(): string
    {
        if ($this->hasExceededLimit()) {
            return 'danger';
        }

        if ($this->isApproachingLimit()) {
            return 'warning';
        }

        return 'success';
    }

    /**
     * Create or update usage for a subscription and metric.
     */
    public static function updateForSubscription(
        UserSubscription $subscription,
        string $metric,
        int $count,
        string $periodType = 'monthly'
    ): self {
        $period = self::getCurrentPeriod($periodType);
        
        $usage = self::firstOrCreate([
            'user_subscription_id' => $subscription->id,
            'tenant_id' => $subscription->tenant_id,
            'metric_type' => $metric,
            'period_start' => $period['start'],
            'period_end' => $period['end'],
            'period_type' => $periodType,
        ], [
            'limit_count' => $subscription->subscriptionPlan->getLimit($metric),
            'current_count' => 0,
            'percentage_used' => 0,
            'previous_count' => 0,
            'peak_count' => 0,
        ]);

        $usage->updateUsage($count);
        
        return $usage;
    }

    /**
     * Get current period dates based on period type.
     */
    protected static function getCurrentPeriod(string $periodType): array
    {
        $now = Carbon::now();

        return match($periodType) {
            'daily' => [
                'start' => $now->startOfDay()->toDateString(),
                'end' => $now->endOfDay()->toDateString(),
            ],
            'weekly' => [
                'start' => $now->startOfWeek()->toDateString(),
                'end' => $now->endOfWeek()->toDateString(),
            ],
            'monthly' => [
                'start' => $now->startOfMonth()->toDateString(),
                'end' => $now->endOfMonth()->toDateString(),
            ],
            'yearly' => [
                'start' => $now->startOfYear()->toDateString(),
                'end' => $now->endOfYear()->toDateString(),
            ],
            default => [
                'start' => $now->startOfMonth()->toDateString(),
                'end' => $now->endOfMonth()->toDateString(),
            ],
        };
    }
}
