<?php

namespace App\Models\SubscriptionManagement;

use App\Models\User;
use App\Models\Tenant;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Carbon\Carbon;

class UserSubscription extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'tenant_id',
        'subscription_plan_id',
        'status',
        'starts_at',
        'ends_at',
        'trial_ends_at',
        'grace_period_ends_at',
        'cancelled_at',
        'suspended_at',
        'billing_cycle',
        'amount',
        'currency',
        'next_billing_date',
        'last_payment_date',
        'stripe_subscription_id',
        'stripe_customer_id',
        'stripe_price_id',
        'stripe_metadata',
        'current_users',
        'current_orders',
        'current_storage_gb',
        'current_buyers',
        'current_garment_orders',
        'current_stock_items',
        'cancellation_reason',
        'cancellation_feedback',
        'cancelled_by',
        'notes',
        'metadata',
    ];

    protected $casts = [
        'starts_at' => 'datetime',
        'ends_at' => 'datetime',
        'trial_ends_at' => 'datetime',
        'grace_period_ends_at' => 'datetime',
        'cancelled_at' => 'datetime',
        'suspended_at' => 'datetime',
        'next_billing_date' => 'datetime',
        'last_payment_date' => 'datetime',
        'amount' => 'decimal:2',
        'current_storage_gb' => 'decimal:2',
        'stripe_metadata' => 'array',
        'metadata' => 'array',
    ];

    /**
     * Get the user that owns the subscription.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the tenant that owns the subscription.
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class);
    }

    /**
     * Get the subscription plan.
     */
    public function subscriptionPlan(): BelongsTo
    {
        return $this->belongsTo(SubscriptionPlan::class);
    }

    /**
     * Get the user who cancelled the subscription.
     */
    public function cancelledBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'cancelled_by');
    }

    /**
     * Get the subscription usage records.
     */
    public function usageRecords(): HasMany
    {
        return $this->hasMany(SubscriptionUsage::class);
    }

    /**
     * Get the subscription payments.
     */
    public function payments(): HasMany
    {
        return $this->hasMany(SubscriptionPayment::class);
    }

    /**
     * Get the subscription notifications.
     */
    public function notifications(): HasMany
    {
        return $this->hasMany(SubscriptionNotification::class);
    }

    /**
     * Scope to get active subscriptions.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope to get subscriptions in grace period.
     */
    public function scopeInGracePeriod($query)
    {
        return $query->where('status', 'grace_period');
    }

    /**
     * Scope to get expired subscriptions.
     */
    public function scopeExpired($query)
    {
        return $query->where('status', 'expired');
    }

    /**
     * Scope to get suspended subscriptions.
     */
    public function scopeSuspended($query)
    {
        return $query->where('status', 'suspended');
    }

    /**
     * Check if subscription is active.
     */
    public function isActive(): bool
    {
        return $this->status === 'active' && 
               ($this->ends_at === null || $this->ends_at->isFuture());
    }

    /**
     * Check if subscription is in trial period.
     */
    public function isInTrial(): bool
    {
        return $this->trial_ends_at && $this->trial_ends_at->isFuture();
    }

    /**
     * Check if subscription is in grace period.
     */
    public function isInGracePeriod(): bool
    {
        return $this->status === 'grace_period' && 
               $this->grace_period_ends_at && 
               $this->grace_period_ends_at->isFuture();
    }

    /**
     * Check if subscription is expired.
     */
    public function isExpired(): bool
    {
        return $this->status === 'expired' || 
               ($this->ends_at && $this->ends_at->isPast());
    }

    /**
     * Check if subscription is suspended.
     */
    public function isSuspended(): bool
    {
        return $this->status === 'suspended';
    }

    /**
     * Check if subscription is cancelled.
     */
    public function isCancelled(): bool
    {
        return $this->status === 'cancelled';
    }

    /**
     * Get days remaining in current period.
     */
    public function getDaysRemaining(): int
    {
        if ($this->isInTrial()) {
            return $this->trial_ends_at->diffInDays(now());
        }

        if ($this->isInGracePeriod()) {
            return $this->grace_period_ends_at->diffInDays(now());
        }

        if ($this->ends_at) {
            return max(0, $this->ends_at->diffInDays(now()));
        }

        return 0;
    }

    /**
     * Get the current period type.
     */
    public function getCurrentPeriodType(): string
    {
        if ($this->isInTrial()) {
            return 'trial';
        }

        if ($this->isInGracePeriod()) {
            return 'grace_period';
        }

        if ($this->isActive()) {
            return 'active';
        }

        return $this->status;
    }

    /**
     * Check if user can access a specific feature.
     */
    public function canAccessFeature(string $feature): bool
    {
        if (!$this->isActive() && !$this->isInTrial() && !$this->isInGracePeriod()) {
            return false;
        }

        return $this->subscriptionPlan->hasFeature($feature);
    }

    /**
     * Check if user has reached a usage limit.
     */
    public function hasReachedLimit(string $metric): bool
    {
        $limit = $this->subscriptionPlan->getLimit($metric);
        
        // 0 means unlimited
        if ($limit === 0) {
            return false;
        }

        $currentUsage = $this->{'current_' . $metric} ?? 0;
        return $currentUsage >= $limit;
    }

    /**
     * Get usage percentage for a metric.
     */
    public function getUsagePercentage(string $metric): float
    {
        $limit = $this->subscriptionPlan->getLimit($metric);
        
        if ($limit === 0) {
            return 0; // Unlimited
        }

        $currentUsage = $this->{'current_' . $metric} ?? 0;
        return min(100, ($currentUsage / $limit) * 100);
    }

    /**
     * Update usage for a specific metric.
     */
    public function updateUsage(string $metric, int $count): void
    {
        $this->update(['current_' . $metric => $count]);
    }

    /**
     * Increment usage for a specific metric.
     */
    public function incrementUsage(string $metric, int $increment = 1): void
    {
        $currentValue = $this->{'current_' . $metric} ?? 0;
        $this->updateUsage($metric, $currentValue + $increment);
    }

    /**
     * Decrement usage for a specific metric.
     */
    public function decrementUsage(string $metric, int $decrement = 1): void
    {
        $currentValue = $this->{'current_' . $metric} ?? 0;
        $this->updateUsage($metric, max(0, $currentValue - $decrement));
    }

    /**
     * Cancel the subscription.
     */
    public function cancel(string $reason = null, string $feedback = null): void
    {
        $this->update([
            'status' => 'cancelled',
            'cancelled_at' => now(),
            'cancellation_reason' => $reason,
            'cancellation_feedback' => $feedback,
            'cancelled_by' => auth()->id(),
        ]);
    }

    /**
     * Suspend the subscription.
     */
    public function suspend(string $reason = null): void
    {
        $this->update([
            'status' => 'suspended',
            'suspended_at' => now(),
            'notes' => $reason,
        ]);
    }

    /**
     * Reactivate the subscription.
     */
    public function reactivate(): void
    {
        $this->update([
            'status' => 'active',
            'suspended_at' => null,
        ]);
    }

    /**
     * Start grace period.
     */
    public function startGracePeriod(int $days = 180): void
    {
        $this->update([
            'status' => 'grace_period',
            'grace_period_ends_at' => now()->addDays($days),
        ]);
    }
}
