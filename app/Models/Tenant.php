<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Artisan;

class Tenant extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'company_name',
        'domain',
        'database_name',
        'database_host',
        'database_username',
        'database_password',
        'database_port',
        'contact_person',
        'email',
        'phone',
        'address',
        'country',
        'timezone',
        'currency',
        'plan',
        'trial_ends_at',
        'subscription_ends_at',
        'is_active',
        'max_users',
        'max_storage_gb',
        'custom_branding',
        'logo_path',
        'primary_color',
        'secondary_color',
        'encryption_key',
        'settings',
    ];

    protected $casts = [
        'free_tier_started_at' => 'datetime',
        'free_tier_ends_at' => 'datetime',
        'grace_period_ends_at' => 'datetime',
        'subscription_ends_at' => 'datetime',
        'trial_ends_at' => 'datetime',
        'last_payment_at' => 'datetime',
        'next_billing_date' => 'datetime',
        'is_active' => 'boolean',
        'is_suspended' => 'boolean',
        'custom_branding' => 'boolean',
        'settings' => 'array',
    ];

    protected $hidden = [
        'database_password',
        'encryption_key',
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($tenant) {
            // Generate unique database name if not provided
            if (!$tenant->database_name) {
                $tenant->database_name = 'tenant_' . strtolower($tenant->name) . '_' . time();
            }

            // Generate encryption key
            if (!$tenant->encryption_key) {
                $tenant->encryption_key = base64_encode(random_bytes(32));
            }
        });

        static::created(function ($tenant) {
            // Only create databases in production environment
            if (app()->environment('production')) {
                $tenant->createDatabase();
                $tenant->runMigrations();
                $tenant->seedDatabase();
            } else {
                // In development, just log the tenant creation
                \Log::info("Tenant created in development mode: {$tenant->name}");
            }
        });

        static::deleting(function ($tenant) {
            if ($tenant->isForceDeleting()) {
                $tenant->dropDatabase();
            }
        });
    }

    /**
     * Create tenant database.
     */
    public function createDatabase(): bool
    {
        try {
            $connection = DB::connection('mysql');
            $connection->statement("CREATE DATABASE IF NOT EXISTS `{$this->database_name}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
            
            // Create database user
            $connection->statement("CREATE USER IF NOT EXISTS '{$this->database_username}'@'{$this->database_host}' IDENTIFIED BY '{$this->database_password}'");
            $connection->statement("GRANT ALL PRIVILEGES ON `{$this->database_name}`.* TO '{$this->database_username}'@'{$this->database_host}'");
            $connection->statement("FLUSH PRIVILEGES");
            
            return true;
        } catch (\Exception $e) {
            \Log::error("Failed to create database for tenant {$this->name}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Drop tenant database.
     */
    public function dropDatabase(): bool
    {
        try {
            $connection = DB::connection('mysql');
            $connection->statement("DROP DATABASE IF EXISTS `{$this->database_name}`");
            $connection->statement("DROP USER IF EXISTS '{$this->database_username}'@'{$this->database_host}'");
            
            return true;
        } catch (\Exception $e) {
            \Log::error("Failed to drop database for tenant {$this->name}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Configure database connection for this tenant.
     */
    public function configure(): void
    {
        // In development, use the main database connection
        if (!app()->environment('production')) {
            Config::set('database.connections.tenant', config('database.connections.mysql'));
            return;
        }

        Config::set('database.connections.tenant', [
            'driver' => 'mysql',
            'host' => $this->database_host,
            'port' => $this->database_port,
            'database' => $this->database_name,
            'username' => $this->database_username,
            'password' => $this->database_password,
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
            'prefix' => '',
            'strict' => true,
            'engine' => null,
        ]);

        DB::purge('tenant');
        DB::reconnect('tenant');
    }

    /**
     * Run migrations for tenant database.
     */
    public function runMigrations(): bool
    {
        try {
            $this->configure();
            
            Artisan::call('migrate', [
                '--database' => 'tenant',
                '--path' => 'database/migrations/tenant',
                '--force' => true,
            ]);
            
            return true;
        } catch (\Exception $e) {
            \Log::error("Failed to run migrations for tenant {$this->name}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Seed tenant database.
     */
    public function seedDatabase(): bool
    {
        try {
            $this->configure();
            
            Artisan::call('db:seed', [
                '--database' => 'tenant',
                '--class' => 'TenantSeeder',
                '--force' => true,
            ]);
            
            return true;
        } catch (\Exception $e) {
            \Log::error("Failed to seed database for tenant {$this->name}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Check if tenant is on free tier.
     */
    public function isOnFreeTier(): bool
    {
        return $this->plan === 'free' &&
               $this->free_tier_ends_at &&
               $this->free_tier_ends_at->isFuture();
    }

    /**
     * Check if tenant is in grace period.
     */
    public function isInGracePeriod(): bool
    {
        return $this->grace_period_ends_at &&
               $this->grace_period_ends_at->isFuture() &&
               (!$this->free_tier_ends_at || $this->free_tier_ends_at->isPast());
    }

    /**
     * Check if tenant subscription is active.
     */
    public function hasActiveSubscription(): bool
    {
        if ($this->isOnFreeTier() || $this->isInGracePeriod()) {
            return true;
        }

        return $this->plan === 'premium' &&
               $this->subscription_ends_at &&
               $this->subscription_ends_at->isFuture();
    }

    /**
     * Check if tenant should be suspended.
     */
    public function shouldBeSuspended(): bool
    {
        return !$this->hasActiveSubscription() &&
               (!$this->grace_period_ends_at || $this->grace_period_ends_at->isPast());
    }

    /**
     * Get days remaining in current period.
     */
    public function getDaysRemaining(): int
    {
        if ($this->isOnFreeTier()) {
            return $this->free_tier_ends_at->diffInDays(now());
        }

        if ($this->isInGracePeriod()) {
            return $this->grace_period_ends_at->diffInDays(now());
        }

        if ($this->subscription_ends_at) {
            return max(0, $this->subscription_ends_at->diffInDays(now()));
        }

        return 0;
    }

    /**
     * Start grace period.
     */
    public function startGracePeriod(int $days = 180): void
    {
        $this->update([
            'grace_period_ends_at' => now()->addDays($days),
        ]);
    }

    /**
     * Upgrade to premium.
     */
    public function upgradeToPremium(): void
    {
        $this->update([
            'plan' => 'premium',
            'subscription_ends_at' => now()->addYear(),
            'grace_period_ends_at' => null,
            'is_suspended' => false,
            'suspension_reason' => null,
        ]);
    }

    /**
     * Suspend tenant.
     */
    public function suspend(string $reason = null): void
    {
        $this->update([
            'is_suspended' => true,
            'suspension_reason' => $reason,
        ]);
    }

    /**
     * Reactivate tenant.
     */
    public function reactivate(): void
    {
        $this->update([
            'is_suspended' => false,
            'suspension_reason' => null,
        ]);
    }

    /**
     * Get tenant by domain or subdomain.
     */
    public static function findByDomain(string $domain): ?self
    {
        // Check for custom domain first
        $tenant = static::where('domain', $domain)->where('is_active', true)->first();
        
        if (!$tenant) {
            // Check for subdomain (name.app-domain.com)
            $subdomain = explode('.', $domain)[0];
            $tenant = static::where('name', $subdomain)->where('is_active', true)->first();
        }
        
        return $tenant;
    }

    /**
     * Get tenant URL.
     */
    public function getUrlAttribute(): string
    {
        if ($this->domain) {
            return "https://{$this->domain}";
        }
        
        $appDomain = config('app.domain', 'localhost');
        return "https://{$this->name}.{$appDomain}";
    }

    /**
     * Get the users belonging to this tenant.
     */
    public function users(): HasMany
    {
        return $this->hasMany(\App\Models\User::class);
    }

    /**
     * Get the user subscriptions for this tenant.
     */
    public function userSubscriptions(): HasMany
    {
        return $this->hasMany(\App\Models\SubscriptionManagement\UserSubscription::class);
    }

    /**
     * Get the subscription payments for this tenant.
     */
    public function subscriptionPayments(): HasMany
    {
        return $this->hasMany(\App\Models\SubscriptionManagement\SubscriptionPayment::class);
    }

    /**
     * Get the subscription notifications for this tenant.
     */
    public function subscriptionNotifications(): HasMany
    {
        return $this->hasMany(\App\Models\SubscriptionManagement\SubscriptionNotification::class);
    }

    /**
     * Get the subscription usage records for this tenant.
     */
    public function subscriptionUsage(): HasMany
    {
        return $this->hasMany(\App\Models\SubscriptionManagement\SubscriptionUsage::class);
    }

    /**
     * Get the active subscription for this tenant.
     */
    public function getActiveSubscription(): ?\App\Models\SubscriptionManagement\UserSubscription
    {
        return $this->userSubscriptions()
                   ->where('status', 'active')
                   ->first();
    }
}
