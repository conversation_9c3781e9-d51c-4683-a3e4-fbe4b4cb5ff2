<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Lara<PERSON>\Sanctum\HasApiTokens;
use Spatie\Permission\Traits\HasRoles;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable, HasRoles;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'role',
        'phone',
        'email',
        'password',
        'country',
        'image',
        'created_by',
        'department',
        'employee_id',
        'status',
        'last_login_at',
        'timezone',
        'language',
        'two_factor_enabled',
        'stock_item_categories',
        'subscription_role',
        'is_subscription_admin',
        'tenant_id',
        'subscription_status',
        'free_tier_started_at',
        'has_used_free_tier',
        'email_notifications',
        'usage_alerts',
        'billing_alerts',
        'stripe_customer_id',
        'last_subscription_activity',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'last_login_at' => 'datetime',
        'status' => 'boolean',
        'two_factor_enabled' => 'boolean',
        'stock_item_categories' => 'array',
        'is_subscription_admin' => 'boolean',
        'free_tier_started_at' => 'datetime',
        'has_used_free_tier' => 'boolean',
        'email_notifications' => 'boolean',
        'usage_alerts' => 'boolean',
        'billing_alerts' => 'boolean',
        'last_subscription_activity' => 'datetime',
    ];

    /**
     * Get the custom permissions for this user.
     */
    public function customPermissions(): BelongsToMany
    {
        return $this->belongsToMany(\App\Models\Permission::class, 'user_permissions');
    }

    /**
     * Check if user has a specific permission.
     */
    public function hasPermission(string $permission): bool
    {
        // Check if user has the permission directly
        if ($this->customPermissions()->where('name', $permission)->exists()) {
            return true;
        }

        // Check if user has manage permission for the module
        $parts = explode('.', $permission);
        if (count($parts) === 2) {
            $module = $parts[0];
            $managePermission = "{$module}.manage";

            if ($this->customPermissions()->where('name', $managePermission)->exists()) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if user can access Buyer CRM.
     */
    public function canAccessBuyerCrm(): bool
    {
        return $this->customPermissions()
            ->whereIn('module', \App\Models\Permission::$modules)
            ->exists();
    }

    /**
     * Grant permission to user.
     */
    public function grantPermission(string $permissionName): void
    {
        $permission = \App\Models\Permission::where('name', $permissionName)->first();
        if ($permission && !$this->customPermissions()->where('permission_id', $permission->id)->exists()) {
            $this->customPermissions()->attach($permission->id);
        }
    }

    /**
     * Grant all permissions for a module.
     */
    public function grantModulePermissions(string $module): void
    {
        $permissions = \App\Models\Permission::where('module', $module)->get();
        foreach ($permissions as $permission) {
            $this->grantPermission($permission->name);
        }
    }

    /**
     * Grant all Buyer CRM permissions.
     */
    public function grantAllBuyerCrmPermissions(): void
    {
        foreach (\App\Models\Permission::$modules as $module) {
            $this->grantModulePermissions($module);
        }
    }

    /**
     * Relationships
     */

    /**
     * Get the user who created this user.
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get users created by this user.
     */
    public function createdUsers()
    {
        return $this->hasMany(User::class, 'created_by');
    }

    /**
     * Hierarchical User Management Methods
     */

    /**
     * Check if user is a master account holder (admin who can create other users).
     */
    public function isMasterAccount(): bool
    {
        // Handle case where created_by column doesn't exist yet
        if (!\Schema::hasColumn('users', 'created_by')) {
            return $this->hasRole(['admin', 'superadmin']);
        }
        return $this->hasRole(['admin', 'superadmin']) && $this->created_by === null;
    }

    /**
     * Check if user can create users with specific role.
     */
    public function canCreateRole(string $role): bool
    {
        // Master account can create any role
        if ($this->isMasterAccount()) {
            return true;
        }

        // Admin can create all roles except admin and superadmin
        if ($this->hasRole('admin')) {
            return !in_array($role, ['admin', 'superadmin']);
        }

        // Manager can create limited roles
        if ($this->hasRole('manager')) {
            return in_array($role, ['buyer', 'supplier', 'merchandiser', 'commercial']);
        }

        return false;
    }

    /**
     * Get available roles that this user can assign.
     */
    public function getAssignableRoles(): array
    {
        $allRoles = [
            'admin' => 'Administrator',
            'manager' => 'Manager',
            'buyer' => 'Buyer',
            'supplier' => 'Supplier',
            'merchandiser' => 'Merchandiser',
            'commercial' => 'Commercial',
            'accounts' => 'Accounts',
            'production_manager' => 'Production Manager',
        ];

        // If we're not in multi-tenant mode yet, return empty array
        if (!\Schema::hasColumn('users', 'created_by')) {
            return [];
        }

        if ($this->isMasterAccount()) {
            return $allRoles;
        }

        if ($this->hasRole('admin')) {
            unset($allRoles['admin']);
            return $allRoles;
        }

        if ($this->hasRole('manager')) {
            return array_intersect_key($allRoles, array_flip(['buyer', 'supplier', 'merchandiser', 'commercial']));
        }

        return [];
    }

    /**
     * Create a new user under this user's hierarchy.
     */
    public function createSubUser(array $userData): User
    {
        $userData['created_by'] = $this->id;

        $user = User::create($userData);

        // Assign role if provided
        if (isset($userData['role']) && $this->canCreateRole($userData['role'])) {
            $user->assignRole($userData['role']);
        }

        return $user;
    }

    /**
     * Get all users in this user's hierarchy.
     */
    public function getHierarchyUsers()
    {
        if ($this->isMasterAccount()) {
            // Master account can see all users in the tenant
            return User::all();
        }

        // Return users created by this user and their sub-users
        return User::where('created_by', $this->id)
                   ->orWhereIn('created_by', $this->createdUsers->pluck('id'))
                   ->get();
    }

    /**
     * Check if this user can manage another user.
     */
    public function canManageUser(User $user): bool
    {
        // Master account can manage all users
        if ($this->isMasterAccount()) {
            return true;
        }

        // Can manage users they created
        if ($user->created_by === $this->id) {
            return true;
        }

        // Can manage users created by their sub-users (if they're admin/manager)
        if ($this->hasRole(['admin', 'manager'])) {
            return $this->createdUsers->contains('id', $user->created_by);
        }

        return false;
    }

    /**
     * Security and Activity Methods
     */

    /**
     * Update last login timestamp.
     */
    public function updateLastLogin(): void
    {
        // Only update if the column exists
        if (\Schema::hasColumn('users', 'last_login_at')) {
            try {
                $this->update(['last_login_at' => now()]);
            } catch (\Exception $e) {
                // Silently continue if column doesn't exist
                \Log::info('Could not update last_login_at: ' . $e->getMessage());
            }
        }
    }

    /**
     * Check if user account is active.
     */
    public function isActive(): bool
    {
        return $this->status === true;
    }

    /**
     * Activate user account.
     */
    public function activate(): void
    {
        $this->update(['status' => true]);
    }

    /**
     * Deactivate user account.
     */
    public function deactivate(): void
    {
        $this->update(['status' => false]);
    }

    /**
     * Get user's role display name.
     */
    public function getRoleDisplayName(): string
    {
        $roleNames = [
            'admin' => 'Administrator',
            'manager' => 'Manager',
            'buyer' => 'Buyer',
            'supplier' => 'Supplier',
            'merchandiser' => 'Merchandiser',
            'commercial' => 'Commercial',
            'accounts' => 'Accounts',
            'production_manager' => 'Production Manager',
        ];

        return $roleNames[$this->role] ?? ucfirst($this->role);
    }

    /**
     * Get the tenant this user belongs to.
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class);
    }

    /**
     * Get the user subscriptions.
     */
    public function userSubscriptions(): HasMany
    {
        return $this->hasMany(\App\Models\SubscriptionManagement\UserSubscription::class);
    }

    /**
     * Get the active subscription.
     */
    public function activeSubscription(): HasOne
    {
        return $this->hasOne(\App\Models\SubscriptionManagement\UserSubscription::class)
                   ->where('status', 'active');
    }

    /**
     * Get the subscription payments.
     */
    public function subscriptionPayments(): HasMany
    {
        return $this->hasMany(\App\Models\SubscriptionManagement\SubscriptionPayment::class);
    }

    /**
     * Get the subscription notifications.
     */
    public function subscriptionNotifications(): HasMany
    {
        return $this->hasMany(\App\Models\SubscriptionManagement\SubscriptionNotification::class);
    }

    /**
     * Check if user is a super admin.
     */
    public function isSuperAdmin(): bool
    {
        return $this->subscription_role === 'super_admin';
    }

    /**
     * Check if user is a company admin.
     */
    public function isCompanyAdmin(): bool
    {
        return $this->subscription_role === 'company_admin' || $this->is_subscription_admin;
    }

    /**
     * Check if user can manage subscriptions.
     */
    public function canManageSubscriptions(): bool
    {
        return $this->isSuperAdmin() || $this->isCompanyAdmin();
    }

    /**
     * Check if user has an active subscription.
     */
    public function hasActiveSubscription(): bool
    {
        return $this->activeSubscription()->exists();
    }

    /**
     * Check if user is on free tier.
     */
    public function isOnFreeTier(): bool
    {
        $subscription = $this->activeSubscription;
        return $subscription && $subscription->subscriptionPlan->isFree();
    }

    /**
     * Check if user is on premium plan.
     */
    public function isPremiumUser(): bool
    {
        $subscription = $this->activeSubscription;
        return $subscription && $subscription->subscriptionPlan->isPremium();
    }

    /**
     * Check if user can access a feature.
     */
    public function canAccessFeature(string $feature): bool
    {
        if ($this->isSuperAdmin()) {
            return true;
        }

        $subscription = $this->activeSubscription;
        return $subscription && $subscription->canAccessFeature($feature);
    }

    /**
     * Get user's subscription status.
     */
    public function getSubscriptionStatus(): string
    {
        $subscription = $this->activeSubscription;

        if (!$subscription) {
            return 'inactive';
        }

        return $subscription->getCurrentPeriodType();
    }

    /**
     * Start free tier for user.
     */
    public function startFreeTier(): void
    {
        $this->update([
            'subscription_status' => 'active',
            'free_tier_started_at' => now(),
            'has_used_free_tier' => true,
        ]);
    }

    /**
     * Update last subscription activity.
     */
    public function updateSubscriptionActivity(): void
    {
        $this->update(['last_subscription_activity' => now()]);
    }

    /**
     * AI Marker Optimization Tool Relationships
     */

    /**
     * Get AI Marker permissions for this user
     */
    public function aiMarkerPermissions(): HasMany
    {
        return $this->hasMany(\App\Models\AiMarker\UserPermission::class);
    }

    /**
     * Get AI Marker optimizations created by this user
     */
    public function aiMarkerOptimizations(): HasMany
    {
        return $this->hasMany(\App\Models\AiMarker\MarkerOptimization::class);
    }

    /**
     * Get AI Marker pattern pieces created by this user
     */
    public function aiMarkerPatternPieces(): HasMany
    {
        return $this->hasMany(\App\Models\AiMarker\PatternPiece::class, 'created_by');
    }

    /**
     * Get AI Marker optimization templates created by this user
     */
    public function aiMarkerOptimizationTemplates(): HasMany
    {
        return $this->hasMany(\App\Models\AiMarker\OptimizationTemplate::class, 'created_by');
    }

    /**
     * Get AI Marker exports created by this user
     */
    public function aiMarkerExports(): HasMany
    {
        return $this->hasMany(\App\Models\AiMarker\MarkerExport::class);
    }
}
