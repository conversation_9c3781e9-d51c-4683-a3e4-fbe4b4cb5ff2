<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\View;
use App\Models\User;
use App\Services\AiMarker\PermissionService;
use App\Services\MarkerOptimizationService;

/**
 * AI Marker Service Provider
 * 
 * Registers services, gates, and view composers for the AI Marker Optimization Tool
 */
class AiMarkerServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Register core services
        $this->app->singleton(PermissionService::class, function ($app) {
            return new PermissionService();
        });

        $this->app->singleton(MarkerOptimizationService::class, function ($app) {
            return new MarkerOptimizationService();
        });

        // Register configuration
        $this->mergeConfigFrom(
            __DIR__.'/../../config/ai-marker.php', 'ai-marker'
        );
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Register authorization gates
        $this->registerGates();

        // Register view composers
        $this->registerViewComposers();

        // Register middleware
        $this->registerMiddleware();

        // Load routes
        $this->loadRoutes();

        // Publish configuration and assets
        $this->publishAssets();
    }

    /**
     * Register authorization gates for AI Marker permissions
     */
    protected function registerGates(): void
    {
        // Basic access gate
        Gate::define('ai-marker.access', function (User $user) {
            // PRIORITY: <NAME_EMAIL>
            if ($user->email === '<EMAIL>') {
                return true;
            }

            // Allow other superadmin users, even if tables don't exist
            if (method_exists($user, 'hasRole') && $user->hasRole('superadmin')) {
                return true;
            }

            if (!$this->tablesExist()) {
                return false; // No access if tables don't exist
            }
            return $this->app->make(PermissionService::class)->hasPermission($user, 'ai-marker.access');
        });

        // Optimization permissions
        Gate::define('ai-marker.optimize.create', function (User $user) {
            return $this->app->make(PermissionService::class)->hasPermission($user, 'ai-marker.optimize.create');
        });

        Gate::define('ai-marker.optimize.view', function (User $user) {
            return $this->app->make(PermissionService::class)->hasPermission($user, 'ai-marker.optimize.view');
        });

        Gate::define('ai-marker.optimize.edit', function (User $user) {
            return $this->app->make(PermissionService::class)->hasPermission($user, 'ai-marker.optimize.edit');
        });

        Gate::define('ai-marker.optimize.delete', function (User $user) {
            return $this->app->make(PermissionService::class)->hasPermission($user, 'ai-marker.optimize.delete');
        });

        // Pattern piece permissions
        Gate::define('ai-marker.patterns.create', function (User $user) {
            return $this->app->make(PermissionService::class)->hasPermission($user, 'ai-marker.patterns.create');
        });

        Gate::define('ai-marker.patterns.view', function (User $user) {
            return $this->app->make(PermissionService::class)->hasPermission($user, 'ai-marker.patterns.view');
        });

        Gate::define('ai-marker.patterns.edit', function (User $user) {
            return $this->app->make(PermissionService::class)->hasPermission($user, 'ai-marker.patterns.edit');
        });

        Gate::define('ai-marker.patterns.delete', function (User $user) {
            return $this->app->make(PermissionService::class)->hasPermission($user, 'ai-marker.patterns.delete');
        });

        // Template permissions
        Gate::define('ai-marker.templates.create', function (User $user) {
            return $this->app->make(PermissionService::class)->hasPermission($user, 'ai-marker.templates.create');
        });

        Gate::define('ai-marker.templates.view', function (User $user) {
            return $this->app->make(PermissionService::class)->hasPermission($user, 'ai-marker.templates.view');
        });

        Gate::define('ai-marker.templates.edit', function (User $user) {
            return $this->app->make(PermissionService::class)->hasPermission($user, 'ai-marker.templates.edit');
        });

        Gate::define('ai-marker.templates.delete', function (User $user) {
            return $this->app->make(PermissionService::class)->hasPermission($user, 'ai-marker.templates.delete');
        });

        // Fabric permissions
        Gate::define('ai-marker.fabrics.create', function (User $user) {
            return $this->app->make(PermissionService::class)->hasPermission($user, 'ai-marker.fabrics.create');
        });

        Gate::define('ai-marker.fabrics.view', function (User $user) {
            return $this->app->make(PermissionService::class)->hasPermission($user, 'ai-marker.fabrics.view');
        });

        Gate::define('ai-marker.fabrics.edit', function (User $user) {
            return $this->app->make(PermissionService::class)->hasPermission($user, 'ai-marker.fabrics.edit');
        });

        Gate::define('ai-marker.fabrics.approve', function (User $user) {
            return $this->app->make(PermissionService::class)->hasPermission($user, 'ai-marker.fabrics.approve');
        });

        // Export permissions
        Gate::define('ai-marker.export.basic', function (User $user) {
            return $this->app->make(PermissionService::class)->hasPermission($user, 'ai-marker.export.basic');
        });

        Gate::define('ai-marker.export.advanced', function (User $user) {
            return $this->app->make(PermissionService::class)->hasPermission($user, 'ai-marker.export.advanced');
        });

        // Reporting permissions
        Gate::define('ai-marker.reports.view', function (User $user) {
            return $this->app->make(PermissionService::class)->hasPermission($user, 'ai-marker.reports.view');
        });

        Gate::define('ai-marker.reports.advanced', function (User $user) {
            return $this->app->make(PermissionService::class)->hasPermission($user, 'ai-marker.reports.advanced');
        });
    }

    /**
     * Register view composers for AI Marker views
     */
    protected function registerViewComposers(): void
    {
        // Sidebar composer
        View::composer('layouts.partials.ai-marker-sidebar', function ($view) {
            if (auth()->check() && auth()->user()->can('ai-marker.access')) {
                $view->with([
                    'todayOptimizations' => $this->getTodayOptimizations(),
                    'patternCount' => $this->getPatternCount(),
                    'avgEfficiency' => $this->getAverageEfficiency()
                ]);
            }
        });

        // Dashboard composer
        View::composer('ai-marker.dashboard.*', function ($view) {
            if (auth()->check()) {
                $view->with([
                    'quickTemplates' => $this->getQuickTemplates(),
                    'availableOrders' => $this->getAvailableOrders(),
                    'chartData' => $this->getChartData()
                ]);
            }
        });


    }

    /**
     * Register custom middleware
     */
    protected function registerMiddleware(): void
    {
        $router = $this->app['router'];

        // Register AI Marker permission middleware
        $router->aliasMiddleware('ai-marker.permission', \App\Http\Middleware\AiMarkerPermission::class);
    }

    /**
     * Load AI Marker routes
     */
    protected function loadRoutes(): void
    {
        if (file_exists(base_path('routes/ai-marker.php'))) {
            $this->loadRoutesFrom(base_path('routes/ai-marker.php'));
        }
    }

    /**
     * Publish configuration and assets
     */
    protected function publishAssets(): void
    {
        if ($this->app->runningInConsole()) {
            // Publish configuration
            $this->publishes([
                __DIR__.'/../../config/ai-marker.php' => config_path('ai-marker.php'),
            ], 'ai-marker-config');

            // Publish migrations
            $this->publishes([
                __DIR__.'/../../database/migrations/ai-marker' => database_path('migrations'),
            ], 'ai-marker-migrations');

            // Publish views
            $this->publishes([
                __DIR__.'/../../resources/views/ai-marker' => resource_path('views/ai-marker'),
            ], 'ai-marker-views');

            // Publish assets
            $this->publishes([
                __DIR__.'/../../public/ai-marker' => public_path('ai-marker'),
            ], 'ai-marker-assets');
        }
    }

    /**
     * Helper methods for view composers
     */
    protected function getTodayOptimizations(): int
    {
        if (!auth()->check() || !$this->tablesExist()) return 0;

        try {
            return \App\Models\AiMarker\MarkerOptimization::where('company_id', auth()->user()->company_id)
                ->whereDate('created_at', today())
                ->count();
        } catch (\Exception $e) {
            return 0;
        }
    }

    protected function getPatternCount(): int
    {
        if (!auth()->check() || !$this->tablesExist()) return 0;

        try {
            return \App\Models\AiMarker\PatternPiece::where('company_id', auth()->user()->company_id)
                ->where('status', 'active')
                ->count();
        } catch (\Exception $e) {
            return 0;
        }
    }

    protected function getAverageEfficiency(): float
    {
        if (!auth()->check() || !$this->tablesExist()) return 0;

        try {
            return \App\Models\AiMarker\MarkerOptimization::where('company_id', auth()->user()->company_id)
                ->whereDate('created_at', '>=', now()->subDays(30))
                ->avg(\DB::raw('JSON_EXTRACT(efficiency_metrics, "$.yield_percentage")')) ?? 0;
        } catch (\Exception $e) {
            return 0;
        }
    }

    protected function getQuickTemplates(): \Illuminate\Database\Eloquent\Collection
    {
        if (!auth()->check() || !$this->tablesExist()) return collect();

        try {
            return \App\Models\AiMarker\OptimizationTemplate::where('company_id', auth()->user()->company_id)
                ->where('status', 'active')
                ->where('is_default', true)
                ->limit(5)
                ->get();
        } catch (\Exception $e) {
            return collect();
        }
    }

    protected function getAvailableOrders(): \Illuminate\Database\Eloquent\Collection
    {
        if (!auth()->check()) return collect();

        try {
            // Only check for AI Marker relationship if tables exist
            $query = \App\Models\Order::where('company_id', auth()->user()->company_id)
                ->where('status', 'confirmed');

            if ($this->tablesExist()) {
                $query->whereDoesntHave('aiMarkerOptimizations');
            }

            return $query->limit(10)->get();
        } catch (\Exception $e) {
            return collect();
        }
    }

    protected function getChartData(): array
    {
        if (!auth()->check() || !$this->tablesExist()) return [];

        try {
            // Get efficiency data for the last 30 days
            $efficiencyData = \App\Models\AiMarker\MarkerOptimization::where('company_id', auth()->user()->company_id)
                ->whereDate('created_at', '>=', now()->subDays(30))
                ->selectRaw('DATE(created_at) as date, AVG(JSON_EXTRACT(efficiency_metrics, "$.yield_percentage")) as avg_efficiency')
                ->groupBy('date')
                ->orderBy('date')
                ->get();

            // Get algorithm usage data
            $algorithmData = \App\Models\AiMarker\MarkerOptimization::where('company_id', auth()->user()->company_id)
                ->selectRaw('algorithm_used, COUNT(*) as count')
                ->groupBy('algorithm_used')
                ->get();

            return [
                'efficiency' => [
                    'labels' => $efficiencyData->pluck('date')->toArray(),
                    'data' => $efficiencyData->pluck('avg_efficiency')->toArray()
                ],
                'algorithms' => [
                    'labels' => $algorithmData->pluck('algorithm_used')->toArray(),
                    'data' => $algorithmData->pluck('count')->toArray()
                ]
            ];
        } catch (\Exception $e) {
            return [];
        }
    }

    protected function getSystemStats(): array
    {
        if (!$this->tablesExist()) {
            return [
                'total_users' => 0,
                'total_companies' => 0,
                'total_optimizations' => 0,
                'api_health' => ['status' => 'pending']
            ];
        }

        try {
            return [
                'total_users' => \App\Models\User::whereHas('aiMarkerPermissions')->count(),
                'total_companies' => \App\Models\Company::whereHas('aiMarkerOptimizations')->count(),
                'total_optimizations' => \App\Models\AiMarker\MarkerOptimization::count(),
                'api_health' => $this->app->make(MarkerOptimizationService::class)->checkApiHealth()
            ];
        } catch (\Exception $e) {
            return [
                'total_users' => 0,
                'total_companies' => 0,
                'total_optimizations' => 0,
                'api_health' => ['status' => 'error']
            ];
        }
    }

    /**
     * Check if AI Marker tables exist
     */
    protected function tablesExist(): bool
    {
        try {
            return \DB::getSchemaBuilder()->hasTable('ai_marker_user_permissions') &&
                   \DB::getSchemaBuilder()->hasTable('marker_optimizations');
        } catch (\Exception $e) {
            return false;
        }
    }
}
