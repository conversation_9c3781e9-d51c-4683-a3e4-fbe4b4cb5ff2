<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Services\BuyerCRM\BuyerScoringService;

/**
 * BuyerCRM Service Provider
 * 
 * Registers all BuyerCRM related services and bindings
 */
class BuyerCrmServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        // Register BuyerCRM services
        $this->app->singleton(BuyerScoringService::class, function ($app) {
            return new BuyerScoringService();
        });
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        // Boot any BuyerCRM specific configurations
        $this->loadMigrationsFrom(database_path('migrations'));
        
        // Register any BuyerCRM specific configurations
        $this->publishes([
            __DIR__.'/../../config/buyer-crm.php' => config_path('buyer-crm.php'),
        ], 'buyer-crm-config');
    }
}
