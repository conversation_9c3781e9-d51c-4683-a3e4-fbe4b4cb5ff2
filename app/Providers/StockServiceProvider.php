<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Repositories\Contracts\StockItemRepositoryInterface;
use App\Repositories\Eloquent\StockItemRepository;
use App\Repositories\Contracts\StockTransactionRepositoryInterface;
use App\Repositories\Eloquent\StockTransactionRepository;
use App\Repositories\Contracts\StockLocationRepositoryInterface;
use App\Repositories\Eloquent\StockLocationRepository;
use App\Repositories\Contracts\StockReportRepositoryInterface;
use App\Repositories\Eloquent\StockReportRepository;
use App\Services\Stock\Enhanced\StockItemService;
use App\Services\Stock\Enhanced\StockTransactionService;
use App\Services\Stock\Enhanced\StockMovementService;
use App\Services\Stock\Enhanced\StockAlertService;
use App\Services\Stock\Enhanced\StockReportService;

class StockServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Bind repository interfaces to their implementations
        $this->app->bind(StockItemRepositoryInterface::class, StockItemRepository::class);
        $this->app->bind(StockTransactionRepositoryInterface::class, StockTransactionRepository::class);
        $this->app->bind(StockLocationRepositoryInterface::class, StockLocationRepository::class);
        $this->app->bind(StockReportRepositoryInterface::class, StockReportRepository::class);

        // Register services as singletons for better performance
        $this->app->singleton(StockItemService::class, function ($app) {
            return new StockItemService(
                $app->make(StockItemRepositoryInterface::class),
                $app->make(StockMovementService::class),
                $app->make(StockAlertService::class)
            );
        });

        $this->app->singleton(StockTransactionService::class, function ($app) {
            return new StockTransactionService(
                $app->make(StockTransactionRepositoryInterface::class),
                $app->make(StockMovementService::class),
                $app->make(StockAlertService::class)
            );
        });

        $this->app->singleton(StockMovementService::class, function ($app) {
            return new StockMovementService();
        });

        $this->app->singleton(StockAlertService::class, function ($app) {
            return new StockAlertService();
        });

        $this->app->singleton(StockReportService::class, function ($app) {
            return new StockReportService(
                $app->make(StockReportRepositoryInterface::class)
            );
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Register custom validation rules
        $this->registerValidationRules();

        // Register event listeners
        $this->registerEventListeners();

        // Register console commands
        $this->registerCommands();

        // Register middleware
        $this->registerMiddleware();

        // Register view composers
        $this->registerViewComposers();
    }

    /**
     * Register custom validation rules for stock management
     */
    protected function registerValidationRules(): void
    {
        // SKU uniqueness validation
        \Validator::extend('unique_sku', function ($attribute, $value, $parameters, $validator) {
            $excludeId = $parameters[0] ?? null;
            $query = \App\Models\Stock\StockItem::where('sku', $value);
            
            if ($excludeId) {
                $query->where('id', '!=', $excludeId);
            }
            
            return !$query->exists();
        });

        // Stock quantity validation
        \Validator::extend('sufficient_stock', function ($attribute, $value, $parameters, $validator) {
            $itemId = $parameters[0] ?? null;
            
            if (!$itemId) {
                return false;
            }
            
            $item = \App\Models\Stock\StockItem::find($itemId);
            return $item && $item->available_quantity >= $value;
        });

        // Batch expiry validation
        \Validator::extend('not_expired_batch', function ($attribute, $value, $parameters, $validator) {
            $batch = \App\Models\Stock\StockBatch::find($value);
            return $batch && (!$batch->expiry_date || $batch->expiry_date > now());
        });

        // Location capacity validation
        \Validator::extend('location_capacity', function ($attribute, $value, $parameters, $validator) {
            $locationId = $parameters[0] ?? null;
            $quantity = $parameters[1] ?? 0;
            
            if (!$locationId) {
                return true; // Skip validation if no location specified
            }
            
            $location = \App\Models\Stock\StockLocation::find($locationId);
            return $location && (!$location->max_capacity || 
                   ($location->current_capacity + $quantity) <= $location->max_capacity);
        });
    }

    /**
     * Register event listeners for stock management
     */
    protected function registerEventListeners(): void
    {
        // Stock level change events
        \Event::listen(
            \App\Events\Stock\StockLevelChanged::class,
            \App\Listeners\Stock\UpdateStockLevels::class
        );

        \Event::listen(
            \App\Events\Stock\StockLevelChanged::class,
            \App\Listeners\Stock\CheckStockAlerts::class
        );

        // Stock transaction events
        \Event::listen(
            \App\Events\Stock\StockTransactionCreated::class,
            \App\Listeners\Stock\UpdateAverageCosts::class
        );

        \Event::listen(
            \App\Events\Stock\StockTransactionCreated::class,
            \App\Listeners\Stock\LogStockActivity::class
        );

        // Stock alert events
        \Event::listen(
            \App\Events\Stock\StockAlertTriggered::class,
            \App\Listeners\Stock\SendStockNotifications::class
        );

        // Stock transfer events
        \Event::listen(
            \App\Events\Stock\StockTransferCompleted::class,
            \App\Listeners\Stock\UpdateLocationStockLevels::class
        );

        // Physical count events
        \Event::listen(
            \App\Events\Stock\StockPhysicalCountCompleted::class,
            \App\Listeners\Stock\AdjustStockLevels::class
        );
    }

    /**
     * Register console commands for stock management
     */
    protected function registerCommands(): void
    {
        if ($this->app->runningInConsole()) {
            $this->commands([
                \App\Console\Commands\Stock\CheckStockAlerts::class,
                \App\Console\Commands\Stock\UpdateAverageCosts::class,
                \App\Console\Commands\Stock\GenerateStockReports::class,
                \App\Console\Commands\Stock\CleanupExpiredBatches::class,
                \App\Console\Commands\Stock\SyncInventoryLevels::class,
                \App\Console\Commands\Stock\ImportStockData::class,
                \App\Console\Commands\Stock\ExportStockData::class,
            ]);
        }
    }

    /**
     * Register middleware for stock management
     */
    protected function registerMiddleware(): void
    {
        // Register stock-specific middleware
        $router = $this->app['router'];
        
        $router->aliasMiddleware('stock.permission', \App\Http\Middleware\StockPermissionMiddleware::class);
        $router->aliasMiddleware('stock.location.access', \App\Http\Middleware\StockLocationAccessMiddleware::class);
        $router->aliasMiddleware('stock.transaction.approval', \App\Http\Middleware\StockTransactionApprovalMiddleware::class);
    }

    /**
     * Register view composers for stock management
     */
    protected function registerViewComposers(): void
    {
        // Stock navigation composer
        \View::composer('layouts.partials.stock-navigation', function ($view) {
            $stockSummary = app(StockItemService::class)->getStockSummary();
            $view->with('stockSummary', $stockSummary);
        });

        // Stock alerts composer
        \View::composer('layouts.partials.stock-alerts', function ($view) {
            $activeAlerts = \App\Models\Stock\StockAlert::where('status', 'active')
                                                      ->where('priority', 'high')
                                                      ->limit(5)
                                                      ->get();
            $view->with('activeAlerts', $activeAlerts);
        });

        // Stock dashboard composer
        \View::composer('stock.dashboard', function ($view) {
            $dashboardData = [
                'summary' => app(StockItemService::class)->getStockSummary(),
                'lowStockItems' => app(StockItemService::class)->getLowStockItems(['category']),
                'recentTransactions' => \App\Models\Stock\StockTransaction::with(['item', 'location', 'creator'])
                                                                        ->latest()
                                                                        ->limit(10)
                                                                        ->get(),
                'activeAlerts' => \App\Models\Stock\StockAlert::where('status', 'active')
                                                             ->with(['item'])
                                                             ->latest()
                                                             ->limit(10)
                                                             ->get(),
            ];
            $view->with($dashboardData);
        });

        // Stock item form composer
        \View::composer(['stock.items.create', 'stock.items.edit'], function ($view) {
            $formData = [
                'categories' => \App\Models\Stock\StockItemCategory::active()->orderBy('name')->get(),
                'locations' => \App\Models\Stock\StockLocation::active()->orderBy('name')->get(),
                'itemTypes' => \App\Models\Stock\StockItem::getItemTypeOptions(),
                'costingMethods' => [
                    'fifo' => 'First In, First Out (FIFO)',
                    'lifo' => 'Last In, First Out (LIFO)',
                    'average' => 'Weighted Average',
                ],
                'unitsOfMeasure' => [
                    'pcs' => 'Pieces',
                    'kg' => 'Kilograms',
                    'g' => 'Grams',
                    'm' => 'Meters',
                    'cm' => 'Centimeters',
                    'l' => 'Liters',
                    'ml' => 'Milliliters',
                    'box' => 'Boxes',
                    'pack' => 'Packs',
                    'roll' => 'Rolls',
                    'yard' => 'Yards',
                    'ft' => 'Feet',
                ],
            ];
            $view->with($formData);
        });
    }

    /**
     * Get the services provided by the provider.
     */
    public function provides(): array
    {
        return [
            StockItemRepositoryInterface::class,
            StockTransactionRepositoryInterface::class,
            StockLocationRepositoryInterface::class,
            StockReportRepositoryInterface::class,
            StockItemService::class,
            StockTransactionService::class,
            StockMovementService::class,
            StockAlertService::class,
            StockReportService::class,
        ];
    }
}
