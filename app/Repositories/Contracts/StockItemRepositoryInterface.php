<?php

namespace App\Repositories\Contracts;

use App\Models\Stock\StockItem;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;

interface StockItemRepositoryInterface
{
    /**
     * Get all stock items with optional filters
     */
    public function getAll(array $filters = [], array $with = []): Collection;

    /**
     * Get paginated stock items with optional filters
     */
    public function getPaginated(array $filters = [], int $perPage = 15, array $with = []): LengthAwarePaginator;

    /**
     * Find stock item by ID
     */
    public function findById(int $id, array $with = []): ?StockItem;

    /**
     * Find stock item by SKU
     */
    public function findBySku(string $sku, array $with = []): ?StockItem;

    /**
     * Create new stock item
     */
    public function create(array $data): StockItem;

    /**
     * Update stock item
     */
    public function update(int $id, array $data): bool;

    /**
     * Delete stock item
     */
    public function delete(int $id): bool;

    /**
     * Get stock items by category
     */
    public function getByCategory(int $categoryId, array $with = []): Collection;

    /**
     * Get stock items by type
     */
    public function getByType(string $type, array $with = []): Collection;

    /**
     * Get low stock items
     */
    public function getLowStockItems(array $with = []): Collection;

    /**
     * Get out of stock items
     */
    public function getOutOfStockItems(array $with = []): Collection;

    /**
     * Get overstock items
     */
    public function getOverstockItems(array $with = []): Collection;

    /**
     * Get items by location
     */
    public function getByLocation(int $locationId, array $with = []): Collection;

    /**
     * Search items by name or SKU
     */
    public function search(string $query, array $with = []): Collection;

    /**
     * Get items with expiring batches
     */
    public function getItemsWithExpiringBatches(int $days = 30, array $with = []): Collection;

    /**
     * Get items requiring physical count
     */
    public function getItemsRequiringCount(array $with = []): Collection;

    /**
     * Update stock quantities
     */
    public function updateQuantities(int $id, array $quantities): bool;

    /**
     * Reserve stock quantity
     */
    public function reserveQuantity(int $id, float $quantity, string $reason = null): bool;

    /**
     * Release reserved quantity
     */
    public function releaseReservedQuantity(int $id, float $quantity): bool;

    /**
     * Get stock summary statistics
     */
    public function getStockSummary(): array;

    /**
     * Get items by multiple IDs
     */
    public function getByIds(array $ids, array $with = []): Collection;

    /**
     * Get active items only
     */
    public function getActive(array $with = []): Collection;

    /**
     * Get inactive items only
     */
    public function getInactive(array $with = []): Collection;

    /**
     * Bulk update items
     */
    public function bulkUpdate(array $items): bool;

    /**
     * Get items with movements in date range
     */
    public function getItemsWithMovements(\DateTime $from, \DateTime $to, array $with = []): Collection;

    /**
     * Get items by supplier
     */
    public function getBySupplier(string $supplier, array $with = []): Collection;

    /**
     * Get items by season
     */
    public function getBySeason(string $season, array $with = []): Collection;

    /**
     * Get items by quality grade
     */
    public function getByQualityGrade(string $grade, array $with = []): Collection;

    /**
     * Get items requiring reorder
     */
    public function getItemsRequiringReorder(array $with = []): Collection;

    /**
     * Get ABC analysis data
     */
    public function getAbcAnalysisData(\DateTime $from, \DateTime $to): array;

    /**
     * Get stock valuation data
     */
    public function getStockValuation(array $filters = []): array;

    /**
     * Get stock aging data
     */
    public function getStockAging(array $filters = []): array;

    /**
     * Get turnover analysis
     */
    public function getTurnoverAnalysis(\DateTime $from, \DateTime $to): array;

    /**
     * Generate next SKU for item type
     */
    public function generateNextSku(string $itemType, int $categoryId = null): string;

    /**
     * Check if SKU exists
     */
    public function skuExists(string $sku, int $excludeId = null): bool;

    /**
     * Get items with custom attributes
     */
    public function getByCustomAttributes(array $attributes, array $with = []): Collection;

    /**
     * Update average costs
     */
    public function updateAverageCosts(array $items = []): bool;

    /**
     * Get items for cycle count
     */
    public function getItemsForCycleCount(int $locationId, string $countType = 'partial'): Collection;

    /**
     * Get stock movement history for item
     */
    public function getMovementHistory(int $id, \DateTime $from = null, \DateTime $to = null): Collection;

    /**
     * Get current stock levels by location
     */
    public function getStockLevelsByLocation(int $id): Collection;

    /**
     * Get batch information for item
     */
    public function getBatchInformation(int $id): Collection;

    /**
     * Get reservation information for item
     */
    public function getReservationInformation(int $id): Collection;
}
