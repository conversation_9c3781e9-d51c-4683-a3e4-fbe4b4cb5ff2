<?php

namespace App\Repositories\Eloquent;

use App\Models\Stock\StockItem;
use App\Models\Stock\StockLocationItem;
use App\Models\Stock\StockMovement;
use App\Repositories\Contracts\StockItemRepositoryInterface;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class StockItemRepository implements StockItemRepositoryInterface
{
    protected $model;

    public function __construct(StockItem $model)
    {
        $this->model = $model;
    }

    public function getAll(array $filters = [], array $with = []): Collection
    {
        $query = $this->model->newQuery();

        if (!empty($with)) {
            $query->with($with);
        }

        return $this->applyFilters($query, $filters)->get();
    }

    public function getPaginated(array $filters = [], int $perPage = 15, array $with = []): LengthAwarePaginator
    {
        $query = $this->model->newQuery();

        if (!empty($with)) {
            $query->with($with);
        }

        return $this->applyFilters($query, $filters)->paginate($perPage);
    }

    public function findById(int $id, array $with = []): ?StockItem
    {
        $query = $this->model->newQuery();

        if (!empty($with)) {
            $query->with($with);
        }

        return $query->find($id);
    }

    public function findBySku(string $sku, array $with = []): ?StockItem
    {
        $query = $this->model->newQuery();

        if (!empty($with)) {
            $query->with($with);
        }

        return $query->where('sku', $sku)->first();
    }

    public function create(array $data): StockItem
    {
        return $this->model->create($data);
    }

    public function update(int $id, array $data): bool
    {
        return $this->model->where('id', $id)->update($data);
    }

    public function delete(int $id): bool
    {
        return $this->model->where('id', $id)->delete();
    }

    public function getByCategory(int $categoryId, array $with = []): Collection
    {
        $query = $this->model->newQuery();

        if (!empty($with)) {
            $query->with($with);
        }

        return $query->where('category_id', $categoryId)->get();
    }

    public function getByType(string $type, array $with = []): Collection
    {
        $query = $this->model->newQuery();

        if (!empty($with)) {
            $query->with($with);
        }

        return $query->where('item_type', $type)->get();
    }

    public function getLowStockItems(array $with = []): Collection
    {
        $query = $this->model->newQuery();

        if (!empty($with)) {
            $query->with($with);
        }

        return $query->lowStock()->active()->get();
    }

    public function getOutOfStockItems(array $with = []): Collection
    {
        $query = $this->model->newQuery();

        if (!empty($with)) {
            $query->with($with);
        }

        return $query->outOfStock()->active()->get();
    }

    public function getOverstockItems(array $with = []): Collection
    {
        $query = $this->model->newQuery();

        if (!empty($with)) {
            $query->with($with);
        }

        return $query->overstock()->active()->get();
    }

    public function getByLocation(int $locationId, array $with = []): Collection
    {
        $query = $this->model->newQuery();

        if (!empty($with)) {
            $query->with($with);
        }

        return $query->whereHas('locationItems', function ($q) use ($locationId) {
            $q->where('location_id', $locationId)->where('quantity', '>', 0);
        })->get();
    }

    public function search(string $query, array $with = []): Collection
    {
        $queryBuilder = $this->model->newQuery();

        if (!empty($with)) {
            $queryBuilder->with($with);
        }

        return $queryBuilder->where(function ($q) use ($query) {
            $q->where('name', 'like', "%{$query}%")
              ->orWhere('sku', 'like', "%{$query}%")
              ->orWhere('description', 'like', "%{$query}%");
        })->get();
    }

    public function getItemsWithExpiringBatches(int $days = 30, array $with = []): Collection
    {
        $query = $this->model->newQuery();

        if (!empty($with)) {
            $query->with($with);
        }

        return $query->whereHas('batches', function ($q) use ($days) {
            $q->where('expiry_date', '<=', Carbon::now()->addDays($days))
              ->where('status', 'active')
              ->where('current_quantity', '>', 0);
        })->get();
    }

    public function getItemsRequiringCount(array $with = []): Collection
    {
        $query = $this->model->newQuery();

        if (!empty($with)) {
            $query->with($with);
        }

        // Items that haven't been counted in the last 90 days
        return $query->whereDoesntHave('physicalCounts', function ($q) {
            $q->where('count_date', '>=', Carbon::now()->subDays(90));
        })->active()->get();
    }

    public function updateQuantities(int $id, array $quantities): bool
    {
        return $this->model->where('id', $id)->update($quantities);
    }

    public function reserveQuantity(int $id, float $quantity, string $reason = null): bool
    {
        return DB::transaction(function () use ($id, $quantity, $reason) {
            $item = $this->model->lockForUpdate()->find($id);
            
            if (!$item || $item->available_quantity < $quantity) {
                return false;
            }

            return $item->update([
                'reserved_quantity' => $item->reserved_quantity + $quantity,
                'available_quantity' => $item->available_quantity - $quantity,
            ]);
        });
    }

    public function releaseReservedQuantity(int $id, float $quantity): bool
    {
        return DB::transaction(function () use ($id, $quantity) {
            $item = $this->model->lockForUpdate()->find($id);
            
            if (!$item || $item->reserved_quantity < $quantity) {
                return false;
            }

            return $item->update([
                'reserved_quantity' => $item->reserved_quantity - $quantity,
                'available_quantity' => $item->available_quantity + $quantity,
            ]);
        });
    }

    public function getStockSummary(): array
    {
        return [
            'total_items' => $this->model->active()->count(),
            'total_value' => $this->model->active()->sum(DB::raw('current_quantity * average_cost')),
            'low_stock_items' => $this->model->lowStock()->active()->count(),
            'out_of_stock_items' => $this->model->outOfStock()->active()->count(),
            'overstock_items' => $this->model->overstock()->active()->count(),
            'total_quantity' => $this->model->active()->sum('current_quantity'),
            'total_reserved' => $this->model->active()->sum('reserved_quantity'),
            'total_available' => $this->model->active()->sum('available_quantity'),
        ];
    }

    public function getByIds(array $ids, array $with = []): Collection
    {
        $query = $this->model->newQuery();

        if (!empty($with)) {
            $query->with($with);
        }

        return $query->whereIn('id', $ids)->get();
    }

    public function getActive(array $with = []): Collection
    {
        $query = $this->model->newQuery();

        if (!empty($with)) {
            $query->with($with);
        }

        return $query->active()->get();
    }

    public function getInactive(array $with = []): Collection
    {
        $query = $this->model->newQuery();

        if (!empty($with)) {
            $query->with($with);
        }

        return $query->where('is_active', false)->get();
    }

    public function bulkUpdate(array $items): bool
    {
        return DB::transaction(function () use ($items) {
            foreach ($items as $item) {
                $this->model->where('id', $item['id'])->update($item['data']);
            }
            return true;
        });
    }

    public function getItemsWithMovements(\DateTime $from, \DateTime $to, array $with = []): Collection
    {
        $query = $this->model->newQuery();

        if (!empty($with)) {
            $query->with($with);
        }

        return $query->whereHas('stockMovements', function ($q) use ($from, $to) {
            $q->whereBetween('movement_date', [$from, $to]);
        })->get();
    }

    public function getBySupplier(string $supplier, array $with = []): Collection
    {
        $query = $this->model->newQuery();

        if (!empty($with)) {
            $query->with($with);
        }

        return $query->whereHas('batches', function ($q) use ($supplier) {
            $q->where('supplier_name', 'like', "%{$supplier}%");
        })->get();
    }

    public function getBySeason(string $season, array $with = []): Collection
    {
        $query = $this->model->newQuery();

        if (!empty($with)) {
            $query->with($with);
        }

        return $query->where('season', $season)->get();
    }

    public function getByQualityGrade(string $grade, array $with = []): Collection
    {
        $query = $this->model->newQuery();

        if (!empty($with)) {
            $query->with($with);
        }

        return $query->where('quality_grade', $grade)->get();
    }

    public function getItemsRequiringReorder(array $with = []): Collection
    {
        return $this->getLowStockItems($with);
    }

    protected function applyFilters($query, array $filters)
    {
        if (isset($filters['category_id'])) {
            $query->where('category_id', $filters['category_id']);
        }

        if (isset($filters['item_type'])) {
            $query->where('item_type', $filters['item_type']);
        }

        if (isset($filters['is_active'])) {
            $query->where('is_active', $filters['is_active']);
        }

        if (isset($filters['search'])) {
            $search = $filters['search'];
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('sku', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        if (isset($filters['location_id'])) {
            $query->whereHas('locationItems', function ($q) use ($filters) {
                $q->where('location_id', $filters['location_id']);
            });
        }

        if (isset($filters['low_stock']) && $filters['low_stock']) {
            $query->lowStock();
        }

        if (isset($filters['out_of_stock']) && $filters['out_of_stock']) {
            $query->outOfStock();
        }

        if (isset($filters['overstock']) && $filters['overstock']) {
            $query->overstock();
        }

        return $query;
    }

    // Additional methods will be implemented in the next part...
    public function getAbcAnalysisData(\DateTime $from, \DateTime $to): array
    {
        // Implementation for ABC analysis
        return [];
    }

    public function getStockValuation(array $filters = []): array
    {
        // Implementation for stock valuation
        return [];
    }

    public function getStockAging(array $filters = []): array
    {
        // Implementation for stock aging
        return [];
    }

    public function getTurnoverAnalysis(\DateTime $from, \DateTime $to): array
    {
        // Implementation for turnover analysis
        return [];
    }

    public function generateNextSku(string $itemType, int $categoryId = null): string
    {
        // Implementation for SKU generation
        return '';
    }

    public function skuExists(string $sku, int $excludeId = null): bool
    {
        $query = $this->model->where('sku', $sku);
        
        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }
        
        return $query->exists();
    }

    public function getByCustomAttributes(array $attributes, array $with = []): Collection
    {
        // Implementation for custom attributes search
        return collect();
    }

    public function updateAverageCosts(array $items = []): bool
    {
        // Implementation for updating average costs
        return true;
    }

    public function getItemsForCycleCount(int $locationId, string $countType = 'partial'): Collection
    {
        // Implementation for cycle count items
        return collect();
    }

    public function getMovementHistory(int $id, \DateTime $from = null, \DateTime $to = null): Collection
    {
        // Implementation for movement history
        return collect();
    }

    public function getStockLevelsByLocation(int $id): Collection
    {
        // Implementation for stock levels by location
        return collect();
    }

    public function getBatchInformation(int $id): Collection
    {
        // Implementation for batch information
        return collect();
    }

    public function getReservationInformation(int $id): Collection
    {
        // Implementation for reservation information
        return collect();
    }
}
