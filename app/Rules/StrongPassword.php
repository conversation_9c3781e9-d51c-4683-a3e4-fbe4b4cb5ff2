<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class StrongPassword implements ValidationRule
{
    /**
     * Run the validation rule.
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        // Minimum length check
        if (strlen($value) < 12) {
            $fail('The :attribute must be at least 12 characters long.');
            return;
        }

        // Maximum length check
        if (strlen($value) > 128) {
            $fail('The :attribute must not exceed 128 characters.');
            return;
        }

        // Check for at least one lowercase letter
        if (!preg_match('/[a-z]/', $value)) {
            $fail('The :attribute must contain at least one lowercase letter.');
            return;
        }

        // Check for at least one uppercase letter
        if (!preg_match('/[A-Z]/', $value)) {
            $fail('The :attribute must contain at least one uppercase letter.');
            return;
        }

        // Check for at least one digit
        if (!preg_match('/\d/', $value)) {
            $fail('The :attribute must contain at least one number.');
            return;
        }

        // Check for at least one special character
        if (!preg_match('/[@$!%*?&]/', $value)) {
            $fail('The :attribute must contain at least one special character (@$!%*?&).');
            return;
        }

        // Check for common weak passwords
        $weakPasswords = [
            'password123', 'admin123456', 'qwerty123456', 'welcome123456',
            '123456789012', 'password1234', 'administrator'
        ];

        if (in_array(strtolower($value), $weakPasswords)) {
            $fail('The :attribute is too common. Please choose a more secure password.');
            return;
        }

        // Check for repeated characters (more than 3 consecutive)
        if (preg_match('/(.)\1{3,}/', $value)) {
            $fail('The :attribute cannot contain more than 3 consecutive identical characters.');
            return;
        }

        // Check for sequential characters
        if ($this->hasSequentialChars($value)) {
            $fail('The :attribute cannot contain sequential characters (e.g., 1234, abcd).');
            return;
        }
    }

    /**
     * Check for sequential characters in password
     */
    private function hasSequentialChars(string $password): bool
    {
        $sequences = [
            '0123456789',
            'abcdefghijklmnopqrstuvwxyz',
            'qwertyuiopasdfghjklzxcvbnm'
        ];

        foreach ($sequences as $sequence) {
            for ($i = 0; $i <= strlen($sequence) - 4; $i++) {
                $substr = substr($sequence, $i, 4);
                if (stripos($password, $substr) !== false) {
                    return true;
                }
            }
        }

        return false;
    }
}
