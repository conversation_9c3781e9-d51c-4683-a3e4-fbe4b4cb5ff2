<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use App\Models\User;
use App\Models\Company;
use App\Models\AiMarker\UserPermission;
use App\Services\AiMarker\PermissionService;
use Spatie\Permission\Models\Role;

/**
 * Seeder for granting AI Marker <NAME_EMAIL>
 */
class SuperadminAiMarkerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('🚀 Setting up AI Marker <NAME_EMAIL>...');

        // Step 1: Find or create the user
        $user = $this->findOrCreateUser();

        // Step 2: Ensure superadmin role
        $this->ensureSuperadminRole($user);

        // Step 3: Grant all AI Marker permissions
        $this->grantAllPermissions($user);

        // Step 4: Verify setup
        $this->verifySetup($user);

        $this->command->info('✅ Superadmin AI Marker setup completed!');
    }

    /**
     * Find or create the superadmin user
     */
    protected function findOrCreateUser(): User
    {
        $email = '<EMAIL>';
        
        $user = User::where('email', $email)->first();

        if ($user) {
            $this->command->info("👤 Found existing user: {$user->name}");
            return $user;
        }

        // Create company if needed
        $company = Company::first();
        if (!$company) {
            $company = Company::create([
                'name' => 'Red Rooks Apparel',
                'email' => '<EMAIL>',
                'phone' => '******-0123',
                'address' => '123 Fashion Street',
                'city' => 'New York',
                'state' => 'NY',
                'country' => 'USA',
                'postal_code' => '10001',
                'status' => 'active',
                'subscription_status' => 'active'
            ]);
            $this->command->info("🏢 Created company: {$company->name}");
        }

        // Create the user
        $user = User::create([
            'name' => 'AI Marker Superadmin',
            'email' => $email,
            'password' => Hash::make('AiMarker2024!'),
            'email_verified_at' => now(),
            'company_id' => $company->id,
            'status' => 'active',
            'phone' => '******-0100',
            'address' => '123 Admin Street',
            'city' => 'New York',
            'state' => 'NY',
            'country' => 'USA',
            'postal_code' => '10001'
        ]);

        $this->command->info("👤 Created user: {$user->name} ({$user->email})");
        $this->command->warn("🔑 Password: AiMarker2024!");

        return $user;
    }

    /**
     * Ensure user has superadmin role
     */
    protected function ensureSuperadminRole(User $user): void
    {
        // Create superadmin role if it doesn't exist
        $superadminRole = Role::firstOrCreate([
            'name' => 'superadmin',
            'guard_name' => 'web'
        ]);

        if (!$user->hasRole('superadmin')) {
            $user->assignRole('superadmin');
            $this->command->info("🔐 Assigned superadmin role");
        } else {
            $this->command->info("🔐 User already has superadmin role");
        }
    }

    /**
     * Grant all AI Marker permissions
     */
    protected function grantAllPermissions(User $user): void
    {
        $this->command->info("🎯 Granting all AI Marker permissions...");

        try {
            $permissionService = app(PermissionService::class);
            $availablePermissions = $permissionService->getAvailablePermissions();

            $grantedCount = 0;

            foreach ($availablePermissions as $code => $permission) {
                try {
                    UserPermission::updateOrCreate(
                        [
                            'user_id' => $user->id,
                            'company_id' => $user->company_id,
                            'permission_code' => $code
                        ],
                        [
                            'permission_category' => $permission['category'],
                            'permission_level' => $permission['level'],
                            'permission_name' => $permission['name'],
                            'permission_description' => $permission['description'],
                            'granted_by' => $user->id,
                            'is_active' => true,
                            'scope' => 'global',
                            'grant_reason' => 'Initial superadmin setup',
                            'approval_status' => 'approved',
                            'approved_by' => $user->id,
                            'approved_at' => now()
                        ]
                    );
                    $grantedCount++;
                } catch (\Exception $e) {
                    $this->command->error("Failed to grant {$code}: " . $e->getMessage());
                }
            }

            $this->command->info("✅ Granted {$grantedCount} permissions");

            // Clear any cached permissions
            \Cache::forget("ai_marker_permissions_{$user->id}");
            \Cache::forget("ai_marker_permissions_detailed_{$user->id}");

        } catch (\Exception $e) {
            $this->command->error("Error granting permissions: " . $e->getMessage());
        }
    }

    /**
     * Verify the setup
     */
    protected function verifySetup(User $user): void
    {
        $this->command->info("🔍 Verifying setup...");

        // Check permissions count
        $permissionCount = UserPermission::where('user_id', $user->id)
            ->where('is_active', true)
            ->count();

        $this->command->info("📊 Total permissions: {$permissionCount}");

        // Check key permissions
        $keyPermissions = [
            'ai-marker.access',
            'ai-marker.optimize.create',
            'ai-marker.patterns.create',
            'ai-marker.admin.users',
            'ai-marker.admin.system'
        ];

        $this->command->info("🔑 Key permissions check:");
        foreach ($keyPermissions as $permission) {
            $exists = UserPermission::where('user_id', $user->id)
                ->where('permission_code', $permission)
                ->where('is_active', true)
                ->exists();
            
            $status = $exists ? '✅' : '❌';
            $this->command->line("   {$status} {$permission}");
        }

        // Check role
        $hasRole = $user->hasRole('superadmin');
        $roleStatus = $hasRole ? '✅' : '❌';
        $this->command->line("   {$roleStatus} superadmin role");
    }
}
