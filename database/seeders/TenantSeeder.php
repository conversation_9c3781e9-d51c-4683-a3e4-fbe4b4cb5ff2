<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use <PERSON>tie\Permission\Models\Permission;

class TenantSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create roles for tenant
        $roles = [
            'admin' => 'Administrator - Full access to all features',
            'manager' => 'Manager - Access to most features with some restrictions',
            'buyer' => 'Buyer - Access to purchasing and supplier management',
            'supplier' => 'Supplier - Limited access for supplier portal',
            'merchandiser' => 'Merchandiser - Product and order management',
            'commercial' => 'Commercial - Sales and customer management',
            'accounts' => 'Accounts - Financial and accounting access',
            'production_manager' => 'Production Manager - Production planning and control',
        ];

        foreach ($roles as $roleName => $description) {
            Role::create([
                'name' => $roleName,
                'guard_name' => 'web',
            ]);
        }

        // Create basic permissions
        $permissions = [
            // Dashboard
            'dashboard-read',
            
            // User Management
            'users-create',
            'users-read',
            'users-update',
            'users-delete',
            
            // Order Management
            'orders-create',
            'orders-read',
            'orders-update',
            'orders-delete',
            
            // Stock Management
            'stock-create',
            'stock-read',
            'stock-update',
            'stock-delete',
            
            // Financial Management
            'finance-create',
            'finance-read',
            'finance-update',
            'finance-delete',
            
            // Production Management
            'production-create',
            'production-read',
            'production-update',
            'production-delete',
            
            // Reports
            'reports-read',
            'reports-export',
            
            // Settings
            'settings-read',
            'settings-update',
        ];

        foreach ($permissions as $permission) {
            Permission::create([
                'name' => $permission,
                'guard_name' => 'web',
            ]);
        }

        // Assign permissions to roles
        $this->assignPermissionsToRoles();

        // Create default options
        $this->createDefaultOptions();
    }

    /**
     * Assign permissions to roles.
     */
    protected function assignPermissionsToRoles(): void
    {
        $adminRole = Role::findByName('admin');
        $managerRole = Role::findByName('manager');
        $buyerRole = Role::findByName('buyer');
        $supplierRole = Role::findByName('supplier');
        $merchandiserRole = Role::findByName('merchandiser');
        $commercialRole = Role::findByName('commercial');
        $accountsRole = Role::findByName('accounts');
        $productionManagerRole = Role::findByName('production_manager');

        // Admin gets all permissions
        $adminRole->givePermissionTo(Permission::all());

        // Manager gets most permissions except user management
        $managerPermissions = Permission::where('name', 'not like', 'users-%')
                                      ->where('name', 'not like', 'settings-%')
                                      ->get();
        $managerRole->givePermissionTo($managerPermissions);

        // Buyer permissions
        $buyerRole->givePermissionTo([
            'dashboard-read',
            'orders-create',
            'orders-read',
            'orders-update',
            'stock-read',
            'reports-read',
        ]);

        // Supplier permissions (limited)
        $supplierRole->givePermissionTo([
            'dashboard-read',
            'orders-read',
            'stock-read',
        ]);

        // Merchandiser permissions
        $merchandiserRole->givePermissionTo([
            'dashboard-read',
            'orders-create',
            'orders-read',
            'orders-update',
            'stock-create',
            'stock-read',
            'stock-update',
            'production-read',
            'reports-read',
        ]);

        // Commercial permissions
        $commercialRole->givePermissionTo([
            'dashboard-read',
            'orders-create',
            'orders-read',
            'orders-update',
            'finance-read',
            'reports-read',
            'reports-export',
        ]);

        // Accounts permissions
        $accountsRole->givePermissionTo([
            'dashboard-read',
            'finance-create',
            'finance-read',
            'finance-update',
            'finance-delete',
            'reports-read',
            'reports-export',
        ]);

        // Production Manager permissions
        $productionManagerRole->givePermissionTo([
            'dashboard-read',
            'orders-read',
            'stock-read',
            'production-create',
            'production-read',
            'production-update',
            'production-delete',
            'reports-read',
        ]);
    }

    /**
     * Create default options for tenant.
     */
    protected function createDefaultOptions(): void
    {
        // This would be implemented based on your options table structure
        // For now, we'll skip this as it depends on the existing options system
    }
}
