<?php

namespace Database\Seeders;

use App\Models\Voucher;
use Illuminate\Database\Seeder;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;

class VoucherTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $vouchers = array(
            array('bill_no' => '1','user_id' => '2','party_id' => '4','bank_id' => NULL,'expense_id' => '2','income_id' => '2','type' => 'order_invoice','date' => '2024-07-27','prev_balance' => '0','current_balance' => '0','payment_method' => NULL,'voucher_no' => 'AINV-00001','status' => '1','bill_type' => NULL,'bill_amount' => '2664','amount' => '2664','is_profit' => '0','particulars' => NULL,'remarks' => NULL,'meta' => NULL,'deleted_at' => NULL,'created_at' => now(),'updated_at' => now()),
            array('bill_no' => '3','user_id' => '2','party_id' => '4','bank_id' => NULL,'expense_id' => '5','income_id' => '3','type' => 'order_invoice','date' => '2024-08-11','prev_balance' => '0','current_balance' => '0','payment_method' => NULL,'voucher_no' => 'AINV-00002','status' => '1','bill_type' => NULL,'bill_amount' => '2288','amount' => '2288','is_profit' => '0','particulars' => NULL,'remarks' => NULL,'meta' => NULL,'deleted_at' => NULL,'created_at' => now(),'updated_at' => now()),
            array('bill_no' => '4','user_id' => '1','party_id' => NULL,'bank_id' => NULL,'expense_id' => NULL,'income_id' => NULL,'type' => 'credit','date' => '2024-07-23','prev_balance' => '0','current_balance' => '********','payment_method' => 'cash','voucher_no' => NULL,'status' => '1','bill_type' => NULL,'bill_amount' => '0','amount' => '********','is_profit' => '0','particulars' => 'different_module','remarks' => NULL,'meta' => NULL,'deleted_at' => NULL,'created_at' => now(),'updated_at' => now()),
            array('bill_no' => '10','user_id' => '1','party_id' => NULL,'bank_id' => NULL,'expense_id' => NULL,'income_id' => NULL,'type' => 'employee_salary','date' => '2024-07-23','prev_balance' => '********','current_balance' => '9998000','payment_method' => 'cash','voucher_no' => NULL,'status' => '1','bill_type' => 'employee_salary','bill_amount' => '200000','amount' => '2000','is_profit' => '0','particulars' => 'different_module','remarks' => NULL,'meta' => '{"cheque_no":null,"issue_date":"2024-07-23"}','deleted_at' => NULL,'created_at' => now(),'updated_at' => now()),
            array('bill_no' => '11','user_id' => '1','party_id' => '5','bank_id' => NULL,'expense_id' => NULL,'income_id' => NULL,'type' => 'credit','date' => '2024-07-24','prev_balance' => '9998000','current_balance' => '********','payment_method' => 'cash','voucher_no' => NULL,'status' => '1','bill_type' => 'advance_payment','bill_amount' => '0','amount' => '5000','is_profit' => '0','particulars' => NULL,'remarks' => NULL,'meta' => NULL,'deleted_at' => NULL,'created_at' => now(),'updated_at' => now()),
            array('bill_no' => '12','user_id' => '2','party_id' => '6','bank_id' => '1','expense_id' => NULL,'income_id' => NULL,'type' => 'credit','date' => '2024-07-25','prev_balance' => '********','current_balance' => '********','payment_method' => 'bank','voucher_no' => NULL,'status' => '1','bill_type' => 'advance_payment','bill_amount' => '0','amount' => '10000','is_profit' => '0','particulars' => NULL,'remarks' => 'LC Payment','meta' => NULL,'deleted_at' => NULL,'created_at' => now(),'updated_at' => now()),
            array('bill_no' => '13','user_id' => '1','party_id' => '4','bank_id' => NULL,'expense_id' => '6','income_id' => '4','type' => 'order_invoice','date' => '2024-08-12','prev_balance' => '0','current_balance' => '0','payment_method' => NULL,'voucher_no' => 'AINV-00003','status' => '1','bill_type' => NULL,'bill_amount' => '176','amount' => '176','is_profit' => '0','particulars' => NULL,'remarks' => 'LC Payment','meta' => NULL,'deleted_at' => NULL,'created_at' => now(),'updated_at' => now()),
            array('bill_no' => '14','user_id' => '1','party_id' => '5','bank_id' => NULL,'expense_id' => '7','income_id' => '5','type' => 'order_invoice','date' => '2024-08-14','prev_balance' => '0','current_balance' => '0','payment_method' => NULL,'voucher_no' => '0000005','status' => '1','bill_type' => NULL,'bill_amount' => '0','amount' => '1320000','is_profit' => '0','particulars' => NULL,'remarks' => NULL,'meta' => NULL,'deleted_at' => NULL,'created_at' => now(),'updated_at' => now()),
            array('bill_no' => '15','user_id' => '1','party_id' => NULL,'bank_id' => '1','expense_id' => NULL,'income_id' => NULL,'type' => 'employee_salary','date' => '2024-07-29','prev_balance' => '********','current_balance' => '********','payment_method' => 'cash','voucher_no' => NULL,'status' => '1','bill_type' => 'employee_salary','bill_amount' => '200000','amount' => '20000','is_profit' => '0','particulars' => 'different_module','remarks' => NULL,'meta' => '{"cheque_no":null,"issue_date":"2024-07-29"}','deleted_at' => NULL,'created_at' => now(),'updated_at' => now()),
            array('bill_no' => '16','user_id' => '9','party_id' => NULL,'bank_id' => '1','expense_id' => NULL,'income_id' => '1','type' => 'credit','date' => '2024-08-03','prev_balance' => '********','current_balance' => '********','payment_method' => 'bank','voucher_no' => NULL,'status' => '1','bill_type' => NULL,'bill_amount' => '0','amount' => '20000','is_profit' => '0','particulars' => NULL,'remarks' => NULL,'meta' => '{"cheque_no":null,"issue_date":"2024-08-03","due_amount":0}','deleted_at' => NULL,'created_at' => now(),'updated_at' => now()),
            array('bill_no' => '17','user_id' => '9','party_id' => NULL,'bank_id' => NULL,'expense_id' => '4','income_id' => NULL,'type' => 'debit','date' => '2024-08-03','prev_balance' => '********','current_balance' => '********','payment_method' => 'cash','voucher_no' => NULL,'status' => '1','bill_type' => NULL,'bill_amount' => '0','amount' => '2000','is_profit' => '0','particulars' => 'Particulars','remarks' => 'Remarks','meta' => '{"cheque_no":null,"issue_date":"2024-08-03"}','deleted_at' => NULL,'created_at' => now(),'updated_at' => now()),
            array('bill_no' => '18','user_id' => '2','party_id' => NULL,'bank_id' => NULL,'expense_id' => NULL,'income_id' => NULL,'type' => 'employee_salary','date' => '2024-08-19','prev_balance' => '********','current_balance' => '9821000','payment_method' => 'cash','voucher_no' => NULL,'status' => '1','bill_type' => 'employee_salary','bill_amount' => '200000','amount' => '200000','is_profit' => '0','particulars' => 'different_module','remarks' => 'xyz','meta' => '{"cheque_no":null,"issue_date":"2024-08-06"}','deleted_at' => NULL,'created_at' => now(),'updated_at' => now()),
            array('bill_no' => '19','user_id' => '2','party_id' => '1','bank_id' => NULL,'expense_id' => '8','income_id' => '6','type' => 'order_invoice','date' => '2024-08-17','prev_balance' => '0','current_balance' => '0','payment_method' => NULL,'voucher_no' => '0000007','status' => '1','bill_type' => NULL,'bill_amount' => '0','amount' => '650000','is_profit' => '0','particulars' => NULL,'remarks' => NULL,'meta' => NULL,'deleted_at' => NULL,'created_at' => now(),'updated_at' => now()),
            array('bill_no' => '20','user_id' => '1','party_id' => '6','bank_id' => NULL,'expense_id' => NULL,'income_id' => '7','type' => 'order_invoice','date' => '2024-08-22','prev_balance' => '0','current_balance' => '0','payment_method' => NULL,'voucher_no' => '0000008','status' => '1','bill_type' => NULL,'bill_amount' => '0','amount' => '1800','is_profit' => '0','particulars' => NULL,'remarks' => 'LC Payment','meta' => NULL,'deleted_at' => NULL,'created_at' => now(),'updated_at' => now()),
            array('bill_no' => '21','user_id' => '3','party_id' => '6','bank_id' => NULL,'expense_id' => NULL,'income_id' => '7','type' => 'credit','date' => '2024-08-15','prev_balance' => '9821000','current_balance' => '9822800','payment_method' => 'cash','voucher_no' => NULL,'status' => '1','bill_type' => 'due_bill','bill_amount' => '0','amount' => '1800','is_profit' => '1','particulars' => NULL,'remarks' => 'jhg','meta' => '{"cheque_no":null,"issue_date":"2024-08-15","due_amount":110000}','deleted_at' => NULL,'created_at' => now(),'updated_at' => now()),
            array('bill_no' => '22','user_id' => '9','party_id' => '5','bank_id' => NULL,'expense_id' => NULL,'income_id' => NULL,'type' => 'debit','date' => '2024-08-19','prev_balance' => '9822800','current_balance' => '9822700','payment_method' => 'cash','voucher_no' => '1','status' => '1','bill_type' => 'balance_withdraw','bill_amount' => '0','amount' => '100','is_profit' => '0','particulars' => 'Ab','remarks' => 'Ab','meta' => '{"cheque_no":null,"issue_date":"2024-08-19"}','deleted_at' => NULL,'created_at' => now(),'updated_at' => now()),
            array('bill_no' => '23','user_id' => '1','party_id' => '7','bank_id' => NULL,'expense_id' => NULL,'income_id' => '8','type' => 'order_invoice','date' => '2024-08-22','prev_balance' => '0','current_balance' => '0','payment_method' => NULL,'voucher_no' => '0000010','status' => '1','bill_type' => NULL,'bill_amount' => '1000','amount' => '200','is_profit' => '0','particulars' => NULL,'remarks' => 'BIG PRODUCT','meta' => NULL,'deleted_at' => NULL,'created_at' => now(),'updated_at' => now()),
            array('bill_no' => '24','user_id' => '1','party_id' => '7','bank_id' => NULL,'expense_id' => NULL,'income_id' => '9','type' => 'order_invoice','date' => '2024-08-22','prev_balance' => '0','current_balance' => '0','payment_method' => NULL,'voucher_no' => '0000011','status' => '1','bill_type' => NULL,'bill_amount' => '1000','amount' => '10000','is_profit' => '0','particulars' => NULL,'remarks' => 'BIG PRODUCT','meta' => NULL,'deleted_at' => NULL,'created_at' => now(),'updated_at' => now()),
            array('bill_no' => '25','user_id' => '1','party_id' => NULL,'bank_id' => '1','expense_id' => NULL,'income_id' => NULL,'type' => 'employee_salary','date' => '2024-08-24','prev_balance' => '9822700','current_balance' => '9802700','payment_method' => 'bank','voucher_no' => NULL,'status' => '1','bill_type' => 'employee_salary','bill_amount' => '200000','amount' => '20000','is_profit' => '0','particulars' => 'different_module','remarks' => NULL,'meta' => '{"cheque_no":null,"issue_date":"2024-08-24"}','deleted_at' => NULL,'created_at' => now(),'updated_at' => now())
          );

          Voucher::insert($vouchers);
    }
}
